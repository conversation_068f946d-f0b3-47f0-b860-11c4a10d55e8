package com.lmig.uscm.booktransfer.opportunity.repo;

import com.lmig.uscm.booktransfer.opportunity.domain.OpportunityEvent;
import org.springframework.data.mongodb.repository.MongoRepository;

/**
 * @see OpportunityEvent for info
 */
public interface OpportunityEventRepo extends MongoRepository<OpportunityEvent, String> {
	/**
	 * This will fail if more than 1 result
	 */
	OpportunityEvent findByOpportunityId(String oppId);
	/**
	 * Gets the top result for matching OpportunityId
	 */
	OpportunityEvent findTopByOpportunityIdOrderByIdDesc(String oppId);
}

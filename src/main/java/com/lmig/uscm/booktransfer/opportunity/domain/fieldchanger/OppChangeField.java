package com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OppChangeField {
	private Integer oppId;
	// instead of doing this we may want to make a different object for each one.
	// And then on import it can tell the difference
	// //so that way field value can be correct
	// field name doesn't seem to give us anything as we are updating all at once
	private String fieldName;
	private String fieldValue;
}

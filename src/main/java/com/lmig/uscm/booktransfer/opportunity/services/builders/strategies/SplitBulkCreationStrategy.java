package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OriginSource;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunitySensitiveDataException;
import com.lmig.uscm.booktransfer.opportunity.services.SensitiveDataHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

public class SplitBulkCreationStrategy implements CreationStrategy {

	private static final String INDIVIDUAL_CREATION_REQUEST_FORMAT = "<ACORD>%s<InsuranceSvcRq><RqUID>%s</RqUID>%s</InsuranceSvcRq></ACORD>";

	private static final String INDIVIDUAL_CREATION_REQUEST_FORMAT_BYPASS_SCRUBBING = "<ACORD>%s<InsuranceSvcRq BypassScrubbing=\"SkipNaming\">" +
			"<RqUID>%s</RqUID>%s</InsuranceSvcRq></ACORD>";
	private static final List<LineOfBusiness> NOT_SPLITTABLE_LOBS = Arrays.asList(LineOfBusiness.MTR, LineOfBusiness.GENERIC);

	private final SensitiveDataHelper sensitiveDataHelper;

	public SplitBulkCreationStrategy(final SensitiveDataHelper sensitiveDataHelper) {
		this.sensitiveDataHelper = sensitiveDataHelper;
	}

	/**
	 * Not all LOBs are created equal
 	 */
	public static List<LineOfBusiness> getSplittableLobs() {
		List<LineOfBusiness> allLobs = new ArrayList<>(Arrays.asList(LineOfBusiness.values()));
		allLobs.removeAll(NOT_SPLITTABLE_LOBS);
		return allLobs;
	}

	/**
	 * Collects a list of Nodes for every {splittableLOB}QuoteInqRq tag
	 * where a splittableLOB is defined in getSplittableLobs()
	 *
	 * @param document
	 * @return
	 */
	public static List<Node> getAllQuoteInqRqNodesForSplittableLobs(final Document document) {
		Set<String> lobRqs = getSplittableLobs().stream()
				.flatMap(lob -> Arrays.stream(lob.getLobXpath().split(",")))
				.map(xpath -> "//" + xpath.trim())
				.collect(Collectors.toSet());

		return lobRqs.stream().map(lobRqXPath -> {
			try {
				// get all Nodes for each LOB xPath
				return XmlHelper.nodeListToList(
						XmlHelper.getNodeListFromParentNode(lobRqXPath, document)
				);
			} catch (XPathExpressionException | NullPointerException e) {
				return null;
			}
		}).filter(Objects::nonNull) // disregard errored
				.flatMap(List::stream) // extract List<List<Node>> to List<Node>
				.filter(Objects::nonNull) // remove empty Nodes
				.collect(Collectors.toList());
	}

	public boolean shouldResolveCreationRequest(OpportunityCreationRequest creationRequest) {
		return OriginSource.LTU.equals(creationRequest.getOriginSource());
	}

	@Override
	public void resolveCreationBundles(final List<CreationStrategyBundle> creationBundles, OpportunityCreationResponse response) {
		List<CreationStrategyBundle> copyCreationBundles = new ArrayList<>(creationBundles);
		for (CreationStrategyBundle bundle : copyCreationBundles) {
			resolveCreationBundle(creationBundles, response, bundle);
		}
	}

	private void resolveCreationBundle(final List<CreationStrategyBundle> creationBundles, OpportunityCreationResponse response, CreationStrategyBundle bundle) {
		bundle.getRequests().forEach(request ->  {
			int countOfQuoteInqRqNodes = getAllQuoteInqRqNodesForSplittableLobs(request.getUploadedACORD()).size();
			// If the request is not from LTU or there are no QuoteInqRqNodes found then continue to next request in the bundle
			if (!shouldResolveCreationRequest(request) || countOfQuoteInqRqNodes == 0) {
				return;
			}

			// If there is only one QuoteInqRqNode then just scrub the request and continue to next request in the bundle
			if (countOfQuoteInqRqNodes == 1) {
				Document scrubbedDoc = null;
				try {
					scrubbedDoc = this.sensitiveDataHelper.scrubXmlWithEnvCheck(request.getUploadedACORD());
				} catch (OpportunitySensitiveDataException e){
					response.incrementFailedCreationCount();
					response.addExceptions(e);
				}
				request.setUploadedACORD(scrubbedDoc);
				return;
			}

			try {
				// No longer need the bulk XML as its own individual request
				creationBundles.remove(bundle);
				// create requests for each QuoteInqRq in the bulk XML
				splitBulkXmlByQuoteInqRq(creationBundles, request, response);
			} catch (Exception e) {
				creationBundles.remove(bundle);
				response.setFailedCreationCount(response.getFailedCreationCount() + bundle.getRequests().size() + 1);
			}
		});
	}

	/**
	 * Adds newly created creationBundles to passed creationBundles list for each QuoteInqRq found in the passed request
	 * If any error occurs along the way, the incurred error will be added to the passed response and counted as a failure
	 * @param creationBundles pointer to original list of creationBundles
	 * @param request current request that should be a bulk XML needing split
	 * @param response current response to log successes/failures and errors
	 */
	private void splitBulkXmlByQuoteInqRq(final List<CreationStrategyBundle> creationBundles, final OpportunityCreationRequest request, final OpportunityCreationResponse response) throws XPathExpressionException, TransformerException {
		Document bulkDoc = request.getUploadedACORD();

		List<Node> foundRqsToSplit = getAllQuoteInqRqNodesForSplittableLobs(bulkDoc);

		List<String> foundOuterRqsToSplit = collectOuterNodeAsString(response, foundRqsToSplit);

		List<Document> createdDocsForNewBundles = buildAndCollectAcords(response, bulkDoc, foundOuterRqsToSplit);

		createdDocsForNewBundles.forEach(document -> {
			OpportunityCreationRequest newRequest = request.clone();
			newRequest.setAsMasterOpp();
			newRequest.setUploadedACORD(document);
			creationBundles.add(new CreationStrategyBundle(newRequest));
		});
	}

	/**
	 * Returns a list of the Documents created from the passed nodes using the predefined ACORD format.
	 * If any error occurs along the way, the incurred error will be added to the passed response and counted as a failure
	 * @param response
	 * @param foundOuterRqsToSplit
	 * @return
	 */
	private List<Document> buildAndCollectAcords(OpportunityCreationResponse response, Document bulkDoc, List<String> foundOuterRqsToSplit) throws XPathExpressionException, TransformerException {
		String bulkRqUID = AcordHelper.getRqUID(bulkDoc).equals("") ? UUID.randomUUID().toString() : AcordHelper.getRqUID(bulkDoc);
		String bulkOuterSignonRq = AcordHelper.getOuterSignonRq(bulkDoc);
		Node insuranceSvcRq = AcordHelper.getInsuranceSvcRq(bulkDoc);
		boolean isSkipNaming = insuranceSvcRq.hasAttributes() && insuranceSvcRq.getAttributes().getNamedItem("BypassScrubbing").getNodeValue().equals("SkipNaming");
		return foundOuterRqsToSplit.stream().map(foundRq -> {
			try {
				Document document = createAcordFromPattern(bulkOuterSignonRq, bulkRqUID, foundRq, isSkipNaming);
				return this.sensitiveDataHelper.scrubXmlWithEnvCheck(document);
			} catch (Exception e) {
				response.incrementFailedCreationCount();
				response.addExceptions(e);
				return null;
			}
		}).filter(Objects::nonNull).collect(Collectors.toList());
	}

	/**
	 * Returns a list of the string representation of the passed nodes, including the node tag itself as part of the string.
	 * If any error occurs along the way, the incurred error will be added to the passed response and counted as a failure
	 * @param response
	 * @param foundRqsToSplit
	 * @return
	 */
	private List<String> collectOuterNodeAsString(OpportunityCreationResponse response, List<Node> foundRqsToSplit) {
		return foundRqsToSplit.stream().map(foundRq -> {
			try {
				return XmlHelper.getOuterXml(foundRq);
			} catch (Exception e) {
				response.incrementFailedCreationCount();
				response.addExceptions(e);
				return null;
			}
		}).filter(Objects::nonNull).collect(Collectors.toList());
	}

	private static Document createAcordFromPattern(String signonRq, String rqUID, String lobQuoteInqRq, boolean isSKipNaming) throws IOException, SAXException, ParserConfigurationException {
		String docAsString = isSKipNaming ?
				String.format(INDIVIDUAL_CREATION_REQUEST_FORMAT_BYPASS_SCRUBBING, signonRq, rqUID, lobQuoteInqRq)
				: String.format(INDIVIDUAL_CREATION_REQUEST_FORMAT, signonRq, rqUID, lobQuoteInqRq);
		return XmlHelper.getDocument(docAsString);
	}

	@Override
	public CreationStrategyName getStrategyName() {
		return CreationStrategyName.SplitBulkXmlByLobRq;
	}
}

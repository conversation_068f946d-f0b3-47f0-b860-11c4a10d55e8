package com.lmig.uscm.booktransfer.opportunity.repo.helpers;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PublishOpportunityEventAnno {

	/**
	 * Signifies the type of PublishOpportunityEvent If the Repo method can be either, use CREATED_OR_UPDATED_EVENT
	 */
	String CREATED_EVENT = "CREATED";
	String UPDATED_EVENT = "UPDATED";
	// Use if a method could be either a CREATE OR UPDATE
	String CREATED_OR_UPDATED_EVENT = "CREATED_OR_UPDATED_EVENT";

	/**
	 * CREATED or UPDATED
	 */
	String value();

}

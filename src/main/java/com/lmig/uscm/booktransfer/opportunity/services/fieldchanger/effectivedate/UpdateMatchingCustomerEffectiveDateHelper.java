package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.email.client.domain.EmailException;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.ChangeEffectiveDateResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.MassUpdaterForChangeEffectiveDate;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.PartialMatchResult;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.ExportHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.utilityservice.CSVBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * This class handles any of the classes (home, true lead) that deal with matching customers
 */
@Slf4j
public abstract class UpdateMatchingCustomerEffectiveDateHelper extends UpdateEffectiveDateHelper {

	private final BookTransferService bookTransferService;

	public UpdateMatchingCustomerEffectiveDateHelper(final OpportunityHelper opportunityHelper,
			final BookTransferService bookTransferService,
			final ExportHelper exportHelper,
			final OpportunityRepoHelper opportunityRepoHelper) {
		super(opportunityHelper, exportHelper, opportunityRepoHelper);
		this.bookTransferService = bookTransferService;
	}

	protected abstract LocalDate getEarliestEffectiveDates(List<OppChangeFieldResult> packagedOpportunities,
			LocalDate bookTransferStartDate);

	@Override
	protected ChangeEffectiveDateResponse sendResultCSVs(List<Opportunity> opps, String resultsCSV, String userEmail)
			throws JsonProcessingException, BookTransferException, EmailException {
		String partialMatchResultCSV = generatePartialMatchOfAgencyIdsCSV(opps);
		// create and send all string to email notification here
		exportHelper.sendUpdateEffectiveDateCSVs(resultsCSV, partialMatchResultCSV, userEmail);

		return new ChangeEffectiveDateResponse(partialMatchResultCSV, resultsCSV);
	}

	@Override
	protected MassUpdaterForChangeEffectiveDate buildInitialOpporytunityResults(List<Opportunity> opportunities) {
		MassUpdaterForChangeEffectiveDate matchingCustomersMap = new MassUpdaterForChangeEffectiveDate();
		for (Opportunity opp : opportunities) {
			OppChangeFieldResult oppChangeFieldResult =
					new OppChangeFieldResult(opp, "EffectiveDate", opp.getEffectiveDate());
			if (StringUtils.isBlank(opp.getAgencyId()) || opp.getBookTransferID() == 0) {
				oppChangeFieldResult.isError("AgencyID or SubCode missing");
			}
			matchingCustomersMap.addOpportunityChangeResult(oppChangeFieldResult);
		}
		return matchingCustomersMap;
	}

	protected String generatePartialMatchOfAgencyIdsCSV(List<Opportunity> opps) {
		List<String> allAgencyIds = getAgencyIds(opps);

		List<PartialMatchResult> partialMatchResult = new ArrayList<>();

		for (Opportunity opp : opps) {
			List<String> partialMatches = getPartialMatches(allAgencyIds, opp);
			if (!partialMatches.isEmpty()) {
				partialMatchResult.add(createPartialMatchObj(opp, partialMatches));
			}
		}
		return createPartialCSV(partialMatchResult);
	}

	private List<String> getAgencyIds(List<Opportunity> opportunities) {
		List<String> allAgencyIds = new ArrayList<>();
		for (Opportunity opp : opportunities) {
			if (StringUtils.isNotBlank(opp.getAgencyId())) {
				allAgencyIds.add(opp.getAgencyId());
			}
		}
		return allAgencyIds;
	}

	protected List<String> getPartialMatches(List<String> allAgencyIds, Opportunity o) {
		List<String> partialMatches = new ArrayList<>();
		for (String agencyId : allAgencyIds) {
			// does agency id the first 6 chars of o.getAgenctId, but agency id doesnt equal
			// opportunity agency id.
			if (seeIfFirst6CharsMatch(o, agencyId) && !agencyId.equalsIgnoreCase(o.getAgencyId())) {
				partialMatches.add(agencyId);
			}
		}
		return partialMatches;
	}

	private static boolean seeIfFirst6CharsMatch(Opportunity o, String agencyId) {
		if (agencyId == null || o == null || o.getAgencyId() == null) {
			return false;
		}
		return agencyId.regionMatches(0, o.getAgencyId(), 0, 6);
	}

	private PartialMatchResult createPartialMatchObj(Opportunity o, List<String> partialMatches) {
		PartialMatchResult result = new PartialMatchResult();
		updateBookTransferInfoForPartialMatches(o.getBookTransferID(), result);
		result.setOpportunityId(o.getOpportunityId());
		result.setCustomerName(o.getCustomerName());
		result.setOriginalEffectiveDate(o.getEffectiveDate());
		result.setLOBCd(o.getBusinessType());

		result.setAgencyId(o.getAgencyId());
		result.setPossibleAgencyIdsMatches(partialMatches);
		return result;
	}

	private PartialMatchResult updateBookTransferInfoForPartialMatches(Integer bookTransferId,
			PartialMatchResult result) {
		try {
			BookTransferDTO bt = bookTransferService.findByBookTransferId(bookTransferId);
			result.setSubCode(bt.getSubCode());
			result.setSalesforceId(bt.getSalesforceCode());
		} catch (BookTransferException btException) {
			log.error("Partial Match Exception", btException);
			result.setSubCode("Could Not Find BookTransfer");
			result.setSalesforceId("Could Not Find BookTransfer");
		}
		return result;
	}

	private String createPartialCSV(List<PartialMatchResult> results) {
		// csv header
		CSVBuilder csvBuilder = new CSVBuilder(
				"OppID,AgencyID,CustomerName,EffectiveDt,BusinessType,Subcode,SalesforceID,PossibleMatches");
		csvBuilder.addNewLine();

		for (PartialMatchResult r : results) {
			csvBuilder.addRowToCSV(true, String.valueOf(r.getOpportunityId()), r.getAgencyId(), r.getCustomerName(),
					r.getOriginalEffectiveDate(), r.getLOBCd(), r.getSubCode(), r.getSalesforceId(),
					String.join(",", r.getPossibleAgencyIdsMatches()));
		}
		return csvBuilder.toString();
	}

	@Override
	protected List<OppChangeFieldResult> updateEffectiveDatesByPackage(List<OppChangeFieldResult> packagedOpportunities,
			LocalDate bookTransferStartDate) {
		LocalDate earliestEffectiveDate = getEarliestEffectiveDates(packagedOpportunities, bookTransferStartDate);
		for (OppChangeFieldResult oppChangeFieldResult : packagedOpportunities) {
			setEffectiveDate(oppChangeFieldResult, earliestEffectiveDate, bookTransferStartDate);
		}

		return packagedOpportunities;
	}

	private OppChangeFieldResult setEffectiveDate(OppChangeFieldResult oppChangeFieldResult,
			LocalDate earliestEffectiveDateForPackage,
			LocalDate bookTransferStartDate) {
		// we need to calculate this based on the earliestEffectiveDate due to the fact
		// the earliest effective date
		// may be more policy terms in the future then booktransfer start date
		// due to the fact that home lead could force this to be a while out
		LocalDate firstPossibleEffectiveDate =
				calculateNewEffectiveDate(oppChangeFieldResult.getOpportunity(), earliestEffectiveDateForPackage);
		LocalDate currentEffectiveDate = LocalDate.parse(oppChangeFieldResult.getOpportunity().getEffectiveDate());

		if (doesDateNotNeedChanged(earliestEffectiveDateForPackage, currentEffectiveDate, bookTransferStartDate)) {
			// This is so that way the business can see the update did not happened, but no
			// errors occurred
			return oppChangeFieldResult.isError("Effective date same as updated date or is in same month");

			// We need to update to firstPossibleEffectiveDate and not the package date as
			// if we have the case were
			// firstPossibleEffectiveDate is 05-15-2019 and earliestEffectiveDateForPackage
			// is 05-29-2019
			// then they are the same month so update to firstPossibleEffectiveDate.
		} else if (hasSameMonthAndYear(firstPossibleEffectiveDate, earliestEffectiveDateForPackage)) {
			return updateEffectiveDate(oppChangeFieldResult, firstPossibleEffectiveDate);
		} else {
			return updateEffectiveDate(oppChangeFieldResult, earliestEffectiveDateForPackage);

		}
	}

	/**
	 * Gets the earliest effective date for all polices.
	 *
	 * @param packagedOpportunities
	 *            the opps packaged together
	 * @param bookTransferStartDate
	 *            the start date of this transfer.
	 */
	protected LocalDate getEarliestLeadEffectiveDates(List<OppChangeFieldResult> packagedOpportunities,
			LocalDate bookTransferStartDate) {
		LocalDate earliestLeadEffectiveDate = null;
		for (OppChangeFieldResult oppChangeFieldResult : packagedOpportunities) {
			Opportunity opp = oppChangeFieldResult.getOpportunity();
			if (StringUtils.isBlank(opp.getEffectiveDate())) {
				continue;
			}
			LocalDate effectiveDate = calculateNewEffectiveDate(opp, bookTransferStartDate);
			// true lead, regardless of lob
			// we need to calculate both as if lead type isnt home or if home lead type is
			// null we need to still have this value
			earliestLeadEffectiveDate = getEarliestDate(earliestLeadEffectiveDate, effectiveDate);
		}
		return earliestLeadEffectiveDate;
	}

	private boolean doesDateNotNeedChanged(LocalDate earliestEffectiveDateForPackage,
			LocalDate currentEffectiveDate,
			LocalDate bookTransferStartDate) {
		return isCurrentDateSameAsEarliestDate(earliestEffectiveDateForPackage, currentEffectiveDate)
				|| isSameMonthAndYearAfterTheBTStartDate(earliestEffectiveDateForPackage, currentEffectiveDate,
						bookTransferStartDate);
	}

	private boolean isSameMonthAndYearAfterTheBTStartDate(LocalDate earliestEffectiveDateForPackage,
			LocalDate currentEffectiveDate,
			LocalDate bookTransferStartDate) {
		return hasSameMonthAndYear(currentEffectiveDate, earliestEffectiveDateForPackage)
				&& !bookTransferStartDate.isAfter(currentEffectiveDate);
	}

	private boolean hasSameMonthAndYear(LocalDate earliestEffectiveDate, LocalDate effectiveDate) {
		return effectiveDate.getMonthValue() == earliestEffectiveDate.getMonthValue()
				&& effectiveDate.getYear() == earliestEffectiveDate.getYear();
	}

	protected LocalDate getEarliestDate(LocalDate currentEarliestEffectiveDate, LocalDate effectiveDate) {
		if (currentEarliestEffectiveDate == null) {
			currentEarliestEffectiveDate = effectiveDate;
		} else if (effectiveDate.isBefore(currentEarliestEffectiveDate)) {
			currentEarliestEffectiveDate = effectiveDate;
		}
		return currentEarliestEffectiveDate;
	}

}

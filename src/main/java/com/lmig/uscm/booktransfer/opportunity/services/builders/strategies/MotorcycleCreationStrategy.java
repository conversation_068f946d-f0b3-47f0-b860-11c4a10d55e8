package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.UtilityService;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.xpath.XPathExpressionException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.StreamSupport;

public class MotorcycleCreationStrategy implements CreationStrategy {

	private static final String[] MOTORCYCLE_BODY_TYPES = {"AT", "AMC", "ATV", "GC", "GO", "GOLFM", "MB", "MC",
			"MNS", "MOPED", "MOTORBIKE", "MOTORCYCLETHREEWHEEL", "MOTORCYCLETWOWHEEL", "MOTORSCOOTER", "MP", "MS", "OMC", "ORAMC", "SNOWM", "TB", "UTV"};
	private static final String[] MOTORCYCLE_MANUFACTURERS = {"Arctic Cat", "Arcti", "Arct", "Arti",
			"Harley-Davidson", "HD", "Harley-Dav", "Harley", "H-D", "Harl", "Pols", "Polaris", "Pola",
			"Ski-Doo", "Skidoo", "Skid", "Yamaha", "Yamah", "Yama", "Victory", "Vcty", "Vict", "Triumph",
			"Trium", "Triu", "Indian", "Indian Motorcycle", "Indi", "India", "Ducate", "Duca",
			"Ezgo", "Easy", "Argo", "Argo Atv", "Vespa", "ClubCar", "Club", "Kawasaki", "Kawa", "Can-Am",
			"Canam", "KTM", "Buell", "Kymco", "Redcat", "Golf", "Golf1", "John Deere", "CF Moto", "Husqvarna"};
	private static final String[] MOTORCYCLE_POLICY_NUMBER_PREFIXES = {"MC", "SM", "MSA"};
	
	public boolean shouldResolveCreationRequest(OpportunityCreationRequest creationRequest) {
		try {
			boolean isMtrPolicy = isMotorcyclePolicy(creationRequest.getUploadedACORD());
			NodeList allVehs = AcordHelper.getPersVehs(creationRequest.getUploadedACORD());
			boolean containsMtr = StreamSupport.stream(
					XmlHelper.iterable(allVehs).spliterator(), false)
					.anyMatch(this::isVehMotorcycle);
			return isMtrPolicy || containsMtr;
		} catch (XPathExpressionException | NullPointerException e) {
			// Just eat it, eat it, just eat it, eat it, ooh
		}
		return false;
	}

	/**
	 * Identifies a vehicle as a motorcycle by all means. Currently, these are:
	 *  VehBodyTypeCd
	 *  Manufacturer
	 */
	private boolean isVehMotorcycle(Node vehicle) {
		return isVehMotorcycleBodyType(vehicle) || isVehMotorcycleManufacturer(vehicle);
	}

	private boolean isVehMotorcycleBodyType(Node vehicle) {
		try {
			return Arrays.asList(MOTORCYCLE_BODY_TYPES).contains(AcordHelper.getVehBodyType(vehicle));
		} catch (XPathExpressionException | NullPointerException  e) {
			// Just eat it, eat it, just eat it, eat it, ooh
		}
		return false;
	}

	private boolean isVehMotorcycleManufacturer(Node vehicle) {
		try {
			String foundManufacturer = AcordHelper.getManufacturer(vehicle);
			return Arrays.stream(MOTORCYCLE_MANUFACTURERS)
					.anyMatch(manufacturer -> StringUtils.equalsIgnoreCase(foundManufacturer, manufacturer));
		} catch (XPathExpressionException | NullPointerException  e) {
			// Just eat it, eat it, just eat it, eat it, ooh
		}
		return false;
	}

	private boolean isMotorcyclePolicy(Document document) {
		try {
			String foundPolicyNumber = AcordHelper.getPersAutoPolicyNumber(document);
			return Arrays.stream(MOTORCYCLE_POLICY_NUMBER_PREFIXES)
					.anyMatch(prefix -> StringUtils.startsWithIgnoreCase(foundPolicyNumber, prefix));
		} catch (XPathExpressionException | NullPointerException  e) {
			// Just eat it, eat it, just eat it, eat it, ooh
		}
		return false;
	}

	@Override
	public void resolveCreationBundles(List<CreationStrategyBundle> creationBundles, OpportunityCreationResponse response) {
		List<CreationStrategyBundle> copyCreationBundles = new ArrayList<>(creationBundles);
		for (CreationStrategyBundle bundle : copyCreationBundles) {
			resolveCreationBundle(creationBundles, response, bundle);
		}
	}

	private void resolveCreationBundle(List<CreationStrategyBundle> creationBundles, OpportunityCreationResponse response, CreationStrategyBundle bundle) {
		for (OpportunityCreationRequest request : bundle.getRequests()) {
			 if (!shouldResolveCreationRequest(request)) {
				 continue;
			 }

			 try {
				 splitMotorcycleRequests(creationBundles, request);
			 } catch (XPathExpressionException e) {
				 creationBundles.remove(bundle);
				 response.setFailedCreationCount(response.getFailedCreationCount() + bundle.getRequests().size() + 1);
				 break;
			 }
		}
	}

	/**
	 * Given the request has an opportunity id already, it is meant to be reuploaded. Mark the request
	 * to replace the originalXML in the saved Opportunity in case motorcycles were recently added and
	 * the splitting logic separates this request into multiple
	 *
	 */
	private void markXmlForReplacementIfReupload(final OpportunityCreationRequest request) {
		int existingOppId = Optional.ofNullable(request.getExistingOpportunityID()).orElse(0);
		if (existingOppId != 0) {
			request.setShouldReplaceOriginalXml(true);
		}
	}

	/**
	 * Splits motorcycle vehicles out of incoming request into a new bundle. If no cars exist on the incoming request, then
	 * the request XML gets replaced by the motorcycle request XML instead of creating an additional bundle.
	 * <p>
	 * Note: Will set the request's shouldReplaceOriginalXml to true if the request splits and an Opportunity exists
	 */
	protected void splitMotorcycleRequests(
			List<CreationStrategyBundle> creationBundles,
			OpportunityCreationRequest request
	) throws XPathExpressionException {
		markXmlForReplacementIfReupload(request);
		// Skip vehicle type check if the policy is marked as motorcycle
		if (isMotorcyclePolicy(request.getUploadedACORD())) {
			AcordHelper.setMotorcycleLobCd(request.getUploadedACORD(), LineOfBusiness.MTR.getPossibleLobAcordValues()[0]);
			return;
		}

		Document document = request.getUploadedACORD();
		NodeList allVehicles = AcordHelper.getPersVehs(document);
		int totalVehicleCount = allVehicles.getLength();
		AcordHelper.removeAllVehicles(document);

		OpportunityCreationRequest motorcycleRequest = request.clone();
		AcordHelper.setMotorcycleLobCd(motorcycleRequest.getUploadedACORD(), LineOfBusiness.MTR.getPossibleLobAcordValues()[0]);

		boolean hasAuto = false;
		for (int i = 0; i < totalVehicleCount; i++) {
			Node vehicle = allVehicles.item(i);
			if (isVehMotorcycle(vehicle)) {
				AcordHelper.addVehicle(motorcycleRequest.getUploadedACORD(), vehicle);
		   } else {
				hasAuto = true;
			   	AcordHelper.addVehicle(document, vehicle);
		   }
		}

		if (hasAuto) {
			double mtrTotalCoverageTermAmt = AcordHelper.getPersVehTotalCoverageCurrentTermAmt(motorcycleRequest.getUploadedACORD());
			subtractMtrAmtsFromAutoPolicyTermAmt(document, mtrTotalCoverageTermAmt);
			calculateMtrPersPolicyTermAmt(motorcycleRequest, mtrTotalCoverageTermAmt);

			creationBundles.add(new CreationStrategyBundle(motorcycleRequest));

		} else {
			// Replace auto request XML with motorcycle request XML
			request.setUploadedACORD(motorcycleRequest.getUploadedACORD());
		}
	}

	private void subtractMtrAmtsFromAutoPolicyTermAmt(Document autoDocument, double mtrTotalCoverageTermAmt) throws XPathExpressionException {
		double autoPersPolicyTermAmt = UtilityService.convertStringToDouble(AcordHelper.getPolicyCurrentTermAmt(autoDocument));
		AcordHelper.setPolicyCurrentTermAmt(autoDocument, autoPersPolicyTermAmt - mtrTotalCoverageTermAmt);
	}

	private void calculateMtrPersPolicyTermAmt(OpportunityCreationRequest motorcycleRequest, double mtrTotalCoverageTermAmt) throws XPathExpressionException {
		AcordHelper.setPolicyCurrentTermAmt(motorcycleRequest.getUploadedACORD(), mtrTotalCoverageTermAmt);
	}

	@Override
	public CreationStrategyName getStrategyName() {
		return CreationStrategyName.SplitUpMotorcycles;
	}
}

package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.AddressCleanseResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.AddressCleanseResult;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.MultipleUnparsedAddressRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.PropertyInfoResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.UnparsedAddress;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.xpath.XPathExpressionException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

@Service
@Slf4j
public class AddressCleanseService {

    private final AddressCleanseHelper addressCleanseHelper;
    private final PropertyInfoHelper propertyInfoHelper;
    public AddressCleanseService(final AddressCleanseHelper addressCleanseHelper, final PropertyInfoHelper propertyInfoHelper) {
        this.addressCleanseHelper= addressCleanseHelper;
        this.propertyInfoHelper = propertyInfoHelper;
    }
    public Document addCountiesToLocations(Document acord) throws XPathExpressionException {
        NodeList locationNodes = XmlHelper.getNodeList(acord, "//Location");
        List<Node> locationsWithoutCounties = new ArrayList<>();

        for (Node location : XmlHelper.iterable(locationNodes)) {
            locationsWithoutCounties.add(location);
        }

        String lob = LineOfBusiness.determineLobForPersonalLineType(acord).name();
        // Create and Send request for all locations
        MultipleUnparsedAddressRequest request = createRequest(locationsWithoutCounties);
        AddressCleanseResponse response = new AddressCleanseResponse();
        if (request.getUnparsedAddresses() != null && !request.getUnparsedAddresses().isEmpty()) {
            response = OpportunityUtil.runFunctionWithRetriesWithDefault(addressCleanseHelper::sendRequest, request, null);
        }

        if (response != null && !response.getAddresses().isEmpty()) {
            if (response.getContext() != null) {
                log.info("Address Service TraceID: {} ", response.getContext().getTraceId());
            }
            List<AddressCleanseResult> cleanseResults = response.getAddresses();
            // Apply counties to the locations
            applyCountiesToLocations(locationsWithoutCounties, cleanseResults, lob);
        }
        return acord;
    }

    private void applyCountiesToLocations(List<Node> locationsWithoutCounties, List<AddressCleanseResult> cleanseResults, String lob) {
       IntStream.range(0,Math.min(locationsWithoutCounties.size(), cleanseResults.size()))
               .forEach(i -> {
                   Node location = locationsWithoutCounties.get(i);
                   AddressCleanseResult result = cleanseResults.get(i);
                   String county = result.getAddress().getCountyName();
                  PropertyInfoResponse propertyInfoResponse = propertyInfoHelper.getTerritoryAndCoastalLine(result.getAddress(), lob);

                   try {
                       updateLocationWithCounty(location, county, propertyInfoResponse, lob);
                   } catch (Exception e) {
                       log.warn("Error updating location with county", e);
                   }
               });
    }

    private MultipleUnparsedAddressRequest createRequest(List<Node> locations) throws XPathExpressionException {
        MultipleUnparsedAddressRequest request = new MultipleUnparsedAddressRequest();
        List<UnparsedAddress> addresses = new ArrayList<>();

        for (Node location : locations) {
            Node addr = XmlHelper.getNodeFromParentNode("./Addr",location);
            if (addr != null) {
                String addr1 = AcordHelper.getAddr1(addr);
                String city = AcordHelper.getCity(addr);
                String stateProvCd = AcordHelper.getState(addr);
                String postalCode = AcordHelper.getPostalCode(addr);
                if (StringUtils.isNotEmpty(addr1) && StringUtils.isNotEmpty(postalCode)) {
                    UnparsedAddress unparsedAddress = new UnparsedAddress(addr1, city, stateProvCd, postalCode);
                    addresses.add(unparsedAddress);
                }
            }
        }
        request.setUnparsedAddresses(addresses);
        return request;
    }

    private void updateLocationWithCounty(Node location, String county, PropertyInfoResponse propertyInfoResponse,
                                          String lob) throws XPathExpressionException {
        Document ownerDocument = location.getOwnerDocument();

        Node territoryNode = getTerritoryNode(propertyInfoResponse.getTerritory(), ownerDocument, lob);
        Node coastalLineNode = getCoastalLineNode(propertyInfoResponse.getDistanceToCoastalLine(), ownerDocument);
        Node ratingTerritory = getRatingTerritoryNode(propertyInfoResponse.getRatingTerritory(), ownerDocument);
        Node addr = XmlHelper.getNodeFromParentNode("./Addr",location);
        Node countyNode = XmlHelper.getNodeFromParentNode("./County", addr);
        if (countyNode == null) {
            countyNode = ownerDocument.createElement("County");
        }
        countyNode.setTextContent(StringUtils.isNotBlank(county) ?
          county.replace("County", "").trim() : "");

        addr.appendChild(countyNode);
        addr.appendChild(territoryNode);
        addr.appendChild(ratingTerritory);
        addr.appendChild(coastalLineNode);
    }

    Node getRatingTerritoryNode(String ratingTerritory, Document ownerDocument) {
        if (StringUtils.isEmpty(ratingTerritory)) {
            return ownerDocument.createElement("RatingTerritory");
        }

        Node ratingTerritoryNode = ownerDocument.createElement("RatingTerritory");
        ratingTerritoryNode.setTextContent(ratingTerritory);
        return ratingTerritoryNode;
    }

    protected Node getTerritoryNode(String territory, Document ownerDocument, String lob) {
        log.info("Territory: {}", territory);
        if (StringUtils.isEmpty(territory)) {
            return ownerDocument.createElement("Territory");
        }

        if (LineOfBusiness.determineLobFromValue(lob) == LineOfBusiness.AUTOP) {
            Node territoryNode = ownerDocument.createElement("Territory");
            String[] territories = territory.split(",");
            for (String terr : territories) {
                String[] terrParts = terr.split(":");
                log.info("Territory: {} : {}", terrParts[0], terrParts[1]);
                Node territoryItem = ownerDocument.createElement(terrParts[0].trim());
                territoryItem.setTextContent(terrParts[1].trim());
                territoryNode.appendChild(territoryItem);
            }
            return territoryNode;
        }

        Node territoryNode = ownerDocument.createElement("Territory");
        territoryNode.setTextContent(territory);
        return territoryNode;
    }

    private Node getCoastalLineNode(String distanceToCoastalLine, Document ownerDocument) {
        if (StringUtils.isEmpty(distanceToCoastalLine)) {
            return ownerDocument.createElement("CoastalLine");
        }

        Node coastalLineNode = ownerDocument.createElement("CoastalLine");
        Node coastalLineDistanceNode = ownerDocument.createElement("Distance");
        coastalLineDistanceNode.setTextContent(distanceToCoastalLine);
        coastalLineNode.appendChild(coastalLineDistanceNode);
        Node coastalLineMeasureNode = ownerDocument.createElement("Measure");
        coastalLineMeasureNode.setTextContent("Feet");
        coastalLineNode.appendChild(coastalLineMeasureNode);
        return coastalLineNode;
    }
}

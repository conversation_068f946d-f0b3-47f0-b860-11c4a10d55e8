package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lmig.uscm.booktransfer.email.client.domain.EmailException;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.ChangeEffectiveDateResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.MassUpdaterForChangeEffectiveDate;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.ExportHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;

import java.time.LocalDate;
import java.util.List;

/**
 * This class handles the set effective data option
 */
public class UpdateSetEffectiveDateHelper extends UpdateEffectiveDateHelper {
	public UpdateSetEffectiveDateHelper(final OpportunityHelper opportunityHelper, final ExportHelper exportHelper, final OpportunityRepoHelper opportunityRepoHelper) {
		super(opportunityHelper, exportHelper, opportunityRepoHelper);
	}

	@Override
	protected ChangeEffectiveDateResponse sendResultCSVs(List<Opportunity> opps, String resultsCSV, String userEmail)
			throws JsonProcessingException, EmailException {
		exportHelper.sendUpdateEffectiveDateCSVs(resultsCSV, null, userEmail);

		return new ChangeEffectiveDateResponse(null, resultsCSV);
	}

	@Override
	protected List<OppChangeFieldResult> updateEffectiveDatesByPackage(List<OppChangeFieldResult> packagedOpportunities,
			LocalDate newEffectiveDate) {

		for (OppChangeFieldResult oppChangeFieldResult : packagedOpportunities) {
			updateEffectiveDate(oppChangeFieldResult, newEffectiveDate);
		}

		return packagedOpportunities;
	}

	@Override
	protected MassUpdaterForChangeEffectiveDate buildInitialOpporytunityResults(List<Opportunity> opportunities) {
		MassUpdaterForChangeEffectiveDate matchingCustomersMap = new MassUpdaterForChangeEffectiveDate();
		for (Opportunity opp : opportunities) {
			OppChangeFieldResult oppChangeFieldResult =
					new OppChangeFieldResult(opp, "EffectiveDate", opp.getEffectiveDate());
			if (opp.getBookTransferID() == 0) {
				oppChangeFieldResult.isError("SubCode missing");
			}
			matchingCustomersMap.addOpportunityChangeResult(oppChangeFieldResult);
		}
		return matchingCustomersMap;
	}

}

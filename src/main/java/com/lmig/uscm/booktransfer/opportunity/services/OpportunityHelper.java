/*
 * Copyright (C) 2017, Liberty Mutual Group
 *
 * Created on Oct 9, 2017
 */

package com.lmig.uscm.booktransfer.opportunity.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.masking.MaskHelper;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityDetails;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityHomeErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityXmlData;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataQuoteResults;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.DetokenizeRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.MasterOppSPQEMeta;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.SPQEOpportunity;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.SafecoLobs;
import com.lmig.uscm.booktransfer.opportunity.client.domain.quoteadapter.QuoteResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.ActionableOpportunitiesResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.PartnerResult;
import com.lmig.uscm.booktransfer.opportunity.domain.PriorCarrierData;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountServiceResponseWrapper;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerPolicy;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunitySensitiveDataException;
import com.lmig.uscm.booktransfer.opportunity.domain.preview.Preview;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import com.lmig.uscm.booktransfer.rating.client.domain.QuoteAndIssueUpdate;
import com.lmig.uscm.booktransfer.spipolicydata.client.AccountDetails;
import com.lmig.uscm.booktransfer.utilityservice.DateUtils;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordXPaths;
import com.lmig.uscm.booktransfer.utilityservice.xml.BusinessAcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import com.lmig.usconsumermarkets.booktransfer.btpaymentservice.client.domain.BillingDetails;
import com.lmig.usconsumermarkets.booktransfer.btpaymentservice.client.domain.PaymentDataDTO;
import com.lmig.usconsumermarkets.booktransfer.btpaymentservice.client.domain.SafecoBillingDataDTO;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.domain.QGFunctionalXmlRequest;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.domain.QuotingGuidelineException;
import com.uscm.lmig.booktransfer.processresult.client.ProcessResultService;
import com.uscm.lmig.booktransfer.processresult.client.domain.LegacyQuoteDataResponse;
import com.uscm.lmig.booktransfer.processresult.client.domain.ProcessResultException;
import com.uscm.lmig.booktransfer.processresult.client.domain.ProcessResultItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;
import reactor.core.publisher.Mono;

import jakarta.persistence.EntityNotFoundException;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.ArrayList;
import java.util.NoSuchElementException;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.Set;
import java.util.HashSet;
import java.util.Date;
import java.util.Objects;
@Primary
@Service
@Slf4j
public class OpportunityHelper {
	public static final String FAILED_TO_UPDATE_OPPORTUNITY = "Failed to update opportunity";

	private QuoteReportItemHelper quoteReportItemHelper;
	private BookTransferService bookTransferService;
	private final ReportGenerator reportGenerator;
	private final ProcessResultService processResultService;
	private final QuotingAdapterService quotingAdapterService;
	private final TransformationService transformationService;
	private final SensitiveDataHelper sensitiveDataHelper;
	/**
	 * NOTE: we are intentionally using opportunityRepoHelper instead of interacting with the repos directly here Please
	 * continue to use the opportunityRepoHelper instead of adding the OpportunityRepos here.
	 */
	private OpportunityRepoHelper opportunityRepoHelper;
	private QuotingGuidelineHelper quotingGuidelineHelper;
	private final BTPaymentServiceHelper paymentServiceHelper;
	private final CustomerAccountHelper customerAccountHelper;

	@Value(value = "${transformation.validation.packageId}")
	private String VALIDATION_PACKAGE;

	@Value("${spipolicydata.url}")
	private String spipolicydata;

	private final ObjectMapper objectMapper;

	public OpportunityHelper(
		final QuoteReportItemHelper quoteReportItemHelper,
		final BookTransferService bookTransferService,
		final ReportGenerator reportGenerator,
		final ProcessResultService processResultService,
		final QuotingAdapterService quotingAdapterService,
		final TransformationService transformationService,
		final SensitiveDataHelper sensitiveDataHelper,
		final OpportunityRepoHelper opportunityRepoHelper,
		final QuotingGuidelineHelper quotingGuidelineHelper,
		final BTPaymentServiceHelper paymentServiceHelper,
		final CustomerAccountHelper customerAccountHelper,
		final ObjectMapper objectMapper
	) {
		this.quoteReportItemHelper = quoteReportItemHelper;
		this.bookTransferService = bookTransferService;
		this.reportGenerator = reportGenerator;
		this.processResultService = processResultService;
		this.quotingAdapterService = quotingAdapterService;
		this.transformationService = transformationService;
		this.sensitiveDataHelper = sensitiveDataHelper;
		this.opportunityRepoHelper = opportunityRepoHelper;
		this.quotingGuidelineHelper = quotingGuidelineHelper;
		this.paymentServiceHelper = paymentServiceHelper;
		this.customerAccountHelper = customerAccountHelper;
		this.objectMapper = objectMapper;
	}

	public Opportunity updateNewOpportunity(OpportunityCreationRequest opportunityCreationRequest,
											Opportunity opportunity,
											Document workingDoc) throws OpportunityException {
		try {
			opportunity.setPriorPremium(AcordHelper.extractPriorPremium(workingDoc));
			updateOpportunity(opportunity, workingDoc);

			opportunity.setData(XmlHelper.getDocumentString(workingDoc));
		} catch (XPathExpressionException | TransformerException e) {
			throw new OpportunityException(FAILED_TO_UPDATE_OPPORTUNITY, e);
		}

		validateMissingData(opportunity);
		return opportunity;

	}

	protected Opportunity updateOpportunity(Opportunity opportunity) throws OpportunityException {
		try {
			Document xml = XmlHelper.getDocument(opportunity.getData());
			return updateOpportunity(opportunity, xml);
		} catch (XPathExpressionException | ParserConfigurationException | IOException | SAXException e) {
			throw new OpportunityException(FAILED_TO_UPDATE_OPPORTUNITY, e);
		}
	}

	private Opportunity updateOpportunity(Opportunity opportunity, Document xml) throws XPathExpressionException {
		opportunity.setBusinessType(LineOfBusiness.determineLobForPersonalLineType(xml).name());

		String str = AcordHelper.getPolicyGuid(xml);
		if (!str.isEmpty()) {
			opportunity.setLastPolicyGuid(str);
		}

		String naiccd = AcordHelper.getNaicCd(xml);
		if (StringUtils.isNotEmpty(naiccd) && naiccd.length() <= 10) {
			opportunity.setNAICCd(naiccd);
		}

		opportunity.setState(AcordHelper.getLocationState(xml));

		str = AcordHelper.getPriorPolicyNumber(xml);
		if (!str.isEmpty()) {
			opportunity.setPriorCarrierGuid(str);
		}
		str = AcordHelper.getAgencyId(xml);
		if (!str.isEmpty()) {
			opportunity.setAgencyId(OpportunityUtil.trimAgencyId(str));
		}
		str = AcordHelper.getEffectiveDt(xml);
		if (!str.isEmpty()) {
			opportunity.setEffectiveDate(str);
		}
		opportunity.setPriorPremium(AcordHelper.extractPriorPremium(xml));

		String customerName = AcordHelper.getCustomerNameOrInsuredCommercialName(xml);
		opportunity.setCustomerName(customerName.substring(0, Math.min(customerName.length(), 1000)));

		return opportunity;
	}

	public Opportunity updateOppData(Opportunity opp, boolean addExtraFields) throws OpportunityException {
		try {
			Document xmlDoc = XmlHelper.getDocument(opp.getData());
			String effDate = AcordHelper.getEffectiveDt(xmlDoc);
			String customerName = AcordHelper.getInsuredOrPrincipalCommercialName(xmlDoc);
			String state = AcordHelper.getLocationState(xmlDoc);
			if (!StringUtils.isEmpty(effDate)) {
				opp.setEffectiveDate(effDate);
			}
			opp.setCustomerName(customerName.substring(0, Math.min(customerName.length(), 1000)));
			opp.setState(state);
			if (addExtraFields) {
				String naiccd = AcordHelper.getNaicCd(xmlDoc);
				String priorPremium = AcordHelper.getPolicyCurrentTermAmt(xmlDoc);
				if (StringUtils.isNotEmpty(naiccd) && naiccd.length() <= 10) {
					opp.setNAICCd(naiccd);
				}
				if (!StringUtils.isEmpty(priorPremium)) {
					opp.setPriorPremium(Double.valueOf(priorPremium));
				}
			}
		} catch (XPathExpressionException | ParserConfigurationException | IOException | SAXException e) {
			throw new OpportunityException(FAILED_TO_UPDATE_OPPORTUNITY, e);
		}
		return opp;
	}

	public List<Opportunity> getOpportunitiesForStatusChange(List<Integer> oppIds) {
		return opportunityRepoHelper.findOpportunitiesByIds(oppIds);
	}

	public Opportunity getOpportunityForEdit(int oppId) {
		DetokenizeRequest request = new DetokenizeRequest(false, null, false, false);
		return getOpportunityForEdit(oppId, request);
	}

	/**
	 * Returns the data for the requested opportunity ID
	 *
	 * @param oppId unique id for Opportunity
	 * @param request detokenize using sensitive data service and should mask or unmask NPPI data
	 * @return Opportunity object
	 */
	public Opportunity getOpportunityForEdit(int oppId, DetokenizeRequest request) throws NoSuchElementException {
		Opportunity opp = opportunityRepoHelper.findOpportunityForEdit(oppId);
		if (StringUtils.isEmpty(opp.getData())) {
			throw new NoSuchElementException();
		}
		addQuotingGuidelineXmlToDataXml(opp);
		addPaymentDataToOpp(opp);
		checkMaskEnabledForBusinessLines(opp.getLineType(), request);
		if (!detokenize(opp, request.isDetokenize(), request.getRequester())) {
			// Mask sensitive data before sending to caller
			maskOpportunityDataAndOriginalXml(opp, request.isUnmaskBirthDt(), request.isUnmaskLicensePermitNumber());
		}
		return new Opportunity(opp.getData(), opp.getOriginalXML());
	}

	public Pair<Opportunity, List<String>> getQuotableOpportunity(int oppId) {
		return getQuotableOpportunity(oppId, false, null);
	}

	public Pair<Opportunity, List<String>> getQuotableOpportunity(int oppId, boolean shouldDetokenize, String requester) {
		Opportunity opp = opportunityRepoHelper.findOpportunityById(oppId);

		if (StringUtils.isEmpty(opp.getData())) {
			throw new NoSuchElementException();
		}

		List<String> errors = new ArrayList<>();
		String error = addQuotingGuidelineXmlToDataXml(opp);

		if (StringUtils.isNotEmpty(error)) {
			errors.add(error);
		}

		error = addPaymentDataToOpp(opp);

		if (StringUtils.isNotEmpty(error)) {
			errors.add(error);
		}

		detokenize(opp, shouldDetokenize, requester);

		return Pair.of(opp, errors);
	}

	private boolean detokenize(Opportunity opp, boolean shouldDetokenize, String requester) {
		if (shouldDetokenize) {
			log.info(String.format(
					"%s requested detokenization for opp %s %s %s",
					requester, opp.getOpportunityId(), opp.getLineType(), opp.getBusinessType()));
		}

		if (shouldDetokenize && opp.getLineType().equals(LineType.Business) && StringUtils.isNotEmpty(requester)) {
			log.info(String.format(
					"Detokenizing opp %s for requester %s", opp.getOpportunityId(), requester));
			try {
				opp.setOriginalXML(XmlHelper.getDocumentString(
						sensitiveDataHelper.deTokenizeXml(XmlHelper.getDocument(opp.getOriginalXML()), requester)));
				opp.setData(XmlHelper.getDocumentString(
						sensitiveDataHelper.deTokenizeXml(XmlHelper.getDocument(opp.getData()), requester)));
			} catch (OpportunitySensitiveDataException | IOException | SAXException | ParserConfigurationException |
					 TransformerException e) {
				throw new RuntimeException(e);
			}

			return true;
		}

		return false;
	}

	private String addQuotingGuidelineXmlToDataXml(Opportunity opp) {
		String quotingGuidelineXml;
		try {
			quotingGuidelineXml = getQuotingGuidelineXml(opp);
		} catch (Exception e) {
			log.error("Unable to add quoting guideline xml to the data xml", e);
			return "Error while adding quoting guideline information, please try again.";
		}

		if (StringUtils.isNotBlank(quotingGuidelineXml)) {
			try {
				Document dataXmlDoc = XmlHelper.getDocument(opp.getData());
				AcordHelper.addQuotingGuideLine(dataXmlDoc, XmlHelper.createNode(quotingGuidelineXml));
				opp.setData(XmlHelper.getDocumentString(dataXmlDoc));
			} catch (Exception e) {
				log.error("Unable to add quoting guideline xml to the data xml", e);
				return "Error while adding quoting guideline information, please try again.";
			}
		}

		return StringUtils.EMPTY;
	}

	private String getQuotingGuidelineXml(Opportunity opp) throws BookTransferException, ParseException, QuotingGuidelineException{
		BookTransferDTO bookTransfer = bookTransferService.findByBookTransferId(opp.getBookTransferID());
		String castedOrDefaultNaicCd = StringUtils.isEmpty(opp.getNAICCd()) ? null : opp.getNAICCd();
		String policyTypeCd = null;
		try{
			policyTypeCd = AcordHelper.getPolicyTypeCd(XmlHelper.getDocument(opp.getData()));
		}catch(SAXException | IOException | ParserConfigurationException | XPathExpressionException e){
			log.error("Unable to get policyTypeCd");
		}
		return quotingGuidelineHelper.getQuotingGuidelineFunctionalXml(
				QGFunctionalXmlRequest.builder()
						.salesforceCode(bookTransfer.getSalesforceCode())
						.naicCd(castedOrDefaultNaicCd)
						.naicCd(castedOrDefaultNaicCd)
						.lob(opp.getBusinessType())
						.state(opp.getState())
						.btFirstEffectiveDate(bookTransfer.getFirstEffectiveDate()!=null?DateUtils.getDateInUTC(bookTransfer.getFirstEffectiveDate()):"")
						.policyTypeCd(policyTypeCd)
						.build());
	}

	protected String addPaymentDataToOpp(Opportunity opp) {
		if (opp.getLineType() != LineType.Personal) {
			return StringUtils.EMPTY;
		}

		// Don't add payment data for non-state auto books
		if (isNotStateAutoBook(opp)) {
			return StringUtils.EMPTY;
		}

		BillingDetails billingDetails = null;

		if (StringUtils.isNotEmpty(opp.getBillingAccountNumber())) {
			try {
				billingDetails = paymentServiceHelper.getPaymentInfoUsingBillingAccountNumber(opp.getBillingAccountNumber());
			} catch (Exception e) {
				// Do not populate the tags if bt-payment-service threw
				log.error("Unable to add payment data to the data xml for opp {}", opp.getOpportunityId(), e);
				return "Error while adding payment data, please try again.";
			}
		}

		if (billingDetails == null) {
			billingDetails = BillingDetails.builder().build();
		}

		injectPaymentData(opp, billingDetails);

		return StringUtils.EMPTY;
	}

	private void injectSafecoBillingData(List<SafecoBillingDataDTO> safecoBillingDetails, Document dataXmlDoc) throws XPathExpressionException {
		// Create empty SafecoBillingAccountNumber node if no safecoBillingDetails available
		if (CollectionUtils.isEmpty(safecoBillingDetails)) {
			AcordHelper.addSafecoBillingNode(dataXmlDoc);
			return;
		}

		for (SafecoBillingDataDTO safecoBillingDetail : safecoBillingDetails) {
			//create empty parent node
			AcordHelper.addSafecoBillingNode(dataXmlDoc);
			//Add safeco billing details
			AcordHelper.setSafecoBillingLob(dataXmlDoc, safecoBillingDetail.getLob());
			AcordHelper.setSafecoBillingAccountNumber(dataXmlDoc, safecoBillingDetail.getSafecoBillingAccountNumber());
			AcordHelper.setSafecoBillingPolicyNumber(dataXmlDoc, safecoBillingDetail.getSafecoPolicyNumber());
		}

	}

	private void injectPaymentData(Opportunity opp, BillingDetails billingDetails) {
		try{
			Document dataXmlDoc = XmlHelper.getDocument(opp.getData());

			injectPaymentDataToOppXml(
				dataXmlDoc,
				billingDetails.getPaymentData() == null
					? PaymentDataDTO.builder().build()
					: billingDetails.getPaymentData()
			);

			injectSafecoBillingData(billingDetails.getSafecoBillingDetails(), dataXmlDoc);

			opp.setData(XmlHelper.getDocumentString(dataXmlDoc));
		} catch (ParserConfigurationException | IOException | SAXException | XPathExpressionException |
				 TransformerException e) {
			log.error("Failed to add InstrumentId and TokenizedAccountNumber", e);
		}
	}

	private void injectPaymentDataToOppXml(Document dataXmlDoc, PaymentDataDTO paymentData) throws XPathExpressionException {
		AcordHelper.setInstrumentId(dataXmlDoc, paymentData.getInstrumentId());
		AcordHelper.setTokenizedAccountNumber(dataXmlDoc, paymentData.getTokenizedAccountNumber());
		AcordHelper.setAccountNumberId(dataXmlDoc, paymentData.getLast4());
		AcordHelper.setAccountType(dataXmlDoc, paymentData.getAccountType());
		AcordHelper.setAccountHolderName(dataXmlDoc, paymentData.getAccountHolderName());
		AcordHelper.setRoutingNumber(dataXmlDoc, paymentData.getRoutingNumber());
	}

	/**
	 * Masks opportunity's DataXML and OriginalXML based on utility service MaskHelper Will set the data to null if any
	 * error occurs to ensure no sensitive data is leaked
	 */
	private static void maskOpportunityDataAndOriginalXml(final Opportunity opportunity, final boolean unmaskBirthDt, final boolean unmaskLicensePermitNumber) {
		opportunity.setData(MaskHelper.maskDocumentOrNullify(opportunity.getData(), unmaskBirthDt, unmaskLicensePermitNumber));
		opportunity.setOriginalXML(MaskHelper.maskDocumentOrNullify(opportunity.getOriginalXML(), unmaskBirthDt, unmaskLicensePermitNumber));
	}

	/**
	 * Masks processResultItem's DataXML and PartnerRequestXML based on utility service MaskHelper Will set the data to
	 * null if any error occurs to ensure no sensitive data is leaked
	 *
	 * @param pri
	 * 	processResultItem to mask
	 */
	private static void maskProcessResultData(final ProcessResultItem pri) {
		pri.setData(MaskHelper.maskDocumentOrNullify(pri.getData(), false, false));
		pri.setPartnerRequestXML(MaskHelper.maskDocumentOrNullify(pri.getPartnerRequestXML(), false, false));
	}

	public CustomFilterResponse getCustomFilterResponse(CustomFilterRequest cfRequest) throws IllegalArgumentException {
		if (CollectionUtils.isNotEmpty(cfRequest.getOppIds())) {
			return new CustomFilterResponse(opportunityRepoHelper.getCustomFilteredOpportunities(cfRequest),
					opportunityRepoHelper.getCustomFilteredOpportunitiesCount(cfRequest));
		} else {
			throw new IllegalArgumentException("Search failed no request ids given");
		}
	}

	/**
	 * Updates the opportunity's DataXml This will check if the DataXml has unmasked sensitive data and will update the
	 * previous DataXml with the changes
	 */
	public void updateOpportunityXML(Opportunity newOpportunity) throws Exception {
		Opportunity foundOpp = opportunityRepoHelper.findOpportunityById(newOpportunity.getOpportunityId());
		updateOpportunityXml(newOpportunity, foundOpp);
	}

	/**
	 * Updates the savedOpp's DataXml with the updatedOpp's DataXml This will check if the DataXml has unmasked
	 * sensitive data and will update the previous DataXml with the changes
	 *
	 * @param updatedOpp
	 * 		updated opportunity
	 * @param savedOpp
	 * 		saved opportunity
	 * @throws Exception
	 * 		exception
	 */
	public void updateOpportunityXml(Opportunity updatedOpp, Opportunity savedOpp) throws Exception {
		Document updatedOppXmlDoc = XmlHelper.getDocument(updatedOpp.getData());

		Document savedOppXmlWithSensitive = XmlHelper.getDocument(savedOpp.getData());
		MaskHelper.consolidateSensitiveDataToDestDoc(savedOppXmlWithSensitive, updatedOppXmlDoc);

		if (savedOpp.getLineType().equals(LineType.Business)) {
			// move TaxId nodes from savedOpp (tokenized) -> updatedOpp (detokenized)
			moveTaxIdNodes(savedOppXmlWithSensitive, updatedOppXmlDoc);
		}

		//Remove quoting guideline data
		XmlHelper.removeAllNodesFromDocument(updatedOppXmlDoc, AcordXPaths.BT_META_QG);

		savedOpp.setData(XmlHelper.getDocumentString(updatedOppXmlDoc));
		savedOpp = validateAndUpdateOpportunity(savedOpp);

		saveUpdatedOpportunity(savedOpp);
	}

	private Opportunity validateAndUpdateOpportunity(Opportunity opportunity) throws OpportunityException {
		if (StringUtils.isEmpty(opportunity.getLastPolicyGuid())) {
			validateMissingData(opportunity);
			opportunity = updateOppData(opportunity, true);
		} else {
			opportunity = updateOppData(opportunity, false);
		}
		return opportunity;
	}

	/**
	 * This is going to take in an opportunity you want to partitionSave. Before it is saved we will check to make
	 * quoteReportItem is updated with the correct info we will then partitionSave it
	 *
	 * @param opportunity the opportunity we are updating.
	 */
	public void saveUpdatedOpportunity(Opportunity opportunity) {
		log.info("OPP_SAVE_START - {}", opportunity.getOpportunityId());
		if(LineType.Business.equals(opportunity.getLineType())) {
			updateCustomerAccounts(List.of(opportunity), null);
		}
		opportunity = opportunityRepoHelper.save(opportunity);
		log.info("OPP_SAVE_FINISH - {}", opportunity.getOpportunityId());
		quoteReportItemHelper.updateQuoteReportItem(opportunity);
	}

	public void saveUpdatedOpportunityPostQuote(Opportunity opportunity) {
		log.info("OPP_SAVE_POST_QUOTE_START - {}", opportunity.getOpportunityId());
		opportunityRepoHelper.updateOpportunityPostQuote(opportunity);
		log.info("OPP_SAVE_POST_QUOTE_FINISH - {}", opportunity.getOpportunityId());
		// passed opportunity may have stale/changed information outside what is needed to update
		// therefore, get opp from source of truth after update to have all correct info
		opportunity = opportunityRepoHelper.findOpportunityById(opportunity.getOpportunityId());
		quoteReportItemHelper.updateQuoteReportItem(opportunity);
	}

	public void updateOpportunityFromBLQuoteResult(final String xml, final Integer oppId, final Long milliseconds)
			throws IOException, SAXException, ParserConfigurationException, XPathExpressionException {
		final String trimmedString = XmlHelper.removeXmlProlog(xml);
		final Document dataXml = XmlHelper.getDocument(trimmedString);
		final String premiumValue = AcordHelper.getInsuredFullToBePaidAmt(dataXml);
		final boolean doesFirstTimestampExist = opportunityRepoHelper.doesFirstTimestampExist(oppId);
		final String time = DateUtils.getDateTimeFromMilliseconds(milliseconds);
		opportunityRepoHelper.updateOpportunityForBLCall(oppId, time, premiumValue, doesFirstTimestampExist);
	}

	public void validateMissingData(final Opportunity opportunity) throws OpportunityException {
		try {
			Document xmlToValidate = XmlHelper.getDocument(opportunity.getData());
			Document validatedXml = runRules(xmlToValidate, VALIDATION_PACKAGE, opportunity.getOpportunityId());
			updateOpportunityWithValidationResults(opportunity, validatedXml);
		} catch (ParserConfigurationException | IOException | SAXException | TransformerException ex) {
			throw new OpportunityException("Failed to validate missing data", ex);
		}
	}

	public Document runRules(Document xmlDoc, String packageId, @Nullable Integer oppId) {
		return transformationService.runRules(xmlDoc, packageId, oppId);
	}

	protected boolean hasMissingData(final Document doc) {
		NodeList errorNodes = doc.getElementsByTagName("com.BookTransfer_errors");
		return errorNodes.getLength() > 0;
	}

	public void updateOpportunityWithValidationResults(final Opportunity opp, final Document workingDoc) throws TransformerException {
		OpportunityStatus status = OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED;
		if (hasMissingData(workingDoc)) {
			status = OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA;
		}
		opp.setStatus(status.getOppStatusCode());
		opp.setData(XmlHelper.getDocumentString(workingDoc));
	}

	/**
	 * Grab the preview data from a process result item associated with an Opportunity ID and returns it in a response
	 * entity
	 */


	public PreviewDataResponse getPreviewData(int oppId) throws Exception {
		Opportunity opp = opportunityRepoHelper.findOpportunityById(oppId);
		PreviewDataResponse response = buildQuoteResults(oppId, opp.getLastPolicyGuid(), opp.getLineType());
		Document dataXml = XmlHelper.getDocument(opp.getData());
		response.setPreview(Preview.buildPreview(dataXml, response, opp.getLineType()));
		return response;
	}

	protected PreviewDataResponse buildQuoteResults(int oppId, String oppPolicyGuid, LineType lineType) {
		PreviewDataResponse response = new PreviewDataResponse();
		QuoteResponse quoteResponse = quotingAdapterService.getLatestQuoteResponseByOppId(oppId);

		if (quoteResponse == null && lineType.equals(LineType.Personal)) {
			log.info(
					"Could not create QuoteResponse from quoting adapter. Calling legacy process-result-service for oppId {}",
					oppId);
			return buildQuoteResultsFromLegacyProcessResultService(oppId, response, oppPolicyGuid);
		}
		if (quoteResponse != null) {
			PreviewDataQuoteResults results = new PreviewDataQuoteResults();
			results.setOpportunityId(quoteResponse.getOpportunityId());
			results.setLastQuotedEnvironment(quoteResponse.getRequestOrigin().getRequestEnvironment().name());
			results.setPolicyGuid(quoteResponse.getResponse().getResponseId());
			results.setStatus(quoteResponse.getStatus().name());
			String completionTimestamp = DateUtils.getDateTimeFromMilliseconds(quoteResponse.getCompletionTimestamp());
			results.setTimeStamp(completionTimestamp);
			results.setKoMessage(quoteResponse.getMessages()[0].getMessageStatusDescription());
			results.setKoCode(getKoCode(results.getKoMessage()));

			response.setQuoteResponse(results);
		}
		return response;
	}

	private String getKoCode(String koMessage) {
		if (!StringUtils.isBlank(koMessage)) {
			int index = koMessage.indexOf("(");
			if (index != -1) {
				koMessage = koMessage.substring(index + 1);
				return koMessage.replace(")", "");
			}
		}
		return "";
	}

	protected PreviewDataResponse buildQuoteResultsFromLegacyProcessResultService(int oppId, PreviewDataResponse response, String oppPolicyGuid) {
		try {
			LegacyQuoteDataResponse quoteResultData = processResultService.getLatestQuoteResultForOpportunity(oppId);
			if (StringUtils.isNotBlank(oppPolicyGuid)) {
				quoteResultData.setPolicyGuid(oppPolicyGuid);
			}
			PreviewDataQuoteResults results = getPreviewDataQuoteResults(quoteResultData);

			response.setQuoteResponse(results);
		} catch (ProcessResultException e) {
			log.error("Failed to retrieve quote result for opportunity ID {}: {}", oppId, e.getMessage(), e);
			return response;
		} catch (Exception e) {
			log.error("An unexpected exception occurred while retrieving quote result for opportunity ID {}: {}", oppId, e.getMessage(), e);
			return response;
		}
		return response;
	}

	private PreviewDataQuoteResults getPreviewDataQuoteResults(LegacyQuoteDataResponse quoteResultData) {
		PreviewDataQuoteResults results = new PreviewDataQuoteResults();

		results.setOpportunityId(String.valueOf(quoteResultData.getOpportunityId()));
		results.setLastQuotedEnvironment(quoteResultData.getLastQuotedEnvironment());
		results.setPolicyGuid(quoteResultData.getPolicyGuid());
		results.setStatus(quoteResultData.getStatus());
		results.setTimeStamp(quoteResultData.getTimeStamp());
		results.setKoMessage(quoteResultData.getKoMessage());
		results.setKoCode(quoteResultData.getKoCode());
		return results;
	}

	/**
	 * build a map of parameters to update opportunity status to Issued
	 *
	 * @param oppStatus
	 *  the status to update to
	 * @param oppID
	 *  the opportunity ID
	 * @return Map
	 */
	public Map<String, Object> buildParametersUpdateIssued(int oppStatus, String oppID) {
		Map<String, Object> parameters = new HashMap<>();
		parameters.put("OppStatus", oppStatus);
		parameters.put("OppID", oppID);

		return parameters;
	}

	/**
	 * build a map of parameters for an opportunity query with policyGuid
	 *
	 * @param policyGuid
	 * 		the policyGuid to query
	 * @return Map
	 */
	public Map<String, Object> buildParametersPolicyGuid(String policyGuid) {
		Map<String, Object> parameters = new HashMap<>();
		parameters.put("PolicyGUID", policyGuid);
		return parameters;
	}

	public String initilizeArgument(String argument) {
		if (argument == null || argument.isEmpty()) {
			argument = null;
		}
		return argument;
	}

	public Opportunity checkForUpdatedSubCode(OpportunityCreationRequest opportunityCreationRequest,
											  Opportunity opportunity,
											  Document workingDoc) throws XPathExpressionException, BookTransferException {
		String sfdcid = AcordHelper.getCampaignId(workingDoc);
		log.info("SFCID reviced");
		log.info("sfcid - {}", sfdcid);
		if (sfdcid != null && !sfdcid.isEmpty()) {
			sfdcid = sfdcid.replaceAll("[^\\d]", "");
			BookTransferDTO bt = bookTransferService.getTopBySFDCIDOrderByBookTransferID(Integer.parseInt(sfdcid));
			if (bt != null) {
				if (bt.getBookTransferID() != opportunity.getBookTransferID()) {
					log.info("bookTeansfer id- {}", bt.getBookTransferID());
					log.info("SUBCODE_KEY - {}", bt.getSubCode());
					opportunity.setBookTransferID(bt.getBookTransferID());
					log.info("Set SubCode");

				}
				// TODO: Why update the request and not the opportunity?
				// updating these so that quotereport will be updated with correct values
				opportunityCreationRequest.setSFDCID(bt.getSfdcid());
				opportunityCreationRequest.setSubCode(bt.getSubCode());
			} else {
				log.error("Failed to find book transfer in DB");
			}
		}
		return opportunity;
	}

	public void updateQRIForLeadEffectiveDate(List<QuoteReportItemLegacy> quoteReports) {
		quoteReportItemHelper.updateQuoteReportForLeadEffectiveDate(quoteReports);
	}

	/**
	 * Update opportunity with the data from QnI
	 *
	 * @param qniData
	 * 		QuoteAndIssueUpdate
	 * @throws OpportunityException
	 * 		OpportunityException
	 */
	public void updateOppWithQnIData(QuoteAndIssueUpdate qniData) throws OpportunityException {
		try {
			final Document dataXml = updateDataXmlWithQnIData(qniData);
			final String dataXmlString = XmlHelper.getDocumentString(dataXml);
			if (StringUtils.isNotEmpty(dataXmlString)) {
				Opportunity opportunity = opportunityRepoHelper.updateOpportunityWithQnIData(dataXmlString, qniData);
				saveSafecoBillingDetails(opportunity, qniData);
			}
		} catch (XPathExpressionException | ParserConfigurationException | IOException
				| SAXException | TransformerException ex) {
			throw new OpportunityException("Failed to update opp with QnI data", ex);
		}
	}

	protected void saveSafecoBillingDetails(Opportunity opp, QuoteAndIssueUpdate qniData) throws OpportunityException {
		try {
			//Check whether the opp been issued. If not then don't call spi service and save safeco billing data
			if(opp.getStatus() != OpportunityStatus.OPPORTUNITY_STATUS_ISSUED.getOppStatusCode()) {
				log.info("Opportunity {} is not issued, skip saving safeco billing details", opp.getOpportunityId());
				return;
			}
			// Don't call spi service and save safeco billing data if the opp isn't state auto policy
			if (isNotStateAutoBook(opp)) {
				return;
			}
			Optional<AccountDetails> optionalAccountDetails = OpportunityUtil.runFunctionWithRetriesWithDefault(this::getAccountDetails, qniData, Optional.empty());

			if (optionalAccountDetails.isEmpty()) {
				log.info("spi call returned no billing data for {}", qniData.getPolicyNumber());
				return;
			}
			AccountDetails accountDetails = optionalAccountDetails.get();
			String lobName = SafecoLobs.determineSafecoLob(accountDetails.getProduct(), qniData.getLineOfBusiness());
			saveSafecoBillingData(opp.getBillingAccountNumber(), accountDetails.getBillingAccountNumber(), lobName, qniData.getPolicyNumber());
		} catch(Exception ex) {
			log.error("Failed to save safeco billing account details for opportunity {}", opp.getOpportunityId(), ex);
			throw new OpportunityException("Failed to save safeco billing account details", ex);
		}
	}

	private void saveSafecoBillingData(String billingAccountNumber, String safecoBillingAccountNumber, String lobName, String policyNumber) {
		SafecoBillingDataDTO safecoBillingDataDTO = SafecoBillingDataDTO.builder()
				.lob(lobName)
				.safecoBillingAccountNumber(safecoBillingAccountNumber)
				.priorCarrierBillingAccountNumber(billingAccountNumber)
				.safecoPolicyNumber(policyNumber)
				.build();
		OpportunityUtil.runFunctionWithRetries(paymentServiceHelper::saveSafecoBillingData, safecoBillingDataDTO);
	}

	private boolean isNotStateAutoBook(Opportunity opp) {
		BookTransferDTO bookTransfer = OpportunityUtil.runFunctionWithRetriesWithDefault(this::getBookTransfer, opp.getBookTransferID(), null);
		if(bookTransfer == null || !("State Auto Book Migration".equalsIgnoreCase(bookTransfer.getNBDRelationship()))) {
			log.info("{} is not part of state auto book", opp.getOpportunityId());
			return true;
		}
		return false;
	}

	private BookTransferDTO getBookTransfer(int bookTransferId) {
		try {
			return bookTransferService.findByBookTransferId(bookTransferId);
		} catch (BookTransferException e) {
			throw new RuntimeException(e);
		}
	}

	protected Optional<AccountDetails> getAccountDetails(QuoteAndIssueUpdate qniData) {
		WebClient webClient = WebClient.builder().build();
		String url = spipolicydata + "/getAccountDetails?policyNumber=" + qniData.getPolicyNumber() + "&lobId=" + qniData.getLineOfBusiness();

		return webClient.get()
				.uri(url)
				.retrieve()
				.onStatus(HttpStatusCode::is4xxClientError, response ->
					response.bodyToMono(String.class)
							.flatMap(errorBody -> {
								log.error("Bad request: {}", errorBody);
								return Mono.empty();
							})
				)
				.onStatus(HttpStatusCode::is5xxServerError, response ->
						response.bodyToMono(String.class)
								.flatMap(errorBody -> {
									log.error("Spiservice failure: {}", errorBody);
									return Mono.empty();
								})
				)
				.onStatus(HttpStatus.NO_CONTENT::equals, response -> {
					log.info("No content response from SPI policy data service");
					return Mono.empty();
				})
				.bodyToMono(AccountDetails.class)
				.blockOptional();
	}


	protected Document updateDataXmlWithQnIData(QuoteAndIssueUpdate qniData)
			throws XPathExpressionException, ParserConfigurationException, IOException, SAXException {
		String oppXml = opportunityRepoHelper.getDataXmlStringByLastPolicyGuid(qniData.getLastPolicyGuid());
		if (StringUtils.isNotEmpty(oppXml)) {
			Document dataXml = XmlHelper.getDocument(oppXml);

			//update some ACORD values with QnIData
			updateEffectiveDt(dataXml, qniData.getEffectiveDate());
			updateInsuredCommercialName(dataXml, qniData);
			AcordHelper.setPolicyGuid(dataXml, qniData.getLastPolicyGuid());
			updateInsuredFirstName(dataXml, qniData.getFirstName());
			updateInsuredLastName(dataXml, qniData.getLastName());

			return dataXml;
		}
		return null;
	}

	private void updateEffectiveDt(Document dataXml, Date date) throws XPathExpressionException {
		if (date != null) {
			String effDate = DateUtils.getSQLDateString(date);
			if (StringUtils.isNotEmpty(effDate)) {
				AcordHelper.setEffectiveDt(dataXml, effDate);
			}
		}
	}

	private void updateInsuredCommercialName(Document dataXml, QuoteAndIssueUpdate qniData)
			throws XPathExpressionException {
		String fullName = qniData.getFirstName() + " " + qniData.getLastName();
		if (StringUtils.isNotEmpty(fullName)) {
			AcordHelper.setInsuredCommercialName(dataXml, fullName);
		}
	}

	private void updateInsuredFirstName(Document dataXml, String firstName) throws XPathExpressionException {
		if (StringUtils.isNotEmpty(firstName)) {
			AcordHelper.setInsuredGivenName(dataXml, firstName);
		}
	}

	private void updateInsuredLastName(Document dataXml, String lastName) throws XPathExpressionException {
		if (StringUtils.isNotEmpty(lastName)) {
			AcordHelper.setInsuredSurname(dataXml, lastName);
		}
	}

	/**
	 * For testing
	 */
	@VisibleForTesting
	public void setQuoteReportItemHelper(QuoteReportItemHelper quoteReportItemHelper) {
		this.quoteReportItemHelper = quoteReportItemHelper;
	}

	/**
	 * Build out the Brush map csv
	 *
	 * @return BrushMap string
	 */
	public String buildOutBrushMapData(final List<Integer> oppIds) {
		List<Opportunity> opportunitylist = opportunityRepoHelper.findByOpportunityIdIn(oppIds);

		return reportGenerator.generateBrushMapCSV(opportunitylist);
	}

	/**
	 * Build out the Customer List csv
	 *
	 * @return Customer List string
	 */
	public String buildCustomerListCSVAsString(final List<Integer> oppIds) {
		List<Opportunity> opportunityList = opportunityRepoHelper.findByOpportunityIdIn(oppIds);

		return reportGenerator.generateCustomerListCSV(opportunityList);
	}

	public List<OpportunityDetails> getOppDetails(OpportunityFilterRequest request) throws BookTransferException {

		Map<Integer, String> bookIdToSalesForceCode = getBookIdToSalesForceCode(request.getSfdcid());

		List<OpportunityDetails> oppDetails =
				opportunityRepoHelper.getOppDetails(bookIdToSalesForceCode.keySet(), request.getLobs(),
						request.getStartEffectiveDate(), request.getEndEffectiveDate(), request.getLineType());
		oppDetails.parallelStream().forEach(item -> {
			String salesforceCode = bookIdToSalesForceCode.get(item.getBooktransferId());
			item.setSalesforceCode(salesforceCode);
		});
		return oppDetails;
	}

	private Map<Integer, String> getBookIdToSalesForceCode(List<String> salesforceCodes) throws BookTransferException {
		List<BookTransferDTO> bookTransfers = bookTransferService.getBookTransfersBySalesforceCodes(salesforceCodes);

		return bookTransfers.parallelStream()
				.collect(Collectors.toMap(BookTransferDTO::getBookTransferID, BookTransferDTO::getSalesforceCode));
	}

	public List<Opportunity> getAllOppsByOppIds(List<Integer> oppIds) {
		List<Opportunity> oppList = new ArrayList<>();
		for (List<Integer> partition : ListUtils.partition(oppIds, 1500)) {
			oppList.addAll(opportunityRepoHelper.findByOpportunityIdIn(partition));
		}
		return oppList;
	}

	//TODO needs to refactor when we rewrite the analyzer tool from old rules engine in new Transformation Engine.
	public String maskAndBuildPartnerResult(String oppId) throws OpportunityException, ProcessResultException {
		try {
			Opportunity baseData = opportunityRepoHelper.findOpportunityById(Integer.parseInt(oppId));
			if (LineType.Business.equals(baseData.getLineType())) {
				return "BL Opportunity does not have latestResult from Partner";
			}
			ProcessResultItem pri =
					processResultService.getProcessResultItemForOpportunity(baseData.getOpportunityId());
			return objectMapper.writeValueAsString(maskAndBuildPartnerResult(baseData, pri));
		} catch (JsonProcessingException | NumberFormatException e) {
			log.error("Exception occurred while getting process result item for opportunity {}", oppId, e);
			throw new OpportunityException(e);
		} catch (ProcessResultException e) {
			throw new ProcessResultException(String.format("Could not retrieve item for opportunity %s with exception %s", oppId, e));
		}
	}

	/**
	 * Masks data within the passed Opportunity and ProcessResultItem before building a PartnerResult
	 */
	public static PartnerResult maskAndBuildPartnerResult(final Opportunity opp, final ProcessResultItem pri) {
		maskOpportunityDataAndOriginalXml(opp, false, false);
		maskProcessResultData(pri);
		return new PartnerResult(opp, pri);
	}

	/**
	 * Determines if an Opportunity is quotable based on it's status Heritage, Withdrawn, and Issued statuses are not
	 * quotable. All Business Lines are currently not quotable All other statuses are quotable.
	 */
	public boolean isOpportunityQuotable(Opportunity opp) {
		try {
			return OpportunityStatus
					.getOppStatusFromCode(opp.getStatus()) != OpportunityStatus.OPPORTUNITY_STATUS_HERITAGE
					&& OpportunityStatus
					.getOppStatusFromCode(opp.getStatus()) != OpportunityStatus.OPPORTUNITY_STATUS_ISSUED
					&& OpportunityStatus
					.getOppStatusFromCode(opp.getStatus()) != OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN;
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * Retrieves the opportunities from the database matching the given oppIds and sorts each opp id into the
	 * ActionableOpportunitiesResponse if it is quotable or not
	 *
	 * @param oppIds - list of opportunity ids (Integer)
	 * @return ActionableOpportunitiesResponse
	 */
	public ActionableOpportunitiesResponse getQuotableOpportunities(List<Integer> oppIds) {
		ActionableOpportunitiesResponse resp = new ActionableOpportunitiesResponse();
		List<Opportunity> opps = opportunityRepoHelper.findOpportunitiesByIds(oppIds);
		opps.forEach(opp -> {
			if (isOpportunityQuotable(opp)) {
				resp.addActionableOpp(opp.getOpportunityId());
			} else {
				resp.addNonActionableOpp(opp.getOpportunityId());
			}
		});
		return resp;
	}

	public List<OpportunityErrorInfo> getOpportunitiesErrorInfo(List<Integer> opportunityIds)
			throws BookTransferException {
		List<OpportunityErrorInfo> errorInfos =
				new ArrayList<>(opportunityRepoHelper.getErrorItemsByOppIds(opportunityIds));
		populateHomePolicyForm(errorInfos);
		populateBookTransferInfo(errorInfos);
		return errorInfos;
	}

	private void populateHomePolicyForm(List<OpportunityErrorInfo> opportunityErrors) {
		Set<Integer> opportunityIds =
				opportunityErrors.stream().filter(OpportunityHomeErrorInfo.class::isInstance)
						.map(OpportunityErrorInfo::getOpportunityId).collect(Collectors.toSet());
		if (opportunityIds.isEmpty()) {
			return;
		}
		Map<Integer, String> opportunityXmlDatas = opportunityRepoHelper.getDataXmlForOppIds(opportunityIds).stream()
				.collect(Collectors.toMap(OpportunityXmlData::getOpportunityId, this::getPolicyForm));

		opportunityErrors.stream().filter(OpportunityHomeErrorInfo.class::isInstance)
				.map(OpportunityHomeErrorInfo.class::cast)
				.forEach(errorInfo -> errorInfo.setPolicyForm(opportunityXmlDatas.get(errorInfo.getOpportunityId())));
	}

	private String getPolicyForm(OpportunityXmlData opportunityXmlData) {
		try {
			Document xml = XmlHelper.getDocument(opportunityXmlData.getXmlData());
			return AcordHelper.getDwellPolicyTypeCd(xml);
		} catch (Exception e) {
			log.info("Error Retrieving XML for Opportunity Error Report", e);
			return "";
		}
	}

	public String getOpportunityXml(Integer id, boolean detokenizeXml)
			throws OpportunitySensitiveDataException, EntityNotFoundException, OpportunityException {
		HashSet<Integer> oppIdSet = new HashSet<>();
		oppIdSet.add(id);
		List<OpportunityXmlData> matchingXmls = opportunityRepoHelper.getDataXmlForOppIds(oppIdSet);
		if(matchingXmls.isEmpty()) {
			throw new EntityNotFoundException("Matching Xml for Opp not found with id: " + id);
		}

		try {
			OpportunityXmlData opportunityXmlData = matchingXmls.get(0);
			Document oppXml = XmlHelper.getDocument(opportunityXmlData.getXmlData());
			oppXml = detokenizeXml ? sensitiveDataHelper.deTokenizeXml(oppXml, "") : oppXml;

			// Add Rampcode
			Document updateDoc = updateAgentNumberAndInjectEcliqAccountNumber(opportunityXmlData, oppXml);
			return XmlHelper.getDocumentString(updateDoc);
		} catch(ParserConfigurationException | SAXException | IOException | TransformerException e) {
			log.info("Error Retrieving Opportunity XML", e);
			throw new OpportunitySensitiveDataException(e);
		}

	}

	private Document updateAgentNumberAndInjectEcliqAccountNumber(OpportunityXmlData opportunityXmlData, Document oppXml) throws OpportunityException {
		try {
			BookTransferDTO bookTransferDTO = bookTransferService.findByBookTransferId(
					opportunityXmlData.getBookTransferId());
			log.info("lineType: {}", opportunityXmlData.getLineType());
			if(LineType.Business.equals(opportunityXmlData.getLineType())){
				injectEcliqAccountNumber(oppXml, bookTransferDTO.getSfdcid());
			}
			String agentNumber = LineType.Business.equals(opportunityXmlData.getLineType()) ?
					bookTransferDTO.getRampCode() :
					bookTransferDTO.getSubCode();
			if(agentNumber == null) {
				log.info("agent number is null for book {}", bookTransferDTO.getSalesforceCode());
				agentNumber = "";
			}
			return OpportunityUtil.setAgentNumber(oppXml, agentNumber, opportunityXmlData.getLineType());
		} catch (XPathExpressionException e) {
			log.error("Could not set AgentNumber", e);
		} catch (BookTransferException e) {
			log.info("Error Retrieving BookTransfer data", e);
			throw new OpportunityException(e);
		}
		return oppXml;
	}

	private void injectEcliqAccountNumber(Document oppXml, Integer sfdcid) {
		String priorPolicyNumber="";
    try {
      priorPolicyNumber = BusinessAcordHelper.getPriorPolicyNumber(oppXml);
    } catch (XPathExpressionException e) {
      log.info("Error getting prior policy number from opp xml", e);
    }
		log.info("priorPolicyNumber: {}", priorPolicyNumber);
		if (StringUtils.isNotEmpty(priorPolicyNumber)) {
			String ecliqAccountNumber = OpportunityUtil.runFunctionWithRetries(customerAccountHelper::getEcliqAccountNumber,
				String.format("btCode=%d&priorPolicyNumber=%s", sfdcid, priorPolicyNumber));
			log.info("ecliqAccountNumber: {}", ecliqAccountNumber);
			if(StringUtils.isNotEmpty(ecliqAccountNumber)) {
				try {
					BusinessAcordHelper.setEcliqAccountNumber(oppXml, ecliqAccountNumber);
				} catch (XPathExpressionException e) {
					log.info("Error setting ecliq account number in opp xml", e);
				}
			}
		}
  	}

	private void populateBookTransferInfo(List<OpportunityErrorInfo> errorInfos) throws BookTransferException {
		Set<Integer> bookTransferIds =
				errorInfos.stream().map(OpportunityErrorInfo::getBookTransferId).collect(Collectors.toSet());
		Map<Integer, BookTransferDTO> bookTransferMap = bookTransferService.getBookTransfersForQuoteErrors(bookTransferIds)
				.stream().collect(Collectors.toMap(BookTransferDTO::getBookTransferID, bookTransfer -> bookTransfer));
		errorInfos.forEach(errorInfo -> {
			BookTransferDTO booktransfer = bookTransferMap.get(errorInfo.getBookTransferId());
			errorInfo.setSalesForceCode(booktransfer.getSalesforceCode());
			errorInfo.setAgency(booktransfer.getName());
		});
	}

	//For Testing purpose
	@VisibleForTesting
	public void setBookTransferService(BookTransferService bookTransferService) {
		this.bookTransferService = bookTransferService;
	}

	@VisibleForTesting
	public void setQuotingGuidelineHelper(QuotingGuidelineHelper quotingGuidelineHelper) {
		this.quotingGuidelineHelper = quotingGuidelineHelper;
	}

	@VisibleForTesting
	public void setOpportunityRepoHelper(OpportunityRepoHelper opportunityRepoHelper) {
		this.opportunityRepoHelper = opportunityRepoHelper;
	}

	public List<PriorCarrierData> findPriorCarrierData(List<Integer> opportunityIds) {
		List<PriorCarrierData> priorCarrierDataList = new ArrayList<>();
		List<Opportunity> opportunities = opportunityRepoHelper.findOpportunitiesByIds(opportunityIds);
		opportunities.forEach(opportunity -> {
			try {
				Document doc = XmlHelper.getDocument(opportunity.getData());
				String name = AcordHelper.getPriorInsurerName(doc);
				String priorCarrierGuid = opportunity.getPriorCarrierGuid();
				String opportunityId = Integer.toString(opportunity.getOpportunityId());
				priorCarrierDataList.add(new PriorCarrierData(opportunityId, validateString(name), validateString(priorCarrierGuid)));
			} catch (XPathExpressionException | ParserConfigurationException | SAXException | IOException ex) {
				log.error(ex.getMessage());
			}
		});
		return priorCarrierDataList;
	}

	private String validateString(String input){
		return StringUtils.isBlank(input) ? StringUtils.EMPTY : input;
	}

	@VisibleForTesting
	static void moveTaxIdNodes(Document srcDoc, Document destDoc) throws XPathExpressionException {
		List<Node> srcNodes = XmlHelper.nodeListToList(XmlHelper.getNodeList(srcDoc, "//TaxId"));
		List<Node> destNodes = XmlHelper.nodeListToList(XmlHelper.getNodeList(destDoc, "//TaxId"));

		for (int i = 0; i < srcNodes.size(); i++) {
			destNodes.get(i).setTextContent(srcNodes.get(i).getTextContent());
		}
	}

	private void checkMaskEnabledForBusinessLines(LineType lineType, DetokenizeRequest request){
		if(LineType.Business.equals(lineType) ){
			request.setUnmaskBirthDt(false);
			request.setUnmaskLicensePermitNumber(false);
		}
	}

	public List<Opportunity> updateCustomerAccounts(List<Opportunity> opportunities, String sFDCID) {
		// get booktransferids for commercial opportunities
		Set<Integer> bookTransferIds = opportunities.stream()
				.map(Opportunity::getBookTransferID)
				.collect(Collectors.toSet());
    try {

      Map<Integer, Integer> bookTransferIdToSfdcid = bookTransferService.getSfdcidsForBookTransferIds(bookTransferIds);
			for(List<Opportunity> partition : ListUtils.partition(opportunities, 200)) {
				CustomerAccountServiceResponseWrapper responseWrapper = customerAccountHelper.updateCustomerAccounts(partition, bookTransferIdToSfdcid, sFDCID);
				updateOpportunities(partition, responseWrapper);
			}
		} catch (Exception e) {
      log.error("Error creating customer account - During opportunity updates", e);
    }

    return opportunities;
	}

	private void updateOpportunities(List<Opportunity> opportunities, CustomerAccountServiceResponseWrapper responseWrapper)
		throws ParserConfigurationException, SAXException, IOException, XPathExpressionException, TransformerException {
		// get all customer policies from get data
		List<CustomerPolicy> customerPolicies = new ArrayList<>();

		log.debug("responseWrapper: {}", responseWrapper);

		if(responseWrapper != null && responseWrapper.getData() != null) {
			responseWrapper.getData().forEach(data ->
				customerPolicies.addAll(data.getCustomerPolicies())
			);
		}
		log.info("customerPolicies: {}", customerPolicies);
		// update opportunities with customer account ids
		for(Opportunity opportunity : opportunities) {
			String customerAccountId = CustomerAccountHelper.CUSTOMER_ACCOUNT_SERVICE_ERROR;
			String customerPolicyId = CustomerAccountHelper.CUSTOMER_ACCOUNT_SERVICE_ERROR;
			log.info(" Before OpportunityId: {}, acctId: {}, policyId: {}", opportunity.getOpportunityId(), customerAccountId, customerPolicyId);
			CustomerPolicy customerPolicy = customerPolicies.stream()
					.filter(policy -> Objects.equals(policy.getAqe().getOpportunityId(), opportunity.getOpportunityId()))
				.findFirst()
				.orElse(null);

			if (customerPolicy != null) {
				customerAccountId = customerPolicy.getCustomerAccountId();
				customerPolicyId = customerPolicy.get_id();
			}
			log.info("After OpportunityId: {}, acctId: {}, policyId: {}", opportunity.getOpportunityId(), customerAccountId, customerPolicyId);
			Document xmlDoc = XmlHelper.getDocument(opportunity.getData());
			AcordHelper.setCustomerAccountId(xmlDoc, customerAccountId);
			AcordHelper.setCustomerPolicyId(xmlDoc, customerPolicyId);
			opportunity.setData(XmlHelper.getDocumentString(xmlDoc));
		}
	}

	public void addLobToMasterOppSPQEMeta(MasterOppSPQEMeta masterOppSPQEMeta){
		if(masterOppSPQEMeta.getPreviousOppIds().isEmpty()){
			return;
		}
		List<OpportunityErrorInfo> oppData = opportunityRepoHelper.getErrorItemsByOppIds(masterOppSPQEMeta.getPreviousOppIds());

		List<SPQEOpportunity> opportunities =  oppData.stream().map(opp ->  SPQEOpportunity.builder()
				.opportunityId(opp.getOpportunityId())
				.lineOfBusiness(opp.getProductLine())
				.build())
				.collect(Collectors.toList());
		masterOppSPQEMeta.setPreviousOpportunities(opportunities);
	}

	public void updateQRIForStatusChange(List<Integer> opportunityIds, Integer status) {
    try {
      quoteReportItemHelper.updateQuoteReportItemForStatus(opportunityIds.stream()
					.map(String::valueOf).collect(Collectors.toList()),
				OpportunityStatus.getOppStatusFromCode(status).getOppStatusValue());
    } catch (OpportunityException e) {
      log.error("Error updating QRI for status change", e);
    }
  }
}


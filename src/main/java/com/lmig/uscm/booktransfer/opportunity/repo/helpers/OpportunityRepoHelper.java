package com.lmig.uscm.booktransfer.opportunity.repo.helpers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.rating.client.domain.QuoteAndIssueUpdate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityDetails;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityDisplay;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityXmlData;
import com.lmig.uscm.booktransfer.opportunity.client.domain.ScheduleRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilter;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequestForOppIds;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.EffectiveDateRange;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJpaRepository;

@Slf4j
public class OpportunityRepoHelper implements OpportunityRepoHelperInterface {
	private OpportunityJpaRepository opportunityRepository;

	private OpportunityJDBCRepo opportunityJDBCRepo;

	public OpportunityRepoHelper(final OpportunityJpaRepository opportunityRepository,
								 final OpportunityJDBCRepo opportunityJDBCRepo) {
		this.opportunityRepository = opportunityRepository;
		this.opportunityJDBCRepo = opportunityJDBCRepo;
	}

	// testing only
	protected OpportunityJpaRepository getOpportunityJpaRepo() {
		return this.opportunityRepository;
	}

	// testing only
	protected void setOpportunityJpaRepo(final OpportunityJpaRepository opportunityRepository) {
		this.opportunityRepository = opportunityRepository;
	}

	// testing only
	protected OpportunityJDBCRepo getOpportunityJdbcRepo() {
		return this.opportunityJDBCRepo;
	}

	// testing only
	protected void setOpportunityJdbcRepo(final OpportunityJDBCRepo opportunityJDBCRepo) {
		this.opportunityJDBCRepo = opportunityJDBCRepo;
	}

	/**
	 * CREATE / UPDATES All Modifiers Should have @PublishOpportunityEventAnno() This allows create and updates of
	 * opportunities to publish to Salesforce and downstream consumers.
	 * <p>
	 * see /PublishOpportunityEventAnno().java for more info
	 */

	@Override
	@PublishOpportunityEventAnno(PublishOpportunityEventAnno.CREATED_OR_UPDATED_EVENT)
	public Opportunity save(Opportunity opportunity) {
		return opportunityRepository.saveAndFlush(opportunity);
	}

	@Override
	@PublishOpportunityEventAnno(PublishOpportunityEventAnno.CREATED_OR_UPDATED_EVENT)
	public List<Opportunity> saveAll(List<Opportunity> opportunities) {
		List<Opportunity> savedOpps = opportunityRepository.saveAll(opportunities);
		opportunityRepository.flush();
		return savedOpps;
	}

	// TODO: can we just use pportunityRepository.saveAndFlush here?
	@Override
	@PublishOpportunityEventAnno(PublishOpportunityEventAnno.UPDATED_EVENT)
	public Opportunity updateOpportunity(Opportunity opportunity) {
		return opportunityJDBCRepo.updateOpportunity(opportunity);
	}

	@Override
	@PublishOpportunityEventAnno(PublishOpportunityEventAnno.UPDATED_EVENT)
	public Opportunity updateOpportunityPostQuote(Opportunity opportunity) {
		return opportunityJDBCRepo.updateOpportunityPostQuote(opportunity);
	}

	@Override
	@PublishOpportunityEventAnno(PublishOpportunityEventAnno.UPDATED_EVENT)
	public Opportunity updateOpportunityForBLCall(Integer oppId,
												  String time,
												  String premiumValue,
												  boolean doesFirstTimestampExist) {
		opportunityJDBCRepo.updateOpportunityForBLCall(oppId, time, premiumValue, doesFirstTimestampExist);
		return new Opportunity(oppId);
	}

	@Override
	@PublishOpportunityEventAnno(PublishOpportunityEventAnno.UPDATED_EVENT)
	public List<Opportunity> updateOpportunitiesStatus(List<Opportunity> opportunities, OpportunityStatus status) {
		opportunityRepository.updateOpportunityStatus(
				opportunities.stream().map(Opportunity::getOpportunityId).collect(Collectors.toList()),
				status.getOppStatusCode());
		return opportunities;
	}

	@Override
	public List<Opportunity> updateOpportunitiesStatus(List<Integer> opportunityIdsToUpdate, int status) {
		for (List<Integer> part : ListUtils.partition(opportunityIdsToUpdate, 2000)) {
			opportunityRepository.updateOpportunityStatus(part, status);
		}
		ArrayList<Opportunity> updatedOpps = new ArrayList<>();
		for (Integer oppId : opportunityIdsToUpdate) {
			updatedOpps.add(new Opportunity(oppId));
		}
		return updatedOpps;
	}

	@Override
	@PublishOpportunityEventAnno(PublishOpportunityEventAnno.UPDATED_EVENT)
	public Opportunity updateOpportunityWithQnIData(String dataXmlString, QuoteAndIssueUpdate qniData) {
		try {
			opportunityJDBCRepo.updateOpportunityWithQnIData(dataXmlString, qniData);
			// TODO: Fix for QE work
			return opportunityJDBCRepo.findOpportunityByLastPolicyGuid(qniData.getLastPolicyGuid());
		} catch (Exception e) {
			log.error("UPDATE OPP EVENT HIT ERROR", e);
			return null;
		}
	}

	@Override
	// TODO: Opp event here? @PublishOpportunityEventAnno(PublishOpportunityEventAnno.Deleted) ?
	public Opportunity deleteOpportunity(Integer opportunityId) {
		opportunityRepository.deleteById(opportunityId);
		return new Opportunity(opportunityId);
	}

	/**
	 * READS
	 */

	@Override
	public List<Opportunity> findOpportunitiesByIds(List<Integer> oppIds) {
		return opportunityJDBCRepo.findByOpportunityIdIn(oppIds);
	}

	@Override
	public Opportunity findOpportunityById(int oppId) {
		return opportunityJDBCRepo.findByOpportunityId(oppId);
	}

	@Override
	public Opportunity findOpportunityForEdit(Integer oppId) {
		return opportunityJDBCRepo.findOpportunityForEdit(oppId);
	}

	@Override
	public Page<Opportunity> getPagedOpportunities(int numOfRecords, LineType lineType) {
		Page<Opportunity> results = null;

		// handle non-defaulted, incorrect lineType
		if (lineType == null) {
			lineType = LineType.Personal;
		}

		if (numOfRecords > 0) {
			Pageable arrayOfRecords = PageRequest.of(0, numOfRecords);
			// TODO update it to use JDBC
			if (LineType.All.equals(lineType)) {
				results = opportunityRepository.getOpportunities(arrayOfRecords);
			} else {
				results = opportunityRepository.getOpportunities(arrayOfRecords, lineType);
			}
		}
		return results;
	}

	@Override
	public List<OpportunityDisplay> reloadDataTable(List<Integer> oppIds) {
		return opportunityJDBCRepo.reloadDataTable(oppIds);
	}

	@Override
	public boolean doesFirstTimestampExist(Integer oppId) {
		return opportunityJDBCRepo.doesFirstTimestampExist(oppId);
	}

	@Override
	public Map<LineType, List<Integer>> getOppIdsForSchedule(ScheduleRequest schedule) {
		return opportunityJDBCRepo.getOppIdsForTheGivenSchedule(schedule);
	}

	@Override
	public List<OpportunityErrorInfo> getErrorItemsByOppIds(List<Integer> opportunityIds) {
		return opportunityJDBCRepo.getErrorItemsByOppIds(opportunityIds);
	}

	@Override
	public List<OpportunityXmlData> getDataXmlForOppIds(Set<Integer> opportunityIds) {
		return opportunityJDBCRepo.getDataXmlForOppIds(opportunityIds);
	}

	@Override
	public List<String> getNAICCd(int uploadId, LineType lineType) {
		return opportunityJDBCRepo.getNAICCd(uploadId, lineType);
	}

	@Override
	public List<String> getStateList(int uploadId, LineType lineType) {
		return opportunityJDBCRepo.getStateList(uploadId, lineType);
	}

	@Override
	public List<String> getOpportunityPriorCarriers(LineType lineType) {
		return opportunityJDBCRepo.findAllDistinctOppPriorCarrier(lineType);
	}

	@Override
	public List<Opportunity> findBybookTransferID(int bookTransferId) {
		return opportunityJDBCRepo.findByBookTransferID(bookTransferId);
	}

	@Override
	public List<CustomFilter> getCustomFilteredOpportunities(CustomFilterRequest cfRequest) {
		return opportunityJDBCRepo.getCustomFilteredOpportunities(cfRequest);
	}

	@Override
	public Integer getCustomFilteredOpportunitiesCount(CustomFilterRequest cfRequest) {
		return opportunityJDBCRepo.getCustomFilteredOpportunitiesCount(cfRequest);
	}

	@Override
	public List<Integer> getCustomFilterOppIds(CustomFilterRequestForOppIds cfRequest) {
		cfRequest.addPolicyTypeCds();
		return opportunityJDBCRepo.getCustomFiltersOppIds(cfRequest);
	}

	public EffectiveDateRange getCustomFilterOpportunitiesEffectiveDateRange(List<Integer> oppIds) {
		return opportunityJDBCRepo.getCustomFilterOpportunitiesEffectiveDateRange(oppIds);
	}

	@Override
	public String getDataXmlStringByLastPolicyGuid(String lastPolicyGuid) {
		return opportunityJDBCRepo.getDataXmlStringByLastPolicyGuid(lastPolicyGuid);
	}

	public List<Opportunity> findByOpportunityIdIn(List<Integer> oppIds) {
		return opportunityJDBCRepo.findByOpportunityIdIn(oppIds);
	}

	public Map<Integer, Integer> getBookIdsForOppIds(List<Integer> opportunityIds) {
		return opportunityJDBCRepo.getBookIds(opportunityIds);

	}

	public List<OpportunityDetails> getOppDetails(Set<Integer> bookTransferIds,
												  List<String> lobs,
												  String startEffectiveDate,
												  String endEffectiveDate,
												  LineType lineType) {
		return opportunityJDBCRepo.getOppDetails(bookTransferIds, lobs, startEffectiveDate, endEffectiveDate, lineType);
	}

}

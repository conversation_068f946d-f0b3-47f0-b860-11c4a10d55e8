package com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PartialMatchResult {
	private int opportunityId;
	private String customerName;
	private String originalEffectiveDate;
	private String lOBCd;
	private String subCode;
	private String salesforceId;
	private String agencyId;
	private List<String> possibleAgencyIdsMatches;

	public PartialMatchResult() {
		possibleAgencyIdsMatches = new ArrayList<>();
	}
}

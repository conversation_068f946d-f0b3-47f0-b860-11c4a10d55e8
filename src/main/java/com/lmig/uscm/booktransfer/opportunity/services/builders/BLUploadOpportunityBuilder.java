package com.lmig.uscm.booktransfer.opportunity.services.builders;

import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.AddressCleanseService;
import com.lmig.uscm.booktransfer.opportunity.services.CustomerAccountHelper;
import com.lmig.uscm.booktransfer.opportunity.services.EFTPaymentAccountsService;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;

@Slf4j
public class BLUploadOpportunityBuilder extends OpportunityBuilder {

	public BLUploadOpportunityBuilder(
			final OpportunityRepoHelper opportunityRepoHelper,
			final OpportunityConstructor businessOpportunityConstructor,
			final QuoteReportItemHelper quoteReportItemHelper,
			final AddressCleanseService addressCleanseService,
			final EFTPaymentAccountsService eftPaymentAccountsService,
			final CustomerAccountHelper customerAccountHelper,
			final boolean isForSPQE,
			final String importPackage
	) {
		super(opportunityRepoHelper,
				businessOpportunityConstructor,
				quoteReportItemHelper,
				addressCleanseService,
				eftPaymentAccountsService,
				customerAccountHelper,
				isForSPQE,
				importPackage);
		super.isForSPQE = isForSPQE;
	}

	@Override
	public Opportunity uploadOpportunity(OpportunityCreationRequest opportunityCreationRequest)
			throws Exception {
		Opportunity newOpportunity = getOpportunity(opportunityCreationRequest);
		newOpportunity = updateInitialOpportunityBeforeRules(opportunityCreationRequest, newOpportunity);
		Document workingDoc = XmlHelper.getDocument(newOpportunity.getOriginalXML());
		AcordHelper.setNbdRelationship(workingDoc, opportunityCreationRequest.getNbdRelationship());
		newOpportunity = applyRules(opportunityCreationRequest, newOpportunity, workingDoc);
		newOpportunity = saveOpportunity(newOpportunity);
		super.postOpportunitySave(opportunityCreationRequest, newOpportunity);

		log.info("Business Lines OpportunityId - {}", newOpportunity.getOpportunityId());

		return newOpportunity;
	}

	@Override
	protected Opportunity getOpportunity(OpportunityCreationRequest opportunityCreationRequest) throws Exception {
		return opportunityConstructor.buildInitialOpportunity(opportunityCreationRequest);
	}

	/**
	 * BL does not utilize masterOppId
	 *
	 * @return newOpportunity
	 */
	@Override
	protected Opportunity updateMasterOpp(boolean isMasterOpp, Opportunity newOpportunity) {
		return newOpportunity;
	}

}

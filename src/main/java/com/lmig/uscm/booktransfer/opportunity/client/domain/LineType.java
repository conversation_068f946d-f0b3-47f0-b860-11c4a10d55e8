package com.lmig.uscm.booktransfer.opportunity.client.domain;

import com.lmig.uscm.booktransfer.utilityservice.xml.PersonalAcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;

public enum LineType {

	Personal(LineTypeValues.PERSONAL, new String[]{"PL", LineTypeValues.PERSONAL}),
	Business(LineTypeValues.BUSINESS, new String[]{"BL", LineTypeValues.BUSINESS}),
	All(LineTypeValues.ALL);

	private final String value;
	private String[] possibleValues;

	LineType(final String value) {
		this.value = value;
		this.possibleValues = new String[0];
	}
	LineType(final String value, final String[] possibleValues) {
		this(value);
		this.possibleValues = possibleValues;
	}

	public String getValue() {
		return value;
	}

	@Override
	public String toString() {
		return value;
	}

	public static final class LineTypeValues {
		public static final String PERSONAL = "Personal";
		public static final String BUSINESS = "Business";
		public static final String ALL = "All";

		private LineTypeValues() {

		}
	}

	/**
	 * Serializes String to LineType enum. Defaults to LineType.All
	 */
	public static LineType determineLineType(String rawLineType) {
		if (StringUtils.isBlank(rawLineType)) {
			return All;
		}
		Optional<LineType> foundLineType = Arrays.stream(values())
				.filter(lineTypeValue -> Arrays.stream(lineTypeValue.possibleValues)
						.map(String::toLowerCase)
						.collect(Collectors.toList())
						.contains(rawLineType.toLowerCase()))
				.findAny();

		return foundLineType.orElse(All);
	}

	public static LineType determineLineTypeFromXml(String xml) throws OpportunityException {
		try {
			return determineLineTypeFromXml(XmlHelper.getDocument(xml));
		}catch(ParserConfigurationException | IOException | SAXException e) {
			throw new OpportunityException("Unable to determine linetype", e);
		}
	}
	
	public static LineType determineLineTypeFromXml(Document xml) throws OpportunityException {
		try {
			return PersonalAcordHelper.getPersPolicyNode(xml) != null ? LineType.Personal : LineType.Business;
		} catch (XPathExpressionException e) {
			throw new OpportunityException("Unable to determine linetype", e);
		}
	}
}

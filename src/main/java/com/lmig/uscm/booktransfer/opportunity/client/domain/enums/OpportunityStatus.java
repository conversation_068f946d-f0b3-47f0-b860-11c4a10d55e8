
package com.lmig.uscm.booktransfer.opportunity.client.domain.enums;

import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;

public enum OpportunityStatus {
	OPPORTUNITY_STATUS_UPLOAD_FAILED(1,
			"Upload Failed",
			QuoteReportStatus.QUOTE_REPORT_UNQUOTED),
	OPPORTUNITY_STATUS_UNQUOTED(2,
			"Unquoted",
			QuoteReportStatus.QUOTE_REPORT_UNQUOTED),
	OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA(3,
			"Missing Required Data",
			QuoteReportStatus.QUOTE_REPORT_MISSING_REQUIRED_DATA),
	OPPORTUNITY_STATUS_QUOTED_SUCCESS(4,
			"Quoted - Success",
			QuoteReportStatus.QUOTE_REPORT_QUOTE_IN_PROGRESS),
	OPPORTUNITY_STATUS_QUOTED_FAILED(5,
			"Quoted - Failed",
			QuoteReportStatus.QUOTE_REPORT_QUOTE_IN_PROGRESS),
	OPPORTUNITY_STATUS_QUOTED_ERROR(6,
			"Quoted - Error",
			QuoteReportStatus.QUOTE_REPORT_QUOTE_IN_PROGRESS),
	OPPORTUNITY_STATUS_QUOTED_IMPORTED(7,
			"Quoted - Imported",
			QuoteReportStatus.QUOTE_REPORT_QUOTE_IN_PROGRESS),
	OPPORTUNITY_STATUS_QUOTED_PROCESSING(8,
			"Quoted - Processing",
			QuoteReportStatus.QUOTE_REPORT_QUOTE_IN_PROGRESS),
	OPPORTUNITY_STATUS_QUOTE_CLEAN_UP(9,
			"Quote Clean Up",
			QuoteReportStatus.QUOTE_REPORT_QUOTE_IN_PROGRESS),
	OPPORTUNITY_STATUS_ISSUED(10,
			"Issued",
			QuoteReportStatus.QUOTE_REPORT_ISSUED),
	OPPORTUNITY_STATUS_WITHDRAWN(11,
			"Withdrawn",
			QuoteReportStatus.QUOTE_REPORT_QUOTE_WITHDRAWN),
	OPPORTUNITY_STATUS_AGENT_UPDATED(12,
			"Agent Updated",
			QuoteReportStatus.QUOTE_REPORT_UNQUOTED),
	OPPORTUNITY_STATUS_HERITAGE(13,
			"Heritage",
			QuoteReportStatus.QUOTE_REPORT_HERITAGE);

    private final int oppStatus;
    private final String oppStatusValue;
    private final QuoteReportStatus qrStatus;

    OpportunityStatus(int status, String oppStatusValue, QuoteReportStatus qrStatus) {
    	this.oppStatus = status;
    	this.oppStatusValue = oppStatusValue;
    	this.qrStatus = qrStatus;
    }

    public int getOppStatusCode() {
        return oppStatus;
    }

    public String getOppStatusValue() {
        return oppStatusValue;
    }
    
    public QuoteReportStatus getQuoteReportStatus() {
    	return qrStatus;
    }
    
    public static OpportunityStatus getOppStatusFromCode(int oppCode) throws OpportunityException {

		for (OpportunityStatus opp : OpportunityStatus.values()) {
			if (opp.getOppStatusCode() == oppCode) {
				return opp;
			}
		}
		throw new OpportunityException("opportunity status doesn't exists " + oppCode);
	}

	public static OpportunityStatus getOppStatusFromStatusValue(String oppStatusValue) throws OpportunityException {

		for (OpportunityStatus opp : OpportunityStatus.values()) {
			if (opp.getOppStatusValue().equalsIgnoreCase(oppStatusValue)) {
				return opp;
			}
		}
		throw new OpportunityException("opportunity status doesn't exists " + oppStatusValue);
	}
}

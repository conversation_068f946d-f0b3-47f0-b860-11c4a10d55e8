package com.lmig.uscm.booktransfer.opportunity.client;

import com.lmig.uscm.booktransfer.opportunity.client.config.OpportunityUrlProvider;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityDetails;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.DetokenizeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.util.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class OpportunityService {

	private final WebClient opportunityWebClient;
	private final String url;

	public OpportunityService(
			final WebClient opportunityWebClient,
			final OpportunityUrlProvider opportunityUrlProvider
	) {
		this.opportunityWebClient = opportunityWebClient;
		this.url = opportunityUrlProvider.getBaseUrl() + opportunityUrlProvider.getPath();
	}

	/**
	 * Find a single Opportunity by Opp Id
	 *
	 * @return Matching Opportunity
	 */
	public Opportunity findByOpportunityId(Integer oppId) throws OpportunityException {
		try {
			return opportunityWebClient.post()
					.uri(url + "v3/opportunity/findByOpportunityId?oppId=" + oppId)
					.body(BodyInserters.fromValue(new DetokenizeRequest()))
					.retrieve()
					.bodyToMono(Opportunity.class)
					.block();
		} catch (Exception e) {
			log.error("Error finding opp by id: {}", oppId);
			throw new OpportunityException(e);
		}
	}

	/**
	 * Find a single Opportunity by Opp Id with errors
	 *
	 * @return Matching Opportunity
	 */
	public Pair<Opportunity, List<String>> findByOpportunityIdWithErrors(
			Integer oppId,
			boolean detokenize,
			String requester,
			boolean unmaskBirthDt,
			boolean unmaskLicensePermitNumber
	) throws OpportunityException {
		try {
			ResponseEntity<Opportunity> getOppByIdResponse = opportunityWebClient.post()
					.uri(url + "v3/opportunity/findByOpportunityId?oppId=" + oppId + "&detokenize=" + detokenize)
					.body(BodyInserters.fromValue(new DetokenizeRequest(detokenize, requester, unmaskBirthDt, unmaskLicensePermitNumber)))
					.retrieve()
					.toEntity(Opportunity.class)
					.block();
			List<String> errors = getOppByIdResponse.getHeaders().get("Errors") == null
					? List.of()
					: getOppByIdResponse.getHeaders().get("Errors");

			return Pair.of(Objects.requireNonNull(getOppByIdResponse.getBody(), "null response body"), errors);
		} catch (Exception e) {
			log.error("Error finding opp by id: {}", oppId);
			throw new OpportunityException(e);
		}
	}

	/**
	 * Find all opportunities by list of opportunity ids
	 *
	 * @return List of Opportunities
	 */
	public List<Opportunity> getAllOppsByOppIds(List<Integer> oppIds) throws OpportunityException {
		try {
			return opportunityWebClient.post()
					.uri(url + "v3/opportunity/byIds")
					.body(BodyInserters.fromValue(oppIds))
					.retrieve()
					.bodyToMono(new ParameterizedTypeReference<List<Opportunity>>() {

					}).block();
		} catch (Exception e) {
			log.error("Error finding opps by ids: {}", oppIds);
			throw new OpportunityException(e);
		}
	}

	/**
	 * Get Opportunity Details by params
	 *
	 * @return List of Opp Details
	 */
	public List<OpportunityDetails> getOppDetails(OpportunityFilterRequest request) throws OpportunityException {
		try {
			return opportunityWebClient.post()
					.uri(url + "opportunity-details")
					.body(BodyInserters.fromValue(request))
					.retrieve()
					.bodyToMono(new ParameterizedTypeReference<List<OpportunityDetails>>() {

					})
					.block();
		} catch (Exception e) {
			log.error("Error finding opp details for: {}", request.getSfdcid());
			throw new OpportunityException(e);
		}
	}

	public OpportunityCreationResponse createOpportunity(OpportunityCreationRequest request) throws OpportunityException {
		try {
			return opportunityWebClient.post()
					.uri(url + "v3/opportunity/create")
					.body(BodyInserters.fromValue(request))
					.retrieve()
					.bodyToMono(new ParameterizedTypeReference<OpportunityCreationResponse>() {

					})
					.block();
		} catch (Exception e) {
			log.error("Error creating opp from request: {}", request);
			throw new OpportunityException(e);
		}
	}

	public OpportunityCreationResponse createOpportunityForSPQE(OpportunityCreationRequest request) throws OpportunityException {
		try {
			return opportunityWebClient.post()
					.uri(url + "v3/opportunity/SPQE")
					.body(BodyInserters.fromValue(request))
					.retrieve()
					.bodyToMono(new ParameterizedTypeReference<OpportunityCreationResponse>() {

					})
					.block();
		} catch (Exception e) {
			log.error("Error creating opp for SPQE from request: {}", request);
			throw new OpportunityException(e);
		}
	}

	public <T> Map<LineType, List<Integer>> getOpportunitiesFromSchedule(T schedule) throws OpportunityException {
		try {
			return opportunityWebClient.post()
					.uri(url + "v3/opportunity/getOppIdsForSchedule")
					.body(BodyInserters.fromValue(schedule))
					.retrieve()
					.bodyToMono(new ParameterizedTypeReference<Map<LineType, List<Integer>>>() {

					})
					.block();
		} catch (Exception e) {
			log.error("Error creating opp for Schedule from request: {}", schedule);
			throw new OpportunityException(e);
		}
	}

}

package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.UtilityService;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.PersonalAcordXPaths;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.xpath.XPathExpressionException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class PropertyCreationStrategy implements CreationStrategy {


    public boolean shouldResolveCreationRequest(final OpportunityCreationRequest creationRequest) {
        try {
            LineOfBusiness policyType = LineOfBusiness.determineLobForPersonalLineType(creationRequest.getUploadedACORD());
            return (policyType == LineOfBusiness.HOME || policyType == LineOfBusiness.DFIRE);
        } catch (XPathExpressionException | NullPointerException e) {
            // Just eat it, eat it, just eat it, eat it, ooh
        }
        return false;
    }

    @Override
    public void resolveCreationBundles(final List<CreationStrategyBundle> creationBundles, final OpportunityCreationResponse creationResponse) {
        creationBundles.forEach(bundle -> resolveCreationBundle(bundle, creationResponse));
    }

    public void resolveCreationBundle(final CreationStrategyBundle creationBundle, final OpportunityCreationResponse creationResponse) {
        List<OpportunityCreationRequest> incomingCreationRequests = new ArrayList<>(creationBundle.getRequests());

        for (OpportunityCreationRequest creationRequest : incomingCreationRequests) {
            if (!shouldResolveCreationRequest(creationRequest)) {
                continue;
            }

            try {
                splitMultipleLocationPropertyPolicies(creationBundle, creationRequest);
            } catch (XPathExpressionException e) {
                creationResponse.incrementFailedCreationCount();
            }
        }
    }

    private void splitMultipleLocationPropertyPolicies(final CreationStrategyBundle creationBundle, final OpportunityCreationRequest creationRequest) throws XPathExpressionException {
        Document document = creationRequest.getUploadedACORD();
        NodeList allLocations = AcordHelper.getLocations(document);
        NodeList allDwellings = AcordHelper.getDwellings(document);
        NodeList allDwellCoverages = AcordHelper.getDwellCoverages(document);

        // No Splitting as there is only one dwell coverage
        if (allDwellings.getLength() < 2 || allDwellCoverages.getLength() < 2) {
            log.info("No splitting needed. Number of Dwell nodes: {}. Number of Dwell coverages: {}",
                    allDwellings.getLength(), allDwellCoverages.getLength());
            return;
        }

        creationRequest.setAsMasterOpp();
        OpportunityCreationRequest emptyRequestToClone = createEmptyRequestToClone(creationRequest);

        for (int i = 1; i < allLocations.getLength(); i++) {
            Node location = allLocations.item(i);
            Node locationId = location.getAttributes().getNamedItem("id");
            Node correspondingDwelling = getCorrespondingDwelling(document, locationId);
            NodeList propertySchedules = getCorrespondingPropertySchedules(document, locationId);

            if (ObjectUtils.isNotEmpty(correspondingDwelling)) {
                OpportunityCreationRequest newRequest = emptyRequestToClone.clone();
                splitPropertyToNewOpportunity(newRequest, location, correspondingDwelling, propertySchedules);
                removePropertyFromFirstOpportunity(creationRequest, location, correspondingDwelling, propertySchedules);

                //calculate new premium
                double dwellTotalCoverageTermAmt = AcordHelper.getTotalDwellCoverageAmount(newRequest.getUploadedACORD());
                subtractDwellCoverageTotalFromPolicyTermAmt(document, dwellTotalCoverageTermAmt);
                calculatePropertyPersPolicyTermAmt(newRequest, dwellTotalCoverageTermAmt);

                creationBundle.addCreationRequest(newRequest);
            }
        }
    }

    private void splitPropertyToNewOpportunity(OpportunityCreationRequest newRequest, Node location, Node matchingDwelling, NodeList propertySchedules) throws XPathExpressionException {
        Node locationNodeToCopy = location.cloneNode(true);
        Node dwellingNodeToCopy = matchingDwelling.cloneNode(true);

        AcordHelper.addNodeAsChild(newRequest.getUploadedACORD(), locationNodeToCopy, getPropertyPolicyQuoteInqRq(newRequest.getUploadedACORD()));
        AcordHelper.addNodeAsChild(newRequest.getUploadedACORD(), dwellingNodeToCopy, getPropertyBusinessLine(newRequest.getUploadedACORD()));

        for (int i = 0; i < propertySchedules.getLength(); i++) {
            Node propertySchedule = propertySchedules.item(i).cloneNode(true);
            AcordHelper.addNodeAsChild(newRequest.getUploadedACORD(), propertySchedule, getPropertyBusinessLine(newRequest.getUploadedACORD()));
        }
    }

    private void removePropertyFromFirstOpportunity(OpportunityCreationRequest request, Node location, Node matchingDwelling, NodeList propertSchedules) throws XPathExpressionException {
        Node propertyPolicyQuoteInqRq = getPropertyPolicyQuoteInqRq(request.getUploadedACORD());
        Node propertyBusinessLine = getPropertyBusinessLine(request.getUploadedACORD());

        propertyPolicyQuoteInqRq.removeChild(location);
        propertyBusinessLine.removeChild(matchingDwelling);

        for (int i = 0; i < propertSchedules.getLength(); i++) {
            Node propertySchedule = propertSchedules.item(i);
            propertyBusinessLine.removeChild(propertySchedule);
        }
    }

    private OpportunityCreationRequest createEmptyRequestToClone(OpportunityCreationRequest request) throws XPathExpressionException {
        OpportunityCreationRequest emptyRequestToClone = request.clone();
        AcordHelper.removeAllDwelling(emptyRequestToClone.getUploadedACORD());
        AcordHelper.removeAllLocations(emptyRequestToClone.getUploadedACORD());
        AcordHelper.removeAllPropertySchedule(emptyRequestToClone.getUploadedACORD());
        return emptyRequestToClone;
    }

    private void subtractDwellCoverageTotalFromPolicyTermAmt(Document homeDfireDocument, double dwellTotalCoverageTermAmt) throws XPathExpressionException {
        double homeDfirePersPolicyTermAmt = UtilityService.convertStringToDouble(AcordHelper.getPolicyCurrentTermAmt(homeDfireDocument));
        AcordHelper.setPolicyCurrentTermAmt(homeDfireDocument, homeDfirePersPolicyTermAmt - dwellTotalCoverageTermAmt);
    }

    private void calculatePropertyPersPolicyTermAmt(OpportunityCreationRequest request, double dwellTotalCoverageTermAmt) throws XPathExpressionException {
        AcordHelper.setPolicyCurrentTermAmt(request.getUploadedACORD(), dwellTotalCoverageTermAmt);
    }

    public Node getPropertyPolicyQuoteInqRq(final Document document) throws XPathExpressionException {
        return (StringUtils.equalsIgnoreCase(LineOfBusiness.determineLobForPersonalLineType(document).toString(), LineOfBusiness.DFIRE.toString())) ?
                AcordHelper.getDwellFirePolicyQuoteInqRq(document) :
                AcordHelper.getHomePolicyQuoteInqRq(document);
    }

    public static Node getPropertyBusinessLine(final Document document) throws XPathExpressionException {
        return (StringUtils.equalsIgnoreCase(LineOfBusiness.determineLobForPersonalLineType(document).toString(), LineOfBusiness.DFIRE.toString())) ?
                AcordHelper.getDwellFireLineBusiness(document) :
                AcordHelper.getHomeLineBusiness(document);
    }

    public static Node getCorrespondingDwelling(Document document, Node locationId) throws XPathExpressionException {
        // find a dwelling with a matching LocationRef attribute as the Location Id attribute
        // and make sure it has a dwell coverage
        Node correspondingDwelling = null;
        Node dwellCoverage = null;

        if (locationId != null) {
            // find a dwell node with a location ref attribute value that we pass in
            String correspondingDwellingLookupPath = String.format("%s[@LocationRef='%s']",
                    PersonalAcordXPaths.DWELLING_ALL_NODE, locationId.getTextContent());
            correspondingDwelling = XmlHelper.getNodeFromDoc(correspondingDwellingLookupPath, document);

            // check if dwell node has a coverage with a dwell code
            String dwellCoverageLookupPath = String.format("%s/%s", correspondingDwellingLookupPath, PersonalAcordXPaths.COVERAGE_DWELL);
            dwellCoverage = XmlHelper.getNodeFromDoc(dwellCoverageLookupPath, document);
        }

        return (dwellCoverage != null) ? correspondingDwelling : null;
    }

    public static NodeList getCorrespondingPropertySchedules(Document document, Node locationId) throws XPathExpressionException{
        // find a property schedule with a matching LocationRef attribute as the Location Id attribute
        NodeList correspondingPropertySchedule = null;

        if (locationId != null) {
            // find a property schedule node with a location ref attribute value that we pass in
            String correspondingPropertyScheduleLookupPath = String.format("%s[@LocationRef='%s']",
                    PersonalAcordXPaths.PROPERTY_SCHEDULE, locationId.getTextContent());
            correspondingPropertySchedule = XmlHelper.getNodeList(document, correspondingPropertyScheduleLookupPath);
        }

        return correspondingPropertySchedule;
    }

    @Override
    public CreationStrategyName getStrategyName() {
        return CreationStrategyName.SplitUpProperties;
    }
}

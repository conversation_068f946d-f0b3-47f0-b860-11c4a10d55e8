package com.lmig.uscm.booktransfer.opportunity.repo.helpers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

public class OpportunityForScheduleResultExtractor implements ResultSetExtractor<Map<LineType, List<Integer>>> {

	@Override
	public Map<LineType, List<Integer>> extractData(ResultSet resultSet) throws SQLException, DataAccessException {
		List<Integer> personalOpportunities = new ArrayList<>();
		List<Integer> businessOpportunities = new ArrayList<>();

		while (resultSet.next()){
			final Integer opportunityId = resultSet.getInt("OpportunityID");
			final LineType lineType = OpportunityUtil
					.defaultPersonalOrConvertStringToLineType(resultSet.getString("LineType"));
			if (LineType.Business.equals(lineType)) {
				businessOpportunities.add(opportunityId);
			} else {
				personalOpportunities.add(opportunityId);
			}
		}

		Map<LineType, List<Integer>> resultMap = new EnumMap<>(LineType.class);

		resultMap.put(LineType.Business, businessOpportunities);
		resultMap.put(LineType.Personal, personalOpportunities);
		return resultMap;
	}
}

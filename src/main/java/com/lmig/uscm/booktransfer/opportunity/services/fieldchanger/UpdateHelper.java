package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.FieldChangerException;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeField;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.uscm.booktransfer.opportunity.services.utility.QuoteReportItemUtil;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;

import java.util.List;
import java.util.Map;

public abstract class UpdateHelper {
	protected OpportunityHelper opportunityHelper;
	protected QuoteReportItemHelper quoteReportItemHelper;
	protected OpportunityRepoHelper opportunityRepoHelper;
	
	private final Map<Integer, Opportunity> oppIdToMap;
	private final Map<String, QuoteReportItemLegacy> qriIdToMap;

	public UpdateHelper(List<Opportunity> oppList, List<QuoteReportItemLegacy> quoteItemsList) {
		this.oppIdToMap = OpportunityUtil.getOpportunityIdToOpportunityMap(oppList);
		this.qriIdToMap = QuoteReportItemUtil.getQuoteSalesForceIdToQuoteReportItemMap(quoteItemsList);
	}

	/**
	 * This method is used to see if the field name is the current fieldName of the
	 * given class. This allows us to see if it is the correct field to update
	 *
	 * @return true if field name equals expected field name
	 */
	protected abstract boolean isUpdatingField(String fieldName);

	/**
	 * This builds out the intitial result, so it checks the current values and sees
	 * if it is a valid update. If it is it will then save it on a object for bulk
	 * updating in the future. This is called after we find a given opp and quote
	 * report item
	 */
	protected abstract OppChangeFieldResult buildInitialResultForSpecificValue(OppChangeField oppChangeField,
			Opportunity opp);

	/**
	 * This will save the list of opps to the database with the given value to
	 * update it to.
	 */
	protected abstract void saveFieldUpdate(List<Opportunity> opportunities, String value) throws FieldChangerException;

	public void setDatabaseHelpers(OpportunityHelper opportunityHelper, QuoteReportItemHelper quoteReportItemHelper, OpportunityRepoHelper opportunityRepoHelper) {
		this.opportunityHelper = opportunityHelper;
		this.quoteReportItemHelper = quoteReportItemHelper;
		this.opportunityRepoHelper = opportunityRepoHelper;
	}

	/**
	 * This has you update the field based on the oppChangeField
	 */
	protected OppChangeFieldResult buildInitialResult(OppChangeField oppChangeField) {
		Opportunity opp;
		try {
			opp = getOpportunity(oppChangeField);
			// if we get rid of quote report item from spreadsheet we will not need this
			// check
			QuoteReportItemLegacy quoteItem = qriIdToMap.get(Integer.toString(oppChangeField.getOppId()));
			if (quoteItem == null && !LineType.Business.equals(opp.getLineType())) {
				return new OppChangeFieldResult(opp, oppChangeField).isError("Quote report item does not exist");
			}
			return buildInitialResultForSpecificValue(oppChangeField, opp);
		} catch (FieldChangerException e) {
			return new OppChangeFieldResult(null, oppChangeField).isError("Opp does not exist");
		}
	}

	protected Opportunity getOpportunity(OppChangeField oppChangeField) throws FieldChangerException {
		Opportunity opp = this.oppIdToMap.get(oppChangeField.getOppId());
		if (opp == null) {
			// this would be cannot find opportunity this will be logged else where.
			throw new FieldChangerException("Opportunity not found: " + oppChangeField.getOppId());
		}
		return opp;
	}
}

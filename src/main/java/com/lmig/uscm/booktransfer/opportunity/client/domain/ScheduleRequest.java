package com.lmig.uscm.booktransfer.opportunity.client.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.javatuples.Pair;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ScheduleRequest {
	public static final String INITIAL_ACTIVE_STATUS = "Active";
	public static final String IN_PROGRESS_STATUS = "In Progress";
	public static final String WITHDRAWN_STATUS = "On Hold";
	protected String description;

	private String id;
	private String status;
	private Pair<String, String> assignedUser;
	private Set<String> salesforceCodes;
	private String statCode;
	private String masterStatCode;
	private String agencyName;
	private Set<LineOfBusiness> businessTypes;
	private Set<String> ratingStates;
	private Integer exportSLA;
	private String rulesPackageId;
	private String bookStatus;
	private String nbdRelationship;
	private Set<String> priorCarriers;
	private Set<Pair<String, String>> quoteStatuses;
	private Date createdDate;
	private Date lastModifiedDate;
	private Long version;
	private Date startEffectiveDate;

	public ScheduleRequest() {

		this.priorCarriers = new HashSet<>();
		this.salesforceCodes = new HashSet<>();
		this.businessTypes = new HashSet<>();
		this.quoteStatuses = new HashSet<>();
		this.ratingStates = new HashSet<>();
	}

	public ScheduleRequest(String id, String salesforceCode, String agencyName, LineOfBusiness businessTypes, Integer exportSLA, String rulesPackageId, String bookStatus) {
		this();
		this.id = id;
		this.status = INITIAL_ACTIVE_STATUS;
		this.addSalesForceCodes(salesforceCode);
		this.agencyName = agencyName;
		this.addBusinessType(businessTypes);
		this.exportSLA = exportSLA;
		this.rulesPackageId = rulesPackageId;
		this.bookStatus = bookStatus;
	}

	public ScheduleRequest(String id, String description) {
		this.description = description;
		this.id = id;
	}

	public ScheduleRequest(String id) {
		this.id = id;
	}

	public void setSalesforceCodes(List<String> salesforceCodes) {
		this.salesforceCodes = new HashSet<>();
		this.salesforceCodes.addAll(salesforceCodes);
	}

	public void setBusinessTypes(List<LineOfBusiness> businessTypes) {
		this.businessTypes = new HashSet<>();
		this.businessTypes.addAll(businessTypes);
	}

	public void setPriorCarriers(List<String> priorCarriers) {
		this.priorCarriers = new HashSet<>();
		this.priorCarriers.addAll(priorCarriers);
	}

	public void setQuoteStatuses(List<Pair<String, String>> quoteStatuses) {
		this.quoteStatuses = new HashSet<>();
		this.quoteStatuses.addAll(quoteStatuses);
	}

	public void addPriorCarriers(String priorCarrier) {
		this.priorCarriers.add(priorCarrier);
	}

	public Set<String> readQuoteStatusValues() {
		Set<String> quoteStatusValues = new HashSet<>();

		for (Pair<String, String> strings : this.quoteStatuses) {
			quoteStatusValues.add(strings.getValue1());
		}

		return quoteStatusValues;
	}

	public Set<String> readQuoteStatusKeys() {
		Set<String> quoteStatusKeys = new HashSet<>();

		for (Pair<String, String> strings : this.quoteStatuses) {
			quoteStatusKeys.add(strings.getValue0());
		}

		return quoteStatusKeys;
	}

	public void addBusinessType(LineOfBusiness businessTypeToAdd) {
		if (this.businessTypes == null) {
			this.businessTypes = new HashSet<>();
		}

		this.businessTypes.add(businessTypeToAdd);
	}

	public void addQuoteStatuses(Pair<String, String> quoteStatusToAdd) {
		if (this.quoteStatuses == null) {
			this.quoteStatuses = new HashSet<>();
		}

		this.quoteStatuses.add(quoteStatusToAdd);
	}

	public void addSalesForceCodes(String salesforceCodeToAdd) {
		if (this.salesforceCodes == null) {
			this.salesforceCodes = new HashSet<>();
		}

		this.salesforceCodes.add(salesforceCodeToAdd);
	}

	public void setActiveStatus() {
		this.setStatus(INITIAL_ACTIVE_STATUS);
	}

	public void setWithdrawnStatus() {
		this.setStatus(WITHDRAWN_STATUS);
	}

	public void setInProgressStatus() {
		this.setStatus(IN_PROGRESS_STATUS);
	}

	public Set<String> getRatingStates() {
		return this.ratingStates;
	}

	public void setRatingStates(Set<String> ratingStates) {
		this.ratingStates = ratingStates;
	}

	public void addRatingState(String state) {
		this.ratingStates.add(state);
	}
}


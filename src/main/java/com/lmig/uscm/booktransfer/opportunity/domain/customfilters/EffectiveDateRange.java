package com.lmig.uscm.booktransfer.opportunity.domain.customfilters;

import lombok.Data;

@Data
public class EffectiveDateRange {
	/**
	 * These dates used in the confirmation when the user selects all opportunities
	 * to quote. we have to return these with the filter results as we do server
	 * side pagination and we dont get the min and max dates from the data we have
	 * on the client side
	 */
	private String minEffectiveDate;
	private String maxEffectiveDate;
}

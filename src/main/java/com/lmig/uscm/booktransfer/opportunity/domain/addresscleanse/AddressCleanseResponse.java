package com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AddressCleanseResponse {
    private List<AddressCleanseResult> addresses = new ArrayList<>();

    public Context getContext() {
        return context;
    }

    private Context context;

    public List<AddressCleanseResult> getAddresses() {
        return addresses;
    }
}

package com.lmig.uscm.booktransfer.opportunity.client.domain;

public class OpportunityException extends Exception {
	private static final long serialVersionUID = 5283333808750922323L;
	private int statusValue;

	public OpportunityException(Exception e) {
		super(e);
	}

	public OpportunityException(String message, int statusValue) {
		super(message);
		this.statusValue = statusValue;
	}

	public OpportunityException(String message) {
		super(message);
	}

	public OpportunityException(String message, Exception e) {
		super(message, e);
	}

	public int getStatusValue() {
		return statusValue;
	}
}

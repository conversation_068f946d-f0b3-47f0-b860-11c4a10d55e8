package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.config.properties.ServiceUrlProperties;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.UploadEventException;
import com.lmig.uscm.booktransfer.uploadmanager.client.domain.UploadEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

@Slf4j
public class UploadEventService {
	private static final String CREATE_PATH = "/v2/create-upload-event";
	private static final String UPDATE_STATUS_PATH = "/setUploadEventStatus";

	private final RestTemplate restTemplate;
	private final ServiceUrlProperties.UploadServiceUrlProvider urlProvider;


	public UploadEventService(final ServiceUrlProperties.UploadServiceUrlProvider uploadServiceUrlProvider) {
		this.restTemplate = new RestTemplate(new HttpComponentsClientHttpRequestFactory());
		this.urlProvider = uploadServiceUrlProvider;
	}

	public Integer createUploadEvent(UploadEvent uploadEvent) throws OpportunityException {
		try {
			String url = urlProvider.getBaseUrl() + CREATE_PATH;

			ResponseEntity<UploadEvent> responseItems = restTemplate.exchange(url, HttpMethod.POST,
					new HttpEntity<>(uploadEvent), new ParameterizedTypeReference<>() {

					});
			if (responseItems.getStatusCode().is2xxSuccessful()) {
				return Objects.requireNonNull(
						responseItems.getBody(), "POST - url returned null response body").getUploadEventID();
			}
			throw new UploadEventException("Could not create a new upload event");
		} catch (RestClientException | UploadEventException | NullPointerException e) {
			log.error("Error creating new Upload Event", e);
			throw new OpportunityException(e);
		}
	}

	public void updateUploadEventStatus(final Integer uploadEventId, final String status) throws OpportunityException {
		try {
			String baseUrl = urlProvider.getBaseUrl() + UPDATE_STATUS_PATH;
			String url = String.format("%s?uploadEventID=%d&status=%s", baseUrl, uploadEventId, status);

			ResponseEntity<Void> responseItems = restTemplate.exchange(url, HttpMethod.POST,
					HttpEntity.EMPTY, Void.class);
			if (!responseItems.getStatusCode().is2xxSuccessful()) {
				throw new UploadEventException("Could not set status for upload event " + uploadEventId);
			}
		} catch (RestClientException | UploadEventException | NullPointerException e) {
			log.error("Error setting status for Upload Event", e);
			throw new OpportunityException(e);
		}
	}
}

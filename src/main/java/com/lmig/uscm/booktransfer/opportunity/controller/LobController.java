package com.lmig.uscm.booktransfer.opportunity.controller;

import com.lmig.uscm.booktransfer.opportunity.repo.LobGateway;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Controller to get lob values
 */

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/lobs")
public class LobController {
    private final LobGateway lobGateway;

    public LobController(final LobGateway lobGateway) {
        this.lobGateway = lobGateway;
    }

    @GetMapping("/all")
    Map<String, List<String>> getAllLobs() {
        return lobGateway.getAllLobs();
    }
}

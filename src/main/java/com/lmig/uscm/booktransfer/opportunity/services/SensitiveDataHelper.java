package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.booktransfer.btsensitivedataservice.client.SensitiveDataServiceWebClient;
import com.lmig.booktransfer.btsensitivedataservice.client.domain.exception.SensitiveDataException;
import com.lmig.booktransfer.btsensitivedataservice.client.domain.tokenization.DetokenizationRequest;
import com.lmig.booktransfer.btsensitivedataservice.client.domain.tokenization.DetokenizationResponse;
import com.lmig.booktransfer.btsensitivedataservice.client.domain.tokenization.TokenizationRequest;
import com.lmig.booktransfer.btsensitivedataservice.client.domain.tokenization.TokenizationResponse;
import com.lmig.booktransfer.btsensitivedataservice.client.helper.TokenizationHelper;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunitySensitiveDataException;
import com.lmig.uscm.booktransfer.quotereport.client.domain.enums.LineOfBusiness;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import com.lmig.usconsumermarkets.booktransfer.btuploadpreprocessor.client.UploadPreprocessorWebClient;
import com.lmig.usconsumermarkets.booktransfer.btuploadpreprocessor.client.domain.DataType;
import com.lmig.usconsumermarkets.booktransfer.btuploadpreprocessor.client.domain.ScrubbingRequest;
import com.lmig.usconsumermarkets.booktransfer.btuploadpreprocessor.client.domain.ScrubbingResponse;
import com.lmig.usconsumermarkets.booktransfer.btuploadpreprocessor.client.domain.UploadPreprocessorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Used to tokenize or scrub sensitive data from a document. Will dynamically
 * pull in all active profiles on start up and use those to decide if scrubbing or
 * tokenization should occur given the *XmlWithEnvCheck methods are called
 * <p>
 * Requires profile "Tokenization" for utility-service to provide XmlTokenization bean
 */
@Slf4j
public class SensitiveDataHelper {
	private static final List<String> ENVS_TO_SCRUB = Arrays.asList("dev", "test", "staging", "local", "unit");
	private static final List<String> ENVS_TO_TOKENIZE = Arrays.asList("dev", "test", "staging", "production", "local", "unit");

	private SensitiveDataServiceWebClient sensitiveDataServiceClient;
	private UploadPreprocessorWebClient uploadPreprocessorWebClient;
	private List<String> currentProfiles;

	public SensitiveDataHelper() {
	}

	public SensitiveDataHelper(final SensitiveDataServiceWebClient sensitiveDataServiceClient,
							   final UploadPreprocessorWebClient uploadPreprocessorWebClient,
							   final List<String> currentProfiles) {
		this.sensitiveDataServiceClient = sensitiveDataServiceClient;
		this.uploadPreprocessorWebClient = uploadPreprocessorWebClient;
		this.currentProfiles = currentProfiles;
	}

	public Document scrubXmlWithEnvCheck(Document originalDoc) throws OpportunitySensitiveDataException {
		if (ENVS_TO_SCRUB.stream().anyMatch(this.currentProfiles::contains)) {
			return scrubXml(originalDoc);
		}
		return originalDoc;
	}

	public Document scrubXml(Document originalDoc) throws OpportunitySensitiveDataException {
		try {
			ScrubbingResponse response = uploadPreprocessorWebClient.scrub(ScrubbingRequest.builder()
					.data(XmlHelper.getDocumentString(originalDoc))
					.dataType(DataType.XML)
					.lobName(LineOfBusiness.determineLob(originalDoc).name())
					.scrubSsnToken(true)
					.build());
			if (StringUtils.isNotEmpty(response.getData())) {
				return XmlHelper.getDocument(response.getData());
			}
			throw new OpportunitySensitiveDataException("Could not scrub sensitive data");
		} catch (UploadPreprocessorException e) {
			throw new OpportunitySensitiveDataException("Could not scrub sensitive data", e);
		} catch (ParserConfigurationException | XPathExpressionException | IOException | SAXException | TransformerException e) {
			throw new OpportunitySensitiveDataException("Could not create document from scrubbed data", e);
		}
	}

	public void tokenizeXmlWithEnvCheck(final Document originalDoc) throws OpportunitySensitiveDataException {
		if (ENVS_TO_TOKENIZE.stream().anyMatch(this.currentProfiles::contains)) {
			tokenizeXml(originalDoc);
		}
	}

	public void tokenizeXml(final Document originalDoc) throws OpportunitySensitiveDataException {
		List<Node> ssnNodes = null;
		List<String> replacementTokens = null;
		try {
			ssnNodes = TokenizationHelper.getSSNNodes(originalDoc);
			TokenizationRequest tokenizationRequest = TokenizationHelper.buildTokenizationRequestForSSNs(ssnNodes);
			Optional<TokenizationResponse> tokenizationResponseOptional = this.sensitiveDataServiceClient.tokenize(tokenizationRequest);

			if(tokenizationResponseOptional.isPresent()) {
				replacementTokens = tokenizationResponseOptional.get().getTokens();
			}

		} catch (SensitiveDataException e) {
			String errorMsg = "Failed to tokenize SSNs and cannot determine sensitivity";
			log.error(errorMsg, e);
			throw new OpportunitySensitiveDataException(e);
		} finally {
			TokenizationHelper.replaceSSNsWithValuesOrDefault(ssnNodes, replacementTokens);
		}
	}

	public Document deTokenizeXml(final Document opportunityXml, final String requester) throws OpportunitySensitiveDataException {
		try {
			List<Node> ssnNodes =  TokenizationHelper.getSSNNodes(opportunityXml);
			DetokenizationRequest detokenizationRequest = TokenizationHelper.buildDetokenizationRequestForSSNs(ssnNodes, requester);
			Optional<DetokenizationResponse> detokenizationResponseOptional = sensitiveDataServiceClient.detokenize(detokenizationRequest);

			if(detokenizationResponseOptional.isPresent()) {
				List<String> replacementValues = detokenizationResponseOptional.get().getValues();
				TokenizationHelper.replaceSSNsWithValuesOrDefault(ssnNodes, replacementValues);
				return opportunityXml;
			} else {
				String errorMsg = "Failed to detokenize Opportunity XML";
				log.error(errorMsg);
				throw new OpportunitySensitiveDataException(errorMsg);
			}
		} catch(SensitiveDataException e) {
			log.error(e.getMessage(), e);
			throw new OpportunitySensitiveDataException(e);
		}

	}
}

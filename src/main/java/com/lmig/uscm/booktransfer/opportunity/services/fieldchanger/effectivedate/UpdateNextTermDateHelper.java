package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lmig.uscm.booktransfer.email.client.domain.EmailException;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.ChangeEffectiveDateResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.MassUpdaterForChangeEffectiveDate;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.ExportHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;

import java.time.LocalDate;
import java.util.List;

/**
 * This class handles the next term update effective data option
 */
public class UpdateNextTermDateHelper extends UpdateEffectiveDateHelper {
	public UpdateNextTermDateHelper(OpportunityHelper opportunityHelper, ExportHelper exportHelper, final OpportunityRepoHelper opportunityRepoHelper) {
		super(opportunityHelper, exportHelper, opportunityRepoHelper);
	}

	@Override
	protected ChangeEffectiveDateResponse sendResultCSVs(List<Opportunity> opps, String resultsCSV, String userEmail)
			throws JsonProcessingException, EmailException {
		// create and send all string to email notification here
		exportHelper.sendUpdateEffectiveDateCSVs(resultsCSV, null, userEmail);

		return new ChangeEffectiveDateResponse(null, resultsCSV);
	}

	@Override
	protected List<OppChangeFieldResult> updateEffectiveDatesByPackage(List<OppChangeFieldResult> packagedOpportunities,
			LocalDate bookTransferStartDate) {
		for (OppChangeFieldResult oppChangeFieldResult : packagedOpportunities) {
			setEffectiveDateToNextTerm(oppChangeFieldResult, bookTransferStartDate);
		}

		return packagedOpportunities;
	}

	@Override
	protected MassUpdaterForChangeEffectiveDate buildInitialOpporytunityResults(List<Opportunity> opportunities) {
		MassUpdaterForChangeEffectiveDate matchingCustomersMap = new MassUpdaterForChangeEffectiveDate();
		for (Opportunity opp : opportunities) {
			OppChangeFieldResult oppChangeFieldResult =
					new OppChangeFieldResult(opp, "EffectiveDate", opp.getEffectiveDate());
			if (opp.getBookTransferID() == 0) {
				oppChangeFieldResult.isError("SubCode missing");
			}
			matchingCustomersMap.addOpportunityChangeResult(oppChangeFieldResult);
		}
		return matchingCustomersMap;
	}

	/**
	 * If the effective date of the opportunity is before the start date then update the effectivedate to expiration
	 * date and expiration date to new effective date plus policy term
	 *
	 * @param oppChangeFieldResult
	 * @param bookTransferStartDate
	 * @return OppChangeFieldResult
	 */
	private OppChangeFieldResult setEffectiveDateToNextTerm(OppChangeFieldResult oppChangeFieldResult,
			LocalDate bookTransferStartDate) {
		LocalDate newEffectiveDate =
				calculateNewEffectiveDate(oppChangeFieldResult.getOpportunity(), bookTransferStartDate);
		LocalDate currentEffectiveDate = LocalDate.parse(oppChangeFieldResult.getOpportunity().getEffectiveDate());

		if (isCurrentDateSameAsEarliestDate(newEffectiveDate, currentEffectiveDate)) {
			// This is so that way the business can see the update did not happened, but no
			// errors occurred
			return oppChangeFieldResult.isError("Effective date is on or after the start date");
		}

		return updateEffectiveDate(oppChangeFieldResult, newEffectiveDate);
	}

}

package com.lmig.uscm.booktransfer.opportunity.domain;

import com.lmig.uscm.booktransfer.utilityservice.DateUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.time.LocalDateTime;

/**
 * Custom mapper for /client/domain/Opportunity and /domain/Opportunity that requires `mvn clean install -DskipTests -Dmaven.clover.skip=true` to
 * generate the implementation in /target/generated-sources/annotations by the mapstruct factory or you'll get an error like:
 * java.lang.ClassNotFoundException: Cannot find implementation for com.lmig.uscm.booktransfer.opportunity.domain.OpportunityMapper
 * <p>
 * More information regarding custom mappings using mapstruct can be found [here](https://www.baeldung.com/mapstruct)
 */
@Mapper
public interface OpportunityMapper {

	@Mappings({
			@Mapping(source = "timestampCallPartner", target = "timestampCallPartner", qualifiedByName = "stringToLocalDateTime"),
			@Mapping(source = "timestampFirstCallPartner", target = "timestampFirstCallPartner", qualifiedByName = "stringToLocalDateTime"),
			@Mapping(source = "timestampUpload", target = "timestampUpload", qualifiedByName = "stringToLocalDateTime"),
			@Mapping(source = "timestampCleanup", target = "timestampCleanup", qualifiedByName = "stringToLocalDateTime"),
			@Mapping(source = "timestampIssued", target = "timestampIssued", qualifiedByName = "stringToLocalDateTime")
	})
	Opportunity opportunityDTOtoOpportunity(com.lmig.uscm.booktransfer.opportunity.client.domain.Opportunity oppDTOSource);

	@Mappings({
			@Mapping(source = "timestampCallPartner", target = "timestampCallPartner", qualifiedByName = "localDateTimeToString"),
			@Mapping(source = "timestampFirstCallPartner", target = "timestampFirstCallPartner", qualifiedByName = "localDateTimeToString"),
			@Mapping(source = "timestampUpload", target = "timestampUpload", qualifiedByName = "localDateTimeToString"),
			@Mapping(source = "timestampCleanup", target = "timestampCleanup", qualifiedByName = "localDateTimeToString"),
			@Mapping(source = "timestampIssued", target = "timestampIssued", qualifiedByName = "localDateTimeToString")
	})
	com.lmig.uscm.booktransfer.opportunity.client.domain.Opportunity opportunityToOpportunityDTO(Opportunity oppSource);

	@Named("localDateTimeToString")
	static String localDateTimeToString(LocalDateTime dateTime) {
		try {
			return dateTime.toString();
		} catch (Exception e) {
			return null;
		}
	}

	@Named("stringToLocalDateTime")
	static LocalDateTime stringToLocalDateTime(String timeAsString) {
		try {
			return DateUtils.getDateTimeFromString(timeAsString);
		} catch (Exception e) {
			return null;
		}
	}
}

/*
 * Copyright (c) 2019, Liberty Mutual
 * Proprietary and Confidential
 * All Rights Reserved
 */
package com.lmig.uscm.booktransfer.opportunity.repo.mappers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityDetails;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row Mapper to map JDBC Data to the Opportunity Details Class
 */
public class OpportunityDetailsMapper implements RowMapper<OpportunityDetails> {
	/***
	 * Intakes a result set and a row number and converts it to a Opportunity
	 * Details
	 *
	 * @param resultSet the values for that row
	 * @param rowNumber the row the result set coresponds to
	 * @returns a new Opportunity Details object
	 * @throws SQLException when the result set is malformed
	 */
	@Override
	public OpportunityDetails mapRow(ResultSet resultSet, int rowNumber) throws SQLException {
		OpportunityDetails item = new OpportunityDetails();
		item.setOpportunityId(resultSet.getInt("OpportunityId"));
		item.setBooktransferId(resultSet.getInt("BooktransferId"));
		item.setLob(resultSet.getString("Businesstype"));
		item.setEffectiveDate(resultSet.getString("EffectiveDate"));
		final LineType databaseLineType = OpportunityUtil.defaultPersonalOrConvertStringToLineType(resultSet.getString("LineType"));
		item.setLineType(databaseLineType);
		return item;
	}
}

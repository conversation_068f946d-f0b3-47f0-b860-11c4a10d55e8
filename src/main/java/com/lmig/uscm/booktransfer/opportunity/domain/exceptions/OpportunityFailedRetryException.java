package com.lmig.uscm.booktransfer.opportunity.domain.exceptions;

public class OpportunityFailedRetryException extends RuntimeException {

	public OpportunityFailedRetryException() {

	}

	public OpportunityFailedRetryException(final String msg) {
		super(msg);

	}

	public OpportunityFailedRetryException(final String msg, final Exception e) {
		super(msg, e);

	}

	public OpportunityFailedRetryException(final Exception e) {
		super(e);
	}
}

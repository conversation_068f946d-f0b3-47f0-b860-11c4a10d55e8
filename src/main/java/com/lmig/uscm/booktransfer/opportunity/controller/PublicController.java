/*
 * Copyright (C) 2017, Liberty Mutual Group
 *
 * Created on Mar 28, 2017
 */

package com.lmig.uscm.booktransfer.opportunity.controller;

import java.util.List;

import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequestForOppIds;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;

/**
 * This controller is for endpoints that need to not have OAuth. An example is for use by a Google Chrome Extension.
 */
@CrossOrigin(origins = "*")
@RestController
@Component
@RequestMapping(value = "/public")
@Slf4j
public class PublicController {
    private final OpportunityRepoHelper opportunityRepoHelper;

    public PublicController(final OpportunityRepoHelper opportunityRepoHelper) {
        this.opportunityRepoHelper = opportunityRepoHelper;
    }

    /**
     * READS
     */

    @Operation(summary = "getSafecoPolicyGUIDByOppId")
    @GetMapping(value = "/opportunity/safecoPolicyGUID")
    public String getSafecoPolicyGUIDByOppId(@RequestParam(value = "oppId") Integer id) {
        return opportunityRepoHelper.findOpportunityById(id).getLastPolicyGuid();
    }

    @Operation(summary = "getOppIdBySafecoPolicyGUID")
    @GetMapping(value = "/opportunity/oppId")
    public List<Integer> getOppIdBySafecoPolicyGUID(@RequestParam(value = "safecoPolicyGUID") String guid) {
        return opportunityRepoHelper.getCustomFilterOppIds(new CustomFilterRequestForOppIds().setSafecoPolicyGuid(guid));
    }
}

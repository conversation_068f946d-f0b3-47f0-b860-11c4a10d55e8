package com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger;

import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * This class allows a mass update of filednames by fieldvalues by value change
 * to be stored as a list of results
 */
public class MassUpdateResultsOfFieldChange {
	// This map should work as following {fieldNameToUpdate: {fildValueToUpdateToo:
	// List<OppChangeFieldResult>ToUpdate}}
	private final Map<String, Map<String, List<OppChangeFieldResult>>> fieldNamesToFieldValuesToOppChangeResult = new HashMap<>();

	public void addOpportunityChangeResult(OppChangeFieldResult oppChangeFieldResult) {
		Map<String, List<OppChangeFieldResult>> fieldValuesToIds;

		if (fieldNamesToFieldValuesToOppChangeResult.containsKey(oppChangeFieldResult.getFieldName())) {
			fieldValuesToIds = fieldNamesToFieldValuesToOppChangeResult.get(oppChangeFieldResult.getFieldName());
		} else {
			fieldValuesToIds = new HashMap<>();
		}

		List<OppChangeFieldResult> oppChanges;
		if (fieldValuesToIds.containsKey(oppChangeFieldResult.getFieldUpdatedValue())) {
			oppChanges = fieldValuesToIds.get(oppChangeFieldResult.getFieldUpdatedValue());
		} else {
			oppChanges = new ArrayList<>();
		}

		oppChanges.add(oppChangeFieldResult);
		fieldValuesToIds.put(oppChangeFieldResult.getFieldUpdatedValue(), oppChanges);
		fieldNamesToFieldValuesToOppChangeResult.put(oppChangeFieldResult.getFieldName(), fieldValuesToIds);
	}

	public Iterable<String> getAllFieldNamesToChange() {
		return fieldNamesToFieldValuesToOppChangeResult.keySet();
	}

	public Iterable<String> getAllFieldValuesToChange(String fieldName) {
		return fieldNamesToFieldValuesToOppChangeResult.get(fieldName).keySet();
	}

	/**
	 * Gets all Opp change results on this object by searching for params
	 */
	public List<OppChangeFieldResult> getAllOpportunitiesResults(String fieldName, String fieldValue) {
		return fieldNamesToFieldValuesToOppChangeResult.get(fieldName).get(fieldValue);
	}

	/**
	 * Gets all Opp change results on this object
	 */
	public List<OppChangeFieldResult> getAllOpportunitiesResults() {
		List<OppChangeFieldResult> oppChangeFields = new ArrayList<>();
		for (Map<String, List<OppChangeFieldResult>> fieldValuesToOppChangeResult : fieldNamesToFieldValuesToOppChangeResult
				.values()) {
			for (List<OppChangeFieldResult> res : fieldValuesToOppChangeResult.values()) {
				oppChangeFields.addAll(res);
			}
		}
		return oppChangeFields;
	}

	public List<Opportunity> getAllNotFailedOpportunities(String fieldName, String fieldValue) {
		List<OppChangeFieldResult> oppChangeFieldResults = fieldNamesToFieldValuesToOppChangeResult
				.get(fieldName)
				.get(fieldValue);
		List<Opportunity> opportunities = new ArrayList<>();
		for (OppChangeFieldResult oppChangeFieldResult : oppChangeFieldResults) {
			if (!oppChangeFieldResult.didFail()) {
				opportunities.add(oppChangeFieldResult.getOpportunity());
			}
		}
		return opportunities;
	}

}

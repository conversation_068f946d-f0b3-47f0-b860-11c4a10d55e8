package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.EFTPaymentAccountsResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.domain.Constants;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;

import javax.xml.xpath.XPathExpressionException;

@Slf4j
public final class EFTPaymentAccountsHelper {

    private EFTPaymentAccountsHelper(){}

    public static boolean isSingleCA(Document doc) throws XPathExpressionException {
        if (doc != null) {
            LineOfBusiness lob = LineOfBusiness.determineLobForPersonalLineType(doc);
            if (lob.equals(LineOfBusiness.AUTOP) || lob.equals(LineOfBusiness.MTR)) {
                return StringUtils.isNotEmpty(AcordHelper.getMigrationInfoInstrumentId(doc));
            }
        }
        return false;
    }
    public static void addPaymentAccountData(Document doc, EFTPaymentAccountsResponse eftPaymentAccountsData) throws XPathExpressionException {
        if (eftPaymentAccountsData == null) {
            AcordHelper.setMigrationInfoPaymentLookupStatus(doc, Constants.PAYMENT_DATA_DOES_NOT_EXIST);
        } else if(StringUtils.isBlank(eftPaymentAccountsData.getAccountPaymentType())){
            AcordHelper.setMigrationInfoPaymentLookupStatus(doc, Constants.PAYMENT_TYPE_IS_BLANK);
        } else if (!Constants.EFT_PAYMENT_TYPE.equals(eftPaymentAccountsData.getAccountPaymentType())) {
            AcordHelper.setMigrationInfoPaymentLookupStatus(doc, Constants.PAYMENT_TYPE_IS_NOT_EFT);
        }else {
            if (eftPaymentAccountsData.getBankAccountType() != null) {
                AcordHelper.setAccountType(doc, eftPaymentAccountsData.getBankAccountType());
            }
            if (eftPaymentAccountsData.getFinancialDetailActId() != null) {
                AcordHelper.setInstrumentId(doc, eftPaymentAccountsData.getFinancialDetailActId());
            }
            if (eftPaymentAccountsData.getAcctHldrFullName() != null) {
                AcordHelper.setAccountHolderName(doc, eftPaymentAccountsData.getAcctHldrFullName());
            }
            if (eftPaymentAccountsData.getAccHldrFirstName() != null) {
                AcordHelper.setAccountHolderGivenName(doc, eftPaymentAccountsData.getAccHldrFirstName());
            }
            if (eftPaymentAccountsData.getAccHldrLastName() != null) {
                AcordHelper.setAccountHolderSurname(doc, eftPaymentAccountsData.getAccHldrLastName());
            }
            if (eftPaymentAccountsData.getBankRoutingNumber() != null) {
                AcordHelper.setRoutingNumber(doc, eftPaymentAccountsData.getBankRoutingNumber());
            }
            if (eftPaymentAccountsData.getBankAccountNumber() != null) {
                AcordHelper.setAccountNumberId(doc, eftPaymentAccountsData.getBankAccountNumber());
            }
            if (eftPaymentAccountsData.getPaymentAccountToken() != null) {
                AcordHelper.setTokenizedAccountNumber(doc, eftPaymentAccountsData.getPaymentAccountToken());
            }
            AcordHelper.setMonthlyPaymentPlan(doc, Constants.MONTHLY_PAYMENT_PLAN);
        }
    }
    public static void addPaymentAccountDataForReUpload(Document workingDoc, Document document) throws XPathExpressionException{
        if (AcordHelper.getAccountType(document) != null) {
            AcordHelper.setAccountType(workingDoc, AcordHelper.getAccountType(document));
        }
        if (AcordHelper.getInstrumentId(document) != null) {
            AcordHelper.setInstrumentId(workingDoc, AcordHelper.getInstrumentId(document));
        }
        if (AcordHelper.getAccountHolderName(document) != null) {
            AcordHelper.setAccountHolderName(workingDoc, AcordHelper.getAccountHolderName(document));
        }
        if (AcordHelper.getAccountHolderGivenName(document) != null) {
            AcordHelper.setAccountHolderGivenName(workingDoc, AcordHelper.getAccountHolderGivenName(document));
        }
        if (AcordHelper.getAccountHolderSurname(document) != null) {
            AcordHelper.setAccountHolderSurname(workingDoc, AcordHelper.getAccountHolderSurname(document));
        }
        if (AcordHelper.getRoutingNumber(document) != null) {
            AcordHelper.setRoutingNumber(workingDoc, AcordHelper.getRoutingNumber(document));
        }
        if (AcordHelper.getAccountNumberId(document) != null) {
            AcordHelper.setAccountNumberId(workingDoc, AcordHelper.getAccountNumberId(document));
        }
        if (AcordHelper.getTokenizedAccountNumber(document) != null) {
            AcordHelper.setTokenizedAccountNumber(workingDoc, AcordHelper.getTokenizedAccountNumber(document));
        }
        AcordHelper.setMonthlyPaymentPlan(workingDoc, Constants.MONTHLY_PAYMENT_PLAN);
    }

    public static void postProcessPaymentAccountInfo(
            final EFTPaymentAccountsResponse eftPaymentAccountsResponse,
            final Integer existingOppId,
            final Document document,
            final int oppId,
            final boolean retryMaxAttemptsReached
    ) throws XPathExpressionException{
        if (document != null
                && (LineOfBusiness.determineLobForPersonalLineType(document).equals(LineOfBusiness.AUTOP)
                    || LineOfBusiness.determineLobForPersonalLineType(document).equals(LineOfBusiness.MTR))
                && StringUtils.isNotEmpty(AcordHelper.getMethodPaymentCd(document))
                && AcordHelper.getMethodPaymentCd(document).equalsIgnoreCase(Constants.EFT_PAYMENT_TYPE)
                && AcordHelper.getSafecoMigrationInfo(document) != null
                && StringUtils.isEmpty(AcordHelper.getMigrationInfoInstrumentId(document))
        ) {
            log.info("EFT Payment Instrument ID is missing: {}", oppId);
        }
        if (isSingleCA(document) && existingOppId == null && eftPaymentAccountsResponse == null){
            log.info("{}: {}", Constants.PAYMENT_DATA_DOES_NOT_EXIST, oppId);
        }
        if (retryMaxAttemptsReached){
            log.info("Failed to lookup payment data: {}", oppId);
        }
    }
}

package com.lmig.uscm.booktransfer.opportunity.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.uscm.booktransfer.opportunity.messaging.Consumer;
import com.lmig.uscm.booktransfer.opportunity.messaging.Publisher;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityCreationHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.rating.client.domain.QniUpdateMessaging;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SpringRabbitConfig {

	public static final String APPLICATION_TOPIC = "upload.topic"; // External
	public static final String OPPORTUNITY_QUEUE = "messaging-opportunity-update-queue";
	public static final String OPPORTUNITY_REUPLOAD_QUEUE = "opportunity-reupload-queue";

	@Bean
	public Consumer consumer(
			final OpportunityHelper opportunityHelper,
			final OpportunityCreationHelper opportunityCreationHelper,
			final ObjectMapper objectMapper) {
		return new Consumer(opportunityHelper, opportunityCreationHelper, objectMapper);

	}

	@Bean
	public Publisher publisher(final RabbitTemplate rabbitTemplate) {
		return new Publisher(rabbitTemplate);

	}

	@Bean(name = "UploadTopic")
	public TopicExchange simpleExchange() {
		return ExchangeBuilder.topicExchange(APPLICATION_TOPIC).durable(true).autoDelete().build();
	}

	@Bean(name = "UploadQueue")
	public Queue oppQueue() {
		return QueueBuilder.durable(OPPORTUNITY_QUEUE).autoDelete().build();
	}

	@Bean
	public Binding oppBinding(@Qualifier("UploadTopic") final TopicExchange topic,
			@Qualifier("UploadQueue") final Queue queue) {
		return BindingBuilder.bind(queue).to(topic).with(OPPORTUNITY_QUEUE);
	}

	@Bean(name = "ReuploadQueue")
	public Queue oppReuploadQueue() {
		return QueueBuilder.durable(OPPORTUNITY_REUPLOAD_QUEUE).autoDelete().build();
	}

	@Bean
	public Binding oppReuploadBinding(@Qualifier("UploadTopic") final TopicExchange topic,
			@Qualifier("ReuploadQueue") final Queue queue) {
		return BindingBuilder.bind(queue).to(topic).with(OPPORTUNITY_REUPLOAD_QUEUE);
	}

	@Bean("QnITopic")
	public TopicExchange qniUpdateTopic() {
		return ExchangeBuilder
				.topicExchange(QniUpdateMessaging.QNI_UPDATE_TOPIC_EXCHANGE).durable(true).autoDelete().build();
	}

	@Bean("QnIQueue")
	public Queue qniUpdateOppQueue() {
		return QueueBuilder.durable(QniUpdateMessaging.QNI_UPDATE_OPPORTUNITY_QUEUE).autoDelete().build();
	}

	@Bean
	public Binding qniUpdateOppBinding(@Qualifier("QnITopic") final TopicExchange topic,
			@Qualifier("QnIQueue") final Queue queue) {
		return BindingBuilder.bind(queue).to(topic).with(QniUpdateMessaging.QNI_UPDATE_ROUTING_KEY);
	}

}

/*
 * Copyright (C) 2017, Liberty Mutual Group
 *
 * Created on Oct 10, 2017
 */

package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.QuoteReportStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.QuoteReportBulkUpdate;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import javax.xml.xpath.XPathExpressionException;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class QuoteReportItemHelper {

	private final QuoteReportService quoteReportService;

	public QuoteReportItemHelper(final QuoteReportService quoteReportService) {
		this.quoteReportService = quoteReportService;
	}

	/**
	 * Updates the bookinfo for associated quoteReportItems in the mass move
	 *
	 * @param quoteIds Ids to update
	 * @param SFDCID   sfcid to update
	 * @param subCode  subcode to update
	 */
	public void updateQuoteReportItemForMoveBooks(List<String> quoteIds, Integer SFDCID, String subCode) {
		QuoteReportBulkUpdate update = new QuoteReportBulkUpdate();
		update.setQuoteSalesforceIds(quoteIds);
		update.setSubCode(subCode);
		update.setSalesforceId(SFDCID);
		quoteReportService.patchQuoteReportFields(update);
	}

	/**
	 * Updates the bookinfo for associated quoteReportItems in the mass move
	 *
	 * @param quoteIds Ids to update
	 * @param status   status to update
	 */
	public void updateQuoteReportItemForStatus(List<String> quoteIds, String status) {
		QuoteReportBulkUpdate update = new QuoteReportBulkUpdate();
		update.setQuoteSalesforceIds(quoteIds);
		update.setStatus(status);
		quoteReportService.patchQuoteReportFields(update);
	}

	public QuoteReportItemLegacy buildAndSaveQuoteReportItem(Opportunity opportunity, Integer SFDCID, String subCode)
			throws Exception {
		QuoteReportItemLegacy quoteReportItem = build(opportunity, SFDCID, subCode);
		if (quoteReportItem.getQuoteReportID() == 0) {
			return quoteReportService.addQuoteReport(quoteReportItem);
		}
		return quoteReportService.updateQuoteReport(quoteReportItem);
	}

	public QuoteReportItemLegacy build(Opportunity opportunity, Integer SFDCID, String subCode) throws Exception {
		log.info("Creating QRI for opportunity - {}", opportunity.getOpportunityId());
		QuoteReportItemLegacy quoteReportItem = quoteReportService
				.getQuoteReportByOpportunityId(opportunity.getOpportunityId());
		if (quoteReportItem == null) {
			quoteReportItem = new QuoteReportItemLegacy();
		}
		Document xmlDoc = XmlHelper.getDocument(opportunity.getData());

		quoteReportItem.setSalesforceID(SFDCID);
		quoteReportItem.setEffectiveDate(opportunity.getEffectiveDate());
		quoteReportItem.setCustomerName(opportunity.getCustomerName());
		quoteReportItem.setRatingState(opportunity.getState());
		quoteReportItem.setlOB(
				getLob(LineOfBusiness.determineLobFromValue(opportunity.getBusinessType()), AcordHelper.getPolicyTypeCd(xmlDoc), opportunity));
		quoteReportItem.setSafecoPolicyTerm(
				Opportunity.calcNewPolicyTerm(opportunity.getBusinessType(), xmlDoc) == 12 ? "Annual" : "6 month");
		// set the address info for slp
		String customerMailAddr1 = AcordHelper.getInsuredMailingAddr1(xmlDoc);
		String customerMailAddr2 = AcordHelper.getInsuredMailingAddr2(xmlDoc);
		String customerMailCity = AcordHelper.getInsuredMailingCity(xmlDoc);
		String customerMailState = AcordHelper.getInsuredMailingState(xmlDoc);
		String customerMailZip = AcordHelper.getInsuredMailingPostalCode(xmlDoc);

		quoteReportItem.setCustomerMailAddress1(customerMailAddr1);
		quoteReportItem.setCustomerMailAddress2(customerMailAddr2);
		quoteReportItem.setCustomerMailCity(customerMailCity);
		quoteReportItem.setCustomerMailState(customerMailState);
		quoteReportItem.setCustomerMailZip(customerMailZip);

		LineOfBusiness lob = LineOfBusiness.determineLobFromValue(quoteReportItem.getlOB());
		if (lob.equals(LineOfBusiness.AUTOP) || lob.equals(LineOfBusiness.MTR)) {
			quoteReportItem.setPriorCarrierPolicyTerm(Opportunity.extractPolicyTerm(xmlDoc) == 12 ? "Annual" : "6 month");
		} else {
			quoteReportItem.setPriorCarrierPolicyTerm(quoteReportItem.getSafecoPolicyTerm());
		}
		String str = AcordHelper.getCoverageDwellLimit(xmlDoc);
		try {
			Double d = Double.parseDouble(str);
			quoteReportItem.setPriorCovA(d);
		} catch (Exception ignored) {
		}

		quoteReportItem.setSafecoCovA(quoteReportItem.getPriorCovA());
		quoteReportItem.setPriorPremium(opportunity.getPriorPremium());
		quoteReportItem.setSafecoPremium(opportunity.getLastQuotedPremium());
		int oppStatus = opportunity.getStatus();
		if (oppStatus == 3) {
			quoteReportItem.setStatus("Missing Required Data");
			// Mark a quote for review for agents
			quoteReportItem.setExtra3("false");
		} else {
			quoteReportItem.setStatus("Unquoted");
		}
		if (opportunity.getLastPolicyGuid() != null) {
			quoteReportItem.setPolicyGuid(opportunity.getLastPolicyGuid());
		}
		quoteReportItem.setSubCode(subCode);
		quoteReportItem.setQuoteSalesforceID(Integer.toString(opportunity.getOpportunityId()));

		quoteReportItem.setCustomerFirstName(getCustomerFirstName(xmlDoc));
		quoteReportItem.setCustomerLastName(getCustomerLastName(xmlDoc));
		quoteReportItem.setPhase("Active - Automation");
		String expiringPremium = AcordHelper.getStateAutoExpiredPremium(xmlDoc);
		try {
			quoteReportItem.setExpiringPremium(Double.parseDouble(expiringPremium));
		} catch (Exception ignored) {
			// expiringPremium was "", default to 0.0
			quoteReportItem.setExpiringPremium(0.0);
		}

		return quoteReportItem;

	}

	protected String getLob(Document xmlDoc, Opportunity opportunity)
			throws XPathExpressionException {

		return getLob(LineOfBusiness.determineLobForPersonalLineType(xmlDoc), AcordHelper.getPolicyTypeCd(xmlDoc), opportunity);
	}

	// TODO: create method to handle different home types
	String getLob(LineOfBusiness businessType, String policyTypeCd, Opportunity opportunity) {
		String lob = "";
		switch (businessType) {
			case AUTOP:
				if (StringUtils.isNotEmpty(opportunity.getMasterOppID())
						&& !"null".equalsIgnoreCase(opportunity.getMasterOppID())
						&& !opportunity.getMasterOppID().equalsIgnoreCase(String.valueOf(opportunity.getOpportunityId()))) {
					lob = "Auto - 5th Car Policy";
				} else {
					lob = "Auto";
				}
				break;
			case MTR:
				if (StringUtils.isNotEmpty(opportunity.getMasterOppID())
						&& !"null".equalsIgnoreCase(opportunity.getMasterOppID())
						&& !opportunity.getMasterOppID().equalsIgnoreCase(String.valueOf(opportunity.getOpportunityId()))) {
					lob = "Motorcycle - 5th Car Policy";
				} else {
					lob = "Motorcycle";
				}
				break;
			case BOAT:
				lob = "Watercraft";
				break;
			case UMBRP:
				lob = "Umbrella";
				break;
			case DFIRE:
				lob = "Fire";
				break;
			case HOME:
				switch (policyTypeCd) {
					case "03":
					case "05":
					case "H03":
					case "H05":
					case "T2":
					case "T3":
						lob = "Home";
						break;
					case "04":
					case "H04":
					case "4A":
					case "T4":
						lob = "Renters";
						break;
					case "06":
					case "6A":
					case "H06":
					case "08":
					case "T6":
					case "T7":
						lob = "Condo";
						break;
					default:
						lob = "Home";
						break;
				}
				break;
		}

		return lob;
	}

	public List<QuoteReportItemLegacy> updateQuoteReportItem(Opportunity opp) {
		if (LineType.Business.equals(opp.getLineType())) {
			log.info("Opportunity is LineType.Business, skipping QRI update");
			return null;
		}
		log.info("QRI_GET_START - {}", new Date());
		List<QuoteReportItemLegacy> quotereportItemList = quoteReportService
				.getAllQuoteReportsForOpportunityId(opp.getOpportunityId());
		log.info("QRI_GET_FINISH - {}", new Date());

		if (CollectionUtils.isEmpty(quotereportItemList)) {
			log.info("No quote report items for opportunity id {}", opp.getOpportunityId());
			return Collections.emptyList();
		}
		log.info("QRI_SIZE - {}", quotereportItemList.size());

		// TODO: Determine business flow for error handling
		try {
			Document xmlDoc = XmlHelper.getDocument(opp.getData());
			String lob = getLob(xmlDoc, opp);

			// retrieve the policy terms
			// Non auto policies are set to Annual terms
			String priorPolicyTerm = "Annual";
			String safecoPolicyTerm = "Annual";
			if (lob.equalsIgnoreCase("auto")) {
				// if an auto policy try to get derived term
				// if we cannot get term, both terms will be set to null
				priorPolicyTerm = getPolicyTerm(xmlDoc);
				safecoPolicyTerm = StringUtils.isNotEmpty(priorPolicyTerm) ? priorPolicyTerm : null;
			}

			String priorCovA = AcordHelper.getCoverageDwellLimit(xmlDoc);
			String firstName = getCustomerFirstName(xmlDoc);
			String lastName = getCustomerLastName(xmlDoc);
			String customerMailAddr1 = AcordHelper.getInsuredMailingAddr1(xmlDoc);
			String customerMailAddr2 = AcordHelper.getInsuredMailingAddr2(xmlDoc);
			String customerMailCity = AcordHelper.getInsuredMailingCity(xmlDoc);
			String customerMailState = AcordHelper.getInsuredMailingState(xmlDoc);
			String customerMailZip = AcordHelper.getInsuredMailingPostalCode(xmlDoc);

			QuoteReportStatus status = QuoteReportStatus.getQuoteReportStatusFromCode(opp.getStatus());
			for (QuoteReportItemLegacy quoteReport : quotereportItemList) {
				quoteReport.setStatus(status.getQuoteReportMessage());
				quoteReport.setlOB(lob);

				if (!priorCovA.isEmpty()) {
					quoteReport.setPriorCovA(Double.valueOf(priorCovA));
				}
				quoteReport.setCustomerFirstName(firstName);
				quoteReport.setCustomerLastName(lastName);
				quoteReport.setCustomerName(opp.getCustomerName());
				quoteReport.setEffectiveDate(opp.getEffectiveDate());
				quoteReport.setRatingState(opp.getState());
				quoteReport.setCustomerMailAddress1(customerMailAddr1);
				quoteReport.setCustomerMailAddress2(customerMailAddr2);
				quoteReport.setCustomerMailCity(customerMailCity);
				quoteReport.setCustomerMailState(customerMailState);
				quoteReport.setCustomerMailZip(customerMailZip);
				quoteReport.setPolicyGuid(opp.getLastPolicyGuid());
				quoteReport.setSafecoPremium(opp.getLastQuotedPremium());
				quoteReport.setPriorPremium(opp.getPriorPremium());

				// only save policy terms if they are not empty
				if (StringUtils.isNoneEmpty(priorPolicyTerm, safecoPolicyTerm)) {
					quoteReport.setPriorCarrierPolicyTerm(priorPolicyTerm);
					quoteReport.setSafecoPolicyTerm(safecoPolicyTerm);
				}
			}

			log.info("QRI_SAVE_START - {}", new Date());
			List<QuoteReportItemLegacy> updatedList = quoteReportService.updateBulkQuoteReports(quotereportItemList);
			log.info("QRI_SAVE_FINISH- {}", new Date());
			return updatedList;
		} catch (Exception e) {
			log.error("QRI_SAVE_FAILED - OppID: {}", opp.getOpportunityId(), e);
			return null;
		}
	}

	private String getCustomerFirstName(Document xmlDoc) throws XPathExpressionException {
		return Optional.ofNullable(AcordHelper.getInsuredGivenName(xmlDoc))
				.filter(StringUtils::isNotEmpty)
				.orElse(AcordHelper.getInsuredOrPrincipalGivenName(xmlDoc));
	}

	private String getCustomerLastName(Document xmlDoc) throws XPathExpressionException {
		return Optional.ofNullable(AcordHelper.getInsuredSurname(xmlDoc))
				.filter(StringUtils::isNotEmpty)
				.orElse(AcordHelper.getInsuredOrPrincipalSurname(xmlDoc));
	}

	protected String getPolicyTerm(Document xmlDoc) {
		String policyTerm = "Annual";
		try {
			String effDate = AcordHelper.getEffectiveDt(xmlDoc);
			String expDate = AcordHelper.getExpirationDt(xmlDoc);
			if ((effDate.trim().length() > 0) && (expDate.trim().length() > 0)) {
				long days = ChronoUnit.DAYS.between(LocalDate.parse(effDate), LocalDate.parse(expDate));
				if (days < 198) {
					policyTerm = "6 month";
				}
			}
		} catch (Exception e) {
			log.error("Exception in getPolicyTerm", e);
			policyTerm = null;
		}
		return policyTerm;
	}

	public List<QuoteReportItemLegacy> getQuoteReportItemsForStatusChange(List<Integer> quoteIds)
			throws OpportunityException {
		List<QuoteReportItemLegacy> quoteItemsList = new ArrayList<>();

		for (List<Integer> partition : ListUtils.partition(quoteIds, OpportunityJDBCRepo.SQL_BATCH_SIZE)) {
			quoteItemsList.addAll(quoteReportService.getAllQuoteReportsForOpportunityIds(partition));
		}

		return quoteItemsList;
	}

	public List<QuoteReportItemLegacy> getQuoteReportItemsForBookChange(List<Integer> quoteIds) throws OpportunityException {
		List<QuoteReportItemLegacy> quoteItemsList = new ArrayList<>();

		for (List<Integer> partition : ListUtils.partition(quoteIds, OpportunityJDBCRepo.SQL_BATCH_SIZE)) {
			quoteItemsList.addAll(quoteReportService.getAllQuoteReportsForOpportunityIds(partition));
		}

		return quoteItemsList;
	}

	public void updateQuoteReportItems(List<String> itemsToUpdate, QuoteReportStatus status) {
		if (CollectionUtils.isNotEmpty(itemsToUpdate)) {
			QuoteReportBulkUpdate update = new QuoteReportBulkUpdate();
			update.setQuoteSalesforceIds(itemsToUpdate);
			if (status != null) {
				update.setStatus(status.getQuoteReportMessage());
			}
			quoteReportService.patchQuoteReportFields(update);
		}
	}

	/**
	 * Update the quote report item with the new lead effective date
	 */
	public void updateQuoteReportForLeadEffectiveDate(List<QuoteReportItemLegacy> quoteReports) {
		quoteReportService.patchQuoteReportFields(quoteReports);
	}
}

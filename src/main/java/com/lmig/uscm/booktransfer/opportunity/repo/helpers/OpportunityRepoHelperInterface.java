
package com.lmig.uscm.booktransfer.opportunity.repo.helpers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityDisplay;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityXmlData;
import com.lmig.uscm.booktransfer.opportunity.client.domain.ScheduleRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilter;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequestForOppIds;
import com.lmig.uscm.booktransfer.rating.client.domain.QuoteAndIssueUpdate;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface OpportunityRepoHelperInterface {
	/**
	 * CREATORS / MODIFIERS All Modifiers should return Opportunity or List Opportunity
	 * for @PublishOpportunityEventAnno
	 */
	Opportunity save(Opportunity opportunity);

	List<Opportunity> saveAll(List<Opportunity> opportunities);

	Opportunity updateOpportunity(Opportunity opportunity);

	Opportunity updateOpportunityPostQuote(Opportunity opportunity);

	Opportunity updateOpportunityForBLCall(Integer oppId,
			String time,
			String premiumValue,
			boolean doesFirstTimestampExist);

	List<Opportunity> updateOpportunitiesStatus(List<Opportunity> opportunities, OpportunityStatus status);

	List<Opportunity> updateOpportunitiesStatus(List<Integer> opportunityIdsToUpdate, int status);

	Opportunity deleteOpportunity(Integer opportunityId);

	Opportunity updateOpportunityWithQnIData(String dataXmlString, QuoteAndIssueUpdate qniData) throws Exception;

	/**
	 * READS
	 */
	Opportunity findOpportunityById(int oppId);

	List<Opportunity> findOpportunitiesByIds(List<Integer> oppIds);

	Opportunity findOpportunityForEdit(Integer oppId);

	Page<Opportunity> getPagedOpportunities(int numOfRecords, LineType lineType);

	List<OpportunityDisplay> reloadDataTable(List<Integer> oppIds);

	boolean doesFirstTimestampExist(Integer oppId);

	Map<LineType, List<Integer>> getOppIdsForSchedule(ScheduleRequest schedule);

	List<OpportunityErrorInfo> getErrorItemsByOppIds(List<Integer> opportunityIds);

	List<OpportunityXmlData> getDataXmlForOppIds(Set<Integer> opportunityIds);

	List<String> getNAICCd(int uploadId, LineType lineType);

	List<String> getStateList(int uploadId, LineType lineType);

	List<String> getOpportunityPriorCarriers(LineType lineType);

	List<Opportunity> findBybookTransferID(int bookTransferId);

	List<CustomFilter> getCustomFilteredOpportunities(CustomFilterRequest cfRequest);

	Integer getCustomFilteredOpportunitiesCount(CustomFilterRequest cfRequest);

	List<Integer> getCustomFilterOppIds(CustomFilterRequestForOppIds cfRequest);

	String getDataXmlStringByLastPolicyGuid(String lastPolicyGuid);

}

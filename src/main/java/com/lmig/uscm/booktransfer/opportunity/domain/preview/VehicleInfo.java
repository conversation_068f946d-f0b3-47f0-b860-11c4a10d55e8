package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import javax.xml.xpath.XPathExpressionException;
import java.math.BigDecimal;

public class VehicleInfo {
	private String vehAddress;
	private String vehMake;
	private String vehModel;
	private String vehYear;
	private String vehLiabilityLimit;
	private String vin;

	private String vehiclePremium;

	protected VehicleInfo() { }

	public String getVehAddress() {
		return vehAddress;
	}

	public void setVehAddress(String vehAddress) {
		this.vehAddress = vehAddress;
	}

	public String getVehMake() {
		return vehMake;
	}

	public void setVehMake(String vehMake) {
		this.vehMake = vehMake;
	}

	public String getVehModel() {
		return vehModel;
	}

	public void setVehModel(String vehModel) {
		this.vehModel = vehModel;
	}

	public String getVehYear() {
		return vehYear;
	}

	public void setVehYear(String vehYear) {
		this.vehYear = vehYear;
	}

	public String getVehLiabilityLimit() {
		return vehLiabilityLimit;
	}

	public void setVehLiabilityLimit(String vehLiabilityLimit) {
		this.vehLiabilityLimit = vehLiabilityLimit;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getVehiclePremium() {
		return vehiclePremium;
	}

	public void setVehiclePremium(String vehiclePremium) {
		this.vehiclePremium = vehiclePremium;
	}

	public void setRequiredData(Node vehNode, PreviewDataResponse response) throws XPathExpressionException {
		String vin = AcordHelper.getVehIdentificationNumber(vehNode);
		if (StringUtils.isBlank(vin)) {
			response.addMissingDataPoint("VIN");
		} else {
			this.setVin(vin);
		}
		if (StringUtils.isBlank(AcordHelper.getFullTermAmt(vehNode))) {
			response.addMissingDataPoint("VehPremium");
		}
	}

	public void setVehicleData(Document originalXml, Node vehicle) throws XPathExpressionException {
		// give us a node off of veh node that is the same as the xPath as
		// ./Coverage[CoverageCd='BI']

		Node splitLiabilityLimit = AcordHelper.getCoverage(vehicle, "BI");
		// give us a node off the that is the same as the xPath as
		// ./Coverage[CoverageCd='CSL']
		Node cslLiabilityLimit = AcordHelper.getCoverage(vehicle, "CSL");
		this.setVehLiabilityLimit(getVehicleLiabilityLimit(splitLiabilityLimit, cslLiabilityLimit));
		setVehicleData(vehicle, getAddressNode(originalXml, vehicle));
	}

	private Node getAddressNode(Document originalXml, Node vehicle) throws XPathExpressionException {
		Node addressNode = AcordHelper.getLocationAddr(originalXml);
		if (addressNode != null && vehicle.getAttributes().getNamedItem("LocationRef") != null) {
				String locationRefId = vehicle.getAttributes().getNamedItem("LocationRef").getNodeValue();
				addressNode = AcordHelper.getPersAutoLocation(originalXml, locationRefId);
		}
		return addressNode;
	}

	private String getVehicleLiabilityLimit(Node splitLiabilityLimit, Node cslLiabilityLimit)
			throws XPathExpressionException {
		String vehicleLiabilityLimit = "";

		if (splitLiabilityLimit != null) {
			vehicleLiabilityLimit = generateSplitLimits(splitLiabilityLimit);
		} else if (cslLiabilityLimit != null) {
			vehicleLiabilityLimit = getCoverageLimit(cslLiabilityLimit);
		}
		return vehicleLiabilityLimit;
	}

	private void setVehicleData(Node vehicleNode, Node addressNode) throws XPathExpressionException {
		this.setVehMake(AcordHelper.getManufacturer(vehicleNode));
		this.setVehModel(AcordHelper.getModel(vehicleNode));
		this.setVehYear(AcordHelper.getModelYear(vehicleNode));
		if (addressNode != null) {
			this.setVehAddress(Preview.buildAddress(addressNode));
		}

	}

	String generateSplitLimits(Node biCoverageNode) throws XPathExpressionException {
		if (isRejected(biCoverageNode)) {
			return "REJ";
		}

		Node perPersonLimitNode = AcordHelper.getPerPersonOrBiEachPersLimit(biCoverageNode);
		Node perAccidentLimitNode = AcordHelper.getPerAccOrOtherOccLimit(biCoverageNode);

		String perPersonLimitAmount = getLimitAmount(perPersonLimitNode);
		String perAccidentLimitAmount = getLimitAmount(perAccidentLimitNode);

		if (StringUtils.isBlank(perPersonLimitAmount) || StringUtils.isBlank(perAccidentLimitAmount))
		{
			Node limitNode = AcordHelper.getLimit(biCoverageNode);
			String limitAmount = getLimitAmount(limitNode);
			if (StringUtils.isBlank(limitAmount)) {
				return "";
			}

			return formatLimitForSplit(limitAmount);
		}
		return formatLimitForSplit(perPersonLimitAmount) + "/" + formatLimitForSplit(perAccidentLimitAmount);
	}

	String getLimitAmount(final Node limit)
			throws XPathExpressionException {
		if (limit == null) {
			return "";
		}

		String limitAmount = AcordHelper.getFormatInteger(limit);

		if (StringUtils.isBlank(limitAmount)) {
			limitAmount = AcordHelper.getFormatCurrencyAmt(limit);
		}

		return limitAmount;
	}

	/**
	 * Verify the coverage has not been rejected
	 *
	 * @param biCoverageNode
	 * @return if coverage is rejected or not
	 * @throws XPathExpressionException
	 */
	boolean isRejected(final Node biCoverageNode) throws XPathExpressionException {
		boolean rejFormatInteger = AcordHelper.getCoverageFormatInteger(biCoverageNode).equalsIgnoreCase("REJ");
		boolean rejFormatCurrencyAmt = AcordHelper.getCoverageFormatCurrencyAmt(biCoverageNode).equalsIgnoreCase("REJ");
		boolean rejOptionCd = AcordHelper.getCoverageOptionCd(biCoverageNode).equalsIgnoreCase("REJ");
		return rejFormatInteger || rejFormatCurrencyAmt || rejOptionCd;
	}

	String formatLimitForSplit(final String limitTextValue) {
		if (StringUtils.isBlank(limitTextValue)) {
			return "";
		}
		String formattedLimit = limitTextValue.replace("$", "").replace(",", "");
		BigDecimal decimalPerPersonLimit = new BigDecimal(formattedLimit);
		if (decimalPerPersonLimit.compareTo(BigDecimal.valueOf(1000)) >= 0) {
			decimalPerPersonLimit = decimalPerPersonLimit.divide(BigDecimal.valueOf(1000));
			formattedLimit = decimalPerPersonLimit.toString();
		}
		return formattedLimit;
	}

	private String getCoverageLimit(Node coverage) throws XPathExpressionException {
		// coverage = ./Coverage[CoverageCd='CSL']
		String coverageLimit = AcordHelper.getCoverageFormatInteger(coverage);

		if (StringUtils.isBlank(coverageLimit)) {
			coverageLimit = AcordHelper.getCoverageFormatCurrencyAmt(coverage);
		}

		if (StringUtils.isBlank(coverageLimit)) {
			String optionCd = AcordHelper.getCoverageOptionCd(coverage);

			if (StringUtils.isNotBlank(optionCd) && optionCd.equalsIgnoreCase("REJ")) {
				return "REJ";
			}
			return "";
		}
		return formatLimitForSplit(coverageLimit);
	}
}

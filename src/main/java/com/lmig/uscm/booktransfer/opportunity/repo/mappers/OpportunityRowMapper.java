package com.lmig.uscm.booktransfer.opportunity.repo.mappers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;

@Component
public class OpportunityRowMapper implements RowMapper<Opportunity> {
    @Override
    public Opportunity mapRow(@NonNull ResultSet resultSet, int i) throws SQLException {

        final int databaseOpportunityId = resultSet.getInt("OPPORTUNITYID");
        final Integer databaseStatus = resultSet.getObject("status", Integer.class);
        final String databaseData = resultSet.getString("data");
        final String databaseLastPolicyGuid = resultSet.getString("lastPolicyGuid");
        final String databaseEffectiveDate = resultSet.getString("effectiveDate");
        final Double databasePriorPremium = resultSet.getObject("priorPremium", Double.class);
        final Double databaseLastQuotedPremium = resultSet.getObject("lastQuotedPremium", Double.class);
        final String databaseBusinessType = resultSet.getString("businessType");
        final int databaseBookTransferId = resultSet.getInt("bookTransferID");
        final int databaseUploadEventId = resultSet.getInt("uploadEventID");
        final String databaseNaicCode = resultSet.getString("nAICCd");
        final String databaseCustomerName = resultSet.getString("customerName");
        final String databasePriorCarrierGuid = resultSet.getString("priorCarrierGuid");
        final String databaseOriginalXml = resultSet.getString("originalXML");
        final String databaseState = resultSet.getString("state");
        final String databaseMasterOpportunityId = resultSet.getString("masterOppID");
        final Timestamp databasePartnerCallTimestamp = resultSet.getTimestamp("timestampCallPartner");
        final Timestamp databaseUploadTimestamp = resultSet.getTimestamp("timestampUpload");
        final Timestamp databaseCleanupTimestamp = resultSet.getTimestamp("timestampCleanup");
        final Timestamp databaseIssuedTimestamp = resultSet.getTimestamp("timestampIssued");
        final String databaseOpportunityPriorCarrier = resultSet.getString("oppPriorCarrier");
        final String databaseAgencyId = resultSet.getString("agencyId");
        final Timestamp databaseFirstPartnerCallTimestamp = resultSet.getTimestamp("timestampFirstCallPartner");
        final LineType databaseLineType = OpportunityUtil.defaultPersonalOrConvertStringToLineType(resultSet.getString("lineType"));
        final String databaseQuoteType = resultSet.getString("quoteType");
        final String nNumber = resultSet.getString("Nnumber");
        final String billingAccountNumber = resultSet.getString("BillingAccountNumber");
        final String policyType = resultSet.getString("policyType");

        final Integer status = defaultStatusIfNull(databaseStatus);
        final LocalDateTime partnerCallLocalDateTime = convertTimestampToLocalDateTime(databasePartnerCallTimestamp);
        final LocalDateTime uploadLocalDateTime = convertTimestampToLocalDateTime(databaseUploadTimestamp);
        final LocalDateTime cleanupLocalDateTime = convertTimestampToLocalDateTime(databaseCleanupTimestamp);
        final LocalDateTime issuedLocalDateTime = convertTimestampToLocalDateTime(databaseIssuedTimestamp);
        final LocalDateTime firstPartnerCallLocalDateTime = convertTimestampToLocalDateTime(databaseFirstPartnerCallTimestamp);

        final Opportunity opportunity = new Opportunity();
        opportunity.setOpportunityId(databaseOpportunityId);
        opportunity.setStatus(status);
        opportunity.setData(databaseData);
        opportunity.setLastPolicyGuid(databaseLastPolicyGuid);
        opportunity.setEffectiveDate(databaseEffectiveDate);
        opportunity.setPriorPremium(databasePriorPremium);
        opportunity.setLastQuotedPremium(databaseLastQuotedPremium);
        opportunity.setBusinessType(databaseBusinessType);
        opportunity.setBookTransferID(databaseBookTransferId);
        opportunity.setUploadEventID(databaseUploadEventId);
        opportunity.setNAICCd(databaseNaicCode);
        opportunity.setCustomerName(databaseCustomerName);
        opportunity.setPriorCarrierGuid(databasePriorCarrierGuid);
        opportunity.setOriginalXML(databaseOriginalXml);
        opportunity.setState(databaseState);
        opportunity.setMasterOppID(databaseMasterOpportunityId);
        opportunity.setTimestampCallPartner(partnerCallLocalDateTime);
        opportunity.setTimestampUpload(uploadLocalDateTime);
        opportunity.setTimestampCleanup(cleanupLocalDateTime);
        opportunity.setTimestampIssued(issuedLocalDateTime);
        opportunity.setOppPriorCarrier(databaseOpportunityPriorCarrier);
        opportunity.setAgencyId(databaseAgencyId);
        opportunity.setTimestampFirstCallPartner(firstPartnerCallLocalDateTime);
        opportunity.setLineType(databaseLineType);
        opportunity.setQuoteType(databaseQuoteType);
        opportunity.setNNumber(nNumber);
        opportunity.setBillingAccountNumber(billingAccountNumber);
        opportunity.setPolicyType(policyType);

        return opportunity;
    }

    protected Integer defaultStatusIfNull(final Integer status) {
        return status == null
                ? OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()
                : status;
    }

    protected LocalDateTime convertTimestampToLocalDateTime(final Timestamp timestamp) {
        if (timestamp == null) {
            return null;
        }
        return timestamp.toLocalDateTime();
    }
}

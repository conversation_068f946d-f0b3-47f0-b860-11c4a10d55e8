package com.lmig.uscm.booktransfer.opportunity.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.w3c.dom.Document;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import javax.xml.xpath.XPathExpressionException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * The Opportunities table contains policy information for each prior carrier policy we receive. Opportunities are
 * created via XML files either by our Automated Upload path or by manually uploading the XML files in AQE; as well as
 * via spreadsheets that are uploaded into the Single Page Quote Entry tool which in turn uploads policy data into AQE.
 */
@Entity
@Table(name = "Opportunities")
@Data
@NoArgsConstructor
public class Opportunity {
	/**
	 * "Table's unique key identifier.
	 * This unique identifier is leveraged by Operations to exclusively identify individual quoting opportunities."
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int opportunityId;

	/**
	 * "This is the version of the other carrier policy ACORD XML after it has been through the Upload Service and the
	 * transformational rules have been applied to it. The original XML we received, which remains unedited, is saved in
	 * the OriginalXML column.
	 * This is the main version used for quoting. It is often referred to as prior policy information or prior carrier
	 * information though from a customer's perspective this is their current insurance policy."
	 */
	@Column(nullable = false)
	private String data;

	/**
	 * "This is the original version of the other carrier policy ACORD XML, it does not have any transformative changes
	 * to the policy.
	 * We retain a copy of the original XML so that we have a record to reference if there are data extraction questions
	 * or policy coverage questions."
	 */
	@Column(nullable = false)
	private String originalXML;

	/**
	 * "This is the database unique key identifier from the Opportunity Status table to identify which status the
	 * opportunity relates to at that current time.
	 * This quote status changes over time as the opportunity moves through the quoting process. There are steps in the
	 * process where the system will set the status; as well as the users are able to set the status via AQE but only
	 * for particular statuses."
	 */
	@Column
	private int status;

	/**
	 * "This is the effective date the Opportunity will be quoted with, this comes from the expiration date of the prior
	 * policy XML.
	 * Since prior policy information only has the current term dates for the customer when we quote the policy we
	 * actually need to quote the next term therefore, we save the expiration date as the effective date in the
	 * database. The effective date is the key element for executing and running a book transfer; everything revolves
	 * around the customers' effective date."
	 */
	@Column
	private String effectiveDate;

	/**
	 * This is the premium for the prior policy extracted from the XML.
	 */
	@Column
	private Double priorPremium;

	/**
	 * This is the premium for the Book Transfer quote, which is populate from the PartnerResponseXML received back
	 * after quoting, and it is updated by the QnI feed when a user is clicking through the quote in QnI.
	 */
	@Column
	private Double lastQuotedPremium;

	/**
	 * "This is the ACORD line of business code extracted from the XML (AUTOP, BOAT, DFIRE, HOME, UMBRP).
	 * Because ACORD does not identify condo, renters or motorcycle policies at the LOBCd level, operations are slightly
	 * blinded to how many of those policies are received within a book."
	 */
	@Column
	private String businessType;

	/**
	 * "This is the Safeco QnI policy unique ID for the Book Transfer quote, which is populated from the
	 * PartnerResponseXML received back after quoting.
	 * This is also used by the QnI feed to identify what item to update in AQE's database."
	 */
	@Column
	private String lastPolicyGuid;

	/**
	 * "This is the database unique key identifier from the Book Transfer table to identify which transfer the
	 * opportunity relates to.
	 * This is set upon upload and creation of the opportunity and if the opportunity is moved to another book this will
	 * be updated."
	 */
	@Column(nullable = false)
	private int bookTransferID;

	/**
	 * This is the database unique key identifier from the Upload Event table to identify which upload this opportunity
	 * relates to.
	 */
	@Column
	private int uploadEventID;

	/**
	 * "This is the National Association of Insurance Commissioners number representing the Carrier of the prior policy
	 * and is extracted from the prior policy XML.
	 * The NAIC code is created by the National Association of Insurance Commissioners and is to help keep track of
	 * insurance companies. The initial reason why we started saving this number was with the thought that we would be
	 * able to leverage it to help identify the carrier on opportunity however, we found that we don't always get this
	 * number, so it's not totally reliable."
	 */
	@Column
	@JsonProperty("nAICCd")
	private String nAICCd;

	/**
	 * This is the customer's name for the opportunity, which is extracted from the prior policy XML.
	 */
	@Column
	private String customerName;

	/**
	 * This is the other carrier's policy number, which is extracted from the prior policy XML.
	 */
	@Column
	private String priorCarrierGuid;

	/**
	 * "This is the state where the prior policy is written in, which is extracted from the prior policy XML.
	 * This is helpful to know from a quoting perspective as Safeco states have different policy coverages and
	 * requirements which will affect what rules the Technical Service Analysts will need to create. It's also helpful
	 * for Operations to track opportunities when there are moratoriums or large Safeco product roll outs that may
	 * affect quoting timelines."
	 */
	@Column
	private String state;

	/**
	 * "This is to identify which auto opportunities are related and need to be quoted together, it is the first auto
	 * opportunity's ID that was created upon upload for master and 5th car situations.
	 * Currently, Safeco Book Transfer can only quote 4 vehicles per auto policy so when a prior policy is received with
	 * more than 4 vehicles the upload process will create multiple auto opportunities in the database. Splitting the
	 * first 4 vehicles off and creating an opportunity which becomes the master opportunity. Vehicles will continue to
	 * be split in groups of four until there will no longer be an opportunity with more than 4 vehicles, all
	 * opportunities will then have the first master opportunity's ID saved to link them together."
	 */
	@Column
	private String masterOppID;

	/**
	 * This captures the last date/time when an opportunity was quoted.
	 */
	@Column(columnDefinition = "TIMESTAMP")
	private LocalDateTime timestampCallPartner;

	/**
	 * "This captures the first date/time when an opportunity was quoted.
	 * <p>
	 * This column was created a while after the TimestampCallPartner column was created which always gets updated each
	 * time an opportunity is quoted. Because TimestampCallPartner can always change, Operations wanted to capture the
	 * original quote time for tracking purposes."
	 */
	@Column(columnDefinition = "TIMESTAMP")
	private LocalDateTime timestampFirstCallPartner;

	/**
	 * This captures the date/time when an opportunity was uploaded and created in AQE.
	 */
	@Column(columnDefinition = "TIMESTAMP")
	private LocalDateTime timestampUpload;

	/**
	 * This captures the date/time when an opportunity's quote was opened from AQE for Quote Clean up.
	 */
	@Column(columnDefinition = "TIMESTAMP")
	private LocalDateTime timestampCleanup;

	/**
	 * This captures the date/time when an opportunity was issued, which is updated by the QnI feed when a user issues
	 * the quote.
	 */
	@Column(columnDefinition = "TIMESTAMP")
	private LocalDateTime timestampIssued;

	/**
	 * "This is the prior carrier name which is set for manual uploads only, the user enters the carrier name on the
	 * Book Upload page of AQE.
	 * <p>
	 * You may be wondering if there is already a carrier on the Book Transfer table that is populated by Salesforce at
	 * the book level then why is there a carrier field on the Opportunity Table? This was asked for because the
	 * Technical Service Analysts were receiving bulk XML policy data for multiple carriers for one transfer and they
	 * needed a way to target rules to specific opportunities for quoting accuracy."
	 */
	@Column
	private String oppPriorCarrier;

	/**
	 * "This is the unique customer ID from the agent's management system, which is extracted from the prior policy XML
	 * if available.
	 * <p>
	 * This is leveraged for the Change Effective Date functionality to help identify multiple policies for a customer
	 * so that the prior policies' effective dates can be updated together."
	 */
	@Column
	private String agencyId;

	/**
	 * This is the business line type of either 'Personal' or 'Business' which is set based on the policy XML type
	 * <p>
	 * This determines which quoting system we use, Partner or eCliq respectively.
	 */
	@Column
	@Enumerated(EnumType.STRING) // required for SQL
	private LineType lineType;


	/**
	 * Either 'Manual' or null
	 * Manual Opportunities are Personal Lines (PL) opps that originate from the Salesforce
	 * a null entry in quoteType represents an Automated Opportunity type. All other flows
	 * will create an Automated Opportunity type.
	 */
	@Column
	private String quoteType;

	/**
	 * The enterprise id nNumber for the creator of the Opportunity
	 * This should be set ONLY when the Opp is created and never updated.
	 * We never want it to be Null.
	 * <p>
	 * We have a few options depending on how the Opp was created:
	 * - B2B automated flow: n00000000
	 * - AQE UI Manual Uploads: logged in users nNumber
	 */
	@Column(updatable = false)
	@JsonProperty("nNumber")
	private String nNumber;

	@Column
	private String billingAccountNumber;

	/**
	 * "This is the ACORD line of business code extracted from the XML (AUTOP, BOAT, DFIRE, HOME, UMBRP).
	 * Because ACORD does not identify condo, renters or motorcycle policies at the LOBCd level, operations are slightly
	 * blinded to how many of those policies are received within a book."
	 */
	@Column
	private String policyType;

	@Transient
	private String customerAccountId; // for OpportunityEvent

	public Opportunity(int opportunityId) {
		this.opportunityId = opportunityId;
	}

	public Opportunity(String oppPriorCarrier) {
		this.oppPriorCarrier = oppPriorCarrier;
	}

	public Opportunity(int opportunityId, int status) {
		this.status = status;
		this.opportunityId = opportunityId;
	}

	public Opportunity(String data, int opportunityId, int status) {
		this(opportunityId, status);
		this.data = data;
	}

	public Opportunity(String data, String originalXML) {
		super();
		setData(data);
		this.originalXML = originalXML;
	}

	public Opportunity(int opportunityId, String data) throws Exception {
		setData(data);
		this.opportunityId = opportunityId;
	}

	public Opportunity(int bookTransferID, int uploadEventID, int opportunityId) {
		this.bookTransferID = bookTransferID;
		this.uploadEventID = uploadEventID;
		this.opportunityId = opportunityId;
	}

	public Opportunity(int bookTransferID, int uploadEventID, int opportunityId, String data) {
		this(bookTransferID, uploadEventID, opportunityId);
		this.data = data;
	}

	public Opportunity(int opportunityId,
					   int status,
					   String effectiveDate,
					   String state,
					   String businessType,
					   String nAICCd,
					   String customerName,
					   Double priorPremium,
					   Double lastQuotedPremium,
					   LineType lineType,
					   String billingAccountNumber,
					   String policyType) {
		this.opportunityId = opportunityId;
		this.status = status;
		this.effectiveDate = effectiveDate;
		this.state = state;
		this.businessType = businessType;
		this.nAICCd = nAICCd;
		this.customerName = customerName;
		this.priorPremium = priorPremium;
		this.lastQuotedPremium = lastQuotedPremium;
		this.lineType = lineType;
		this.billingAccountNumber = billingAccountNumber;
		this.policyType = policyType;
	}

	public Opportunity(String lastPolicyGuid, int bookTransferID, String data) {
		this.lastPolicyGuid = lastPolicyGuid;
		this.bookTransferID = bookTransferID;
		setData(data);
	}

	public Opportunity(String lastPolicyGuid, int bookTransferID, String data, int oppId) {
		this.lastPolicyGuid = lastPolicyGuid;
		this.bookTransferID = bookTransferID;
		setData(data);
		this.opportunityId = oppId;
	}

	@JsonIgnore
	public boolean hasOppId() {
		return (this.getOpportunityId() != 0);
	}

	public LineOfBusiness determineLob() {
		return LineOfBusiness.determineLobFromValue(this.getBusinessType());
	}

	/**
	 * Calculates the policy term by xml doc if it is auto else always set it to 12.
	 *
	 * @return the policy term in months
	 */
	public static int calcNewPolicyTerm(String businessType, Document xmlDoc) {
		try {
			if (businessType.toUpperCase().contains("AUTO")) {
				return extractPolicyTerm(xmlDoc);
			} else {
				return 12;
			}
		} catch (Exception e) {
			return 12;
		}
	}

	/**
	 * Calculates the policy term by opp if it is auto else always set it to 12.
	 *
	 * @param opp - the opp we are finding the policy term on using its lob and data
	 * @return the policy term in months
	 */
	public static int calcNewPolicyTerm(Opportunity opp) {
		try {
			return calcNewPolicyTerm(opp.getBusinessType(), XmlHelper.getDocument(opp.getData()));
		} catch (Exception e) {
			return 12;
		}
	}

	public static int extractPolicyTerm(Document xmlDoc) throws XPathExpressionException {

		int policyTerm = 12;

		String effDate = AcordHelper.getEffectiveDt(xmlDoc);
		String expDate = AcordHelper.getExpirationDt(xmlDoc);
		if (effDate.trim().length() > 0 && expDate.trim().length() > 0) {
			long days = ChronoUnit.DAYS.between(LocalDate.parse(effDate), LocalDate.parse(expDate));
			if (days < 198) { // 6.5 months ~ 198 days
				policyTerm = 6;
			}
		}

		return policyTerm;
	}
}

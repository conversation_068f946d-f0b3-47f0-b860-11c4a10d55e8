/*
 * Copyright (c) 2019, Liberty Mutual
 * Proprietary and Confidential
 * All Rights Reserved
 */

package com.lmig.uscm.booktransfer.opportunity.domain.customfilters;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public class CustomFilterResponse {
	/**
	 * The object that will hold all the information from the custom filters It
	 * needs to be names data for paganation to work
	 */
	private final List<CustomFilter> data;
	/**
	 * This I will make set the same as records Total but will need to change when
	 * we actually filter items
	 */
	private final int recordsFiltered;

}

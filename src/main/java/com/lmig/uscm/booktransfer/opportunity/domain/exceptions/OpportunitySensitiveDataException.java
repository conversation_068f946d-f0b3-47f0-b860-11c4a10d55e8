package com.lmig.uscm.booktransfer.opportunity.domain.exceptions;

public class OpportunitySensitiveDataException extends Exception {
	public OpportunitySensitiveDataException() {

	}

	public OpportunitySensitiveDataException(final String msg) {
		super(msg);

	}

	public OpportunitySensitiveDataException(final String msg, final Exception e) {
		super(msg, e);

	}

	public OpportunitySensitiveDataException(final Exception e) {
		super(e);
	}
}

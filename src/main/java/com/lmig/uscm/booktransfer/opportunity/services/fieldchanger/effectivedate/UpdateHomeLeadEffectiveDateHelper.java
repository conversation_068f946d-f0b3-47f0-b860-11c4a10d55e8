package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate;

import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.ExportHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * This class handles the update home lead effective data option
 */
public class UpdateHomeLeadEffectiveDateHelper extends UpdateMatchingCustomerEffectiveDateHelper {

	public UpdateHomeLeadEffectiveDateHelper(final OpportunityHelper opportunityHelper,
											 final BookTransferService bookTransferService, final ExportHelper exportHelper, final OpportunityRepoHelper opportunityRepoHelper) {
		super(opportunityHelper, bookTransferService, exportHelper, opportunityRepoHelper);
	}

	/**
	 * If there is a home polciy return the earliest home policy effective date.
	 * else return the earliest effective date.
	 *
	 * @param packagedOpportunities the opps packaged together
	 * @param bookTransferStartDate the start date of this transfer.
	 * @return
	 */
	@Override
	protected LocalDate getEarliestEffectiveDates(List<OppChangeFieldResult> packagedOpportunities,
												  LocalDate bookTransferStartDate) {

		LocalDate earliestHomeEffectiveDate = null;
		for (OppChangeFieldResult oppChangeFieldResult : packagedOpportunities) {
			Opportunity opp = oppChangeFieldResult.getOpportunity();
			if (StringUtils.isBlank(opp.getEffectiveDate())) {
				continue;
			}
			LocalDate effectiveDate = calculateNewEffectiveDate(opp, bookTransferStartDate);
			if (StringUtils.equalsIgnoreCase(opp.getBusinessType(), "home")) {
				earliestHomeEffectiveDate = getEarliestDate(earliestHomeEffectiveDate, effectiveDate);
			}
		}
		if (earliestHomeEffectiveDate != null) {
			return earliestHomeEffectiveDate;
		}
		return getEarliestLeadEffectiveDates(packagedOpportunities, bookTransferStartDate);

	}
}

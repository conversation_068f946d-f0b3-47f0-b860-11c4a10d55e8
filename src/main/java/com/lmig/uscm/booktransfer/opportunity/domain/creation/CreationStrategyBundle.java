package com.lmig.uscm.booktransfer.opportunity.domain.creation;

import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;

import java.util.ArrayList;
import java.util.List;

/**
 * Links OpportunityCreationRequests together so that a masterOppId can be set for the created Opportunities
 */
public class CreationStrategyBundle {
	private List<OpportunityCreationRequest> requests;

	public CreationStrategyBundle(OpportunityCreationRequest masterOppRequest) {
		requests = new ArrayList<>();

		if (masterOppRequest == null) {
			return;
		}
		this.addCreationRequest(masterOppRequest);
	}

	public List<OpportunityCreationRequest> getRequests() {
		return requests;
	}

	public void setRequests(List<OpportunityCreationRequest> requests) {
		this.requests = requests;
	}

	public void addCreationRequest(OpportunityCreationRequest creationRequest) {
		this.requests.add(creationRequest);
	}

	public OpportunityCreationRequest getCreationRequest(Integer index) {
		return this.requests.get(index);
	}

	public OpportunityCreationRequest getMasterCreationRequest() {
		if(requests.size() == 1) {
			return requests.get(0);
		}

		return requests.stream().filter(OpportunityCreationRequest::isMasterOpp).findFirst().orElse(null);
	}
}

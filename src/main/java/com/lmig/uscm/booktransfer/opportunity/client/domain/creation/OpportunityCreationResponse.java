package com.lmig.uscm.booktransfer.opportunity.client.domain.creation;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class OpportunityCreationResponse {
	private Integer successfulCreationCount;
	private Integer failedCreationCount;
	private List<Integer> associatedOppIds; // upload-service assumes first as masterOppId
	private Integer associatedBookTransferId;// upload-service needs to check for changes
	private final List<Exception> exceptions;

	public OpportunityCreationResponse() {
		this.successfulCreationCount = 0;
		this.failedCreationCount = 0;
		this.associatedOppIds = new ArrayList<>();
		this.exceptions = new ArrayList<>();
	}

	public void incrementSuccessfulCreationCount() {
		this.setSuccessfulCreationCount(this.getSuccessfulCreationCount() + 1);
	}

	public void incrementFailedCreationCount() {
		this.setFailedCreationCount(this.getFailedCreationCount() + 1);
	}

	public void addAssociatedOppId(Integer oppId) {
		this.associatedOppIds.add(oppId);
	}

	public void addExceptions(Exception exception) {
		this.exceptions.add(exception);
	}
}

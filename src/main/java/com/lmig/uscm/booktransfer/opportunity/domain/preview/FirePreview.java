package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
public class FirePreview extends Preview {

	@Override
	public LineOfBusiness getLob() {
		return LineOfBusiness.DFIRE;
	}

	@Override
	protected void setLOBRequiredData(Document originalXml, PreviewDataResponse response)
			throws ParserConfigurationException, SAXException, XPathExpressionException, IOException {
		if (StringUtils.isBlank(AcordHelper.getCoverageDwellLimit(originalXml))) {
			response.addMissingDataPoint("CovA");
		}
		if (StringUtils
				.isBlank(AcordHelper.getFireInsuredOrPrincipalAddr1(originalXml))) {
			response.addMissingDataPoint("MailingAddress");
		}
	}

}

package com.lmig.uscm.booktransfer.opportunity.client.domain.creation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.w3c.dom.Document;

import javax.xml.transform.TransformerException;
import java.util.Optional;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
public class OpportunityCreationRequest {
	private Document uploadedACORD;
	private Integer uploadEventID;
	private Integer bookTransferID;
	@JsonProperty("sFDCID")
	private Integer sFDCID;
	private Integer masterOppID;
	private String priorCarrier;
	private String subCode;
	private Integer existingOpportunityID;
	private Document data;
	// SPQE required information. It is not copied over in clone() and should only be non-null on master request
	private MasterOppSPQEMeta masterOppSPQEMeta;
	// This field is used by the fifth boat strategy or any strategy that needs to know the strategy it's on was split from a home policy
	private boolean isDerivedFromHome;
	private OriginSource originSource;
	private SourceType sourceType;
	private String originatingId;
	private LineType lineType;
	@JsonProperty("nbdRelationship")
	private String nbdRelationship = "";
	/**
	 * The enterprise id of the documenting employee of the Opportunity to be created
	 * Will be saved on the Nnumber column of the Opp when created
	 */
	@JsonProperty("nNumber")
	private String nNumber;

	private boolean isMasterOpp;
	private boolean shouldReplaceOriginalXml;
	private String fileName;

	public OpportunityCreationRequest(final Document uploadedACORD, final Integer uploadEventID,
									  final Integer bookTransferID, final Integer sFDCID, final Integer masterOppID,
									  final String priorCarrier, final String subCode,
									  final Integer existingOpportunityID, final Document data, final LineType lineType, String nNumber, final String nbdRelationship) {
		this.uploadedACORD = uploadedACORD;
		this.uploadEventID = uploadEventID;
		this.bookTransferID = bookTransferID;
		this.sFDCID = sFDCID;
		this.masterOppID = masterOppID;
		this.priorCarrier = priorCarrier;
		this.subCode = subCode;
		this.existingOpportunityID = existingOpportunityID;
		this.data = data;
		this.isMasterOpp = false;
		this.shouldReplaceOriginalXml = false;
		this.lineType = lineType;
		this.setNNumber(nNumber);
		this.nbdRelationship = nbdRelationship;
	}

	public OriginSource getOriginSource() {
		return Optional.ofNullable(this.originSource).orElse(OriginSource.UNKNOWN);
	}

	public void setAsMasterOpp() {
		this.isMasterOpp = true;
	}

	@Override
	public OpportunityCreationRequest clone() {
		try {
			return (OpportunityCreationRequest) super.clone();
		} catch (CloneNotSupportedException e) {

			Document copiedAcord = this.getUploadedACORD();

			try {
				copiedAcord = XmlHelper.cloneDocument(copiedAcord);
			} catch (TransformerException ex) {
			}

			OpportunityCreationRequest clone = new OpportunityCreationRequest(
					copiedAcord,
					this.getUploadEventID(),
					this.getBookTransferID(),
					this.getSFDCID(),
					this.getMasterOppID(),
					this.getPriorCarrier(),
					this.getSubCode(),
					null,
					null,
					this.getLineType(),
					this.getNNumber(),
					this.getNbdRelationship()
			);

			clone.setOriginSource(this.getOriginSource());
			clone.setOriginatingId(this.getOriginatingId());
			clone.setSourceType(this.getSourceType());

			return clone;
		}
	}

	public boolean hasExistingOpportunityId() {
		return getExistingOpportunityID() != null && getExistingOpportunityID() > 0;
	}

	public boolean hasMasterOppSPQEMetaData() {
		return getMasterOppSPQEMeta() != null && CollectionUtils.isNotEmpty(getMasterOppSPQEMeta().getPreviousOpportunities());
	}

	public SourceType getSourceType() {
		return Optional.ofNullable(this.sourceType).orElse(SourceType.UNKNOWN);
	}
}

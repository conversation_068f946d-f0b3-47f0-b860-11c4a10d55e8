package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.csvbuilder;

import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.utilityservice.CSVBuilder;

import java.util.ArrayList;
import java.util.List;

public class UpdateOtherFieldsResponseBuilder extends UpdateOpportunityFieldResponseBuilder {
	@Override
	public String buildOutStatusChangeReport() {
		CSVBuilder csvSheet = new CSVBuilder();

		buildOpportunitySection(csvSheet);
		csvSheet.addNewLine();
		buildQuoteReportItem(csvSheet);
		csvSheet.addNewLine();
		buildSummarySection(csvSheet);
		return csvSheet.toString();
	}

	@Override
	protected CSVBuilder getOpportunityHeaders(CSVBuilder csvSheet) {
		csvSheet.addRowToCSV(false, "OpportunityID", "FieldName", "Original Value", "Updated Value", "Value Changed?",
				"Status");
		return csvSheet;
	}

	@Override
	protected List<String> addQRIDataToCSV(OppChangeFieldResult oppChangeFieldResult) {
		return buildOutRowData(oppChangeFieldResult, oppChangeFieldResult.getOriginalQuoteReportItemValue());
	}

	private List<String> buildOutRowData(OppChangeFieldResult oppChangeFieldResult, String originalValue) {
		List<String> rowData = new ArrayList<>();
		rowData.add(oppChangeFieldResult.getOppId());
		rowData.add(oppChangeFieldResult.getFieldName());
		rowData.add(originalValue);
		rowData.add(oppChangeFieldResult.getFieldUpdatedValue());
		rowData.add(getChangeValue(oppChangeFieldResult));
		if (oppChangeFieldResult.didFail()) {
			rowData.add(oppChangeFieldResult.getStatusMessage());
		} else {
			rowData.add(SUCCESS);
		}
		return rowData;
	}

	@Override
	protected List<String> addOppDataToRow(OppChangeFieldResult oppChangeFieldResult) {
		return buildOutRowData(oppChangeFieldResult, oppChangeFieldResult.getOriginalValue());
	}

	protected void buildQuoteReportItem(CSVBuilder csvSheet) {
		csvSheet.addRowToCSV(false, "Quote_SalesforceID", "FieldName", "Original Value", "Updated Value",
				"Value Changed?", "Status");
		csvSheet.addRowToCSV(failedQuoteReports);
		csvSheet.addRowToCSV(successQuoteReports);
	}
}

package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.AuditLogWebClient;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.domain.AuditLogException;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.EndTransactionRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.StartTransactionRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.TransactionEventRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.response.TransactionResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Copyright (c) 2022, Liberty Mutual Group
 * <p> Helper class to call auditlog rest api
 * Created on 04/08/2022
 */
@Slf4j
public class AuditLogHelper {
	/**
	 * Saves the transaction to mongo
	 */
	private final AuditLogWebClient auditLogWebClient;

	public AuditLogHelper(final AuditLogWebClient auditLogWebClient) {
		this.auditLogWebClient = auditLogWebClient;
	}

	public TransactionResponse startTransaction(StartTransactionRequest request) {
		try {
			return auditLogWebClient.startTransaction(request);
		} catch (AuditLogException e) {
			log.error("Failed to create audit log. Notifying team. Transaction Request: {};", request, e);
			throw new RuntimeException(e);
		}
	}

	public TransactionResponse addTransactionEvent(String transactionId, TransactionEventRequest request) {
		try {
			return auditLogWebClient.addTransactionEvent(transactionId, request);
		} catch (AuditLogException e) {
			log.error("Failed to update audit log. Notifying team. Transaction Request: {};", request, e);
			throw new RuntimeException(e);
		}
	}

	public TransactionResponse endTransactionEvent(String transactionId, EndTransactionRequest request) {
		try {
			return auditLogWebClient.endTransaction(transactionId, request);
		} catch (AuditLogException e) {
			log.error("Failed to end audit log. Notifying team. Transaction Request: {};", request, e);
			throw new RuntimeException(e);
		}
	}

	public List<TransactionResponse> batchStartTransactions(List<StartTransactionRequest> requests) {
		try {
			return auditLogWebClient.startTransactions(requests);
		} catch (AuditLogException e) {
			log.error("Failed to create audit logs for {} requests. Notifying team.", requests.size(), e);
			throw new RuntimeException(e);
		}
	}

	public List<TransactionResponse> batchAddTransactionEvents(Map<String, List<TransactionEventRequest>> requests) {
		try {
			return auditLogWebClient.batchAddTransactionEvents(requests);
		} catch (AuditLogException e) {
			log.error("Failed to update audit log for {} transactions. Notifying team.", requests.size(), e);
			throw new RuntimeException(e);
		}
	}

	public List<TransactionResponse> batchEndTransactions(Map<String, EndTransactionRequest> requests) {
		try {
			return auditLogWebClient.endTransactions(requests);
		} catch (AuditLogException e) {
			log.error("Failed to end audit log for {} transactions. Notifying team.", requests.size(), e);
			throw new RuntimeException(e);
		}
	}
}

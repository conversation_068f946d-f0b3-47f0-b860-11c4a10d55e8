package com.lmig.uscm.booktransfer.opportunity.domain;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicReference;

public enum SafecoLobs {

    AUTO("1", "Auto", new String[]{"PRF", "STD", "NSTD"}),
    HOME("2", "Home", new String[]{"HOM","QPH", "QCH", "QSH", "V49"}),
    CONDO("2", "Condo", new String[]{"CNDR", "QCC", "QPC", "COND"}),
    RENTERS("2", "Renters", new String[]{"RENT"}),
    BOAT("7", "Boat", new String[]{"BOA"}),
    DFIRE("8", "Dwelling Fire", new String[]{"3"}),
    UMBRP("5", "Umbrella", new String[]{"UMB"}),
    MTR("10", "Motorcycle", new String[]{"MTR"});

    private final String lobCode;
    private final String lobName;

    public String[] getProduct() {
        return product;
    }

    private final String[] product;

    public String getLobCode() {
        return lobCode;
    }

    SafecoLobs(String lobCode, String lobName, String[] product) {
        this.lobCode = lobCode;
        this.lobName = lobName;
        this.product = product;
    }

    public String getLobName() {
        return lobName;
    }

    public static String determineSafecoLob(String product, String lobCode) {
        AtomicReference<String> result = new AtomicReference<>("Undetermined");

        Arrays.stream(values()).forEach(lob -> {
            if (lobCode.equals("2")) {
                if(Arrays.asList(lob.getProduct()).contains(product)) {
                    result.set(lob.lobName);
                }
            }else if (lob.getLobCode().equals(lobCode)) {
                result.set(lob.lobName);
            }

        });

        // Default home for lobcode 2 if not able to determine safeco lob
        if(result.get().equals("Undetermined") && lobCode.equals("2")) {
            return HOME.lobName;
        }

        return result.get();
    }
}

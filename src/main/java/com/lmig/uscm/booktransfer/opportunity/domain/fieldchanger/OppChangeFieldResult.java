package com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger;

import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;

public class OppChangeFieldResult {
	private Opportunity opp;
	private final String fieldName;
	private String fieldUpdatedValue;
	private String originalValue;

	private String originalQuoteReportItemValue;
	private String statusMessage;
	private String oppId;
	private boolean didFail;

	/**
	 * This constructor assumes thats quote report item and opp have the same value
	 */
	public OppChangeFieldResult(Opportunity opp, String fieldName, String originalValue) {
		this(opp, fieldName, originalValue, originalValue);
	}

	public OppChangeFieldResult(Opportunity opp, String fieldName, String originalValue,
			String originalQuoteReportItemValue) {
		this.oppId = String.valueOf(opp.getOpportunityId());
		this.fieldName = fieldName;
		this.didFail = false;
		this.originalValue = originalValue;
		this.opp = opp;
		this.originalQuoteReportItemValue = originalQuoteReportItemValue;
	}

	/**
	 * This constructor assumes thats quote report item and opp have the same value
	 */
	public OppChangeFieldResult(Opportunity opp, OppChangeField oppChangeField, String originalValue) {
		this(opp, oppChangeField, originalValue, originalValue);
	}

	public OppChangeFieldResult(Opportunity opp, OppChangeField oppChangeField, String originalValue,
			String originalQuoteReportItemValue) {
		this(opp, oppChangeField);
		this.originalValue = originalValue;
		this.originalQuoteReportItemValue = originalQuoteReportItemValue;
	}

	public OppChangeFieldResult(Opportunity opp, OppChangeField oppChangeField) {
		this(oppChangeField);
		this.opp = opp;
	}

	public OppChangeFieldResult(OppChangeField oppChangeField) {
		this.oppId = String.valueOf(oppChangeField.getOppId());
		this.fieldName = oppChangeField.getFieldName();
		this.fieldUpdatedValue = oppChangeField.getFieldValue();
		this.didFail = false;
	}

	public String getLob() {
		if (opp == null) {
			return null;
		}
		return opp.getBusinessType();
	}

	public String getPriorCarrierGuid() {
		if (opp == null) {
			return null;
		}
		return opp.getPriorCarrierGuid();
	}

	public String getOppId() {
		return oppId;
	}

	public Opportunity getOpportunity() {
		return opp;
	}

	public void setOpportunity(Opportunity opp) {
		this.opp = opp;
		if (opp != null) {
			this.oppId = String.valueOf(opp.getOpportunityId());
		} else {
			this.oppId = null;
		}
	}

	public String getFieldName() {
		return fieldName;
	}

	public String getFieldUpdatedValue() {
		return fieldUpdatedValue;
	}

	public void setFieldUpdatedValue(String fieldUpdatedValue) {
		this.fieldUpdatedValue = fieldUpdatedValue;
	}

	public String getOriginalValue() {
		return originalValue;
	}

	public String getOriginalQuoteReportItemValue() {
		return originalQuoteReportItemValue;
	}

	public OppChangeFieldResult isError(String errorMessage) {
		// if already failed do not update message
		if (!didFail()) {
			this.statusMessage = errorMessage;
			this.didFail = true;

		}
		return this;
	}

	public String getStatusMessage() {
		return statusMessage;
	}

	public boolean didFail() {
		return didFail;
	}

}

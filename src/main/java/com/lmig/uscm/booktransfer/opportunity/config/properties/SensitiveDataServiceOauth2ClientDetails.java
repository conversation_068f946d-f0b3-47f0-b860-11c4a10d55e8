package com.lmig.uscm.booktransfer.opportunity.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;

@ConfigurationProperties(prefix = "oppservice.oauth2.sensitivedata.client")
public class SensitiveDataServiceOauth2ClientDetails extends ClientCredentialsResourceDetails {

}

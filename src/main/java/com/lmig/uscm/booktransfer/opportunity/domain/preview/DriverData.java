package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Node;

import javax.xml.xpath.XPathExpressionException;

// Required Data for Driver
public class DriverData {

	public void setRequiredData(Node driverNode, PreviewDataResponse response) throws XPathExpressionException {
		if (StringUtils
				.isBlank(AcordHelper.getDriverLicenseNumber(driverNode))) {
			response.addMissingDataPoint("DrvLicenseNumber");
		}
		if (StringUtils.isBlank(AcordHelper.getDriverBirthDt(driverNode))) {
			response.addMissingDataPoint("BirthDate");
		}
	}

}

package com.lmig.uscm.booktransfer.opportunity.client.domain.creation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Used on master OpportunityCreationRequest for SPQE in order to link Opportunities to their respective
 * previously saved versions after going through creation strategies again (could resplit, etc.)
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MasterOppSPQEMeta {
	private List<Integer> previousOppIds;
	private List<SPQEOpportunity> previousOpportunities;

	public MasterOppSPQEMeta(List<Integer> previousOppIds) {
		this.previousOppIds = previousOppIds;
	}
}

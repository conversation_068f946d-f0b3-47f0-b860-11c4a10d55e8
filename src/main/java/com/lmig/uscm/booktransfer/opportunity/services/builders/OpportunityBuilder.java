/*
 * Copyright (C) 2018, Liberty Mutual Group
 *
 * Created on 11/23/18 by n0210477
 */
package com.lmig.uscm.booktransfer.opportunity.services.builders;

import com.lmig.uscm.booktransfer.opportunity.domain.Constants;
import com.lmig.uscm.booktransfer.opportunity.client.domain.EFTPaymentAccountsResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.PolicyType;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountServiceResponseData;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerPolicy;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunityFailedRetryException;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.AddressCleanseService;
import com.lmig.uscm.booktransfer.opportunity.services.BookTransferAttributeService;
import com.lmig.uscm.booktransfer.opportunity.services.CustomerAccountHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.opportunity.services.EFTPaymentAccountsService;
import com.lmig.uscm.booktransfer.opportunity.services.EFTPaymentAccountsHelper;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.stream.XMLStreamException;
import javax.xml.transform.TransformerException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.util.Objects;

/**
 * Builds the initial Opportunity from a flat file or Bulk XML from upload Also reuploads the opportunity from the
 * opportunity's original xml.
 */
@Slf4j
public abstract class OpportunityBuilder {
	protected OpportunityRepoHelper opportunityRepoHelper;
	protected OpportunityConstructor opportunityConstructor;
	protected QuoteReportItemHelper quoteReportItemHelper;
	protected AddressCleanseService addressCleanseService;
	protected EFTPaymentAccountsService eftPaymentAccountsService;
	protected CustomerAccountHelper customerAccountHelper;
	protected boolean isForSPQE;
	protected String importPackageId;

	/**
	 * Constructor to pass all required variables
	 */
	public OpportunityBuilder(
		final OpportunityRepoHelper opportunityRepoHelper,
		final OpportunityConstructor opportunityConstructor,
		final QuoteReportItemHelper quoteReportItemHelper,
		final AddressCleanseService addressCleanseService,
		final EFTPaymentAccountsService eftPaymentAccountsService,
		final CustomerAccountHelper customerAccountHelper,
		final boolean isForSPQE,
		final String importPackageId
	) {
		this.opportunityRepoHelper = opportunityRepoHelper;
		this.opportunityConstructor = opportunityConstructor;
		this.isForSPQE = isForSPQE;
		this.quoteReportItemHelper = quoteReportItemHelper;
		this.addressCleanseService = addressCleanseService;
		this.eftPaymentAccountsService = eftPaymentAccountsService;
		this.customerAccountHelper = customerAccountHelper;
		this.importPackageId = importPackageId;
	}

	protected abstract Opportunity getOpportunity(OpportunityCreationRequest opportunityCreationRequest)
			throws Exception;

	protected abstract Opportunity updateMasterOpp(boolean isMasterOpp, Opportunity newOpportunity);

	/**
	 * Creates and saves a new opportunity and quotereportItem from an opportunity request
	 *
	 * @param opportunityCreationRequest requested info for an opportunity request
	 * @return A new opportunity
	 */
	public Opportunity uploadOpportunity(OpportunityCreationRequest opportunityCreationRequest)
			throws Exception {
		Opportunity newOpportunity = getOpportunity(opportunityCreationRequest);
		newOpportunity = updateInitialOpportunityBeforeRules(opportunityCreationRequest, newOpportunity);
		Document workingDoc = XmlHelper.getDocument(newOpportunity.getOriginalXML());
		newOpportunity.setBillingAccountNumber(AcordHelper.getBillingAccountNumber(workingDoc));
		String policyTypeCd = AcordHelper.getPolicyTypeCd(workingDoc);
		//TODO: Extract to a method to verify if the policy type codes is present in any of the ENUMS
		if (PolicyType.CONDO.getPolicyTypeCds().contains(policyTypeCd) || PolicyType.RENTERS.getPolicyTypeCds().contains(policyTypeCd) ) {
			newOpportunity.setPolicyType(policyTypeCd);
		}
		// Call payment accounts service and add payment accounts data to Opp XML for Single CA
		EFTPaymentAccountsResponse eftPaymentAccountsResponse = null;
		boolean retryMaxAttemptsReached = false;
		try {
			if (EFTPaymentAccountsHelper.isSingleCA(opportunityCreationRequest.getUploadedACORD())) {
				eftPaymentAccountsResponse = addPaymentAccountsInfo(workingDoc, opportunityCreationRequest);
			}
		} catch(OpportunityFailedRetryException e){
			AcordHelper.setMigrationInfoPaymentLookupStatus(workingDoc, Constants.FAILED_TO_LOOKUP_PAYMENT_DATA);
			retryMaxAttemptsReached = true;
		}
		AcordHelper.setNbdRelationship(workingDoc, opportunityCreationRequest.getNbdRelationship());

		newOpportunity = applyRules(opportunityCreationRequest, newOpportunity, workingDoc);

		newOpportunity = saveOpportunity(newOpportunity);
		postOpportunitySave(opportunityCreationRequest, newOpportunity);

		newOpportunity = updateMasterOpp(opportunityCreationRequest.isMasterOpp(), newOpportunity);

		log.info("Personal Lines OpportunityId - {}", newOpportunity.getOpportunityId());
		EFTPaymentAccountsHelper.postProcessPaymentAccountInfo(
				eftPaymentAccountsResponse, opportunityCreationRequest.getExistingOpportunityID(), workingDoc,
				newOpportunity.getOpportunityId(), retryMaxAttemptsReached);

		prepareQuoteReportItem(newOpportunity, opportunityCreationRequest.getSubCode(),
				opportunityCreationRequest.getSFDCID());

		return newOpportunity;
	}

	void postOpportunitySave(OpportunityCreationRequest opportunityCreationRequest, Opportunity newOpportunity)
            throws ParserConfigurationException, IOException, SAXException, XPathExpressionException, TransformerException {
		if (LineType.Business.equals(newOpportunity.getLineType())) {
			if(opportunityCreationRequest.getExistingOpportunityID() != null) {
				Document xmlDoc = XmlHelper.getDocument(newOpportunity.getData());
				AcordHelper.setCustomerAccountId(xmlDoc, AcordHelper.getCustomerAccountId(opportunityCreationRequest.getData()));
				AcordHelper.setCustomerPolicyId(xmlDoc, AcordHelper.getCustomerPolicyId(opportunityCreationRequest.getData()));
			}
			Pair<String, String> customerAccountIdAndPolicyId = createCustomerAccount(opportunityCreationRequest, newOpportunity);

			if (CustomerAccountHelper.CUSTOMER_ACCOUNT_SERVICE_ERROR.equals(customerAccountIdAndPolicyId.getFirst())) {
				log.error("Customer Account ID is null for opportunity {}", newOpportunity.getOpportunityId());
			} else {
				log.info("Created or updated customer account {} for opportunity {} on policy {}",
						customerAccountIdAndPolicyId.getFirst(), newOpportunity.getOpportunityId(), customerAccountIdAndPolicyId.getSecond());
			}

			saveOpportunity(newOpportunity);
		}
	}

	private Pair<String, String> createCustomerAccount(OpportunityCreationRequest opportunityCreationRequest, Opportunity newOpportunity)
			throws ParserConfigurationException, SAXException, IOException, XPathExpressionException, TransformerException {
		Document opportunityDoc = XmlHelper.getDocument(newOpportunity.getData());
		CustomerAccountServiceResponseData responseData = customerAccountHelper.createCustomerAccount(
				newOpportunity, opportunityDoc, opportunityCreationRequest.getSFDCID());

		String customerAccountId = responseData == null || StringUtils.isEmpty(responseData.getId())
				? CustomerAccountHelper.CUSTOMER_ACCOUNT_SERVICE_ERROR
				: responseData.getId();
		String customerPolicyId = CustomerAccountHelper.CUSTOMER_ACCOUNT_SERVICE_ERROR;
		if (responseData != null && responseData.getCustomerPolicies() != null) {
			CustomerPolicy customerPolicy = responseData.getCustomerPolicies().stream()
				.filter(policy -> Objects.equals(policy.getAqe().getOpportunityId(), newOpportunity.getOpportunityId()))
				.findFirst()
				.orElse(null);
			if (customerPolicy != null) {
				customerPolicyId = StringUtils.isNotEmpty(customerPolicy.get_id())
					? customerPolicy.get_id()
					: CustomerAccountHelper.CUSTOMER_ACCOUNT_SERVICE_ERROR;
			}
		}

		opportunityDoc = AcordHelper.setCustomerAccountId(opportunityDoc, customerAccountId);
		opportunityDoc = AcordHelper.setCustomerPolicyId(opportunityDoc, customerPolicyId);

		newOpportunity.setData(XmlHelper.getDocumentString(opportunityDoc));
		newOpportunity.setCustomerAccountId(customerAccountId); // for OpportunityEvent

		return Pair.of(customerAccountId, customerPolicyId);
	}

	protected Opportunity updateInitialOpportunityBeforeRules(final OpportunityCreationRequest opportunityCreationRequest, final Opportunity opportunity) {
		try {
			String updatedOriginalXml = addCoverageAttributes(opportunity.getOriginalXML());
			Document updatedOriginalXmlDoc = XmlHelper.getDocument(updatedOriginalXml);

			updatedOriginalXmlDoc = OpportunityUtil.setAgentNumber(updatedOriginalXmlDoc, opportunityCreationRequest.getSubCode(),
				opportunityCreationRequest.getLineType());

			AcordHelper.setBTMetaCreationOrigin(updatedOriginalXmlDoc, opportunityCreationRequest.getOriginSource().getValue());
			AcordHelper.setBTMetaCreationType(updatedOriginalXmlDoc, opportunityCreationRequest.getSourceType().getValue());


			opportunity.setOriginalXML(XmlHelper.getDocumentString(updatedOriginalXmlDoc));
		} catch (TransformerException | ParserConfigurationException | SAXException | IOException | XPathExpressionException e) {
			log.error("Xml conversion exceptions converting original xml OpportunityId: {}", opportunity.getOpportunityId(), e);
		}

		return opportunity;
	}

	protected Opportunity saveOpportunity(Opportunity newOpportunity) {
		if (newOpportunity.getOpportunityId() != 0 && !isForSPQE) {
			return opportunityRepoHelper.updateOpportunity(newOpportunity);
		}
		return opportunityRepoHelper.save(newOpportunity);
	}

	private String addCoverageAttributes(String xml) {
		try {
			xml = new BookTransferAttributeService().addBookTransferCoverageAttributes(xml);
		} catch (XMLStreamException e) {
			log.error("unexpected error", e);
		}
		return xml;
	}

	protected Opportunity applyRules(
			final OpportunityCreationRequest opportunityCreationRequest,
			final Opportunity newOpportunity,
			Document workingDoc
	) throws Exception {
		workingDoc = opportunityConstructor.runRules(workingDoc, importPackageId, newOpportunity.getOpportunityId());
		workingDoc = addressCleanseService.addCountiesToLocations(workingDoc);
		return updateOpportunityPostRules(opportunityCreationRequest, newOpportunity, workingDoc);
	}

	/**
	 * Takes an opportunity - builds and saves a new quote rport item
	 *
	 * @param opportunity opportunity to create quote report item from
	 * @param subCode     sub code from the related book transfer
	 * @param SFDCID      sfcdid from the related book transfer
	 */
	protected void prepareQuoteReportItem(Opportunity opportunity, String subCode, Integer SFDCID) {
		try {
			quoteReportItemHelper.buildAndSaveQuoteReportItem(opportunity, SFDCID, subCode);
		} catch (Throwable e) {
			log.error("unexpected error", e);
		}
	}

	/**
	 * updates the opportunity with the modified xml
	 *
	 * @param createdOpportunity existing opportunity to update
	 * @return updated opportunity
	 */
	protected Opportunity updateOpportunityPostRules(OpportunityCreationRequest opportunityCreationRequest,
													 Opportunity createdOpportunity,
													 Document workingDoc) throws Exception {
		createdOpportunity = opportunityConstructor.updateOpportunity(opportunityCreationRequest, createdOpportunity, workingDoc);
		// update the book of opportunity if the campaign id has a different book number
		if (this.isForSPQE) {
			createdOpportunity = opportunityConstructor.checkForUpdatedSubCode(opportunityCreationRequest, createdOpportunity, workingDoc);
		}
		return createdOpportunity;
	}
	private EFTPaymentAccountsResponse addPaymentAccountsInfo(Document workingDoc, OpportunityCreationRequest request)
			throws OpportunityFailedRetryException, XPathExpressionException{
		EFTPaymentAccountsResponse eftPaymentAccountsResponse = null;
		if (request.getExistingOpportunityID() == null){
			eftPaymentAccountsResponse = fetchEftPaymentAccountsInfo(workingDoc, AcordHelper.getMigrationInfoInstrumentId(workingDoc));
		} else{
			addPaymentAccountsInfoForReUpload(workingDoc, request.getData(), request.getExistingOpportunityID());
		}
		return eftPaymentAccountsResponse;
	}
	private void addPaymentAccountsInfoForReUpload(Document workingDoc, Document document, Integer existingOppId)
			throws OpportunityFailedRetryException, XPathExpressionException{
		if (document != null && StringUtils.isNotEmpty(AcordHelper.getMigrationInfoPaymentLookupStatus(document))) {
			if(!Constants.PAYMENT_TYPE_IS_NOT_EFT.equals(AcordHelper.getMigrationInfoPaymentLookupStatus(document)) &&
			!Constants.PAYMENT_TYPE_IS_BLANK.equals(AcordHelper.getMigrationInfoPaymentLookupStatus(document))) {
				EFTPaymentAccountsResponse eftPaymentAccountsResponse = fetchEftPaymentAccountsInfo(
						workingDoc, AcordHelper.getMigrationInfoInstrumentId(document));
				if (eftPaymentAccountsResponse == null) {
					AcordHelper.setMigrationInfoPaymentLookupStatus(workingDoc, Constants.PAYMENT_DATA_DOES_NOT_EXIST);
					log.info("{}: {}", Constants.PAYMENT_DATA_DOES_NOT_EXIST, existingOppId);
				}
			} else {
				AcordHelper.setMigrationInfoPaymentLookupStatus(workingDoc, Constants.PAYMENT_TYPE_IS_NOT_EFT);
			}
		} else{
			EFTPaymentAccountsHelper.addPaymentAccountDataForReUpload(workingDoc, document);
		}
	}
	private EFTPaymentAccountsResponse fetchEftPaymentAccountsInfo(Document document, String instrumentId){
		EFTPaymentAccountsResponse eftPaymentAccountsResponse = null;
		try{
			eftPaymentAccountsResponse = OpportunityUtil.runFunctionWithRetries(eftPaymentAccountsService::getEFTPaymentAccountsDetails, instrumentId);
			EFTPaymentAccountsHelper.addPaymentAccountData(document, eftPaymentAccountsResponse);

		} catch (XPathExpressionException e){
			log.error("Error occurred when adding EFT payment info", e);
		}
		return eftPaymentAccountsResponse;
	}

}

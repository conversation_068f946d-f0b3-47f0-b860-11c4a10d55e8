package com.lmig.uscm.booktransfer.opportunity.domain.customfilters;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.sql.Date;

@Data
public class CustomFilter {

	private String opportunityId;
	private String subCode;
	private String state;
	private String lob;
	private String priorCarrierGuid;
	private String safecoPolicyGuid;
	private String customerName;
	private String sfdcid;
	private Date effectiveDate;
	@JsonProperty("nAICCd")
	private String nAICCd;
	private String priorPremium;
	private String lastQuotedPremium;
	private String masterOppId;
	private String status;
	@JsonProperty("nNumber")
	private String nNumber;
	private String salesforceCode;
	private String bookTransferId;
	private String lineType;
	@JsonProperty("nBDRelationship")
	private String nBDRelationship;
}

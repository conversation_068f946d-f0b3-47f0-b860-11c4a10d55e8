package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.SPQEOpportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;

import javax.xml.xpath.XPathExpressionException;
import java.util.Collections;
import java.util.List;

public class SPQEPreviousOppsCreationStrategy implements CreationStrategy {

	public boolean shouldResolveCreationBundle(final CreationStrategyBundle creationBundle) {
		OpportunityCreationRequest masterRequest = creationBundle.getMasterCreationRequest();
		return masterRequest != null && masterRequest.hasMasterOppSPQEMetaData();
	}

	/**
	 * Will update each OpportunityCreationRequest's existingOpportunityId if the master request has masterOppSQPEMeta data
	 * to the previously saved Opportunity in order to update the Opportunity instead of creating a new one
	 * <p>
	 * Linking occurs based on index in bundle requests and index in masterOppSPQEMeta previouslySavedOppIds. If there is not
	 * an index in previouslySavedOppIds, existingOpportunityId should stay null so a new Opportunity gets created. If there
	 * are more previouslySavedOppIds than requests, log them all as failed and remove requests
	 *
	 * @param creationBundles
	 * @param creationResponse
	 */
	@Override
	public void resolveCreationBundles(final List<CreationStrategyBundle> creationBundles, final OpportunityCreationResponse creationResponse) {

		try{
			int creationReqSize = creationBundles.stream().mapToInt(reqeust -> reqeust.getRequests().size()).sum();
			//When Opportunities already exists for the SPQE,do not create new ones
			List<SPQEOpportunity> spqeOpportunities = null;
			if(creationBundles.get(0).getMasterCreationRequest().getMasterOppSPQEMeta() != null) {
				spqeOpportunities = creationBundles.get(0).getMasterCreationRequest().getMasterOppSPQEMeta().getPreviousOpportunities();
			}
			if(spqeOpportunities != null && creationReqSize == spqeOpportunities.size()){
				for (CreationStrategyBundle creationBundle : creationBundles) {
					int i=0;
					for(OpportunityCreationRequest request : creationBundle.getRequests()) {
						String lob = LineOfBusiness.determineLobForPersonalLineType(request.getUploadedACORD()).name();
						request.setExistingOpportunityID(getOppId(spqeOpportunities,lob, i));
						i++;
					}
				}
				return;
			}
		} catch (XPathExpressionException e) {
			creationResponse.incrementFailedCreationCount();
		}

		for (CreationStrategyBundle creationBundle : creationBundles) {
			if (!shouldResolveCreationBundle(creationBundle)) {
				continue;
			}

			OpportunityCreationRequest masterRequest = creationBundle.getMasterCreationRequest();
			List<SPQEOpportunity> previouslySavedOppIds = masterRequest.getMasterOppSPQEMeta().getPreviousOpportunities();

			if (previouslySavedOppIds.size() > creationBundle.getRequests().size()) {
				// Splitting did not result in at least the same number of Opportunities as previously
				creationResponse.setFailedCreationCount(creationResponse.getFailedCreationCount() + creationBundle.getRequests().size());
				creationBundle.setRequests(Collections.emptyList());
				return;
			}

			// Link Opportunities based on index in previousOppIds and creationBundle requests
			for (int i = 0; i < previouslySavedOppIds.size(); i++) {
				creationBundle.getRequests().get(i).setExistingOpportunityID(previouslySavedOppIds.get(i).getOpportunityId());
			}
		}
	}

	@Override
	public CreationStrategyName getStrategyName() {
		return CreationStrategyName.LinkSPQEOppsToPrevious;
	}

	private int getOppId(List<SPQEOpportunity> spqeOpportunities, String lob, int index) {
		List<Integer> oppIdList = spqeOpportunities.stream().filter(spqeOpportunity -> spqeOpportunity.getLineOfBusiness().equals(lob)).map(SPQEOpportunity::getOpportunityId).toList();
		if(oppIdList.size() == 1){
			return oppIdList.get(0);
		}else {
			return oppIdList.get(index);
		}
	}
}

package com.lmig.uscm.booktransfer.opportunity.services.utility;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunityFailedRetryException;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.BusinessAcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public final class OpportunityUtil {
	private static final int AGENCY_ID_COLUMN_SIZE = 30;

	private OpportunityUtil() {

	}

	/**
	 * Sets the agent number in the xml and returns the updated xml.
	 * @param oppXml the xml to update
	 * @param agentNumber the agent number to set
	 * @param lineType the line type of the opportunity
	 * @return updated xml with agent number set
	 */
	public static Document setAgentNumber(Document oppXml, String agentNumber, LineType lineType) throws XPathExpressionException {
		if(StringUtils.isEmpty(agentNumber)) {
			return oppXml;
		}

		String prevContractNumber = AcordHelper.getAgentNumber(oppXml);
		oppXml = LineType.Business.equals(lineType) ?
			BusinessAcordHelper.setAgentNumber(oppXml, agentNumber) :
			AcordHelper.setAgentNumber(oppXml, agentNumber);
		try {
			if(StringUtils.isNotEmpty(prevContractNumber)) {
				AcordHelper.createProducerAgencyId(oppXml, prevContractNumber);
			}
		} catch (Exception e) {
			log.error("Error while creating producer agency id", e);
		}
		return oppXml;
	}

	/**
	 * Get a map of oppId -> oppIDs ignores all null opportunities.
	 */
	public static Map<Integer, Opportunity> getOpportunityIdToOpportunityMap(final List<Opportunity> oppList) {
		Map<Integer, Opportunity> oppIdToMap = new HashMap<>();
		for (Opportunity opportunity : oppList) {
			if (opportunity == null) {
				// This null will be caught later in the function where we check if opp = null
				continue;
			}
			oppIdToMap.put(opportunity.getOpportunityId(), opportunity);
		}
		return oppIdToMap;
	}

	/**
	 * GEts a list of all quoteSalesForceIDs from a list of opportunities. AKA a list of oppIds.
	 */
	public static List<String> getOpportunityIdsByLineType(List<Opportunity> opportunities, LineType lineType) {
		return opportunities.stream()
				.filter(opportunity -> lineType.equals(opportunity.getLineType()))
				.map(opp -> String.valueOf(opp.getOpportunityId()))
				.collect(Collectors.toList());
	}

	/**
	 * Trim agency id to its equivalent sql column size
	 */
	public static String trimAgencyId(String agencyId) {
		if (agencyId.length() > AGENCY_ID_COLUMN_SIZE) {
			log.warn("Agency Id data exceeds its SQL column size");
			return agencyId.substring(0, AGENCY_ID_COLUMN_SIZE);
		}
		return agencyId;
	}

	public static LineType defaultPersonalOrConvertStringToLineType(String rawLineType) {
		try {
			return LineType.valueOf(rawLineType);
		} catch (Exception e) {
			return LineType.Personal;
		}
	}

	/**
	 * Used as a wrapper for lambda functions that throw Exceptions. Specifically, this wrapper assumes the
	 * lambda function expects a Document from the Opportunity data xml. Any exceptions thrown, from xml conversion to
	 * Document or from calling function, will be caught and cause a null to thrown
	 *
	 * @param opp      opportunity to grab data xml from
	 * @param function returning function that consumes a Document
	 * @return return type of function
	 */
	public static <T> T oppDataFunctionWithDefaultNull(final Opportunity opp,
													   final FunctionWithXPathExceptions<Document, T> function) {
		try {
			Document doc = XmlHelper.getDocument(opp.getData());
			return function.apply(doc);
		} catch (DateTimeParseException | XPathExpressionException | ParserConfigurationException | SAXException |
				 IOException e) {
			return null;
		}
	}

	public static <T, R> R runFunctionWithRetriesWithDefault(final FunctionWithUncheckedExceptions<T, R> function,
															  final T functionArg,
															  final R defaultReturn) {
		try {
			return runFunctionWithRetries(function, functionArg);
		} catch (OpportunityFailedRetryException e) {
			log.error("Returning default due to multiple failed retries. Error: ", e);
			return defaultReturn;
		}
	}

	public static <T, R> R runFunctionWithRetries(final FunctionWithUncheckedExceptions<T, R> function,
												   final T functionArg) throws OpportunityFailedRetryException {
		int currAttempt = 0;
		int maxAttempt = 3;
		while (currAttempt++ < maxAttempt) {
			try {
				return function.apply(functionArg);
			} catch (Throwable e) {
				log.error("Failed to run the function", e);
			}
		}
		throw new OpportunityFailedRetryException(String.format("Could not successfully run function within %d max attempts", maxAttempt));
	}

	/**
	 * Interface for serializing lambda functions with unchecked RuntimeException or Error throwables
	 */
	@FunctionalInterface
	public interface FunctionWithUncheckedExceptions<T, R> {
		R apply(T t);
	}

	/**
	 * Interface for serializing lambda functions that throw XPathExpressionException
	 */
	@FunctionalInterface
	public interface FunctionWithXPathExceptions<T, R> {
		R apply(T t) throws XPathExpressionException, ParserConfigurationException, SAXException, IOException;
	}
}

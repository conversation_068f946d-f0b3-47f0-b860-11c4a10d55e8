package com.lmig.uscm.booktransfer.opportunity.auditing;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Set;

@Aspect
@Component
@Slf4j
public class LoggingAspect {
    private static final String STARTING_TRANSACTION = "Starting transaction";
    private static final String ENDING_TRANSACTION_SUCCESS = "Ending transaction success";
    private static final String ENDING_TRANSACTION_FAILURE = "Ending transaction failure";

    private static final Set<String> SPLUNK_NAME_DENY_LIST = Set.of("CUSTOM FILTER REQUEST");

    /**
     * Target any restController
     */
    @Pointcut("@within(org.springframework.web.bind.annotation.RestController)")
    private void restControllerAnnotationPointcut() {

    }

    /**
     * Target any restController
     */
    @Pointcut("within(com.lmig.uscm.booktransfer.opportunity.controller.*)")
    private void controllerPackagePointcut() {
    	
    }

    /**
     * Target any message consumer
     */
    @Pointcut("within(com.lmig.uscm.booktransfer.opportunity.messaging.Consumer)")
    private void consumerClassPointcut() {
    	
    }
    
    /**
     * Methods annotated with LogTransaction annotation will run transaction logging
     */
    @Pointcut("@annotation(com.lmig.uscm.booktransfer.opportunity.auditing.LogTransaction)")
    private void logTransactionAnnotationPointcut() {
    	
    }
   
    /**
     * Aggregation for any controller or consumer weve targeted in above methods to run transaction logging
     */
    @Pointcut("(controllerPackagePointcut() && restControllerAnnotationPointcut()) || consumerClassPointcut() || logTransactionAnnotationPointcut()")
    private void transactionLoggingPointcut() {
    }

    @Around("transactionLoggingPointcut()")
    private Object logTransaction(final ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        final String splunkName = getSplunkOrTransactionName(proceedingJoinPoint);

        if (SPLUNK_NAME_DENY_LIST.contains(splunkName)) {
            try {
                return proceedingJoinPoint.proceed();
            } catch (final Throwable unexpectedThrowable) {
                log.error("Caught unexpected throwable", unexpectedThrowable);
                throw unexpectedThrowable;
            }
        } else {
            try {
                log.info("{} {}", STARTING_TRANSACTION, splunkName);
                final Object returnValue = proceedingJoinPoint.proceed();
                log.info("{} {}", ENDING_TRANSACTION_SUCCESS, splunkName);
                return returnValue;
            } catch (final Throwable unexpectedThrowable) {
                log.error("Caught unexpected throwable", unexpectedThrowable);
                log.info("{} {}", ENDING_TRANSACTION_FAILURE, splunkName);
                throw unexpectedThrowable;
            }

        }
    }

    /**
     * Determines the Transaction Name to use for Splunk If SplunkName annotation is
     * not used, use the method name as Transaction Name
     *
     * @param proceedingJoinPoint - {@link ProceedingJoinPoint} used to reference
     *                            the method intercepted by this aspect
     * @return String Splunk Transaction Name
     */
    protected String getSplunkOrTransactionName(final ProceedingJoinPoint proceedingJoinPoint) {
        final MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        final Method method = methodSignature.getMethod();
        final SplunkName splunkNameAnnotation = method.getAnnotation(SplunkName.class);
        final LogTransaction logTransactionAnnotation = method.getAnnotation(LogTransaction.class);
        

        if (splunkNameAnnotation != null && StringUtils.isNotEmpty(splunkNameAnnotation.value())) {
            return splunkNameAnnotation.value();
        } else if (logTransactionAnnotation != null && StringUtils.isNotEmpty(logTransactionAnnotation.value())) {
            return logTransactionAnnotation.value();
        }

        return proceedingJoinPoint.getSignature().getDeclaringType().getSimpleName() + "."
                + proceedingJoinPoint.getSignature().getName() + "()";
    }
}

package com.lmig.uscm.booktransfer.opportunity.services;

import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import javax.xml.namespace.QName;
import javax.xml.stream.FactoryConfigurationError;
import javax.xml.stream.XMLEventFactory;
import javax.xml.stream.XMLEventReader;
import javax.xml.stream.XMLEventWriter;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLOutputFactory;
import javax.xml.stream.XMLStreamConstants;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.events.Attribute;
import javax.xml.stream.events.EndElement;
import javax.xml.stream.events.StartElement;
import javax.xml.stream.events.XMLEvent;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Set;
import java.util.UUID;

public class BookTransferAttributeService {
	private static final String BOOKTRANSFER_ATTRIBUTE = "btId";
	private static final String BOOKTRANSFER_ASSOCIATED_ATTRIBUTE = "bookTransferAssociatedNodeId";
	private static final Set<String> ATTRIBUTE_SPECIFIC_NODES = Sets.newHashSet("Coverage", "Deductible");
	private XMLEventReader xmlEventReader;
	private final ByteArrayOutputStream outputStream;
	private XMLEventWriter xmlEventWriter;
	//to track the vehicle id of the coverage nested inside a vehicle node
	private String vehId;
	private int counter;

	public BookTransferAttributeService() {
		this.outputStream = new ByteArrayOutputStream();
	}

	public String addBookTransferCoverageAttributes(String xml) throws XMLStreamException {
		this.xmlEventReader = getEventReaderInstance(xml);
		this.xmlEventWriter = XMLOutputFactory.newInstance().createXMLEventWriter(outputStream);
		this.vehId = "";
		this.counter = 0;
		return modifyCoverageNodes();
	}

	private String modifyCoverageNodes() throws XMLStreamException {

		while (xmlEventReader.hasNext()) {
			XMLEvent event = xmlEventReader.nextEvent();

			switch (event.getEventType()) {
				case XMLStreamConstants.START_ELEMENT:
					buildOutputStreamAndSetVehicleIdForStartElement(event);
					break;
				case XMLStreamConstants.END_ELEMENT:
					buildOutputStreamAndSetVehicleIdForEndElement(event);
					break;
				default:
					this.xmlEventWriter.add(event);
					break;
			}
			this.xmlEventWriter.flush();

		}
		return fixXmlDocument(outputStream.toString());
	}

	private String fixXmlDocument(String xml) {
		return xml.replaceAll("<\\?xml(.+?)\\?>", "").trim();
	}

	private void buildOutputStreamAndSetVehicleIdForEndElement(XMLEvent endEvent) throws XMLStreamException {
		EndElement endElement = endEvent.asEndElement();
		this.xmlEventWriter.add(endEvent);
		if (isVehicleNode(endElement.getName().getLocalPart())) {
			this.vehId = "";
		}
	}

	private void buildOutputStreamAndSetVehicleIdForStartElement(XMLEvent startEvent) throws XMLStreamException {
		StartElement startElement = startEvent.asStartElement();

		if (isVehicleNode(startElement.getName().getLocalPart())) {
			setVehicleId(startEvent);
		} else if (ATTRIBUTE_SPECIFIC_NODES.contains(startElement.getName().getLocalPart())) {
			addAttributeToNode(startElement.getName().getLocalPart());
		} else {
			this.xmlEventWriter.add(startEvent);
		}
	}

	private void setVehicleId(XMLEvent event) throws XMLStreamException {
		Attribute vehIdAttribute = event.asStartElement().getAttributeByName(new QName("id"));
		this.xmlEventWriter.add(event);
		if (vehIdAttribute == null) {
			XMLEventFactory xmlEventFactory = XMLEventFactory.newInstance();
			this.vehId = String.valueOf(++counter);
			this.xmlEventWriter.add(xmlEventFactory.createAttribute(new QName("id"), this.vehId));
		} else {
			this.vehId = vehIdAttribute.getValue();
		}
	}

	private void addAttributeToNode(String nodeName) throws XMLStreamException {
		XMLEventFactory xmlEventFactory = XMLEventFactory.newInstance();
		//to remove existing attributes we are creating new coverage element
		XMLEvent event = xmlEventFactory.createStartElement("", "", nodeName);
		this.xmlEventWriter.add(event);
		xmlEventWriter
				.add(xmlEventFactory.createAttribute(new QName(BOOKTRANSFER_ATTRIBUTE), UUID.randomUUID().toString()));

		if (StringUtils.isNoneBlank(this.vehId)) {
			xmlEventWriter
					.add(xmlEventFactory.createAttribute(new QName(BOOKTRANSFER_ASSOCIATED_ATTRIBUTE), this.vehId));
		}
	}

	private XMLEventReader getEventReaderInstance(String xml) throws FactoryConfigurationError, XMLStreamException {
		XMLInputFactory factory = XMLInputFactory.newInstance();
		InputStream stream = new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8));
		return factory.createXMLEventReader(stream);
	}

	private boolean isVehicleNode(String nodeName) {
		return Arrays.asList("PersVeh", "Watercraft").contains(nodeName);
	}
}

package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.config.properties.ServiceUrlProperties;
import com.lmig.uscm.booktransfer.opportunity.domain.QuoteReportBulkUpdate;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.ResponseEntity;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Slf4j
public class QuoteReportService {

  private final WebClient quoteReportWebClient;
  private final ServiceUrlProperties properties;


  public QuoteReportService(final WebClient quoteReportWebClient, final ServiceUrlProperties properties) {
    this.quoteReportWebClient = quoteReportWebClient;
    this.properties = properties;
  }

  /**
   * Retrieve the QuoteReportItemLegacy with the highest <code>QuoteReportId</code> for
   * the provided opportunityId
   *
   * @param opportunityId - int representing the opportunity
   * @return {@link QuoteReportItemLegacy} of the corresponding opportunity
   */
  public QuoteReportItemLegacy getQuoteReportByOpportunityId(final int opportunityId) {
    List<QuoteReportItemLegacy> items = getAllQuoteReportsForOpportunityId(opportunityId);
    if (items != null && !items.isEmpty()) {
      return items.stream() //
        .max(Comparator.comparingInt(QuoteReportItemLegacy::getQuoteReportID)) //
        .get();
    }
    log.info("Could not find QuoteReportItemLegacy for id: {}", opportunityId);
    return null;
  }

  /**
   * Retrieve all QuoteReportItems for the provided opportunityId
   *
   * @param opportunityId - int representing the opportunity
   * @return List of {@link QuoteReportItemLegacy} of the corresponding opportunity
   */
  public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityId(final int opportunityId) {
    String url = properties.getQuoteReportUrlProvider().getBaseUrl()
      + properties.getQuoteReportUrlProvider().getPath() + "/" + opportunityId;

    List<QuoteReportItemLegacy> quoteReports = quoteReportWebClient.get()
      .uri(url)
      .retrieve()
      .bodyToMono(new ParameterizedTypeReference<List<QuoteReportItemLegacy>>() {
      })
      .block();

    if (quoteReports != null && quoteReports.size() > 0) {
      return quoteReports;
    }
    log.error("Error finding QuoteReportItemLegacy for id: {}", opportunityId);
    return null;
  }

  /**
   * Retrieve all QuoteReportItems for the provided list of opportunityIds
   *
   * @return List of {@link QuoteReportItemLegacy} of the corresponding opportunity ids
   */
  public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityIds(final List<Integer> opportunityIds)
    throws OpportunityException {
    try {
      String url = properties.getQuoteReportUrlProvider().getBaseUrl()
        + properties.getQuoteReportUrlProvider().getPath() + "/query";
      log.info("POST - {} - {}", url, opportunityIds);
      List<QuoteReportItemLegacy> quoteReports = quoteReportWebClient.post()
        .uri(url)
        .bodyValue(opportunityIds)
        .retrieve()
        .bodyToMono(new ParameterizedTypeReference<List<QuoteReportItemLegacy>>() {
        })
        .block();
      if (quoteReports == null || quoteReports.isEmpty()) {
        return new ArrayList<>();
      }
      return quoteReports;
    } catch (Exception e) {
      log.error("Error finding QuoteReportItemLegacy for filter: {}", opportunityIds);
      throw new OpportunityException(e);
    }
  }

  /**
   * Add a new QuoteReportItemLegacy
   *
   * @param reportItem - {@link QuoteReportItemLegacy} to be added
   * @return {@link QuoteReportItemLegacy} updated with the quoteReportId from the API
   */
  public QuoteReportItemLegacy addQuoteReport(final QuoteReportItemLegacy reportItem) {
    String url = properties.getQuoteReportUrlProvider().getBaseUrl()
      + properties.getQuoteReportUrlProvider().getPath();
    log.info("QuoteReportItemLegacy: is not null- {}, oppid- {}, book - {}", reportItem != null,
      reportItem.getQuoteSalesforceID(), reportItem.getSalesforceID());
    Integer quoteReportId = quoteReportWebClient.post()
      .uri(url)
      .bodyValue(reportItem)
      .retrieve()
      .bodyToMono(Integer.class)
      .block();
    if (quoteReportId != null) {
      reportItem.setQuoteReportID(quoteReportId);
      return reportItem;
    }
    log.error("Unable to create quote report item for opportunity id: {}",
      reportItem.getQuoteSalesforceID());
    return null;
  }

  /**
   * Update an existing QuoteReportItemLegacy
   *
   * @param reportItem - {@link QuoteReportItemLegacy} to be updated
   * @return {@link QuoteReportItemLegacy} with the updated details
   */
  public QuoteReportItemLegacy updateQuoteReport(final QuoteReportItemLegacy reportItem) {
    String url = properties.getQuoteReportUrlProvider().getBaseUrl()
      + properties.getQuoteReportUrlProvider().getPath();
    ResponseEntity<Void> response = quoteReportWebClient.put()
      .uri(url)
      .bodyValue(reportItem)
      .retrieve()
      .toEntity(Void.class)
      .block();
    if (response.getStatusCode().is2xxSuccessful()) {
      return reportItem;
    }
    log.error("Unable to update quote report item for opportunity id: {}",
      reportItem.getQuoteSalesforceID());
    return null;
  }

  /**
   * Update a group of QuoteReportItemLegacy
   *
   * @param reportItems - List of {@link QuoteReportItemLegacy} to be updated
   * @return List of {@link QuoteReportItemLegacy} with the updated details
   */
  public List<QuoteReportItemLegacy> updateBulkQuoteReports(final List<QuoteReportItemLegacy> reportItems) {
    String url = properties.getQuoteReportUrlProvider().getBaseUrl()
      + properties.getQuoteReportUrlProvider().getPath() + "/bulk";
    ResponseEntity<String> response = quoteReportWebClient.put()
      .uri(url)
      .bodyValue(reportItems)
      .retrieve()
      .toEntity(String.class)
      .block();

    if (response.getStatusCode().is2xxSuccessful()) {
      return reportItems;
    }
    log.error("Unable to update quote report items");
    return null;
  }

  /**
   * Patch a QuoteReportItemLegacy with subset of fields
   */
  public void patchQuoteReportFields(final QuoteReportBulkUpdate bulkPatch) {
    String url = properties.getQuoteReportUrlProvider().getBaseUrl()
      + properties.getQuoteReportUrlProvider().getPath();
    ResponseEntity<String> response = quoteReportWebClient.patch()
      .uri(url)
      .bodyValue(bulkPatch)
      .retrieve()
      .toEntity(String.class)
      .block();

    if (!response.getStatusCode().is2xxSuccessful()) {
      log.error("Unable to update quote report items");
    }
  }

  public void patchQuoteReportFields(final List<QuoteReportItemLegacy> quoteReports) {
    String url = properties.getQuoteReportUrlProvider().getBaseUrl()
      + properties.getQuoteReportUrlProvider().getPath() + "/bulk";
    quoteReportWebClient.patch()
      .uri(url)
      .bodyValue(quoteReports)
      .retrieve()
      .toEntity(String.class)
      .block();
  }
}

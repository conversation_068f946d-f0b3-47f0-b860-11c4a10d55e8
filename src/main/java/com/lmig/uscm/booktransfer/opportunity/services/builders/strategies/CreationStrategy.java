package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;

import java.util.List;

public interface CreationStrategy {

	void resolveCreationBundles(List<CreationStrategyBundle> creationBundles, OpportunityCreationResponse response);

	CreationStrategyName getStrategyName();

}

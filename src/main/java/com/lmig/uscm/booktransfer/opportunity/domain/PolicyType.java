package com.lmig.uscm.booktransfer.opportunity.domain;

import java.util.List;

public enum PolicyType {
    CONDO(List.of("06", "H06", "O6", "HO6", "T6")),
    RENTERS(List.of("04", "H04", "O4", "HO4", "T4"));

    private final List<String> policyTypeCds;
    PolicyType(List<String> policyTypes) {
        this.policyTypeCds = policyTypes;
    }

    public List<String> getPolicyTypeCds() {
        return policyTypeCds;
    }
}

package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate;

import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.ExportHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;

import java.time.LocalDate;
import java.util.List;

/**
 * This class handles the true lead update effective data option
 */
public class UpdateLeadEffectiveDateHelper extends UpdateMatchingCustomerEffectiveDateHelper {
	public UpdateLeadEffectiveDateHelper(final OpportunityHelper opportunityHelper,
										 final BookTransferService bookTransferService, 
										 final ExportHelper exportHelper, 
										 final OpportunityRepoHelper opportunityRepoHelper) {
		super(opportunityHelper, bookTransferService, exportHelper, opportunityRepoHelper);
	}

	@Override
	protected LocalDate getEarliestEffectiveDates(List<OppChangeFieldResult> packagedOpportunities,
												  LocalDate bookTransferStartDate) {
		return getEarliestLeadEffectiveDates(packagedOpportunities, bookTransferStartDate);
	}

}

package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger;

import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.MassUpdateResultsOfFieldChange;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeField;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.opportunity.services.UploadEventService;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.csvbuilder.UpdateOpportunityFieldResponseBuilder;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.csvbuilder.UpdateOtherFieldsResponseBuilder;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Scanner;


public class FieldChangeHelper {

	@Autowired
	private OpportunityRepoHelper opportunityRepoHelper;
	@Autowired
	private OpportunityHelper opportunityHelper;

	private QuoteReportItemHelper quoteReportItemHelper;

	private UploadEventService uploadEventService;

	private BookTransferService bookTransferService;


	public FieldChangeHelper() {
	}

	public FieldChangeHelper(final OpportunityHelper opportunityHelper,
							 final QuoteReportItemHelper quoteReportItemHelper,
							 final UploadEventService uploadEventService,
							 final BookTransferService bookTransferService) {
		this.opportunityHelper = opportunityHelper;
		this.quoteReportItemHelper = quoteReportItemHelper;
		this.uploadEventService = uploadEventService;
		this.bookTransferService = bookTransferService;
	}

	/**
	 * It will take a request of oppIds with the field name and values we want to
	 * change them too and will change the values to match that. IT will then try to
	 * update the database with those results and return a csv that explains what
	 * happened
	 */
	public String updateOppAndQRI(Collection<Integer> oppIdList, String fieldName, String statusValue)
			throws OpportunityException {
		return updateOppAndQRI(buildOppChangeFieldRequest(oppIdList, fieldName, statusValue));
	}

	private List<OppChangeField> buildOppChangeFieldRequest(Collection<Integer> oppIdList, String fieldName,
															String statusValue) {
		List<OppChangeField> oppChangeFieldList = new ArrayList<>();
		for (Integer oppId : oppIdList) {
			OppChangeField oppChangeField = new OppChangeField(oppId, fieldName, statusValue);
			oppChangeFieldList.add(oppChangeField);
		}
		return oppChangeFieldList;
	}

	/**
	 * It will take a request of oppIds with the field name and values we want to
	 * change them too and will change the values to match that. Each value will be
	 * in a row of the following ... oppId, fieldName,statusValue IT will then try
	 * to update the database with those results and return a csv that explains what
	 * happened
	 *
	 * @param file we are parsing
	 */
	public String updateOppAndQRI(MultipartFile file) throws Exception {
		return updateOppAndQRI(parseCSV(file));
	}

	protected List<OppChangeField> parseCSV(MultipartFile file) throws IOException {
		List<OppChangeField> oppChangeFieldList = new ArrayList<>();
		Scanner scanner = new Scanner(file.getInputStream());
		String line = scanner.nextLine();
		while (scanner.hasNext()) {
			line = scanner.nextLine();
			String[] fields = line.split(",");
			if (fields.length > 2) {
				if (!fields[0].isEmpty()) {
					int oppId = Integer.parseInt(fields[0]);
					String fieldName = fields[1];
					String statusValue = fields[2];
					OppChangeField oppChangeField = new OppChangeField(oppId, fieldName, statusValue);
					oppChangeFieldList.add(oppChangeField);
				}
			}

		}
		return oppChangeFieldList;

	}

	/**
	 * It will take a request of changes to make to an opp. IT will then try to
	 * update the database with those results and return a csv that explains what
	 * happened
	 */
	public String updateOppAndQRI(List<OppChangeField> oppChangeFields) throws OpportunityException {
		List<Integer> oppIds = getOppIds(oppChangeFields);
		List<Opportunity> oppList = opportunityHelper.getOpportunitiesForStatusChange(oppIds);
		List<QuoteReportItemLegacy> quoteItemsList = quoteReportItemHelper.getQuoteReportItemsForStatusChange(oppIds);
		return updateOppAndQRI(oppChangeFields, oppList, quoteItemsList);
	}

	protected List<Integer> getOppIds(List<OppChangeField> oppChangeFieldList) {
		List<Integer> oppIds = new ArrayList<>();
		for (OppChangeField oppChangeField : oppChangeFieldList) {
			oppIds.add(oppChangeField.getOppId());
		}
		return oppIds;
	}

	/**
	 * This will update opp and qri and return the csv giving the results
	 */
	protected String updateOppAndQRI(List<OppChangeField> oppChangeFieldList, List<Opportunity> oppList,
									 List<QuoteReportItemLegacy> quoteItemsList) {
		MassUpdateResultsOfFieldChange massUpdateFields = getResultsOfUpdatingOppAndQRI(
				oppChangeFieldList, oppList, quoteItemsList);
		return buildCSV(massUpdateFields);
	}

	protected MassUpdateResultsOfFieldChange getResultsOfUpdatingOppAndQRI(List<OppChangeField> oppChangeFieldList,
																		   List<Opportunity> oppList,
																		   List<QuoteReportItemLegacy> quoteItemsList) {
		MassUpdateResultsOfFieldChange massUpdateFields = new MassUpdateResultsOfFieldChange();
		List<UpdateHelper> updateHelpers = getUpdateHelpers(oppChangeFieldList, oppList, quoteItemsList);
		for (OppChangeField oppChangeField : oppChangeFieldList) {
			boolean foundItem = false;
			for (UpdateHelper updateHelper : updateHelpers) {
				if (updateHelper.isUpdatingField(oppChangeField.getFieldName())) {
					massUpdateFields.addOpportunityChangeResult(updateHelper.buildInitialResult(oppChangeField));
					foundItem = true;
					break;
				}
			}
			if (!foundItem) {
				massUpdateFields.addOpportunityChangeResult(new OppChangeFieldResult(oppChangeField)
						.isError("Rule has not been written to update field. Please request to tech team: "
								+ oppChangeField.getFieldName()));
			}
		}

		return saveItemsToDataBase(massUpdateFields, updateHelpers);
	}

	/**
	 * Take the results as input and build out the csv with those results.
	 *
	 * @return a built of csv with the results in there
	 */
	protected String buildCSV(MassUpdateResultsOfFieldChange massUpdateFields) {
		UpdateOpportunityFieldResponseBuilder resBuilder = new UpdateOtherFieldsResponseBuilder();
		resBuilder.addOppChangeFieldResults(massUpdateFields.getAllOpportunitiesResults());
		return resBuilder.buildOutStatusChangeReport();
	}

	private List<UpdateHelper> getUpdateHelpers(List<OppChangeField> oppChangeFieldList, List<Opportunity> oppList,
												List<QuoteReportItemLegacy> quoteItemsList) {
		List<UpdateHelper> updateHelpers = new ArrayList<>();
		updateHelpers.add(getUpdateStatusHelper(oppList, quoteItemsList));
		updateHelpers.add(getUpdateBookHelper(oppChangeFieldList, oppList, quoteItemsList));
		// To update new field we just need to add a new update field helper to this
		// list and build a new function to handle this

		return updateHelpers;
	}

	private UpdateStatusHelper getUpdateStatusHelper(List<Opportunity> oppList, List<QuoteReportItemLegacy> quoteItemsList) {
		UpdateStatusHelper updateStatusHelper = new UpdateStatusHelper(oppList, quoteItemsList);
		// TODO figure out how to autowire or inject them into the code without a too
		// long constructor
		updateStatusHelper.setDatabaseHelpers(opportunityHelper, quoteReportItemHelper, opportunityRepoHelper);
		return updateStatusHelper;
	}

	private UpdateBookHelper getUpdateBookHelper(List<OppChangeField> oppChangeFieldList, List<Opportunity> oppList,
												 List<QuoteReportItemLegacy> quoteItemsList) {
		UpdateBookHelper updateBookHelper = new UpdateBookHelper(oppList, quoteItemsList);
		// TODO figure out how to autowire or inject them into the code without a too
		// long constructor
		updateBookHelper.setDatabaseHelpersForMoveBook(bookTransferService, uploadEventService);
		updateBookHelper.setDatabaseHelpers(opportunityHelper, quoteReportItemHelper, opportunityRepoHelper);
		updateBookHelper.setBookTransferAndUploadMap(oppChangeFieldList);
		return updateBookHelper;
	}

	/**
	 * This method goes through and actually saves the item to the database this will
	 * go through each change and save in bulk by change field and change value
	 * being the same This allows not to have to pull complete opportunity when
	 * saving data by doing it this way.
	 */
	private MassUpdateResultsOfFieldChange saveItemsToDataBase(MassUpdateResultsOfFieldChange massUpdateFields,
															   List<UpdateHelper> updateHelpers) {
		for (String fieldName : massUpdateFields.getAllFieldNamesToChange()) {
			for (String value : massUpdateFields.getAllFieldValuesToChange(fieldName)) {
				try {
					// for each fieldName and value we are changing it too. get all not failed opps to update
					List<Opportunity> opportunities = massUpdateFields.getAllNotFailedOpportunities(fieldName, value);
					if (opportunities.isEmpty()) {
						// if nothing to update, don't update
						continue;
					}
					for (UpdateHelper updateHelper : updateHelpers) {
						if (updateHelper.isUpdatingField(fieldName)) {
							updateHelper.saveFieldUpdate(opportunities, value);
							break;
						}
					}
					// we will catch all exceptions here as
				} catch (Exception e) {
					// if an exception occurs then we will mark them all as failed as we are
					// throwing up this exception
					for (OppChangeFieldResult oppChangeFieldResult : massUpdateFields
							.getAllOpportunitiesResults(fieldName, value)) {
						oppChangeFieldResult.isError(e.getMessage());
					}
				}
			}
		}
		return massUpdateFields;
	}

	/**
	 * FOR TESTING
	 */
	void setOpportunityHelper(OpportunityHelper opportunityHelper) {
		this.opportunityHelper = opportunityHelper;
	}
	
	void setOpportunityRepoHelper(OpportunityRepoHelper opportunityRepoHelper) {
		this.opportunityRepoHelper = opportunityRepoHelper;
	}

	void setQuoteReportItemHelper(QuoteReportItemHelper quoteReportItemHelper) {
		this.quoteReportItemHelper = quoteReportItemHelper;
	}

	void setBookTransferService(BookTransferService bookTransferService) {
		this.bookTransferService = bookTransferService;
	}

	void setUploadEventService(UploadEventService uploadEventService) {
		this.uploadEventService = uploadEventService;
	}
}

package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import org.w3c.dom.Document;

public class UMBRPPreview extends Preview {

	@Override
	public LineOfBusiness getLob() {
		return LineOfBusiness.UMBRP;
	}

	@Override
	protected void setLOBRequiredData(Document originalXml, PreviewDataResponse response) {
		// Nothing is set in required data
		return;
	}
}

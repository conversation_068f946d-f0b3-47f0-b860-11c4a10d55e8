package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.Address;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.PropertyInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class PropertyInfoHelper {
  private final WebClient webClient;
  private final String propertyInfoServiceUrl;

  public PropertyInfoHelper(WebClient webClient, String propertyInfoServiceUrl) {
    this.webClient = webClient;
    this.propertyInfoServiceUrl = propertyInfoServiceUrl != null ? propertyInfoServiceUrl : "";
  }

  public PropertyInfoResponse getTerritoryAndCoastalLine(Address address, String lob) {
    try {
      Map<String, String> request = new HashMap<>();
      request.put("address", address.getAddressLine());
      request.put("city", address.getCity());
      request.put("state", address.getState());
      request.put("zipCode", address.getPostalCodeInfo().getPostalCodeHyphenPlus4());
      return webClient
        .post()
        .uri(propertyInfoServiceUrl + "/territory/coastal-line?lob=" + lob)
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .retrieve()
        .bodyToMono(PropertyInfoResponse.class)
        .block();
    } catch (Exception e) {
      log.error("Error getting territory and coastal line from property info service", e);
      return new PropertyInfoResponse();
    }
  }
}

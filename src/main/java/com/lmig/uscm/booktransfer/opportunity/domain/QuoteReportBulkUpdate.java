package com.lmig.uscm.booktransfer.opportunity.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class QuoteReportBulkUpdate implements Serializable {
	private static final long serialVersionUID = -8410653639135401351L;

	private List<Integer> quoteReportIds;
	private List<String> quoteSalesforceIds;
	private String effectiveDate;
	private String status;
	private String subCode;
	private int salesforceId;

	public void addQuoteReportId(int quoteReportId) {
		if (this.quoteReportIds == null) {
			this.quoteReportIds = new ArrayList<>();
		}
		this.quoteReportIds.add(quoteReportId);
	}

	public void addQuoteSalesforceIds(final String quoteSalesforceId) {
		if (this.quoteSalesforceIds == null) {
			this.quoteSalesforceIds = new ArrayList<>();
		}
		this.quoteSalesforceIds.add(quoteSalesforceId);
	}
}

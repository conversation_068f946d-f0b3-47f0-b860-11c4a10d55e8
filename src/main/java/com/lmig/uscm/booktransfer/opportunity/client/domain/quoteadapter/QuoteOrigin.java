package com.lmig.uscm.booktransfer.opportunity.client.domain.quoteadapter;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class QuoteOrigin {
	private String executableName;
	private String executableId;
	private QuoteOriginType type; //scheduler, AQE, Quote Adapter?
	@JsonProperty("nNumber")
	private String nNumber; //requesting user
	private String originRequestId; //Process Request ID replacement
	private RequestEnvironment requestEnvironment; //Quoting environment for PL/ BL
	private long requestTimestamp; //timestamp the request was created
	private String originData; //request xml
}

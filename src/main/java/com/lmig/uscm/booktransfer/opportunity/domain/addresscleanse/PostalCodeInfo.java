package com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PostalCodeInfo {
    private String postalCodeClassification;
    private String postalCode;
    private String postalCodeExtension;
    private String postalCodePlus4;
    private String postalCodeHyphenPlus4;
}

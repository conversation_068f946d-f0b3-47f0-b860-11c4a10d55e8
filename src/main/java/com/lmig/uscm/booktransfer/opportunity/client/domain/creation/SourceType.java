package com.lmig.uscm.booktransfer.opportunity.client.domain.creation;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

public enum SourceType {
	XML_EXTRACTION(SourceType.SourceTypeValues.XML),
	CSV_MAPPING(SourceType.SourceTypeValues.CSV),
	ELABEL_TRANSLATION(SourceType.SourceTypeValues.ELABEL),
	MANUAL_ENTRY(SourceType.SourceTypeValues.MANUAL),
	UNKNOWN(SourceType.SourceTypeValues.UNKNOWN);

	private final String value;

	SourceType(final String type) {
		this.value = type;
	}

	public String getValue() {
		return this.value;
	}

	public static SourceType find(String sourceType) {
		String defaultVal = Optional.ofNullable(sourceType).orElse(SourceTypeValues.UNKNOWN);
		return Arrays.stream(SourceType.values())
				.filter(type -> StringUtils.equals(type.getValue(), defaultVal))
				.findFirst()
				.orElse(SourceType.UNKNOWN);
	}

	public static final class SourceTypeValues {
		public static final String XML = "xmlExtraction";
		public static final String CSV = "csvMapping";
		public static final String ELABEL = "eLabelTranslation";
		public static final String MANUAL = "manualEntry";
		public static final String UNKNOWN = "Unknown";

		private SourceTypeValues() {
			throw new IllegalStateException("Utility class");
		}
	}
}

package com.lmig.uscm.booktransfer.opportunity.domain.customeraccount;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAccountAqe {
    private String status;
    private String businessType;
    private Integer opportunityId;
    private String priorExpirationDate;
    private Double priorPremium;
    @JsonProperty("nNumber")
    private String nNumber;
}

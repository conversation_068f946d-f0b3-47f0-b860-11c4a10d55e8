package com.lmig.uscm.booktransfer.opportunity.config.properties;

import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Getter
@ConfigurationProperties(prefix = "service")
public class ServiceUrlProperties {

	private final QuoteReportUrlProvider quoteReportUrlProvider;
	private final BookTransferUrlProvider bookTransferUrlProvider;
	private final QuotingAdapterUrlProvider quotingAdapterUrlProvider;
	private final TransformationUrlProvider transformationUrlProvider;
	private final UploadServiceUrlProvider uploadServiceUrlProvider;
	private final PaymentServiceUrlProvider paymentServiceUrlProvider;
	private final AddressCleanseUrlProvider addressCleanseUrlProvider;
	private final EFTPaymentAccountsUrlProvider eftPaymentAccountsUrlProvider;
	private final CustomerAccountUrlProvider customerAccountUrlProvider;
	private final PropertyInfoUrlProvider propertyInfoUrlProvider;

	public ServiceUrlProperties() {
		quoteReportUrlProvider = new QuoteReportUrlProvider();
		bookTransferUrlProvider = new BookTransferUrlProvider();
		quotingAdapterUrlProvider = new QuotingAdapterUrlProvider();
		paymentServiceUrlProvider = new PaymentServiceUrlProvider();
		transformationUrlProvider = new TransformationUrlProvider();
		uploadServiceUrlProvider = new UploadServiceUrlProvider();
		addressCleanseUrlProvider = new AddressCleanseUrlProvider();
		eftPaymentAccountsUrlProvider = new EFTPaymentAccountsUrlProvider();
		customerAccountUrlProvider = new CustomerAccountUrlProvider();
		propertyInfoUrlProvider = new PropertyInfoUrlProvider();
	}


  public static class UrlProvider implements BaseUrlInterface {
		private String baseUrl;

		public String getBaseUrl() {
			return this.baseUrl;
		}

		public void setBaseUrl(final String baseUrl) {
			this.baseUrl = baseUrl;
		}
	}

	public static class PathUrlProvider extends UrlProvider implements PathUrlInterface {
		private String path;

		public String getPath() {
			return this.path;
		}

		public void setPath(final String baseUrl) {
			this.path = baseUrl;
		}
	}

	public interface BaseUrlInterface {
		String getBaseUrl();

		void setBaseUrl(String baseUrl);
	}

	public interface PathUrlInterface {
		String getPath();

		void setPath(String path);
	}

	public static class BookTransferUrlProvider extends PathUrlProvider {
	}

	public static class QuoteReportUrlProvider extends PathUrlProvider {
	}

	public static class QuotingAdapterUrlProvider extends UrlProvider {
	}

	public static class TransformationUrlProvider extends UrlProvider {
	}

	public static class UploadServiceUrlProvider extends UrlProvider {
	}

	public static class PaymentServiceUrlProvider extends UrlProvider {
	}

	public static class AddressCleanseUrlProvider extends UrlProvider {
	}

	public static class EFTPaymentAccountsUrlProvider extends UrlProvider {
	}

	public static class CustomerAccountUrlProvider extends UrlProvider {
	}

	public static class PropertyInfoUrlProvider extends UrlProvider {
	}
}

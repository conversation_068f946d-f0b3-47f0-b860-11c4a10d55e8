package com.lmig.uscm.booktransfer.opportunity.config;

import com.lmig.idp.common.servlet.IdpSecurityAdapter;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpMethod;
import org.springframework.security.authorization.AuthorityAuthorizationManager;
import org.springframework.security.authorization.AuthorizationManagers;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;

@Configuration
@EnableWebSecurity
@Profile("!local")
public class SecurityConfig implements IdpSecurityAdapter {
    private static final String[] PUBLIC_PATHS = new String[]{"/public/**"};

    private static final String[] V3_GET_PATHS_READ = new String[]{"/v3/**"};
    private static final String[] V3_POST_PATHS_READ = new String[]{
            "/v3/opportunity/opportunityPageOppIds",
            "/v3/opportunity/getBookIdsForOppIds/**",
            "/v3/opportunity/changeOpportunitiesStatus/**",
            "/v3/opportunity/priorCarrierData/**",
            "/v3/opportunity/getOppIdsForSchedule/**",
    };
    private static final String[] V3_GET_OPPXML_NON_DETOKENIZE_PATHS = new String[]{
            "/v3/opportunity/getOpportunityForEdit**"
    };
    private static final String[] V3_GET_OPPXML_DETOKENIZE_PATHS = new String[]{
            "/v3/opportunity/**/xml**"
    };
    private static final String[] V3_POST_OPPXML_DETOKENIZE_PATHS = new String[]{
            "/v3/opportunity/getOpportunityForEdit/**",
            "/v3/opportunity/findByOpportunityId/**",
    };
    private static final String[] V3_PATHS_CREATE = new String[]{"**/v3/**", "/v3/**"};

    private static final String OPPORTUNITY_READ = "OPPORTUNITY.READ";
    private static final String OPPORTUNITY_CREATE = "OPPORTUNITY.CREATE";
    private static final String OPPORTUNITY_DETOKENIZE = "OPPORTUNITY.DETOKENIZE";

    @Override
    public void configure(HttpSecurity httpSecurity) throws Exception {
        httpSecurity.csrf(AbstractHttpConfigurer::disable).cors(Customizer.withDefaults())
                .authorizeHttpRequests(requests -> requests
                        // PUT
                        .requestMatchers(HttpMethod.PUT, V3_PATHS_CREATE)
                        .hasAuthority(OPPORTUNITY_CREATE)
                        // PATCH
                        .requestMatchers(HttpMethod.PATCH, V3_PATHS_CREATE)
                        .hasAuthority(OPPORTUNITY_CREATE)
                        // GET
                        .requestMatchers(HttpMethod.GET, V3_GET_OPPXML_NON_DETOKENIZE_PATHS).hasAuthority(OPPORTUNITY_READ)
                        .requestMatchers(HttpMethod.GET, V3_GET_OPPXML_DETOKENIZE_PATHS)
                        .access(AuthorizationManagers.allOf(
                                AuthorityAuthorizationManager.hasAuthority(OPPORTUNITY_READ),
                                AuthorityAuthorizationManager.hasAuthority(OPPORTUNITY_DETOKENIZE)))
                        .requestMatchers(HttpMethod.GET, V3_GET_PATHS_READ).hasAuthority(OPPORTUNITY_READ)
                        // POST
                        .requestMatchers(HttpMethod.POST, V3_POST_OPPXML_DETOKENIZE_PATHS)
                        .access(AuthorizationManagers.allOf(
                                AuthorityAuthorizationManager.hasAuthority(OPPORTUNITY_READ),
                                AuthorityAuthorizationManager.hasAuthority(OPPORTUNITY_DETOKENIZE)))
                        .requestMatchers(HttpMethod.POST, V3_POST_PATHS_READ).hasAuthority(OPPORTUNITY_READ)
                        .requestMatchers(HttpMethod.POST, V3_PATHS_CREATE).hasAuthority(OPPORTUNITY_CREATE)
                        .requestMatchers(PUBLIC_PATHS).permitAll()
                );
    }
}

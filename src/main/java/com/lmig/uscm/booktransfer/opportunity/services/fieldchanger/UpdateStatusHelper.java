package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeField;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class UpdateStatusHelper extends UpdateHelper {
	public UpdateStatusHelper(List<Opportunity> oppList, List<QuoteReportItemLegacy> quoteItemsList) {
		super(oppList, quoteItemsList);
	}

	@Override
	protected boolean isUpdatingField(String fieldName) {
		return StringUtils.equalsIgnoreCase(fieldName, "status");
	}

	@Override
	protected OppChangeFieldResult buildInitialResultForSpecificValue(OppChangeField oppChangeField, Opportunity opp) {
		String quoteReportOriginalStatus = getQuoteStatusMessageFromOppStatus(opp.getStatus());
		OppChangeFieldResult oppChangeFieldResult = new OppChangeFieldResult(opp, oppChangeField,
				String.valueOf(opp.getStatus()), quoteReportOriginalStatus);
		try {
			// right now this throws an error if something happens that shouldn't
			updateStatusOfOpp(oppChangeField, opp);
		} catch (Exception e) {
			oppChangeFieldResult.isError(e.getMessage());
		}
		return oppChangeFieldResult;
	}

	private String getQuoteStatusMessageFromOppStatus(int status) {
		String quoteReportOriginalStatus;
		try {
			quoteReportOriginalStatus =
					OpportunityStatus.getOppStatusFromCode(status).getQuoteReportStatus().getQuoteReportMessage();
		} catch (Exception e) {
			quoteReportOriginalStatus = "Invalid Status";
		}
		return quoteReportOriginalStatus;
	}

	/**
	 * Updates the status return voids if success. Throws exception if it cannot update field
	 */
	private void updateStatusOfOpp(OppChangeField oppChangeField, Opportunity opp) throws Exception {
		OpportunityStatus currentOpportunityStatus;
		try {
			currentOpportunityStatus = OpportunityStatus.getOppStatusFromCode(opp.getStatus());
		} catch (Exception e) {
			// if status is invalid in aqe we should let them change it to fix it.
			currentOpportunityStatus = OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED;
		}
		if (!isStatusUpdateValid(oppChangeField.getFieldValue())) {
			throw new Exception("Invalid Opp status to change too: " + oppChangeField.getFieldValue());

		} else if (currentOpportunityStatus == OpportunityStatus.OPPORTUNITY_STATUS_ISSUED) {
			throw new Exception("Status cannot be updated if policy has been issued");
		}
	}

	private boolean isStatusUpdateValid(String status) {
		OpportunityStatus opportunityStatus;
		try {
			opportunityStatus = OpportunityStatus.getOppStatusFromCode(Integer.parseInt(status));
		} catch (Exception e) {
			// invalid field to update it too
			return false;
		}
		return (opportunityStatus == OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN
				|| opportunityStatus == OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED
				|| opportunityStatus == OpportunityStatus.OPPORTUNITY_STATUS_QUOTE_CLEAN_UP
				|| opportunityStatus == OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_IMPORTED
				|| opportunityStatus == OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_PROCESSING
				|| opportunityStatus == OpportunityStatus.OPPORTUNITY_STATUS_HERITAGE);

	}

	@Override
	protected void saveFieldUpdate(final List<Opportunity> opportunities, String value) {
		try {
			OpportunityStatus opportunityStatus = OpportunityStatus.getOppStatusFromCode(Integer.parseInt(value));
			//Update customer accounts on change opportunity status
			List<Opportunity> commercialOpportunities = getCommercialOpportunities(opportunities, opportunityStatus) ;
			for (List<Opportunity> partition : ListUtils.partition(commercialOpportunities, OpportunityJDBCRepo.SQL_BATCH_SIZE)) {
				opportunityHelper.updateCustomerAccounts(partition, null);
			}

			for (List<Opportunity> partition : ListUtils.partition(opportunities, OpportunityJDBCRepo.SQL_BATCH_SIZE)) {
				opportunityRepoHelper.saveAll(partition);

				// Only get Personal LineType Opp Ids
				List<String> personalOppIdsAsString = partition.stream()
						.filter(opp -> LineType.Personal.equals(opp.getLineType()))
						.map(opp -> String.valueOf(opp.getOpportunityId()))
						.collect(Collectors.toList());

				// Update QRI for Personal LineType Opps
				quoteReportItemHelper.updateQuoteReportItems(personalOppIdsAsString,
						opportunityStatus.getQuoteReportStatus());
			}
		} catch (Exception e) {
			// this should not happen but if it does we cannot update these rows.
			// may want to think more about this course of action
			// This also should have already been checked
			throw new RuntimeException(e);
		}

	}

	protected List<Opportunity> getCommercialOpportunities(List<Opportunity> opportunities, OpportunityStatus opportunityStatus){
		List<Opportunity> commercialOpportunities = new ArrayList<>();
		for(Opportunity opp : opportunities){
			opp.setStatus(opportunityStatus.getOppStatusCode());
			if (LineType.Business.equals(opp.getLineType())) {
				commercialOpportunities.add(opp);
			}
		}
		return commercialOpportunities;
	}
}

package com.lmig.uscm.booktransfer.opportunity.repo.mappers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;

@Component
public class OpportunityForStatusChangeRowMapper implements RowMapper<Opportunity> {
    @Override
    public Opportunity mapRow(@NonNull ResultSet resultSet, int i) throws SQLException {

        final int databaseOpportunityId = resultSet.getInt("OPPORTUNITYID");
        final Integer databaseStatus = resultSet.getObject("status", Integer.class);
        final int databaseBookTransferId = resultSet.getInt("bookTransferID");
        final LineType databaseLineType = OpportunityUtil.defaultPersonalOrConvertStringToLineType(resultSet.getString("lineType"));

        final Integer status = defaultStatusIfNull(databaseStatus);

        final Opportunity opportunity = new Opportunity();
        opportunity.setOpportunityId(databaseOpportunityId);
        opportunity.setStatus(status);
        opportunity.setBookTransferID(databaseBookTransferId);
        opportunity.setLineType(databaseLineType);

        return opportunity;
    }

    protected Integer defaultStatusIfNull(final Integer status) {
        return status == null
                ? OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()
                : status;
    }
}

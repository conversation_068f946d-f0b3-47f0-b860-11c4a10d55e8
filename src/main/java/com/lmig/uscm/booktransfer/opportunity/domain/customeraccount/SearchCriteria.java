package com.lmig.uscm.booktransfer.opportunity.domain.customeraccount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchCriteria {
    private String customerAccountId;
    private String eCliqAccountNumber;
    private String priorCarrierPolicyNumber;
    private String btCode;
    private CustomerMetadata customerMetadata;
}

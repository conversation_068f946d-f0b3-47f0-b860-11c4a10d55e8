package com.lmig.uscm.booktransfer.opportunity.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;

@ConfigurationProperties(prefix = "oppservice.oauth2.quoting.guideline.client")
public class QuotingGuidelineOauth2ClientDetails extends ClientCredentialsResourceDetails {
}

/*
 * Copyright (C) 2018, Liberty Mutual Group
 *
 * Created on 11/23/18 by n0210477
 */
package com.lmig.uscm.booktransfer.opportunity.services.builders;

import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OriginSource;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.SourceType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.AddressCleanseService;
import com.lmig.uscm.booktransfer.opportunity.services.CustomerAccountHelper;
import com.lmig.uscm.booktransfer.opportunity.services.EFTPaymentAccountsService;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;

/**
 * Reupload the opportunity from the opportunity's original xml
 */
@Slf4j
public class ReuploadOpportunityBuilder extends OpportunityBuilder {

	public ReuploadOpportunityBuilder(
			final OpportunityRepoHelper opportunityRepoHelper,
			final OpportunityConstructor opportunityConstructor,
			final QuoteReportItemHelper quoteReportItemHelper,
			final AddressCleanseService addressCleanseHelper,
			final EFTPaymentAccountsService eftPaymentAccountsService,
			final CustomerAccountHelper customerAccountHelper,
			final boolean isForSPQE,
			final String importPackageId
	) {
		super(opportunityRepoHelper,
				opportunityConstructor,
				quoteReportItemHelper,
				addressCleanseHelper,
				eftPaymentAccountsService,
				customerAccountHelper,
				isForSPQE,
				importPackageId);
	}

	@Override
	protected Opportunity getOpportunity(final OpportunityCreationRequest opportunityCreationRequest)
			throws Exception {
		Opportunity opportunity = findExistingOpportunity(opportunityCreationRequest.getExistingOpportunityID());
		OpportunityStatus currentOpportunityStatus = OpportunityStatus.getOppStatusFromCode(opportunity.getStatus());
		if (doesOppIssuedOrWithDrawn(currentOpportunityStatus)) {
			throw new Exception("Opportunity cannot be reuploaded if policy has been issued or withdrawn");
		}

		setBtMetaFromPreviousBeforeReplacement(opportunityCreationRequest, opportunity);

		if (super.isForSPQE || opportunityCreationRequest.isShouldReplaceOriginalXml()) {
			opportunity.setOriginalXML(XmlHelper.getDocumentString(opportunityCreationRequest.getUploadedACORD()));
		}

		return opportunity;
	}

	/**
	 * Any Book Transfer specific information from the Opportunity of the soon-to-be reuploaded can be added to the opportunityCreationRequest here
	 */
	private static void setBtMetaFromPreviousBeforeReplacement(OpportunityCreationRequest opportunityCreationRequest, Opportunity opportunity) throws OpportunityException {
		try {
			Document foundOriginalDoc = XmlHelper.getDocument(opportunity.getOriginalXML());
			String foundOrigin = AcordHelper.getBTMetaCreationOrigin(foundOriginalDoc);
			String foundSourceType = AcordHelper.getBTMetaCreationType(foundOriginalDoc);
			opportunityCreationRequest.setOriginSource(OriginSource.find(foundOrigin));
			opportunityCreationRequest.setSourceType(SourceType.find(foundSourceType));
		} catch (XPathExpressionException | ParserConfigurationException | IOException | SAXException e) {
			throw new OpportunityException("Couldnt extract information from current Opportunity before re-upload begins", e);
		}
	}

	private boolean doesOppIssuedOrWithDrawn(OpportunityStatus currentOpportunityStatus) {
		return currentOpportunityStatus == OpportunityStatus.OPPORTUNITY_STATUS_ISSUED
				|| currentOpportunityStatus == OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN;
	}

	@Override
	protected Opportunity updateMasterOpp(boolean isMasterOpp, Opportunity opportunity) {
		// for reupload, we don't want to check whether it is a master opp or not so just
		// return the opportunity
		return opportunity;
	}

	private Opportunity findExistingOpportunity(int existingOppId) {
		Opportunity newOpportunity = opportunityRepoHelper.findOpportunityById(existingOppId);
		log.info("Opportunity Id - {}", newOpportunity.getOpportunityId());
		return newOpportunity;
	}
}

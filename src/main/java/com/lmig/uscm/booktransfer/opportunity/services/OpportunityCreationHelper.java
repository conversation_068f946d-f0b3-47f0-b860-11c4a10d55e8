package com.lmig.uscm.booktransfer.opportunity.services;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferServiceWebClient;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OriginSource;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.SourceType;

import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.FileContent;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunitySensitiveDataException;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunityTopicException;
import com.lmig.uscm.booktransfer.opportunity.messaging.Publisher;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.builders.BLUploadOpportunityBuilder;
import com.lmig.uscm.booktransfer.opportunity.services.builders.OpportunityBuilder;
import com.lmig.uscm.booktransfer.opportunity.services.builders.OpportunityConstructor;
import com.lmig.uscm.booktransfer.opportunity.services.builders.ReuploadOpportunityBuilder;
import com.lmig.uscm.booktransfer.opportunity.services.builders.UploadOpportunityBuilder;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.CreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.CreationStrategyFactory;
import com.lmig.uscm.booktransfer.uploadmanager.client.domain.UploadEvent;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.w3c.dom.Document;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPathExpressionException;

import org.w3c.dom.Element;

import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;

import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * helper class for opportunity creation controller which initializes all the objects
 */
@Slf4j
public class OpportunityCreationHelper {
	public static final String MAIN_STREET_AMERICA = "Main Street America";
	public static final int MSA_DEFAULT_BOOK = 1103;
	private final OpportunityRepoHelper opportunityRepoHelper;
	private final OpportunityConstructor opportunityConstructor;
	private final OpportunityConstructor businessOpportunityConstructor;
	private final QuoteReportItemHelper quoteReportItemHelper;
	private final Publisher publisher;
	private final BookTransferServiceWebClient bookTransferService;
	private final CreationStrategyFactory creationStrategyFactory;
	private final OpportunityEventHelper opportunityEventHelper;
	private final UploadEventService uploadEventService;
	private final TransformationService transformationService;
	private final SensitiveDataHelper sensitiveDataHelper;
	private final AddressCleanseService addressCleanseService;
	private final EFTPaymentAccountsService eftPaymentAccountsService;
	private final CustomerAccountHelper customerAccountHelper;

	@Value(value = "${transformation.import.packageId}")
	public String IMPORT_PACKAGE;

	public OpportunityCreationHelper(
		final OpportunityRepoHelper opportunityRepoHelper,
		final OpportunityConstructor opportunityConstructor,
		final OpportunityConstructor businessOpportunityConstructor,
		final QuoteReportItemHelper quoteReportItemHelper,
		final Publisher publisher,
		final BookTransferServiceWebClient bookTransferService,
		final OpportunityEventHelper opportunityEventHelper,
		final CreationStrategyFactory creationStrategyFactory,
		final UploadEventService uploadEventService,
		final TransformationService transformationService,
		final SensitiveDataHelper sensitiveDataHelper,
		final AddressCleanseService addressCleanseService,
		final EFTPaymentAccountsService eftPaymentAccountsService,
		final CustomerAccountHelper customerAccountHelper
	) {
		this.opportunityConstructor = opportunityConstructor;
		this.businessOpportunityConstructor = businessOpportunityConstructor;
		this.opportunityRepoHelper = opportunityRepoHelper;
		this.quoteReportItemHelper = quoteReportItemHelper;
		this.publisher = publisher;
		this.bookTransferService = bookTransferService;
		this.creationStrategyFactory = creationStrategyFactory;
		this.opportunityEventHelper = opportunityEventHelper;
		this.uploadEventService = uploadEventService;
		this.transformationService = transformationService;
		this.sensitiveDataHelper = sensitiveDataHelper;
		this.addressCleanseService = addressCleanseService;
		this.eftPaymentAccountsService = eftPaymentAccountsService;
		this.customerAccountHelper = customerAccountHelper;
	}

	public OpportunityCreationResponse createOpportunityFromOpportunityRequest(
			final boolean isForSPQE, final OpportunityCreationRequest opportunityCreationRequest)
			throws OpportunityException, OpportunityTopicException {
		OpportunityCreationResponse creationResponse;

		try {
			log.info("Creating opportunity with NBD: " +  opportunityCreationRequest.getNbdRelationship());
			if(isForSPQE && opportunityCreationRequest.getSFDCID() == null) {
				BookTransferDTO bookTransfer = bookTransferService.findByBookTransferId(opportunityCreationRequest.getBookTransferID());
				opportunityCreationRequest.setSFDCID(bookTransfer.getSfdcid());
				opportunityCreationRequest.setNbdRelationship(bookTransfer.getNBDRelationship());
			}
			creationResponse = runRequestThroughStrategies(isForSPQE, opportunityCreationRequest);
		} catch (Exception e) {
			final OpportunityException opportunityException = new OpportunityException(e);
			log.error("Opportunity Creation exception", opportunityException);
			throw opportunityException;
		}

		return creationResponse;
	}

	public OpportunityCreationResponse runRequestThroughStrategies(final boolean isForSPQE, final OpportunityCreationRequest opportunityCreationRequest) {
		final OpportunityCreationResponse creationResponse = new OpportunityCreationResponse();

		final List<CreationStrategyBundle> splitCreationRequestBundles = new ArrayList<>();
		splitCreationRequestBundles.add(new CreationStrategyBundle(opportunityCreationRequest));

		// Run through splitting strategies
		for (CreationStrategy strategy : creationStrategyFactory.getAllStrategies()) {
			strategy.resolveCreationBundles(splitCreationRequestBundles, creationResponse);
		}

		// Create Opportunities for each split request
		for (CreationStrategyBundle creationBundle : splitCreationRequestBundles) {
			Iterator<OpportunityCreationRequest> requestIterator = creationBundle.getRequests().iterator();
			int previousFailures = creationResponse.getFailedCreationCount();
			int previousSuccesses = creationResponse.getSuccessfulCreationCount();
			Integer masterOppId = null;

			while (requestIterator.hasNext()) {
				OpportunityCreationRequest currentRequest = requestIterator.next();
				currentRequest.setMasterOppID(masterOppId);
				Opportunity createdOpp = new Opportunity();
				try {
					createdOpp = uploadOpportunityAndUpdateCreationResponse(isForSPQE, currentRequest, creationResponse);
					if (masterOppId == null) {
						masterOppId = createdOpp.getOpportunityId();
						creationResponse.setAssociatedBookTransferId(createdOpp.getBookTransferID());
					}
				} catch (OpportunityException e) {
					// If any opps fail in the bundle, mark all as failures (even successful ones)
					creationResponse.setFailedCreationCount(previousFailures + creationBundle.getRequests().size());
					creationResponse.setSuccessfulCreationCount(previousSuccesses);
					creationResponse.addExceptions(e);
					continue;
				}
			}
		}
		return creationResponse;
	}

	/**
	 * Creates an Opportunity from the passed request and will track successes/fails on the passed response
	 */
	public Opportunity uploadOpportunityAndUpdateCreationResponse(final boolean isForSPQE, final OpportunityCreationRequest request, final OpportunityCreationResponse response) throws OpportunityException {
		try {
			OpportunityBuilder opportunityBuilder = opportunityBuilderFactory(isForSPQE, request);
			Opportunity createdOpp = opportunityBuilder.uploadOpportunity(request);
			response.incrementSuccessfulCreationCount();
			response.addAssociatedOppId(createdOpp.getOpportunityId());
			return createdOpp;
		} catch (Exception e) {
			log.error("Could not create opportunity from request: {}", request, e);
			response.incrementFailedCreationCount();
			throw new OpportunityException(e);
		}
	}

	OpportunityBuilder opportunityBuilderFactory(boolean isForSPQE,
												 OpportunityCreationRequest opportunityCreationRequest) {
		// Business Lines
		if (LineType.Business.equals(opportunityCreationRequest.getLineType())) {
			if(opportunityCreationRequest.hasExistingOpportunityId()){
				return new ReuploadOpportunityBuilder(opportunityRepoHelper, businessOpportunityConstructor,
						quoteReportItemHelper, addressCleanseService, eftPaymentAccountsService, customerAccountHelper,
						isForSPQE, IMPORT_PACKAGE);
			}
			return new BLUploadOpportunityBuilder(opportunityRepoHelper, businessOpportunityConstructor,
					quoteReportItemHelper, addressCleanseService, eftPaymentAccountsService, customerAccountHelper,
					isForSPQE, IMPORT_PACKAGE);
		}
		// Personal Lines
		if (opportunityCreationRequest.hasExistingOpportunityId()
				|| opportunityCreationRequest.hasMasterOppSPQEMetaData()) {
			return new ReuploadOpportunityBuilder(opportunityRepoHelper, opportunityConstructor,
					quoteReportItemHelper, addressCleanseService, eftPaymentAccountsService, customerAccountHelper,
					isForSPQE, IMPORT_PACKAGE);
		}
		return new UploadOpportunityBuilder(opportunityRepoHelper, opportunityConstructor,
				quoteReportItemHelper, addressCleanseService, eftPaymentAccountsService, customerAccountHelper,
				isForSPQE, IMPORT_PACKAGE);
	}

	/**
	 * update the opportunity data xml with the original xml after all the required rules applied. Get original xmls for
	 * the given list of opportunity ids, create request with oppid and original xml.
	 */
	public void reuploadOpportunities(List<Integer> oppIds) {
		oppIds.parallelStream().forEach(opportunityId ->
			publisher.sendRequestToReUploadOpportunity(String.valueOf(opportunityId))
		);
	}

	/**
	 * reupload the opportunity by updating data xml with the original xml after the required rules applied
	 */
	public void reuploadOpportunity(final int opportunityId) throws Exception {
		log.info("opportunityId - {}", opportunityId);
		try {
			Opportunity foundOpp = opportunityRepoHelper.findOpportunityById(opportunityId);

			OpportunityCreationRequest opportunityCreationRequest = createReUploadRequestFromOpportunity(foundOpp);

			createOpportunityFromOpportunityRequest(false, opportunityCreationRequest);
		} catch (Exception e) {
			log.error("Reupload Exception", e);
			throw (e);
		}
	}

	/**
	 * In the case that we need to split a motorcycle from an auto opportunity on reupload,
	 * make sure the creationRequest has the auto opportunity's OriginalXml, uploadEventId, priorCarrier, etc.
	 *
	 * @param foundOpp Opportunity we are going to ReUpload
	 * @return OpportunityCreationRequest specifying ReUpload builder
	 */
	protected OpportunityCreationRequest createReUploadRequestFromOpportunity(Opportunity foundOpp) throws Exception {
		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		if (foundOpp != null) {
			try {
				Document foundOppOriginalXml = XmlHelper.getDocument(foundOpp.getOriginalXML());
				opportunityCreationRequest.setUploadedACORD(foundOppOriginalXml);
				opportunityCreationRequest.setBookTransferID(foundOpp.getBookTransferID());
				opportunityCreationRequest.setExistingOpportunityID(foundOpp.getOpportunityId());
				opportunityCreationRequest.setLineType(foundOpp.getLineType());
				opportunityCreationRequest.setData(XmlHelper.getDocument(foundOpp.getData()));
				opportunityCreationRequest.setUploadEventID(foundOpp.getUploadEventID());
				opportunityCreationRequest.setPriorCarrier(foundOpp.getPriorCarrierGuid());

				BookTransferDTO bookTransfer = bookTransferService.findByBookTransferId(foundOpp.getBookTransferID());
				opportunityCreationRequest.setSubCode(bookTransfer.getSubCode());
				opportunityCreationRequest.setSFDCID(bookTransfer.getSfdcid());
				opportunityCreationRequest.setNbdRelationship(bookTransfer.getNBDRelationship());
			} catch (Exception e) {
				log.error("Could not create ReUpload request from existing Opp id {}: ", foundOpp.getOpportunityId(), e);
				throw e;
			}
		}
		return opportunityCreationRequest;
	}

	/**
	 * Asynchronous function to create Opportunities based on the passed OpportunityCreationRequest.
	 * If the passed allFilesContents is null, it is assumed that the OpportunityCreationRequest.uploadedAcord is populated.
	 * Otherwise, create subsequent OpportunityCreationRequests for each String in allFilesContents
	 * <p>
	 * Updates the status of the UploadEvent associated with the OpportunityCreationRequest throughout the process
	 *
	 * @throws OpportunityException when a critical failure occurs and the Opportunity creation status is unknown
	 */
	@Async
	public Future<OpportunityCreationResponse> createOpportunityFromLargeTransferOpportunityRequest(final List<FileContent> allFilesContents, final OpportunityCreationRequest opportunityCreationRequest, final String tePackageId) throws OpportunityException {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		try {
			final BookTransferDTO associatedBookTransfer = bookTransferService.getTopBySFDCIDOrderByBookTransferID(opportunityCreationRequest.getSFDCID());
			opportunityCreationRequest.setBookTransferID(associatedBookTransfer.getBookTransferID());
			opportunityCreationRequest.setOriginSource(OriginSource.LTU);
			opportunityCreationRequest.setSourceType(SourceType.XML_EXTRACTION);
			opportunityCreationRequest.setNbdRelationship(associatedBookTransfer.getNBDRelationship());
			response = createOpportunitiesForAllContent(allFilesContents, opportunityCreationRequest, associatedBookTransfer, tePackageId);
		} catch (OpportunityException | BookTransferException e) {
			response.addExceptions(e);
			response.incrementFailedCreationCount();
			log.error("Failed to create Opportunities from Large Transfer {}", response, e);
			throw new OpportunityException(e);
		}

		return new AsyncResult<>(response);
	}

	private OpportunityCreationResponse createOpportunitiesForAllContent(List<FileContent> allFilesContents,
																																			 OpportunityCreationRequest parentRequest,
																																			 BookTransferDTO bookTransfer, String tePackageId)
		throws OpportunityException, BookTransferException {
		if (allFilesContents == null) {
			throw new OpportunityException("No file content uploaded", HttpStatus.BAD_REQUEST.value());
		}

		BookRecord bookRecord = getBooksForAgentIds(allFilesContents, parentRequest.getFileName());

		OpportunityCreationResponse parentResponse = new OpportunityCreationResponse();
		for (FileContent fileContent : allFilesContents) {
			BookTransferDTO associatedBookTransfer = getBookTransfer(bookTransfer, fileContent, bookRecord.bookTransferMap(), bookRecord.defaultBookTransfer());

			// Create new UploadEvent for this file
			UploadEvent perFileUploadEvent = createLargeTransferUploadEvent(
				associatedBookTransfer.getBookTransferID(), associatedBookTransfer.getAgentNum(), fileContent.getFileName(), parentRequest.getNNumber());
			// Create Opportunities from this file
			OpportunityCreationRequest childRequest = parentRequest.clone();
			childRequest.setUploadEventID(perFileUploadEvent.getUploadEventID());
			childRequest.setBookTransferID(associatedBookTransfer.getBookTransferID());
			childRequest.setSFDCID(associatedBookTransfer.getSfdcid());
			childRequest.setSubCode(associatedBookTransfer.getSubCode());
			OpportunityCreationResponse childResponse =
					transformAndSensitizeContentThenCreateOpportunities(childRequest, fileContent, tePackageId);
			// Update associated UploadEvent
			completeLargeTransferUploadEvent(perFileUploadEvent.getUploadEventID(), childResponse);
			// Merge creation data into final response
			updateParentCreationResponseWithChildResponse(parentResponse, childResponse);
		}

		return parentResponse;
	}

	BookRecord getBooksForAgentIds(List<FileContent> allFilesContents, String fileName) {
		Map<String, BookTransferDTO> bookTransferMap = new HashMap<>();
		BookTransferDTO defaultBookTransfer = null;

		if (fileName != null && fileName.contains(MAIN_STREET_AMERICA)) {
			try {
				defaultBookTransfer = bookTransferService.getTopBySFDCIDOrderByBookTransferID(MSA_DEFAULT_BOOK);

				Set<String> agentNumbers = allFilesContents.stream()
					.map(fileContent -> {
						try {
							Document document = XmlHelper.getDocument(fileContent.getContent());
							return AcordHelper.getProducerSubCode(document);
						} catch (ParserConfigurationException | SAXException | IOException |
										 XPathExpressionException e) {
							log.error("Error parsing agent number from file content", e);
							return null;
						}
					})
					.filter(StringUtils::isNotEmpty)
					.collect(Collectors.toSet());

				if(!agentNumbers.isEmpty()) {
					bookTransferMap = bookTransferService.getBookTransfersForMultipleAgencyIdsAndCarrier(agentNumbers, MAIN_STREET_AMERICA)
						.orElseThrow();
				}

			} catch (BookTransferException | NoSuchElementException e) {
				log.error("No book transfer found for agency ids", e);
			}
		}

		return new BookRecord(bookTransferMap, defaultBookTransfer);
	}

	record BookRecord(Map<String, BookTransferDTO> bookTransferMap, BookTransferDTO defaultBookTransfer) {
	}

	BookTransferDTO getBookTransfer(BookTransferDTO bookTransfer,
																	FileContent fileContent,
																	Map<String, BookTransferDTO> bookTransferMap,
																	BookTransferDTO defaultBookTransfer) {
		BookTransferDTO associatedBookTransfer = null;
		if(fileContent.getFileName().contains(MAIN_STREET_AMERICA)) {
			try {
				associatedBookTransfer = bookTransferMap.getOrDefault(AcordHelper.getProducerSubCode(XmlHelper.getDocument(fileContent.getContent())),
					defaultBookTransfer);
			} catch (XPathExpressionException | ParserConfigurationException | SAXException | IOException e) {
				log.error("Failed to get agent number from xml", e);
			}
		}

		return associatedBookTransfer != null ? associatedBookTransfer : bookTransfer;
	}

	private void updateParentCreationResponseWithChildResponse(final OpportunityCreationResponse parentResponse, final OpportunityCreationResponse childResponse) {
		parentResponse.setFailedCreationCount(parentResponse.getFailedCreationCount() + childResponse.getFailedCreationCount());
		parentResponse.setSuccessfulCreationCount(parentResponse.getSuccessfulCreationCount() + childResponse.getSuccessfulCreationCount());
		childResponse.getExceptions().forEach(parentResponse::addExceptions);
	}

	/**
	 * Sensitizes the passed FileContent after running the passed Transformation Engine package (if non-null)
	 * and then creates Opportunities. Will mark the returned response
	 * appropriately for caller to handle (successes, failures, and exceptions)
	 */
	private OpportunityCreationResponse transformAndSensitizeContentThenCreateOpportunities(OpportunityCreationRequest opportunityCreationRequest, FileContent fileContent, String tePackageId) {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		try {
			Document document = XmlHelper.getDocument(fileContent.getContent());

			if (StringUtils.isNotEmpty(tePackageId)) {
				document = transformationService.runRules(document, tePackageId, null);
			}

			this.sensitiveDataHelper.tokenizeXmlWithEnvCheck(document);

			opportunityCreationRequest.setUploadedACORD(document);
			response = createOpportunityFromOpportunityRequest(false, opportunityCreationRequest);
		} catch (Exception e) {
			// Could not create Opportunity for any reason
			response.incrementFailedCreationCount();
			response.addExceptions(e);
			log.error("Failed to create Opportunities from Large Transfer {}", response, e);
		}
		return response;
	}

	/**
	 * Updates the UploadEvent status based on the number of successes and failures
	 */
	private void completeLargeTransferUploadEvent(Integer bulkUploadEventId, OpportunityCreationResponse response) throws OpportunityException {
		if (response.getFailedCreationCount() == 0 && response.getSuccessfulCreationCount() > 0) {
			uploadEventService.updateUploadEventStatus(bulkUploadEventId, "Split");
		} else if (response.getSuccessfulCreationCount() > 0) {
			uploadEventService.updateUploadEventStatus(bulkUploadEventId, "Split With Errors");
		} else if (doesResponseContainSensitiveDataException(response)) {
			uploadEventService.updateUploadEventStatus(bulkUploadEventId, "Failed - Unprotected Data");
		} else {
			uploadEventService.updateUploadEventStatus(bulkUploadEventId, "Failed");
		}
	}

	private boolean doesResponseContainSensitiveDataException(OpportunityCreationResponse response) {
		return response.getExceptions().stream().anyMatch(ex -> StringUtils.equals(ex.getClass().getSimpleName(), OpportunitySensitiveDataException.class.getSimpleName()));
	}

	/**
	 * Creates a new UploadEvent associated with the passed bookTransferID
	 */
	public UploadEvent createLargeTransferUploadEvent(final Integer bookTransferID, final String agentNumber, final String fileName, final String nNumber) throws OpportunityException {
		UploadEvent uploadEvent = new UploadEvent();
		uploadEvent.setStatus("Processing");
		uploadEvent.setBookTransferID(bookTransferID);
		uploadEvent.setNnumber(nNumber);
		uploadEvent.setAgentNum(agentNumber);
		uploadEvent.setFileName(fileName);

		int createdId = uploadEventService.createUploadEvent(uploadEvent);
		uploadEvent.setUploadEventID(createdId);

		return uploadEvent;
	}

	public void updateFileContents(List<FileContent> allFilesContents) throws ParserConfigurationException, SAXException, IOException, TransformerException {
		DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
		DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
		TransformerFactory tf = TransformerFactory.newInstance();
		Transformer transformer = tf.newTransformer();

		for (FileContent fileContent : allFilesContents) {
			String filename = fileContent.getFileName();
			String content = fileContent.getContent();

			if (filename.toLowerCase().contains("bypass-scrubbing")) {
				Document doc = dBuilder.parse(new InputSource(new StringReader(content)));
				NodeList insuranceSvcRqNodes = doc.getElementsByTagName("InsuranceSvcRq");

				for (int i = 0; i < insuranceSvcRqNodes.getLength(); i++) {
					Element insuranceSvcRqNode = (Element) insuranceSvcRqNodes.item(i);

					// Only add the attribute if it's not already present
					if (!insuranceSvcRqNode.hasAttribute("BypassScrubbing")) {
						insuranceSvcRqNode.setAttribute("BypassScrubbing", "SkipNaming");
					}
				}

				StringWriter writer = new StringWriter();
				transformer.transform(new DOMSource(doc), new StreamResult(writer));
				String updatedContent = writer.getBuffer().toString();

				fileContent.setContent(updatedContent);
			}
		}
	}
}

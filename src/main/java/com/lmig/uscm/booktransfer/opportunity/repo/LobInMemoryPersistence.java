package com.lmig.uscm.booktransfer.opportunity.repo;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.PolicyType;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Get Lobs from In-Memory(LineOfBusiness Enum)
 */
public class LobInMemoryPersistence implements LobGateway {

    /**
     * Gte all lobs
     * @return a map with line type to lobs
     * @example {"Personal": ["AUTOP"],"Business": ["WORK"]}
     */
    @Override
    public Map<String, List<String>> getAllLobs() {
        Map<String, List<String>> lineTypeToLobsMap = Arrays.stream(LineOfBusiness.values())
                .collect(Collectors.groupingBy(s -> s.getLineType().name(),
                        Collectors.mapping(Enum::name, Collectors.toList())));
        lineTypeToLobsMap.get(LineType.Personal.name()).addAll(
                Arrays.stream(
                        PolicyType.values())
                        .map(Enum::name)
                        .collect(Collectors.toList()));
        return lineTypeToLobsMap;
    }
}

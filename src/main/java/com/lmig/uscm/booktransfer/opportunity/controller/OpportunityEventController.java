package com.lmig.uscm.booktransfer.opportunity.controller;


import com.lmig.uscm.booktransfer.opportunity.auditing.SplunkName;
import com.lmig.uscm.booktransfer.opportunity.domain.OpportunityEvent;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityEventRepo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * This is mainly a Utility Controller for devs to debug and interact with
 * The Infrastructure around OpportunityEvent(s), OpportunityTopic
 */
@CrossOrigin
@RestController
@RequestMapping(value = "/opportunityevent")
public class OpportunityEventController {

	private final OpportunityEventRepo opportunityEventRepo;

	public OpportunityEventController(
			final OpportunityEventRepo opportunityEventRepo
	) {
		this.opportunityEventRepo = opportunityEventRepo;
	}

	/**
	 * Will save an OpportunityEvent record to the OppEvent Mongo database
	 * This is useful for testing saving of records to the DB.
	 * Saving of a record will publish to our Kafka Opp Topic 
	 * and create or update corresponding SalesForce Quote Entries
	 * @param opportunityEvent
	 */
	@PostMapping(value = "/process/event")
	@Operation(summary = "Process an opportunity event")
	@SplunkName(("POST - Process an Opportunity Event"))
	public HttpStatus processOpportunityEvent(@RequestBody OpportunityEvent opportunityEvent) {
		try {
			opportunityEventRepo.save(opportunityEvent);
			return HttpStatus.I_AM_A_TEAPOT;
		} catch (final Exception e) {
			return HttpStatus.INTERNAL_SERVER_ERROR;
		}
	}
}

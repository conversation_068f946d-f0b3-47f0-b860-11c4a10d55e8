package com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Address {
    private String addressLine;
    private String addressLine2;
    private String addressLastLine;
    private String city;
    private String state;
    private String country;
    private String countyName;
    private String firmName;
    private PostalCodeInfo postalCodeInfo;
}

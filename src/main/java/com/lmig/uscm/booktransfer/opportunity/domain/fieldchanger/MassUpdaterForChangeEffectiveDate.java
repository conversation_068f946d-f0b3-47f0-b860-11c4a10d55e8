package com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger;

import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * This class is used to hide the map the map customers package to the
 * oppsResult it looks like
 */
public class MassUpdaterForChangeEffectiveDate {
	// This map should work as following {fieldNameToUpdate: {fildValueToUpdateToo:
	// List<OppChangeFieldResult>ToUpdate}}
	public Map<String, List<OppChangeFieldResult>> fieldNamesToFieldValuesToOppChangeResult = new HashMap<>();

	public void addOpportunityChangeResult(OppChangeFieldResult oppChangeFieldResult) {
		List<OppChangeFieldResult> oppChanges;
		String key = getKeyForCustomerMap(oppChangeFieldResult.getOpportunity());
		if (fieldNamesToFieldValuesToOppChangeResult.containsKey(key)) {
			oppChanges = fieldNamesToFieldValuesToOppChangeResult.get(key);
		} else {
			oppChanges = new ArrayList<>();
		}

		oppChanges.add(oppChangeFieldResult);
		fieldNamesToFieldValuesToOppChangeResult.put(key, oppChanges);
	}

	public Iterable<String> getAllCustomPackagePairingKey() {
		return fieldNamesToFieldValuesToOppChangeResult.keySet();
	}

	public List<OppChangeFieldResult> getAllNotFailedOpportunityResults(String key) {
		List<OppChangeFieldResult> oppChangeFieldResults = fieldNamesToFieldValuesToOppChangeResult.get(key);
		List<OppChangeFieldResult> notFailedOppsResults = new ArrayList<>();
		for (OppChangeFieldResult oppChangeFieldResult : oppChangeFieldResults) {
			if (!oppChangeFieldResult.didFail()) {
				notFailedOppsResults.add(oppChangeFieldResult);
			}
		}
		return notFailedOppsResults;
	}

	public List<OppChangeFieldResult> getAllOpportunityResults() {
		List<OppChangeFieldResult> ret = new ArrayList<>();
		for (List<OppChangeFieldResult> results : fieldNamesToFieldValuesToOppChangeResult.values()) {
			ret.addAll(results);
		}
		return ret;
	}

	// we check match based on agency id and book as agency id might repeat outside
	// book and agency id is old agency id for the customer
	private String getKeyForCustomerMap(Opportunity opp) {
		return opp.getAgencyId() + opp.getBookTransferID();
	}
}

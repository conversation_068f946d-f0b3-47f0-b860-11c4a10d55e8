package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;

public class HomePreview extends Preview {
	private String completeAddress;

	private String covA;
	private String policyTypeCd;

	@Override
	public LineOfBusiness getLob() {
		return LineOfBusiness.HOME;
	}

	public String getCovA() {
		return covA;
	}

	public void setCovA(String covA) {
		this.covA = covA;
	}

	public String getPolicyTypeCd() {
		return policyTypeCd;
	}

	public void setPolicyTypeCd(String policyTypeCd) {
		this.policyTypeCd = policyTypeCd;
	}

	public String getCompleteAddress() {
		return completeAddress;
	}

	public void setCompleteAddress(String completeAddress) {
		this.completeAddress = completeAddress;
	}

	@Override
	protected void setLOBRequiredData(Document originalXml, PreviewDataResponse response)
			throws ParserConfigurationException, SAXException, XPathExpressionException, IOException {
		setDwellCoverage(originalXml, response);
		setPolicyForm(originalXml, response);
		this.setCompleteAddress(buildAddress(
				AcordHelper.getLocationAddr(originalXml)));
	}

	private void setPolicyForm(Document originalXml, PreviewDataResponse response)
			throws XPathExpressionException {
		String poliyForm = AcordHelper.getPolicyTypeCd(originalXml);
		if (StringUtils.isBlank(poliyForm)) {
			response.addMissingDataPoint("PolicyTypeCd");
		} else {
			this.setPolicyTypeCd(poliyForm);
		}
	}

	private void setDwellCoverage(Document originalXml, PreviewDataResponse response)
			throws XPathExpressionException {
		String covALimit = AcordHelper.getCoverageDwellLimit(originalXml);
		if (StringUtils.isBlank(covALimit)) {
			response.addMissingDataPoint("CovA");
		} else {
			this.setCovA(covALimit);
		}
	}

}

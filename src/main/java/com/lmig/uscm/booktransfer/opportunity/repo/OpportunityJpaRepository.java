package com.lmig.uscm.booktransfer.opportunity.repo;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Created by n0142149 on 10/20/2017.
 */
public interface OpportunityJpaRepository extends JpaRepository<Opportunity, Integer> {

	Opportunity findByOpportunityId(int opportunityId);

	List<Opportunity> findByOpportunityIdIn(List<Integer> oppIdList);

	@Transactional
	@Modifying
	@Query("Update Opportunity" + " Set status = :status" + " Where opportunityId in :oppIds")
	void updateOpportunityStatus(@Param("oppIds") List<Integer> oppIds, @Param("status") int status);

	@Query("Select new com.lmig.uscm.booktransfer.opportunity.domain.Opportunity(opp.opportunityId,opp.status,opp.effectiveDate,opp.state,opp.businessType,opp.nAICCd,opp.customerName,opp.priorPremium,opp.lastQuotedPremium,opp.lineType,opp.billingAccountNumber,opp.policyType) FROM Opportunity opp WHERE opp.lineType = :lineType")
	Page<Opportunity> getOpportunities(Pageable pageable, @Param("lineType") LineType lineType);

	@Query("Select new com.lmig.uscm.booktransfer.opportunity.domain.Opportunity(opp.opportunityId,opp.status,opp.effectiveDate,opp.state,opp.businessType,opp.nAICCd,opp.customerName,opp.priorPremium,opp.lastQuotedPremium,opp.lineType,opp.billingAccountNumber,opp.policyType) FROM Opportunity opp")
	Page<Opportunity> getOpportunities(Pageable pageable);

}

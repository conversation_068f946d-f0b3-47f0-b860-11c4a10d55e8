package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.AddressCleanseResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.MultipleUnparsedAddressRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
public class AddressCleanseHelper {
    private final WebClient addressCleanseClient;
    private final String url;

    public AddressCleanseHelper(final WebClient addressCleanseClient, final String url) {
        this.addressCleanseClient = addressCleanseClient;
        this.url = url != null ? url : "";
    }

    public AddressCleanseResponse sendRequest(MultipleUnparsedAddressRequest request) {
        log.info("Address Request - {}", request);
        return addressCleanseClient
                .post()
                .uri(url + "/v1/multiple-unparsed-address")
                .body(BodyInserters.fromValue(request))
                .retrieve()
                .onStatus(HttpStatusCode::is4xxClientError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody -> {
                                    log.error("Address Service Bad request: {}", errorBody);
                                    return Mono.empty();
                                })
                )
                .onStatus(HttpStatusCode::is5xxServerError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody -> {
                                    log.error("AddressService failure: {}", errorBody);
                                    return Mono.empty();
                                })
                )
                .onStatus(HttpStatus.NO_CONTENT::equals, response -> {
                    log.info("No content response from Address data service");
                    return Mono.empty();
                })
                .bodyToMono(AddressCleanseResponse.class)
                .block();
    }
}

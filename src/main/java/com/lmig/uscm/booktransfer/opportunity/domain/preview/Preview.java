package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;

@Slf4j
public abstract class Preview {

	public Preview() {
	}

	protected abstract void setLOBRequiredData(Document originalXml, PreviewDataResponse response)
			throws ParserConfigurationException, SAXException, XPathExpressionException, IOException;

	public abstract LineOfBusiness getLob();

	/**
	 * Build a preview data for the given opportunity with the required data, quote
	 * result and opportunity details to display them in the AQE UI
	 *
	 * @return preview data
	 */
	public static Preview buildPreview(Document acordXml, PreviewDataResponse response, LineType lineType) throws Exception {
		Preview preview;
		LineOfBusiness lobFromRequest = LineOfBusiness.determineLobForPersonalLineType(acordXml);
		//Using lineType to separate LOB Xpaths
		if (lineType == LineType.Business) {
			 lobFromRequest = LineOfBusiness.determineLobForBusinessLineType(acordXml);
		}
		switch (lobFromRequest) {
			case MTR:
			case AUTOP:
				preview = new AutoPreview();
				break;
			case HOME:
				preview = new HomePreview();
				break;
			case DFIRE:
				preview = new FirePreview();
				break;
			case UMBRP:
				preview = new UMBRPPreview();
				break;
			case BOAT:
				preview = new WaterCraftPreview();
				break;
			case WORK:
				preview = new WorkPreview();
				break;
			default:
				throw new RuntimeException("Cannot Find type lob of " + lobFromRequest.getFirstPossibleLOBString());
		}
		preview.setRequiredData(acordXml, response);
		return preview;
	}

	public void setRequiredData(Document originalXml, PreviewDataResponse response) {
		setResponseCommonMissingData(originalXml, response);
		try {
			setLOBRequiredData(originalXml, response);
		} catch (XPathExpressionException | ParserConfigurationException | IOException | SAXException e) {
			log.error("failed to create preview for lob {}",  getLob().getFirstPossibleLOBString(), e);
		}

	}

	protected void setResponseCommonMissingData(Document originalXml, PreviewDataResponse response) {
		try {
			// Add Missing Applicant
			if (StringUtils.isBlank(AcordHelper.getInsuredOrPrincipalCommercialName(originalXml))) {
				response.addMissingDataPoint("Applicant");
			}
			// Add missing location
			if (StringUtils.isBlank(AcordHelper.getLocationAddr1(originalXml))) {
				response.addMissingDataPoint("Location");
			}
			// Add missing PriorCarrier
			if (StringUtils.isBlank(AcordHelper.getPersPolicyCurrentTermAmt(originalXml))) {
				response.addMissingDataPoint("PriorCarrier");
			}
			// Add missing EffectiveDate
			if (StringUtils.isBlank(AcordHelper.getEffectiveDt(originalXml))) {
				response.addMissingDataPoint("EffectiveDate");
			}
		} catch (Exception e) {
			log.error("Failed to create common lines preview", e);
		}
	}

	protected static String buildAddress(Node addressNode) throws XPathExpressionException {
		StringBuilder address = new StringBuilder();

		if (addressNode == null) {
			return "";
		}
		String addr1 = AcordHelper.getAddr1(addressNode);
		if (StringUtils.isNotBlank(addr1)) {
			address.append(addr1);
		}

		String addr2 = AcordHelper.getAddr2(addressNode);
		if (StringUtils.isNotBlank(addr2)) {
			address.append(" ").append(addr2);
		}

		String city = AcordHelper.getCity(addressNode);
		if (StringUtils.isNotBlank(city)) {
			address.append(" ").append(city);
		}

		String stateCd = AcordHelper.getState(addressNode);
		if (StringUtils.isNotBlank(stateCd)) {
			address.append(",").append(stateCd);
		}

		String postalCode = AcordHelper.getPostalCode(addressNode);
		if (StringUtils.isNotBlank(postalCode)) {
			address.append(" ").append(postalCode);
		}

		String county = AcordHelper.getCounty(addressNode);
		if (StringUtils.isNotBlank(county)) {
			address.append(" ").append(county);
		}
		return address.toString();
	}

}

package com.lmig.uscm.booktransfer.opportunity.domain.customeraccount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerPolicy {
    private String _id;
    private String customerAccountId;
    private Integer btCode;
    private String emailAddress;
    private String phoneNumber;
    private String ratingState;
    private String ecliqAccountNumber;
    private PriorPolicy priorPolicy;
    private LibertyPolicy libertyPolicy;
    private CustomerAccountAqe aqe;
}

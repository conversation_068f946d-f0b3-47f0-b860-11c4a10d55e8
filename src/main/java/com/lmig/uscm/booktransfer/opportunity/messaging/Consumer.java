package com.lmig.uscm.booktransfer.opportunity.messaging;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.uscm.booktransfer.opportunity.auditing.SplunkName;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.config.SpringRabbitConfig;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityCreationHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.rating.client.domain.QniUpdateMessaging;
import com.lmig.uscm.booktransfer.rating.client.domain.QuoteAndIssueUpdate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
public class Consumer {
	private final OpportunityHelper opportunityHelper;
	private final OpportunityCreationHelper opportunityCreationHelper;

	private final ObjectMapper objectMapper;

	public Consumer(
			OpportunityHelper opportunityHelper,
			OpportunityCreationHelper opportunityCreationHelper,
			ObjectMapper objectMapper
	) {
		this.opportunityHelper = opportunityHelper;
		this.opportunityCreationHelper = opportunityCreationHelper;
		this.objectMapper = objectMapper;
	}

	@RabbitListener(queues = {SpringRabbitConfig.OPPORTUNITY_QUEUE})
	@Transactional
	@SplunkName("UPDATE QUOTED OPPORTUNITY")
	public void quoteOpportunityEventReceived(String message, @Header(AmqpHeaders.REDELIVERED) boolean redelivered) throws Exception {
		Opportunity opportunity = objectMapper.readValue(message, Opportunity.class);
		log.info("Processing update quoted message rabbitmq consumer");
		if(redelivered) {
			log.info("Processing redelivered opportunity: {}", message);
		}
		log.info("UPDATE QUOTED OPPORTUNITY id : {}", opportunity.getOpportunityId());
		opportunityHelper.saveUpdatedOpportunityPostQuote(opportunity);
	}

	@RabbitListener(queues = {SpringRabbitConfig.OPPORTUNITY_REUPLOAD_QUEUE})
	@Transactional
	@SplunkName("RE UPLOAD OPPORTUNITY")
	public void reuploadOpportunityEventReceived(String message, @Header(AmqpHeaders.REDELIVERED) boolean redelivered) throws Exception {
		int opportunityId = objectMapper.readValue(message, Integer.class);
		log.info("Processing reuploaded rabbitmq consumer");
		if(redelivered) {
			log.info("Processing redelivered re-upload opp message: {}", message);
		}
		log.info("RE UPLOAD OPPORTUNITY id : {}", opportunityId);
		opportunityCreationHelper.reuploadOpportunity(opportunityId);
	}

	@RabbitListener(queues = {QniUpdateMessaging.QNI_UPDATE_OPPORTUNITY_QUEUE})
	@SplunkName("UPDATE QUOTE AND ISSUE")
	public void qniUpdateOpportunityEventReceived(String message,
												  @Header(AmqpHeaders.REDELIVERED) boolean redelivered)
			throws OpportunityException {
		log.info("Processing qniUpdate rabbitmq consumer");
		try {
			QuoteAndIssueUpdate qniData = objectMapper.readValue(message, QuoteAndIssueUpdate.class);
			if (redelivered) {
				log.info("Processing redelivered qniData: {}", qniData);
			}
			log.info("UPDATE QUOTE AND ISSUE lastPolicyGuid - {}", qniData.getLastPolicyGuid());
			opportunityHelper.updateOppWithQnIData(qniData);
		} catch (JsonProcessingException e) {
			log.error("Failed to process qni data feed: {}", message, e);
		}
	}

}

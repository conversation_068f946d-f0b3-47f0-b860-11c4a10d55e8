package com.lmig.uscm.booktransfer.opportunity.repo.mappers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityHomeErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class OpportunityErrorInfoMapper implements RowMapper<OpportunityErrorInfo> {


    @Override
    public OpportunityErrorInfo mapRow(ResultSet resultSet, int i) throws SQLException {
        final String businessType = resultSet.getString("BusinessType");
        OpportunityErrorInfo item = new OpportunityErrorInfo();

        if (businessType.equals("HOME")) {
            item = new OpportunityHomeErrorInfo();
        }
        item.setProductLine(businessType);
        item.setOpportunityId(resultSet.getInt("OpportunityId"));
        item.setBookTransferId(resultSet.getInt("BookTransferId"));
        item.setState(resultSet.getString("State"));
        item.setQuoteTime(resultSet.getTimestamp("TimestampCallPartner"));
        final LineType databaseLineType = OpportunityUtil.defaultPersonalOrConvertStringToLineType(resultSet.getString("LineType"));
        item.setLineType(databaseLineType);

        return item;
    }
}

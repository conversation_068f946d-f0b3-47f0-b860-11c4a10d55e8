package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.xpath.XPathExpressionException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class BoatCreationStrategy implements CreationStrategy {
    public boolean shouldResolveCreationRequest(OpportunityCreationRequest creationRequest) {
        try {
            Document uploadedAcord = creationRequest.getUploadedACORD();
            return hasNodes(AcordHelper.getHomeLineWatercraft(uploadedAcord)) ||
                    hasNodes(AcordHelper.getHomeLineWatercraftAccessory(uploadedAcord)) ||
                    hasNodes(AcordHelper.getHomePolicyQuoteInqRqWatercraft(uploadedAcord)) ||
                    hasNodes(AcordHelper.getHomePolicyQuoteInqRqWatercraftAccessory(uploadedAcord));
        } catch (XPathExpressionException | NullPointerException e) {
            // Just eat it, eat it, just eat it, eat it, ooh
        }
        return false;
    }

    private boolean hasNodes(NodeList nodeList) {
        return nodeList != null && nodeList.getLength() > 0;
    }

    @Override
    public void resolveCreationBundles(List<CreationStrategyBundle> creationBundles, OpportunityCreationResponse response) {
        List<CreationStrategyBundle> copyCreationBundles = new ArrayList<>(creationBundles);
        for (CreationStrategyBundle bundle : copyCreationBundles) {
            resolveCreationBundle(bundle, response);
        }
    }

    private void resolveCreationBundle(CreationStrategyBundle creationBundle, OpportunityCreationResponse response) {
        List<OpportunityCreationRequest> incomingCreationRequests = new ArrayList<>(creationBundle.getRequests());


        for (OpportunityCreationRequest request : incomingCreationRequests) {
            if (!shouldResolveCreationRequest(request)) {
                continue;
            }

            try {
                splitBoatRequests(creationBundle, request);
            } catch (XPathExpressionException e) {
                response.incrementFailedCreationCount();
            }
        }
    }

    private void markXmlForReplacementIfReupload(final OpportunityCreationRequest request) {
        int existingOppId = Optional.ofNullable(request.getExistingOpportunityID()).orElse(0);
        if (existingOppId != 0) {
            request.setShouldReplaceOriginalXml(true);
        }
    }

    private void splitBoatRequests(CreationStrategyBundle creationBundle, OpportunityCreationRequest request) throws XPathExpressionException {
        markXmlForReplacementIfReupload(request);
        request.setAsMasterOpp();

        Document document = request.getUploadedACORD();
        OpportunityCreationRequest boatRequest = request.clone();
        Document boatDocument = boatRequest.getUploadedACORD();

        Node homeDwellPLCoverage = AcordHelper.getHomeDwellPL(document);
        Node homeDwellMEDPMCoverage = AcordHelper.getHomeDwellMEDPM(document);

        // This is setting the home line new premium
        AcordHelper.setHomelineCurrentTermAmount(document);

        // This is removing watercraft data from home
        AcordHelper.removeAllWatercraftFromHomeLine(document);
        AcordHelper.removeAllWatercraftAccessoriesFromHomeLine(document);
        AcordHelper.removeAllWatercraftDriversFromHomeLine(document);
        AcordHelper.removeAllWatercraftFromHomePolicyQuoteInqRq(document);
        AcordHelper.removeAllWatercraftAccessoriesFromHomePolicyQuoteInqRq(document);
        AcordHelper.removeAllWatercraftDriversFromHomePolicyQuoteInqRq(document);

        // This is removing home data from boat
        AcordHelper.removeAllDwelling(boatDocument);
        AcordHelper.removeAllPropertySchedule(boatDocument);

        // This is renaming home nodes to watercraft nodes
        AcordHelper.renameHomeQuoteRequest(boatDocument);
        AcordHelper.renameHomeLineBusiness(boatDocument);

        // This is boat premium and LOB
        AcordHelper.setBoatCurrentTermAmount(boatDocument);
        AcordHelper.setWatercraftLOBCd(boatDocument, LineOfBusiness.BOAT.getPossibleLobAcordValues()[0]);
        AcordHelper.setPersPolicyLOBCd(boatDocument, LineOfBusiness.BOAT.getPossibleLobAcordValues()[0]);

        // This is adding PL and MEDPM coverages to the boat opportunity.
        // This must execute after the boat premium is calculated
        AcordHelper.addPLAndMEDPMToWatercraft(boatDocument, homeDwellPLCoverage, homeDwellMEDPMCoverage);

        // This field will be set to true because this request was split from a home policy
        boatRequest.setDerivedFromHome(true);

        creationBundle.addCreationRequest(boatRequest);
    }

    @Override
    public CreationStrategyName getStrategyName() {
        return CreationStrategyName.SplitUpBoats;
    }
}
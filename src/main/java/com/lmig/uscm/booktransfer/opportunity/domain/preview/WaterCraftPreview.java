package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;

public class WaterCraftPreview extends Preview {

	@Override
	public LineOfBusiness getLob() {
		return LineOfBusiness.BOAT;
	}

	@Override
	protected void setLOBRequiredData(Document originalXml, PreviewDataResponse response)
			throws ParserConfigurationException, SAXException, XPathExpressionException, IOException {
		// if there is a trailer and it has at least one value listed for either the
		// year or make, then make Hull value required
		boolean modelMakeExists = true;
		if (StringUtils.isBlank(AcordHelper.getWatercraftManufacturer(originalXml))) {
			response.addMissingDataPoint("Make");
			modelMakeExists = false;
		}
		if (StringUtils.isBlank(AcordHelper.getWatercraftModel(originalXml))) {
			response.addMissingDataPoint("Model");
		}
		if (StringUtils.isBlank(AcordHelper.getWatercraftModelYear(originalXml))) {
			response.addMissingDataPoint("ModelYear");
			modelMakeExists = false;
		}
		if (StringUtils.isBlank(AcordHelper.getWatercraftWaterUnitTypeCd(originalXml))) {
			response.addMissingDataPoint("WatercraftStyle");
		}
		if (StringUtils.isBlank(AcordHelper.getWatercraftPropulsionTypeCd(originalXml))) {
			response.addMissingDataPoint("PropulsionType");
		}
		if (StringUtils
				.isBlank(AcordHelper.getWatercraftHorsePower(originalXml))) {
			response.addMissingDataPoint("TotalHorsePower");
		}

		if (modelMakeExists && StringUtils.isBlank(returnEmptyStringWhenZero(AcordHelper.getWatercraftAccessoryPresentValueAmt(originalXml)))) {
			response.addMissingDataPoint("TrailerCost");
		}

		if (StringUtils.isBlank(AcordHelper.getWatercraftLength(originalXml))) {
			response.addMissingDataPoint("Length");
		}
		if (StringUtils.isBlank(returnEmptyStringWhenZero(AcordHelper.getWatercraftCoverageHullLimit(originalXml)))) {
			response.addMissingDataPoint("HullValue");
		}
	}

	private String returnEmptyStringWhenZero(String value) {
		return value.equals("0") ? "" : value;
	}
}

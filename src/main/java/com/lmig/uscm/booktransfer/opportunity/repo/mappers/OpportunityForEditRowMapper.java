package com.lmig.uscm.booktransfer.opportunity.repo.mappers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;

@Component
public class OpportunityForEditRowMapper implements RowMapper<Opportunity> {
    @Override
    public Opportunity mapRow(@NonNull ResultSet resultSet, int i) throws SQLException {

        final String databaseData = resultSet.getString("data");
        final String databaseOriginalXml = resultSet.getString("originalXML");
        final int databaseBookTransferId = resultSet.getInt("bookTransferID");
        final String databaseNaicCd = resultSet.getString("naicCd");
        final String databaseBusinessType = resultSet.getString("businessType");
        final String databaseBillingAccountNumber = resultSet.getString("billingAccountNumber");
        final int databaseOppId = resultSet.getInt("opportunityID");
        final LineType databaseLineType = OpportunityUtil.defaultPersonalOrConvertStringToLineType(resultSet.getString("lineType"));
        final String state = resultSet.getString("state");

        final Opportunity opportunity = new Opportunity();
        opportunity.setData(databaseData);
        opportunity.setOriginalXML(databaseOriginalXml);
        opportunity.setBookTransferID(databaseBookTransferId);
        opportunity.setNAICCd(databaseNaicCd);
        opportunity.setBusinessType(databaseBusinessType);
        opportunity.setBillingAccountNumber(databaseBillingAccountNumber);
        opportunity.setOpportunityId(databaseOppId);
        opportunity.setLineType(databaseLineType);
        opportunity.setState(state);

        return opportunity;
    }
}

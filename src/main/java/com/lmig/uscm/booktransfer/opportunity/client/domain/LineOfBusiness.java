package com.lmig.uscm.booktransfer.opportunity.client.domain;


import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;

import javax.xml.xpath.XPathExpressionException;
import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;

public enum LineOfBusiness {

	AUTOP("PersAutoPolicyQuoteInqRq", "Auto Validation", "AUTO Import Rules", "//PersAutoLineBusiness/PersVeh", new String[]{"AUTO", "AUTOP", "AUTO - 5TH CAR POLICY", "RV"}, LineType.Personal),
	HOME("HomePolicyQuoteInqRq", "Home Validation", "Home Import Rules", "//HomeLineBusiness/Dwell", new String[]{"HOME", "HOME - SECONDARY RESIDENCE", "RENT", "RENTERS", "CONDO"}, LineType.Personal),
	BOAT("WatercraftPolicyQuoteInqRq", "Watercraft Validation", "Boat Import Rules", "//WatercraftLineBusiness/Watercraft", new String[]{"BOAT", "WATERCRAFT"}, LineType.Personal),
	DFIRE("DwellFirePolicyQuoteInqRq", "DwellFire Validation", "DFIRE Import Rules", "//DwellFireLineBusiness/Dwell", new String[]{"DFIRE", "FIRE", "DWELLING FIRE"}, LineType.Personal),
	UMBRP("PersUmbrellaPolicyQuoteInqRq", "Umbrella Validation", "UMBRP Import Rules", "//PersUmbrellaLineBusiness/RealEstate", new String[]{"UMBRP", "PUMBR", "UMBRELLA"}, LineType.Personal),
	MTR("PersAutoPolicyQuoteInqRq", "Auto Validation", "AUTO Import Rules", "//PersAutoLineBusiness/PersVeh", new String[]{"com.safeco_MC", "MOTORCYCLE", "MTR"}, LineType.Personal),
	PPKGE("PersPkgPolicyQuoteInqRq", null, null, null, new String[]{"PPKGE"}, LineType.Personal),
	INMRP("PersInlandMarinePolicyQuoteInqRq", "Inland Marine Validation", "Inland Marine Import Rules", "//PersInlandMarineLineBusiness", new String[]{"INLANDMARINE", "INMRP"}, LineType.Personal),
	WORK("WorkCompPolicyQuoteInqRq", null, null, null, new String[]{"WORK", "Work", "Workers Compensation", "Workers Comp"}, LineType.Business),
	AUTOB("CommlAutoPolicyQuoteInqRq", null, null, null, new String[]{"AUTOB"}, LineType.Business),
	BOP("BOPPolicyQuoteInqRq", null, null, null, new String[]{"BOP"}, LineType.Business),
	CGL("GeneralLiabilityPolicyQuoteInqRq", null, null, null, new String[]{"CGL", "General Liability"}, LineType.Business),
	CPKGE("CommlPkgPolicyQuoteInqRq", null, null, null, new String[]{"CPKGE"}, LineType.Business),
	PROP("CommlPropertyPolicyQuoteInqRq", null, null, null, new String[]{"PROP"}, LineType.Business),
	UMBRC("CommlUmbrellaPolicyQuoteInqRq", null, null, null, new String[]{"UMBRC"}, LineType.Business),
	INMRC("CommlInlandMarinePolicyQuoteInqRq", null, null, null, new String[]{"INMRC"}, LineType.Business),
	CFRM("FarmPolicyQuoteInqRq", null, null, null, new String[]{"CFRM"}, LineType.Business),
	CRIM("CommlPkgPolicyQuoteInqRq,CrimePolicyQuoteInqRq", null, null, null, new String[]{"CRIM"}, LineType.Business),
	GENERIC("Import Rules", LineType.All);

	private String lobXpath;
	private String validationRulesPackage;
	private String importRulePackage;
	private String locationXpath;
	private final String[] possibleLobAcordValues;
	private final LineType lineType;

	LineOfBusiness(String lobXpath, String validationRulesPackage, String[] possibleLobAcordValues, LineType lineType) {
		this.lobXpath = lobXpath;
		this.validationRulesPackage = validationRulesPackage;
		this.possibleLobAcordValues = possibleLobAcordValues;
		this.lineType = lineType;
	}

	LineOfBusiness(String importRules, LineType lineType) {
		this.importRulePackage = importRules;
		this.possibleLobAcordValues = new String[0];
		this.lineType = lineType;
	}

	LineOfBusiness(String lobXpath, String validationRulesPackage, String importRules, String[] possibleLobAcordValues, LineType lineType) {
		this(lobXpath, validationRulesPackage, possibleLobAcordValues, lineType);
		this.importRulePackage = importRules;
	}

	LineOfBusiness(String lobXpath, String validationRulesPackage, String importRules, String locationXpath, String[] possibleLobAcordValues, LineType lineType) {
		this(lobXpath, validationRulesPackage, importRules, possibleLobAcordValues, lineType);
		this.locationXpath = locationXpath;
	}

	/**
	 * This method is used for Personal Line types
	 * @param document
	 * @return LineOfBusiness
	 * @throws XPathExpressionException
	 */
	public static LineOfBusiness determineLobForPersonalLineType(Document document) throws XPathExpressionException {
		String lob = AcordHelper.getLobCd(document);
		LineOfBusiness lineOfBusiness = determineLobFromValue(lob);
		if (lineOfBusiness.equals(AUTOP)) {
			String foundCode = AcordHelper.getMotorcycleLobCd(document);
			if (Arrays.stream(MTR.getPossibleLobAcordValues())
					.anyMatch(acordValue -> StringUtils.equalsIgnoreCase(acordValue, foundCode))) {
				return LineOfBusiness.MTR;
			}
		}

		return lineOfBusiness;
	}

	/**
	 * This method is used for Business Line types to seperate logic from personal line type
	 * @param document
	 * @return LineOfBusiness
	 * @throws XPathExpressionException
	 */
	public static LineOfBusiness determineLobForBusinessLineType(Document document) throws XPathExpressionException {
		String lob = AcordHelper.getLobCd(document);
		return determineLobFromValue(lob);
	}

	/**
	 * Serializes String to LineOfBusiness enum. Defaults to LineOfBusiness.GENERIC
	 *
	 * @param rawLob
	 * @return
	 */
	public static LineOfBusiness determineLobFromValue(String rawLob) {
		if (StringUtils.isBlank(rawLob)) {
			return GENERIC;
		}
		Optional<LineOfBusiness> foundLob = Arrays.stream(values())
				.filter(lobValue -> Arrays.stream(lobValue.possibleLobAcordValues)
						.map(String::toLowerCase)
						.map(s -> s.replace("'",""))
						.collect(Collectors.toList()).contains(rawLob.toLowerCase().replace("'","")))
				.findAny();

		return foundLob.orElse(GENERIC);
	}

	public String[] getPossibleLobAcordValues() {
		return this.possibleLobAcordValues;
	}

	public String getLobXpath() {
		return this.lobXpath;
	}

	public String getValidationRulesPackage() {
		return this.validationRulesPackage;
	}

	public String getImportRules() {
		return this.importRulePackage;
	}

	public String getLocationXpath() {
		return this.locationXpath;
	}

	public LineType getLineType() {return this.lineType;}

	public String getLobValues() {
		String lobValues = Arrays.toString(this.possibleLobAcordValues);
		lobValues = lobValues.substring(1, lobValues.length() - 1).replace(", ", ",");
		return lobValues;
	}

	public String getFirstPossibleLOBString() {
		if (ArrayUtils.isEmpty(this.getPossibleLobAcordValues())) {
			return GENERIC.name();
		}
		return this.getPossibleLobAcordValues()[0];
	}
}


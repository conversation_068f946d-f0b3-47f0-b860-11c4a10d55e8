package com.lmig.uscm.booktransfer.opportunity.client.config;

/**
 * Consuming service of built jar will provide service.opportunity url entries to
 * populate the below:
 */
public class OpportunityUrlProvider {
	private String baseUrl;

	public OpportunityUrlProvider(String baseUrl) {
		this.baseUrl = baseUrl;
	}

	/**
	 * @return the baseUrl
	 */
	public String getBaseUrl() {
		return this.baseUrl;
	}

	/**
	 * @return the path
	 */
	public String getPath() {
		return "/";
	}

}

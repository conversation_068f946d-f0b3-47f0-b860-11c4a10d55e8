package com.lmig.uscm.booktransfer.opportunity.domain.customeraccount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAccountRequest {
    private SearchCriteria searchCriteria;
    private CustomerMetadata customerMetadata;
    private List<CustomerPolicy> customerPolicies;
    private List<EcliqAccount> eCliqAccounts;
    private String eventSource;
    private String transactionId;
}

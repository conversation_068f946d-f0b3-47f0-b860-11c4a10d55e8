package com.lmig.uscm.booktransfer.opportunity.client.domain;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.jackson.Jacksonized;

@Getter
@Setter
@Builder
@Jacksonized
public class EFTPaymentAccountsResponse {
    private String financialDetailActId;
    private String accountDeleteEligibility;
    private String accountEditEligibility;
    private String accountStatus;
    private String accountPaymentType;
    private String bankAccountNumber;
    private String bankAccountType;
    private String bankRoutingNumber;
    private String bankInstitutionName;
    private String accHldrFirstName;
    private String accHldrLastName;
    private String accHldrMiddleName;
    private String acctHldrFullName;
    private String lastActivityCode;
    private String lastActivityDate;
    private String paymentAccountToken;
    private String paymentAccountUsageType;
    private String accountOwnershipType;
    private String merchantIdCd;
}

package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.QuotingGuidelineWebClient;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.domain.QGFunctionalXmlRequest;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.domain.QuotingGuidelineException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class QuotingGuidelineHelper {
    private final QuotingGuidelineWebClient quotingGuidelineWebClient;

    public QuotingGuidelineHelper(final QuotingGuidelineWebClient quotingGuidelineWebClient) {
        this.quotingGuidelineWebClient = quotingGuidelineWebClient;
    }

    public String getQuotingGuidelineFunctionalXml(QGFunctionalXmlRequest request) throws QuotingGuidelineException {
        return quotingGuidelineWebClient.getQuotingGuidelineFunctionalXml(request);
    }
}

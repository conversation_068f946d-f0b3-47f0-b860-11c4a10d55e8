package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.UtilityService;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.xpath.XPathExpressionException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class FifthVehicleCreationStrategy implements CreationStrategy {

	private static final Integer MAX_4_VEHICLES_PER_OPPORTUNITY = 4;
	private static final Integer MAX_8_VEHICLES_PER_OPPORTUNITY = 8;

	private boolean isEighthCarEnabled;
	public FifthVehicleCreationStrategy() {}

	public FifthVehicleCreationStrategy(boolean isEnabled){
		this.isEighthCarEnabled = isEnabled;
	}

	public boolean shouldResolveCreationRequest(final OpportunityCreationRequest creationRequest) {
		try {
			int vehCount = AcordHelper.getPersVehs(creationRequest.getUploadedACORD()).getLength();

			if (vehCount > getMaxOpportunitySplit(creationRequest)) {
				return true;
			}
		} catch (XPathExpressionException | NullPointerException e) {
			// Just eat it, eat it, just eat it, eat it, ooh
		}
		return false;
	}

	/**
	 * Checks each OpportunityCreationRequest for shouldResolveCreationRequest and will split every 4 vehicles
	 * into a separate OpportunityCreationRequest for their own Opportunity
	 */
	@Override
	public void resolveCreationBundles(final List<CreationStrategyBundle> creationBundles, final OpportunityCreationResponse creationResponse) {
		creationBundles.forEach(bundle -> resolveCreationBundle(bundle, creationResponse));
	}

	public void resolveCreationBundle(final CreationStrategyBundle creationBundle, final OpportunityCreationResponse creationResponse) {
		// iterate on incoming creations only, not added ones
		List<OpportunityCreationRequest> incomingCreationRequests = new ArrayList<>(creationBundle.getRequests());

		for (OpportunityCreationRequest creationRequest : incomingCreationRequests) {
			if (!shouldResolveCreationRequest(creationRequest)) {
				continue;
			}

			try {
				splitRequestsPerMaxVehicle(creationBundle, creationRequest);
				calculateCurrentTermAmt(creationBundle);
			} catch (XPathExpressionException e) {
				creationResponse.incrementFailedCreationCount();
			}
		}
	}

	private boolean isAutopLob(OpportunityCreationRequest creationRequest) throws XPathExpressionException {
		if (isEighthCarEnabled()) {
			LineOfBusiness lob = LineOfBusiness.determineLobForPersonalLineType(creationRequest.getUploadedACORD());
			return lob == LineOfBusiness.AUTOP;
		} else {
			return false;
		}
	}

	private void splitRequestsPerMaxVehicle(final CreationStrategyBundle creationBundle, final OpportunityCreationRequest creationRequest) throws XPathExpressionException {
		creationRequest.setAsMasterOpp();
		//todo populate creationRequest field "masterOppID" for each request
		Document currRequestDoc = creationRequest.getUploadedACORD();
		NodeList allVehs = AcordHelper.getPersVehs(currRequestDoc);
		// remove all vehicles from master document
		AcordHelper.removeAllVehicles(currRequestDoc);
		int vehCount = allVehs.getLength();
		OpportunityCreationRequest emptyRequestToClone = creationRequest.clone();

		OpportunityCreationRequest currRequest = creationRequest;
		for (int i = 0; i < vehCount; i++) {
			if (i != 0 && i % getMaxOpportunitySplit(creationRequest) == 0) {
				currRequest = emptyRequestToClone.clone();
				// add to same bundle to link masterOppId after Opportunity creation
				creationBundle.addCreationRequest(currRequest);
			}
			// add vehicle to new ACORD
			Node vehNode = allVehs.item(i);
			Document addedRequestDoc = currRequest.getUploadedACORD();
			AcordHelper.addVehicle(addedRequestDoc, vehNode);
		}
	}

	private void calculateCurrentTermAmt(CreationStrategyBundle creationBundle) throws XPathExpressionException {
		double totalPersVehTermAmt = 0;
		// Set all pers policy term amounts after first opportunity to be sum of coverages inside PersVeh nodes
		for (int i = 1; i < creationBundle.getRequests().size(); i++) {
			OpportunityCreationRequest req = creationBundle.getRequests().get(i);
			double totalCoverageTermAmt = AcordHelper.getPersVehTotalCoverageCurrentTermAmt(req.getUploadedACORD());
			AcordHelper.setPolicyCurrentTermAmt(req.getUploadedACORD(), totalCoverageTermAmt);
			totalPersVehTermAmt += totalCoverageTermAmt;
		}

		// First opportunity's pers policy term amount = (pers policy term amount - other opportunities' coverage price total summed)
		Document firstDocument = creationBundle.getCreationRequest(0).getUploadedACORD();
		double persPolicyTermAmt = UtilityService.convertStringToDouble(AcordHelper.getPolicyCurrentTermAmt(firstDocument));
		AcordHelper.setPolicyCurrentTermAmt(firstDocument, persPolicyTermAmt - totalPersVehTermAmt);
	}

	@Override
	public CreationStrategyName getStrategyName() {
		return CreationStrategyName.SplitUpEveryFiveVehicles;
	}

	//Property isEighthCarEnabled Getter and Setter were created for testing
	public boolean isEighthCarEnabled() {
		return isEighthCarEnabled;
	}

	public void setEighthCarEnabled(boolean eighthCarEnabled) {
		isEighthCarEnabled = eighthCarEnabled;
	}

	private Integer getMaxOpportunitySplit(OpportunityCreationRequest creationRequest) throws XPathExpressionException {
		return isAutopLob(creationRequest) ? MAX_8_VEHICLES_PER_OPPORTUNITY : MAX_4_VEHICLES_PER_OPPORTUNITY;
	}
}

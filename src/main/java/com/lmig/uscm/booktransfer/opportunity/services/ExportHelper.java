/*
 * Copyright (C) 2017, Liberty Mutual Group
 *
 * Created on Apr 6, 2017
 */

package com.lmig.uscm.booktransfer.opportunity.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lmig.uscm.booktransfer.email.client.EmailService;
import com.lmig.uscm.booktransfer.email.client.domain.Attachment;
import com.lmig.uscm.booktransfer.email.client.domain.Email;
import com.lmig.uscm.booktransfer.email.client.domain.EmailException;
import com.lmig.uscm.booktransfer.opportunity.config.properties.EmailProperties;
import com.lmig.uscm.booktransfer.opportunity.config.properties.EnvironmentProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class ExportHelper {
	private final EmailService emailSender;
	private final EmailProperties emailProperties;
	private final EnvironmentProperties environmentProperties;

	public ExportHelper(EmailService emailSender,
			EmailProperties emailProperties,
			EnvironmentProperties environmentProperties) {
		this.emailSender = emailSender;
		this.environmentProperties = environmentProperties;
		this.emailProperties = emailProperties;
	}

	/**
	 * Create and send email for csvs for update effective date
	 */
	public void sendUpdateEffectiveDateCSVs(String updatedRecordCsv, String partialCsv, String email)
			throws JsonProcessingException, EmailException {

		List<Attachment> attachments = new ArrayList<>();

		// create csv files names here for both partial and updated records with timestamp
		String currentDate = new SimpleDateFormat("yyyy.MM.dd.HH.mm.ss").format(new Date());
		String updatedRecordsFileName = "UpdatedRecords_" + currentDate + ".csv";

		// push both CSV content and CSV names in Arraylist of class type Attachment
		if (StringUtils.isNotBlank(partialCsv)) {
			String partialFileName = "PartialMatch_" + currentDate + ".csv";
			attachments.add(new Attachment(partialCsv, partialFileName));
		}
		attachments.add(new Attachment(updatedRecordCsv, updatedRecordsFileName));

		// add subject - one subject only
		String subject = "Lead effective date Report";
		if (!environmentProperties.getActive().equalsIgnoreCase("prod")) {
			subject += "[" + environmentProperties.getActive() + "] ";
		}
		if (email == null) {
			email = this.emailProperties.getAddress();
		}
		sendEmail(attachments, email, subject);
	}

	private void sendEmail(List<Attachment> attachments, String emailAddress, String subject)
			throws JsonProcessingException, EmailException {
		Email email = new Email();

		String[] toArray = emailAddress.split(",");
		String[] bccList = new String[]{};
		String[] ccList = new String[]{};

		email.setToList(toArray);
		email.setBody("");
		email.setBccList(bccList);
		email.setCcList(ccList);
		email.setSubject(subject);
		email.setAttachment(attachments);

		emailSender.send(email);

		log.info("Email sent successfully");
	}
}

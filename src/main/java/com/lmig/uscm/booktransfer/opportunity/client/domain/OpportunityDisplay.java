package com.lmig.uscm.booktransfer.opportunity.client.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * used in opportunity service
 */
@Data
@NoArgsConstructor
public class OpportunityDisplay {

	private String opportunityId;
	private String status;
	private Date effectiveDate;
	private String states;
	private String lob;
	@JsonProperty("nAICCd")
	private String nAICCd;
	private String customerName;
	private String priorPremium;
	private String lastQuotedPremium;
	private String safecoPolicyGuid;
	private String priorCarrierGuid;
	private String masterOppId;
	private String statusCode;
	private String carrier;
	@JsonProperty("nNumber")
	private String nNumber;
	private String salesforceCode;
	private String bookTransferId;
	private String lineType;

	public OpportunityDisplay(String opportunityId, String status, Date effectiveDate, String lastPolicyGuid,
							  String nAICCd, String priorPremium, String bookTransferId, String lastQuotedPremium, String businessType,
							  String customerName, String priorCarrierGuid, String state, String masterOppID, String subCode,
							  String carrier, String nNumber, String salesforceCode) {
		this.opportunityId = opportunityId;
		this.status = status;
		this.effectiveDate = effectiveDate;
		this.states = state;
		this.lob = businessType;
		this.nAICCd = nAICCd;
		this.customerName = customerName;
		this.priorPremium = priorPremium;
		this.lastQuotedPremium = lastQuotedPremium;
		this.safecoPolicyGuid = lastPolicyGuid;
		this.priorCarrierGuid = priorCarrierGuid;
		this.masterOppId = masterOppID;
		this.statusCode = subCode;
		this.carrier = carrier;
		this.nNumber = nNumber;
		this.salesforceCode = salesforceCode;
		this.bookTransferId = bookTransferId;
	}
}

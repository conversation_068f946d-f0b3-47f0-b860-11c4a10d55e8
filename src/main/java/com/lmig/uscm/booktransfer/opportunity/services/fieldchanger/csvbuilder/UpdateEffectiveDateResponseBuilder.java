package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.csvbuilder;

import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.utilityservice.CSVBuilder;

import java.util.ArrayList;
import java.util.List;

public class UpdateEffectiveDateResponseBuilder extends UpdateOpportunityFieldResponseBuilder {
	@Override
	public String buildOutStatusChangeReport() {
		CSVBuilder csvSheet = new CSVBuilder();
		buildOpportunitySection(csvSheet);
		return csvSheet.toString();

	}

	@Override
	protected CSVBuilder getOpportunityHeaders(CSVBuilder csvSheet) {
		csvSheet.addRowToCSV(false, "OpportunityID", "lob", "Original Value for the Effective Date",
				"Updated Value for the Effective Date", "Changed: yes/no");
		return csvSheet;
	}

	@Override
	protected List<String> addQRIDataToCSV(OppChangeFieldResult oppChangeFieldResult) {
		// we do not care about that data as we are not displaying it. May want to
		// refactor to pull this out
		return new ArrayList<>();
	}

	@Override
	protected List<String> addOppDataToRow(OppChangeFieldResult oppChangeFieldResult) {
		List<String> rowData = new ArrayList<>();
		rowData.add(oppChangeFieldResult.getOppId());
		rowData.add(oppChangeFieldResult.getLob());
		rowData.add(oppChangeFieldResult.getOriginalValue());
		rowData.add(oppChangeFieldResult.getFieldUpdatedValue());
		rowData.add(getChangeValue(oppChangeFieldResult));
		return rowData;
	}
}

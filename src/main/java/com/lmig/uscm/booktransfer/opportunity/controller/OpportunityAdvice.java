package com.lmig.uscm.booktransfer.opportunity.controller;

import brave.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.NoSuchElementException;

/**
 * Main {@code @ControllerAdvice} class for the opportunity-service repository.
 *
 * <p>Extra context for future devs. This repo used to depend on domain-service, and the <a
 * href="https://github.com/lmigtech/globalexceptionhandlingservice/blob/6549c31338c94bb71c0f333b4665b36593621e62/src/main/java/com/lmig/uscm/booktransfer/globalexceptionhandlingservice/GlobalExceptionHandler.java">
 * GlobalExceptionHandler</a> {@code @ControllerAdvice} class defined within. domain-service was deprecated
 * and the dependency dropped from this repo. Some features may not have been ported over in their entirety.
 */
@ControllerAdvice
@Slf4j
public class OpportunityAdvice {
    private final Tracer tracer;

    public OpportunityAdvice(final Tracer tracer) {
        this.tracer = tracer;
    }

    @ExceptionHandler({NoSuchElementException.class, EmptyResultDataAccessException.class})
    public ResponseEntity<String> handleNoSuchElementException(final Exception e) {
        log.error(e.getMessage(), e);
        return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleUnexpectedException(final Exception unexpectedException) {
        final String message = "Error: Unexpected error occurred | Reference number: " + getTraceId();
        log.error(message, unexpectedException);
        return new ResponseEntity<>(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Retrieves the spring cloud sleuth traceId for the given request
     * @return String sleuth traceId
     */
    protected String getTraceId() {
        // Tracer should only be null during testing.
        // Cannot mock a tracer in test due to final classes
        if (tracer == null) {
            return "no tracer";
        }
        return tracer.currentSpan().context().traceIdString();
    }
}

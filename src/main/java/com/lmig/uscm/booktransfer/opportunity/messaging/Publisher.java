package com.lmig.uscm.booktransfer.opportunity.messaging;

import com.lmig.uscm.booktransfer.opportunity.config.SpringRabbitConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

@Slf4j
public class Publisher {

	private final RabbitTemplate rabbitTemplate;
	private final MessageProperties messageProperties;

	public Publisher(final RabbitTemplate rabbitTemplate) {
		this.rabbitTemplate = rabbitTemplate;
		messageProperties = new MessageProperties();
		messageProperties.setContentType(MessageProperties.CONTENT_TYPE_TEXT_PLAIN);
	}

	// Sends a message to RabbitMQ containing a message with the file name, tags and
	// type
	public void sendRequestToReUploadOpportunity(String oppId) {

		try {
			log.info("Sending message to reupload opportunity in AQE.");
			Message amqpMessage = new Message(oppId.getBytes(), messageProperties);
			rabbitTemplate.sendAndReceive(SpringRabbitConfig.APPLICATION_TOPIC,
					SpringRabbitConfig.OPPORTUNITY_REUPLOAD_QUEUE, amqpMessage);
			log.info("Placed Message in {}.", SpringRabbitConfig.OPPORTUNITY_REUPLOAD_QUEUE);

		} catch (Exception e) {
			log.error("unexpected exception", e);
		}
	}
}

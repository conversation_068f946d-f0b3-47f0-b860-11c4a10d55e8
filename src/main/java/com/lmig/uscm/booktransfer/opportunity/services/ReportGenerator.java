package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.uscm.booktransfer.utilityservice.CSVBuilder;
import com.lmig.uscm.booktransfer.utilityservice.DateUtils;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.BusinessAcordHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ReportGenerator {

	public String generateBrushMapCSV(final List<Opportunity> opportunityList) {
		return "BookTransfer Brush Map Data\r\n" +
				generateCSVByMappingOpps(
						getBrushFirePersonalCSVMapping(),
						getBrushFireBusinessCSVMapping(),
						opportunityList,
						null
				);
	}

	public String generateCustomerListCSV(final List<Opportunity> opportunityList) {
		Map<String, Function<Opportunity, String>> customerListFunctionalMapping =
				getCustomerListPersonalCSVMapping();
		List<String> headerList = new ArrayList<>(customerListFunctionalMapping.keySet());
		int sortingIdx = headerList.indexOf("Expiration Date");
		Comparator<List<String>> expirationDtComparator = getDateAtIndexComparator(sortingIdx);

		// Check if opportunityList contains any opportunities with LineType of Business
		boolean hasBusinessLines = opportunityList.stream()
				.anyMatch(opp -> LineType.Business.equals(opp.getLineType()));

		// If business lines exist, add the new columns
		if (hasBusinessLines) {
			Map<String, Function<Opportunity, String>> newColumns = new LinkedHashMap<>();
			newColumns.put("Package Sub LOBS", ReportGenerator::getCommaSeparatedLobCds);
			newColumns.put("Operations Description", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, BusinessAcordHelper::getOperationsDescription));

			int position = headerList.indexOf("LOB") + 1;
			headerList.addAll(position, newColumns.keySet());
			customerListFunctionalMapping = insertAtPosition(customerListFunctionalMapping, newColumns, position);
		}

		return generateCSVByMappingOpps(
				customerListFunctionalMapping,
				getCustomerListBusinessCSVMapping(),
				opportunityList,
				expirationDtComparator);
	}

	/**
	 * Inserts the entries from the provided map (newEntries) into the original map at the specified position.
	 * The original map remains unmodified and a new map is returned with the inserted entries.
	 * The position is 0-based, meaning that a position of 0 would insert the new entries at the beginning of the map.
	 * If the position is equal to or greater than the size of the original map, the new entries are added at the end.
	 *
	 * @param original The original map where the new entries will be inserted.
	 * @param newEntries The entries to be inserted into the original map.
	 * @param position The position at which to insert the new entries.
	 * @return A new map containing the original entries and the inserted entries.
	 */
	private static <K, V> Map<K, V> insertAtPosition(Map<K, V> original, Map<K, V> newEntries, int position) {
		int i = 0;
		Map<K, V> result = new LinkedHashMap<>();
		for (Map.Entry<K, V> entry : original.entrySet()) {
			if (i == position) {
				result.putAll(newEntries);
			}
			result.put(entry.getKey(), entry.getValue());
			i++;
		}
		return result;
	}

	/**
	 * Creates the Comparator to use to sort by a specific Date located within the passed List
	 */
	private Comparator<List<String>> getDateAtIndexComparator(int sortingIdx) {
		return (rowA, rowB) -> {
			Date dateA;
			Date dateB;

			String rowAData = rowA.get(sortingIdx);
			String rowBData = rowB.get(sortingIdx);

			// Should return 0 if both the values to be compared are null or empty
			if(StringUtils.isBlank(rowAData) && StringUtils.isBlank(rowBData)) {
				return 0;
			}

			try {
				dateA = DateUtils.convertStringToDate(rowAData);
			} catch (ParseException e) {
				return 1;
			}
			try {
				dateB = DateUtils.convertStringToDate(rowBData);
			} catch (ParseException e) {
				return -1;
			}

			return dateA.compareTo(dateB);
		};
	}

	/**
	 * Creates a CSV string by mapping an Opportunity list by a generic set of functions defined
	 * by a map of key:value pairs where keys are CSV column header names and
	 * values are lambda functions extracting String values from an Opportunity
	 * <p>
	 * Function accepts a Comparator of lists for sorting or skip sorting with null
	 *
	 * @return csv string
	 */
	public String generateCSVByMappingOpps(final Map<String, Function<Opportunity, String>> plOppFunction,
										   final Map<String, Function<Opportunity, String>> blOppFunction,
										   final List<Opportunity> opportunityList,
										   final Comparator<List<String>> sortComparator) {
		CSVBuilder csv = new CSVBuilder();
		// header row
		List<String> headers = List.copyOf(plOppFunction.keySet());
		csv.addRowToCSV(List.copyOf(headers));
		// populate rows per opp
		List<List<String>> rows = new ArrayList<>();
		opportunityList.forEach(opp -> {
			// Determine which LineType CSV mapper to use
			Map<String, Function<Opportunity, String>> oppFuncMap = LineType.Business.equals(opp.getLineType()) ? blOppFunction : plOppFunction;
			// Do mapping
			List<String> oppRow = oppFuncMap.values().stream()
					.map(function -> function.apply(opp))
					.map(entryVal -> entryVal == null || StringUtils.equals(entryVal, "null") ?
							StringUtils.EMPTY : entryVal)
					.collect(Collectors.toList());
			rows.add(oppRow);
		});
		// sort rows
		if (sortComparator != null) {
			rows.sort(sortComparator);
		}
		// add rows to csv
		rows.forEach(row -> csv.addRowToCSV(row, false));
		// Convert CSV to string and remove trailing commas
		String csvString = csv.toString();
		csvString = csvString.replaceAll(",\r\n", "\r\n");
		return csvString;
	}

	private static Map<String, Function<Opportunity, String>> getBrushFirePersonalCSVMapping() {
		Map<String, Function<Opportunity, String>> commands = new LinkedHashMap<>();
		commands.put("LOB", Opportunity::getBusinessType);
		commands.put("Client Name", Opportunity::getCustomerName);
		commands.put("Location Address", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getLocationAddr1));
		commands.put("City", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getLocationCity));
		commands.put("State", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getLocationState));
		commands.put("Zip", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getLocationPostalCode));
		commands.put("Roof Material", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getRoofMaterialCd));
		commands.put("OppId", opp -> String.valueOf(opp.getOpportunityId()));
		commands.put("Policy Type", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getDwellPolicyTypeCd));

		return commands;
	}

	private static Map<String, Function<Opportunity, String>> getBrushFireBusinessCSVMapping() {
		Map<String, Function<Opportunity, String>> commands = new LinkedHashMap<>();
		commands.put("LOB", Opportunity::getBusinessType);
		commands.put("Client Name", Opportunity::getCustomerName);
		commands.put("Location Address", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, BusinessAcordHelper::getLocationAddress));
		commands.put("City", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, BusinessAcordHelper::getLocationCity));
		commands.put("State", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, BusinessAcordHelper::getLocationState));
		commands.put("Zip", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, BusinessAcordHelper::getLocationZip));
		commands.put("Roof Material", opp -> StringUtils.EMPTY);
		commands.put("OppId", opp -> String.valueOf(opp.getOpportunityId()));
		commands.put("Policy Type", opp -> StringUtils.EMPTY);

		return commands;
	}

	private static Map<String, Function<Opportunity, String>> getBaseCustomerListCSVMapping() {
		Map<String, Function<Opportunity, String>> commands = new LinkedHashMap<>();
		commands.put("Prior Policy #", Opportunity::getPriorCarrierGuid);
		commands.put("Customer Name", Opportunity::getCustomerName);
		commands.put("Customer First Name", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getInsuredGivenName));
		commands.put("Customer Last Name", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getInsuredSurname));
		commands.put("Effective Date", Opportunity::getEffectiveDate);
		commands.put("Expiration Date", opp -> checkDateFormattedForSorting(OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getExpirationDt)));
		commands.put("Contract Term", opp -> String.valueOf(OpportunityUtil.oppDataFunctionWithDefaultNull(opp, Opportunity::extractPolicyTerm)));
		commands.put("LOB", Opportunity::getBusinessType);
		commands.put("Address", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getInsuredMailingAddr1));
		commands.put("City", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getInsuredMailingCity));
		commands.put("State", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getInsuredMailingState));
		commands.put("Zip Code", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getInsuredMailingPostalCode));
		commands.put("Rating State", Opportunity::getState);
		commands.put("Prior Policy Premium", opp -> String.valueOf(opp.getPriorPremium()));
		commands.put("NAIC", opp -> String.valueOf(opp.getNAICCd()));
		commands.put("AgencyID", Opportunity::getAgencyId);
		commands.put("Opp ID", opp -> String.valueOf(opp.getOpportunityId()));
		commands.put("Policy Status ID", opp -> String.valueOf(opp.getStatus()));
		commands.put("Policy Status", ReportGenerator::getOrDefaultOpportunityStatus);
		return commands;
	}

	private static Map<String, Function<Opportunity, String>> getCustomerListPersonalCSVMapping() {
		return getBaseCustomerListCSVMapping();
	}

	private static Map<String, Function<Opportunity, String>> getCustomerListBusinessCSVMapping() {
		Map<String, Function<Opportunity, String>> commands = new LinkedHashMap<>();
		Map<String, Function<Opportunity, String>> baseCommands = getBaseCustomerListCSVMapping();

		int i = 0;
		for (Map.Entry<String, Function<Opportunity, String>> entry : baseCommands.entrySet()) {
			if (i == 8) {
				commands.put("Package Sub LOBS", ReportGenerator::getCommaSeparatedLobCds);
				commands.put("Operations Description", opp -> OpportunityUtil.oppDataFunctionWithDefaultNull(opp, BusinessAcordHelper::getOperationsDescription));
			}
			commands.put(entry.getKey(), entry.getValue());
			i++;
		}
		return commands;
	}

	/**
	 * This method retrieves the list of LOB codes associated with the given Opportunity object.
	 * If the LOB of the Opportunity is "CPKGE" or "UMBRC", it retrieves the LOB codes.
	 * After the retrieval, it removes the LOB of the Opportunity from the LOB codes.
	 * It then joins the remaining LOB codes into a single, comma-separated string.
	 * If the LOB of the Opportunity is not "CPKGE" or "UMBRC", or if the list of LOB codes is null, the method returns an empty string.
	 *
	 * @param opp The Opportunity object for which the LOB codes are to be retrieved and processed.
	 * @return A comma-separated string of LOB codes, excluding the LOB of the Opportunity if it is "CPKGE" or "UMBRC", or an empty string if the LOB of the Opportunity is not "CPKGE" or "UMBRC", or if the list of LOB codes is null.
	 */
	private static String getCommaSeparatedLobCds(Opportunity opp) {
		String lob = opp.getBusinessType();
		if (lob != null && (lob.equals("CPKGE") || lob.equals("UMBRC"))) {
			List<String> lobCds = OpportunityUtil.oppDataFunctionWithDefaultNull(opp, AcordHelper::getLobCds);
			if (lobCds != null) {
				List<String> filteredLobCds = lobCds.stream()
						.filter(lobCd -> !lobCd.equals(lob))
						.collect(Collectors.toList());
				return String.join(", ", filteredLobCds);
			}
		}
		return "";
	}

	private static String getOrDefaultOpportunityStatus(final Opportunity opp) {
		try {
			return OpportunityStatus.getOppStatusFromCode(opp.getStatus()).getOppStatusValue();
		} catch (Exception e) {
			return OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusValue();
		}
	}
	/**
	 * Nasty way to try to parse Dates correctly. Returns null if we cannot figure it out
	 */
	private static String checkDateFormattedForSorting(String rawDate) {
		try {
			return DateUtils.getSQLDateString(DateUtils.convertStringToDate(rawDate, DateUtils.determineDateFormat(rawDate)));
		} catch (ParseException | DateTimeParseException e) {
			return null;
		}
	}

}

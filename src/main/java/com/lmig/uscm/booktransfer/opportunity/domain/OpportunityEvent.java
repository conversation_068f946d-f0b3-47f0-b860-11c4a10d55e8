package com.lmig.uscm.booktransfer.opportunity.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;

/**
 * An Opportunity has been succcessfully Created or Updated.
 * OpportunityEvent records are saved to Mongo Atlas collection which are then broadcasted to Kafka topics for downstream consumers.
 * Consumers: TMNN (TODO: add details)
 * <p>
 * TODO: Do we want to merge in properties from recently deprecated domain.creation.OpportunityCreationEvent?
 * We may want to if we plan to use this for that flow as well when removing SNS, SQS in place of Kafka.
 */
@Document
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpportunityEvent {
    private static final String CREATED_EVENT = "CREATED";
    private static final String UPDATED_EVENT = "UPDATED";
    @Id
    private String id;
    private String opportunityId; //Opportunity unique identifier
    private String customerAccountId; //Customer Account unique identifier, null if lineType is Personal
    private String eventStatus; //CREATED, UPDATED
    private String opportunityUrl; //url on where to get the opportunity object
    private String lineType; //Personal, Business, All
    // TODO: can this be populated by Mongo or JPA?
    private Instant timestamp; //timestamp when the event was created

    private String transactionId; //Audit log transaction id, passed to downstream so that it will be updated there.

    public static String getCreatedEvent() {
        return CREATED_EVENT;
    }

    public static String getUpdatedEvent() {
        return UPDATED_EVENT;
    }
}

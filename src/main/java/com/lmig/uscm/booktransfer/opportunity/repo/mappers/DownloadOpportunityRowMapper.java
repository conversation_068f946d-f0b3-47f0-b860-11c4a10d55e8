package com.lmig.uscm.booktransfer.opportunity.repo.mappers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;

@Component
public class DownloadOpportunityRowMapper implements RowMapper<Opportunity> {
    @Override
    public Opportunity mapRow(@NonNull ResultSet resultSet, int i) throws SQLException {

        final int databaseOpportunityId = resultSet.getInt("OPPORTUNITYID");
        final String databaseData = resultSet.getString("data");
        final String databaseLastPolicyGuid = resultSet.getString("lastPolicyGuid");
        final String databaseEffectiveDate = resultSet.getString("effectiveDate");
        final int databaseBookTransferId = resultSet.getInt("bookTransferID");
        final String databaseCustomerName = resultSet.getString("customerName");
        final LineType databaseLineType = OpportunityUtil.defaultPersonalOrConvertStringToLineType(resultSet.getString("lineType"));

        final Opportunity opportunity = new Opportunity();
        opportunity.setOpportunityId(databaseOpportunityId);
        opportunity.setData(databaseData);
        opportunity.setLastPolicyGuid(databaseLastPolicyGuid);
        opportunity.setEffectiveDate(databaseEffectiveDate);
        opportunity.setBookTransferID(databaseBookTransferId);
        opportunity.setCustomerName(databaseCustomerName);
        opportunity.setLineType(databaseLineType);

        return opportunity;
    }
}

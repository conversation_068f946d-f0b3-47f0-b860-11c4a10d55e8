package com.lmig.uscm.booktransfer.opportunity.config.properties;

import com.lmig.usconsumermarkets.booktransfer.security.starter.properties.WebClientConfigProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties("bt.webclient.customer-account")
public class CustomerAccountWebClientConfigProperties extends WebClientConfigProperties {
    public CustomerAccountWebClientConfigProperties() {
    }
}

package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.usconsumermarkets.booktransfer.btpaymentservice.client.domain.BillingDetails;
import com.lmig.usconsumermarkets.booktransfer.btpaymentservice.client.domain.SafecoBillingDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.function.client.WebClient;

@Slf4j
public class BTPaymentServiceHelper {
    private final WebClient bTPaymentServiceClient;
    private final String url;

    public BTPaymentServiceHelper(final WebClient bTPaymentServiceClient, final String url) {
        this.bTPaymentServiceClient = bTPaymentServiceClient;
        this.url = url;
    }

    public BillingDetails getPaymentInfoUsingBillingAccountNumber(String billingAccountNumber) {
        String uri = String.format("%s/payment-data/%s", this.url, billingAccountNumber);
        log.info("GET - {}", uri);
        return bTPaymentServiceClient
                .get()
                .uri(uri)
                .retrieve()
                .bodyToMono(BillingDetails.class)
                .block();
    }

    public String saveSafecoBillingData(SafecoBillingDataDTO safecoDTO) {
        return bTPaymentServiceClient
                .post()
                .uri(this.url+"/safeco-billing-data")
                .bodyValue(safecoDTO)
                .retrieve()
                .bodyToMono(String.class)
                .block();
    }
}

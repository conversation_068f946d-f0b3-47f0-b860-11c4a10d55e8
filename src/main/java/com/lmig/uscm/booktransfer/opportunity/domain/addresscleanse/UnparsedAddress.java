package com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UnparsedAddress {
    private String addressLine;
    private String addressLine2;
    private String addressLastLine;
    private String countryCode;
    private String firmName;
    private String addressRequestId;
    private String urbanizationName;

    public UnparsedAddress(String addressLine, String city, String stateProvCd, String postalCode) {
        this.addressLine = addressLine;
        this.addressLastLine = combineCityStatePostal(city, stateProvCd, postalCode);
    }

    private String combineCityStatePostal(String city, String stateProvCd, String postalCode) {
        return city + ", " + stateProvCd + " " + postalCode;
    }
}


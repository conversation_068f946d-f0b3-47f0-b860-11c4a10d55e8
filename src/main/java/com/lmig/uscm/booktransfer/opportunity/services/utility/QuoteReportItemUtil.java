package com.lmig.uscm.booktransfer.opportunity.services.utility;

import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class QuoteReportItemUtil {
	private QuoteReportItemUtil() {
	}

	public static Map<String, QuoteReportItemLegacy> getQuoteSalesForceIdToQuoteReportItemMap(List<QuoteReportItemLegacy> qriList) {
		Map<String, QuoteReportItemLegacy> qriIdToMap = new HashMap<>();
		if (qriList != null) {
			for (QuoteReportItemLegacy qri : qriList) {
				if (qri != null) {
					qriIdToMap.put(qri.getQuoteSalesforceID(), qri);
				}
			}
		}
		return qriIdToMap;
	}
}

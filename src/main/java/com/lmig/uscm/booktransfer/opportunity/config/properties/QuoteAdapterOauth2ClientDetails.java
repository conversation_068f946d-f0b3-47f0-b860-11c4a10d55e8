/*
 * Copyright (c) 2020, Liberty Mutual
 * Proprietary and Confidential
 * All Rights Reserved
 */
package com.lmig.uscm.booktransfer.opportunity.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;

@ConfigurationProperties(prefix = "oppservice.oauth2.quoteadapter.client")
public class QuoteAdapterOauth2ClientDetails extends ClientCredentialsResourceDetails {

}
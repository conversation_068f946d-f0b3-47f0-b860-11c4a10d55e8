package com.lmig.uscm.booktransfer.opportunity.client.domain.quoteadapter;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class QuoteResponse {
	private String id;
	private QuoteStatus status;
	private QuoteRequestType requestType; //Rc1 RC2, BL REQUEST
	private QuoteOrigin requestOrigin; //Where the request originated
	private String opportunityId;
	private long startTimestamp;
	private long completionTimestamp;
	private Response response;
	private Message[] messages;
	private String[] remarkTexts;

	@JsonProperty("_id")
	public String getId() {
		return this.id;
	}
}

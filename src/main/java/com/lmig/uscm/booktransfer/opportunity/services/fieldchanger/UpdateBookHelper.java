package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger;

import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.BookTransferConversion;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.FieldChangerException;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeField;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.services.UploadEventService;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import com.lmig.uscm.booktransfer.uploadmanager.client.domain.UploadEvent;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;

/**
 * This class is a subclass of UpdateHelper. This class is job to handle the
 * moving of an opportunity book from one book to another request.
 */
@Slf4j
public class UpdateBookHelper extends UpdateHelper {
	public static final String BULK_MOVE_N_NUMBER_STATUS = "System Generated - Bulk Move";

	/**
	 * This object holds the information of salesforce Booktransfer id (sfdcid) to
	 * booktransfer and upload information This helps us create these items in bulk
	 * and figure out which items go where
	 */
	private Map<String, BookTransferConversion> salesForceBtIdToBooksWithUploadEvent;

	/**
	 * This object holds the information of Booktransfer id to salesforce
	 * Booktransfer id (sfdcid) This helps us relate a booktransfer id to a
	 * salesforce Booktransfer id. The booktransferId is only on the opportunity and
	 * we need a way to get the sfdcid
	 */
	private Map<Integer, BookTransferDTO> bookTransferIdToSalesForceBtId;
	private BookTransferService bookTransferService;
	private UploadEventService uploadEventService;

	public UpdateBookHelper(List<Opportunity> oppList, List<QuoteReportItemLegacy> quoteItemsList) {
		super(oppList, quoteItemsList);
		this.salesForceBtIdToBooksWithUploadEvent = new HashMap<>();
		this.bookTransferIdToSalesForceBtId = new HashMap<>();
	}

	public void setDatabaseHelpersForMoveBook(BookTransferService bookTransferService,
			UploadEventService uploadEventService) {
		this.bookTransferService = bookTransferService;
		this.uploadEventService = uploadEventService;
	}

	/**
	 * Gets a list of all the unique books based on the SFCID's that were parsed
	 * from the csv file
	 *
	 * @param oppChangeFieldList the list of changes expected
	 */
	protected void setBookTransferAndUploadMap(List<OppChangeField> oppChangeFieldList) {

		Set<String> sfdcids = new HashSet<>();
		Set<Integer> bookTransferIds = new HashSet<>();
		for (OppChangeField oppChangeField : oppChangeFieldList) {
			if (isUpdatingField(oppChangeField.getFieldName())) {
				sfdcids.add(oppChangeField.getFieldValue());
				try {
					Opportunity opp = getOpportunity(oppChangeField);
					bookTransferIds.add(opp.getBookTransferID());
				} catch (FieldChangerException e) {
					log.warn("Opportunity not found: {}", oppChangeField.getOppId());
				}
			}
		}
		getNewBookTransferConversion(sfdcids);
		setBookTransferIdToSalesForceBtId(bookTransferIds);
	}

	private void getNewBookTransferConversion(Set<String> sfdcids) {
		for (String sfdcid : sfdcids) {
			try {
				BookTransferDTO bt = bookTransferService.getTopBySFDCIDOrderByBookTransferID(Integer.parseInt(sfdcid));
				BookTransferConversion btFields = BookTransferConversion.builder()
						.bookTransferID(bt.getBookTransferID())
						.subcode(bt.getSubCode())
						.sfdcid(bt.getSfdcid())
						.rampCode(bt.getRampCode())
						.nBDRelationship(bt.getNBDRelationship())
						.build();

				UploadEvent newUpload = new UploadEvent();
				newUpload.setBookTransferID(bt.getBookTransferID());
				newUpload.setAgentNum(bt.getAgentNum());
				newUpload.setStatus("Move");
				newUpload.setNnumber(BULK_MOVE_N_NUMBER_STATUS);
				newUpload.setFileName(BULK_MOVE_N_NUMBER_STATUS);
				int newUploadId = uploadEventService.createUploadEvent(newUpload);

				btFields.setUploadEventId(newUploadId);
				this.salesForceBtIdToBooksWithUploadEvent.put(sfdcid, btFields);
			} catch (Exception e) {
				log.warn("It should be unable to find book transfer later with sfdcid - {}", sfdcid, e);
			}

		}
	}

	private void setBookTransferIdToSalesForceBtId(Set<Integer> bookTransferIds) {
		for (Integer bookTransferId : bookTransferIds) {
			try {
				BookTransferDTO bt = bookTransferService.findByBookTransferId(bookTransferId);
				this.bookTransferIdToSalesForceBtId.put(bt.getBookTransferID(), bt);
			} catch (Exception e) {
				// this error can be logged but probably not a big deal as this is the old value
				// and we are updating it
				log.warn("Cannot find book transfer with id for old booktransfer - {}", bookTransferId, e);
			}
		}
	}

	@Override
	protected boolean isUpdatingField(String fieldName) {
		return StringUtils.equalsIgnoreCase(fieldName, "SFDCID");
	}

	@Override
	protected OppChangeFieldResult buildInitialResultForSpecificValue(OppChangeField oppChangeField, Opportunity opp) {
		// TODO test
		String sfdcid;
		BookTransferDTO convertedIDs = this.bookTransferIdToSalesForceBtId.get(opp.getBookTransferID());
		if (convertedIDs == null) {
			sfdcid = "Invalid Book TransferId";
		} else {
			sfdcid = String.valueOf(convertedIDs.getSfdcid());
		}
		OppChangeFieldResult result = new OppChangeFieldResult(opp, oppChangeField, sfdcid);

		if (this.salesForceBtIdToBooksWithUploadEvent.get(oppChangeField.getFieldValue()) == null) {
			result.isError("Cannot Find Book with given SFDCID");
		}
		return result;
	}

	@Override
	protected void saveFieldUpdate(List<Opportunity> opportunities, String value) {
		BookTransferConversion bookTransferConversion = this.salesForceBtIdToBooksWithUploadEvent.get(value);

		List<Opportunity> commercialOpportunities = getCommercialOpportunities(opportunities) ;
		for (List<Opportunity> partition : ListUtils.partition(commercialOpportunities, OpportunityJDBCRepo.SQL_BATCH_SIZE)) {
				opportunityHelper.updateCustomerAccounts(partition, value);
		}

		for (List<Opportunity> partition : ListUtils.partition(opportunities, OpportunityJDBCRepo.SQL_BATCH_SIZE)) {
			saveOpportunityForMoveBook(partition, bookTransferConversion);
		}
		saveQuoteReportItemForMoveBook(opportunities, bookTransferConversion);
	}

	private void saveOpportunityForMoveBook(List<Opportunity> opportunities,
			BookTransferConversion bookTransferConversion) {
		try {
			log.info("Before saving opps with updated book");
			for (Opportunity opportunity : opportunities) {
				opportunity.setBookTransferID(bookTransferConversion.getBookTransferID());
				opportunity.setUploadEventID(bookTransferConversion.getUploadEventId());
				Document xmlDoc = XmlHelper.getDocument(opportunity.getData());
				if (LineType.Personal.equals(opportunity.getLineType())) {
					AcordHelper.setAgentNumber(xmlDoc, bookTransferConversion.getSubcode());
				}
				if (LineType.Business.equals(opportunity.getLineType()) && null != bookTransferConversion.getRampCode()) {
					AcordHelper.setAgentNumber(xmlDoc, bookTransferConversion.getRampCode());
				}
				if (bookTransferConversion.getNBDRelationship() != null) {
					/**
					 * Load the existing NBDRelation for the opportunity.
					 */
					String nbdRelationship = AcordHelper.getNbdRelationship(xmlDoc);
					log.info("nbdRelationship: {}" , nbdRelationship);
					/**
					 * If NBDRelationship is not empty then set NBDRelationship with BookTransfer NBDRelationship.
					 * Else set NBDRelationship empty object in the xPath.
					 */
					String updatedData = XmlHelper.getDocumentString(AcordHelper.setNbdRelationship(xmlDoc, bookTransferConversion.getNBDRelationship()));
					opportunity.setData(updatedData);
				}
				opportunity.setData(XmlHelper.getDocumentString(xmlDoc));
			}
		} catch (ParserConfigurationException | SAXException | XPathExpressionException | IOException |
				 TransformerException e) {
			log.error("Caught exception during XML parsing", e);
		}
		opportunityRepoHelper.saveAll(opportunities);
		log.info("Ended saving opps");
	}

	private void saveQuoteReportItemForMoveBook(List<Opportunity> opportunities,
			BookTransferConversion bookTransferConversion) {
		log.info("Saving quote report items");
		// PL opps
		List<String> quoteSalesforceIDs = OpportunityUtil.getOpportunityIdsByLineType(opportunities, LineType.Personal);
		if(!quoteSalesforceIDs.isEmpty()) {
			quoteReportItemHelper.updateQuoteReportItemForMoveBooks(
					quoteSalesforceIDs, bookTransferConversion.getSfdcid(), bookTransferConversion.getSubcode());
		}

		// BL opps
		List<String> blQuoteSalesforceIDs = OpportunityUtil.getOpportunityIdsByLineType(opportunities, LineType.Business);
		if(!blQuoteSalesforceIDs.isEmpty()) {
			quoteReportItemHelper.updateQuoteReportItemForMoveBooks(
					blQuoteSalesforceIDs, bookTransferConversion.getSfdcid(), bookTransferConversion.getRampCode());
		}
		log.info("End saving quote report items");
	}

	/**
	 * For testing only. We need to test the books for our test case but no good way
	 * of doing this.
	 */
	protected Map<String, BookTransferConversion> getSalesForceBtIdToBooksWithUploadEvent() {
		return salesForceBtIdToBooksWithUploadEvent;
	}

	/**
	 * For testing only. We need to test the books for our test case but no good way
	 * of doing this.
	 */
	protected Map<Integer, BookTransferDTO> getBooksToMoveFrom() {
		return bookTransferIdToSalesForceBtId;
	}

	/**
	 * For testing only need away to set books for mock run
	 */
	protected void setBookTransferAndUploadMap(Map<String, BookTransferConversion> sfdcidToBooksOppMovingTo) {
		this.salesForceBtIdToBooksWithUploadEvent = sfdcidToBooksOppMovingTo;
	}

	/**
	 * For testing only need away to set books for mock run, so that way we can
	 * isolate what we are testing.
	 */
	public void setSalesForceBtIdToBooksWithUploadEvent(
			Map<Integer, BookTransferDTO> salesForceBtIdToBooksWithUploadEvent) {
		this.bookTransferIdToSalesForceBtId = salesForceBtIdToBooksWithUploadEvent;

	}

	protected List<Opportunity> getCommercialOpportunities(List<Opportunity> opportunities){
		List<Opportunity> commercialOpportunities = new ArrayList<>();
		for(Opportunity opp : opportunities){
			if (LineType.Business.equals(opp.getLineType())) {
				commercialOpportunities.add(opp);
			}
		}
		return commercialOpportunities;
	}
}

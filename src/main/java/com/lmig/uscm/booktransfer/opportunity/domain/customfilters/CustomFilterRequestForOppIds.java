package com.lmig.uscm.booktransfer.opportunity.domain.customfilters;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.PolicyType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@NoArgsConstructor
@Setter
public class CustomFilterRequestForOppIds {
	private List<Integer> opportunityIDs;
	private List<Integer> statuses;
	private String statCode;
	private String startDate;
	private String endDate;
	private List<String> priorCarriers;
	private List<String> states;
	private List<String> lobs;
	private String priorPolicyGuid;
	private String safecoPolicyGuid;
	private String customerName;
	private String assignedUser;
	private String agentNumber;
	private List<String> oppPriorCarriers;
	private List<Integer> sfdcids;
	private List<String> nbdRelationship;
	private Integer uploadEventId;
	private LineType lineType;
	private List<String> quoteTypes;
	private List<Integer> naicCds;
	private List<String> priorCarrierBillingAccountNumbers;
	private List<String> policyTypeCds;

	public void setStatuses(List<String> statuses) throws OpportunityException {
		List<Integer> statusesCodes = new ArrayList<>();
		for (String status : statuses) {
			statusesCodes.add(OpportunityStatus.getOppStatusFromStatusValue(status).getOppStatusCode());
		}
		this.statuses = statusesCodes;
	}
	public void addPolicyTypeCds() {
		if(CollectionUtils.isEmpty(lobs)){
			return;
		}

		if(CollectionUtils.isEmpty(policyTypeCds)) {
			policyTypeCds = new ArrayList<>();
		}

		//If Lob list contains condo or renters then use policytypecds to query the db
		Arrays.stream(PolicyType.values()).forEach(policyType -> {
			if(lobs.contains(policyType.name())) {
				policyTypeCds.addAll(policyType.getPolicyTypeCds());
			}
		});
	}
}

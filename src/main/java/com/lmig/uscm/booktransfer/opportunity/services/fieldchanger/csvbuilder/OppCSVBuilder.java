package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.csvbuilder;

import com.lmig.uscm.booktransfer.utilityservice.CSVBuilder;
import org.apache.commons.lang3.StringUtils;

public class OppCSVBuilder extends CSVBuilder {

	public OppCSVBuilder() {
		super();
	}

	@Override
	protected String handleEmptyValue(String value) {
		if (StringUtils.isEmpty(value)) {
			value = "N/A";
		}

		return value;
	}
}

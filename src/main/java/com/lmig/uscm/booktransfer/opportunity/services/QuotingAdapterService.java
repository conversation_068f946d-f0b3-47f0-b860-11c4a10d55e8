package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.quoteadapter.QuoteResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatusCode;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientException;
import reactor.core.publisher.Mono;

import java.util.Optional;

@Slf4j
public class QuotingAdapterService {

	private final WebClient quotingAdapterWebClient;

	private final String adapterUrl;

	public QuotingAdapterService(final WebClient quotingAdapterWebClient, final String url) {
		this.quotingAdapterWebClient = quotingAdapterWebClient;
		this.adapterUrl = url;
	}

	/**
	 * Retrieve latest QuoteResponse from quoting-bl-adapter
	 *
	 * @param opportunityId - int representing the opportunity
	 * @return Latest {@link QuoteResponse}
	 */
	public QuoteResponse getLatestQuoteResponseByOppId(final int opportunityId) {
		String url = adapterUrl + "/api/quote/response/opportunityId/" + opportunityId;
		log.info("Calling quoting adapter for quote response: {}", url);
		try {
			Optional<QuoteResponse> optionalQuoteResponse = quotingAdapterWebClient.get()
					.uri(url)
					.retrieve()
					.onStatus(HttpStatusCode::is4xxClientError, response -> response.bodyToMono(String.class)
							.flatMap(errorBody -> {
								log.error("Bad request {}", errorBody);
								return Mono.empty();
							}))
					.onStatus(HttpStatusCode::is4xxClientError, response -> response.bodyToMono(String.class)
							.flatMap(errorBody -> {
								log.error("quote adapter service failure: {}", errorBody);
								return Mono.empty();
							}))
					.bodyToMono(QuoteResponse.class)
					.blockOptional();
			if (optionalQuoteResponse.isPresent()) {
				return optionalQuoteResponse.get();
			}
		} catch (WebClientException e) {
			log.error("Error finding QuoteResponse for id: {}", opportunityId, e);
		}
		return null;
	}
}

package com.lmig.uscm.booktransfer.opportunity.domain.customeraccount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerAccountServiceResponseWrapper {
    private String success;
    private String error;
    private List<CustomerAccountServiceResponseData> data;
}

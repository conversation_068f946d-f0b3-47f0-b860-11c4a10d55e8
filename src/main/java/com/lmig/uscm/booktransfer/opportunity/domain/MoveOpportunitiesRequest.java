/*
 * Copyright (C) 2018, Liberty Mutual Group
 *
 * Created on 11/10/18 by n0210477
 */
package com.lmig.uscm.booktransfer.opportunity.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MoveOpportunitiesRequest {

	private List<Integer> opportunityIDs;
	private Integer bookTransferID;
	private String agentNum;
	@JsonProperty("sFDCID")
	private String sFDCID;
	@JsonProperty("nNumber")
	private String nNumber;
	private String subCode;
}

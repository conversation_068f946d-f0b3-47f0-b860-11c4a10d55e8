package com.lmig.uscm.booktransfer.opportunity.services.builders;

import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.AddressCleanseService;
import com.lmig.uscm.booktransfer.opportunity.services.CustomerAccountHelper;
import com.lmig.uscm.booktransfer.opportunity.services.EFTPaymentAccountsService;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import lombok.extern.slf4j.Slf4j;

/**
 * this is for initial opportunity upload/created or updating the existing
 * opportunity from SPQE
 */
@Slf4j
public class UploadOpportunityBuilder extends OpportunityBuilder {

	public UploadOpportunityBuilder(
			final OpportunityRepoHelper opportunityRepoHelper,
			final OpportunityConstructor opportunityConstructor,
			final QuoteReportItemHelper quoteReportItemHelper,
			final AddressCleanseService addressCleanseHelper,
			final EFTPaymentAccountsService eftPaymentAccountsService,
			final CustomerAccountHelper customerAccountHelper,
			final boolean isForSPQE,
			final String importPackageId
	) {
		super(opportunityRepoHelper,
				opportunityConstructor,
				quoteReportItemHelper,
				addressCleanseHelper,
				eftPaymentAccountsService,
				customerAccountHelper,
				isForSPQE,
				importPackageId);
		super.isForSPQE = isForSPQE;
	}

	@Override
	protected Opportunity getOpportunity(OpportunityCreationRequest opportunityCreationRequest)
			throws Exception {
		return opportunityConstructor.buildInitialOpportunity(opportunityCreationRequest);
	}

	@Override
	protected Opportunity updateMasterOpp(boolean isMasterOpp, Opportunity opportunity) {
		if (isMasterOpp) {
			log.info("Opportunity id {}  - Master Opp", opportunity.getOpportunityId());
			opportunity.setMasterOppID(String.valueOf(opportunity.getOpportunityId()));
			opportunity = opportunityRepoHelper.updateOpportunity(opportunity);
		}
		return opportunity;
	}
}

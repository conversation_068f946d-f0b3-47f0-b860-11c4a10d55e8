package com.lmig.uscm.booktransfer.opportunity.client.domain.enums;

public enum QuoteReportStatus {
	QUOTE_REPORT_UNQUOTED("Unquoted"),
	QUOTE_REPORT_QUOTED("Quoted"),
	QUOTE_REPORT_ISSUED("Issued"),
	QUOTE_REPORT_MISSING_REQUIRED_DATA("Missing Required Data"),
	QUOTE_REPORT_QUOTE_IN_PROGRESS("Quote In Progress"),
	QUOTE_REPORT_QUOTE_DECLINED("CA Pre-Bind Pause - Not Pursuing"),
	QUOTE_REPORT_QUOTE_WITHDRAWN("Withdrawn"),
	QUOTE_REPORT_QUOTED_IMPORTED("Quoted - Imported"),
	QUOTE_REPORT_HERITAGE("Heritage");

	private final String qrStatusMessage;

	QuoteReportStatus(String qrStatus) {
		this.qrStatusMessage = qrStatus;
	}

	public String getQuoteReportMessage() {
		return qrStatusMessage;
	}

	public static QuoteReportStatus getQuoteReportStatusFromCode(int oppCode) throws Exception {
		return OpportunityStatus.getOppStatusFromCode(oppCode).getQuoteReportStatus();
	}
}

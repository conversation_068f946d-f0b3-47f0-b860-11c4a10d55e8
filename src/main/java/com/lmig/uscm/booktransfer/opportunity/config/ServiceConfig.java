package com.lmig.uscm.booktransfer.opportunity.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.booktransfer.btsensitivedataservice.client.SensitiveDataServiceWebClient;
import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferServiceWebClient;
import com.lmig.uscm.booktransfer.email.client.EmailService;
import com.lmig.uscm.booktransfer.opportunity.client.config.OpportunityUrlProvider;
import com.lmig.uscm.booktransfer.opportunity.config.properties.AddressCleanseServiceOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.AuditLogRestApiOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.BTPaymentServiceOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.EmailProperties;
import com.lmig.uscm.booktransfer.opportunity.config.properties.EnvironmentProperties;
import com.lmig.uscm.booktransfer.opportunity.config.properties.QuoteAdapterOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.QuotingGuidelineOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.SensitiveDataServiceOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.ServiceUrlProperties;
import com.lmig.uscm.booktransfer.opportunity.config.properties.UploadPreprocessorOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.EFTPaymentAccountsOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.messaging.Publisher;
import com.lmig.uscm.booktransfer.opportunity.repo.LobGateway;
import com.lmig.uscm.booktransfer.opportunity.repo.LobInMemoryPersistence;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityEventRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJpaRepository;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.AddressCleanseHelper;
import com.lmig.uscm.booktransfer.opportunity.services.AddressCleanseService;
import com.lmig.uscm.booktransfer.opportunity.services.AuditLogHelper;
import com.lmig.uscm.booktransfer.opportunity.services.BTPaymentServiceHelper;
import com.lmig.uscm.booktransfer.opportunity.services.BusinessOpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.CustomerAccountHelper;
import com.lmig.uscm.booktransfer.opportunity.services.DownloadZipFileHelper;
import com.lmig.uscm.booktransfer.opportunity.services.ExportHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityCreationHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityEventHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.PropertyInfoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportService;
import com.lmig.uscm.booktransfer.opportunity.services.QuotingAdapterService;
import com.lmig.uscm.booktransfer.opportunity.services.QuotingGuidelineHelper;
import com.lmig.uscm.booktransfer.opportunity.services.ReportGenerator;
import com.lmig.uscm.booktransfer.opportunity.services.SensitiveDataHelper;
import com.lmig.uscm.booktransfer.opportunity.services.TransformationService;
import com.lmig.uscm.booktransfer.opportunity.services.UploadEventService;
import com.lmig.uscm.booktransfer.opportunity.services.EFTPaymentAccountsService;
import com.lmig.uscm.booktransfer.opportunity.services.builders.OpportunityConstructor;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.BoatCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.CreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.CreationStrategyFactory;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.FifthBoatCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.FifthVehicleCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.MotorcycleCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.PropertyCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.SPQEPreviousOppsCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.SplitBulkCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.FieldChangeHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate.UpdateEffectiveDateHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate.UpdateHomeLeadEffectiveDateHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate.UpdateLeadEffectiveDateHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate.UpdateNextTermDateHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate.UpdateSetEffectiveDateHelper;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.AuditLogWebClient;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.QuotingGuidelineWebClient;
import com.lmig.usconsumermarkets.booktransfer.btuploadpreprocessor.client.UploadPreprocessorWebClient;
import com.uscm.lmig.booktransfer.processresult.client.ProcessResultService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
@EnableConfigurationProperties({
		ServiceUrlProperties.class,
		EmailProperties.class,
		EnvironmentProperties.class,
		QuoteAdapterOauth2ClientDetails.class,
		SensitiveDataServiceOauth2ClientDetails.class,
		QuotingGuidelineOauth2ClientDetails.class,
		AuditLogRestApiOauth2ClientDetails.class,
		UploadPreprocessorOauth2ClientDetails.class,
		BTPaymentServiceOauth2ClientDetails.class,
		AddressCleanseServiceOauth2ClientDetails.class,
		EFTPaymentAccountsOauth2ClientDetails.class
})
public class ServiceConfig {

	@Value("${spring.profiles.active:Unknown}")
	private List<String> currentProfiles;

	@Value("${eighthcar.enabled}")
	private boolean enabled;

	@Bean
	public QuotingGuidelineHelper quotingGuidelineHelper(final QuotingGuidelineWebClient quotingGuidelineWebClient){
		return new QuotingGuidelineHelper(quotingGuidelineWebClient);
	}

	@Bean
	public BTPaymentServiceHelper paymentServiceHelper(
			@Qualifier("paymentServiceClient") final WebClient paymentServiceClient,
			final ServiceUrlProperties properties
	){
		return new BTPaymentServiceHelper(paymentServiceClient, properties.getPaymentServiceUrlProvider().getBaseUrl());
	}

	@Bean
	public AddressCleanseHelper addressCleanseHelper(
			@Qualifier("addressCleanseClient") final WebClient addressCleanseClient,
			final ServiceUrlProperties properties
	) {
		return new AddressCleanseHelper(addressCleanseClient, properties.getAddressCleanseUrlProvider().getBaseUrl());
	}

	@Bean
	public PropertyInfoHelper propertyInfoHelper(
			@Qualifier("propertyInfoClient") final WebClient propertyInfoClient,
			final ServiceUrlProperties properties
	) {
		return new PropertyInfoHelper(propertyInfoClient, properties.getPropertyInfoUrlProvider().getBaseUrl());
	}

	@Bean
	public QuotingAdapterService quotingAdapterService(@Qualifier("quotingAdapterWebClient") final WebClient quotingAdapterWebClient, final ServiceUrlProperties properties) {
		return new QuotingAdapterService(quotingAdapterWebClient, properties.getQuotingAdapterUrlProvider().getBaseUrl());
	}

	@Bean
	public TransformationService transformationService(@Qualifier("transformationWebClient") final WebClient transformationServiceWebClient,
													   final ServiceUrlProperties properties) {
		return new TransformationService(transformationServiceWebClient, properties.getTransformationUrlProvider().getBaseUrl());
	}
	@Bean
	public EFTPaymentAccountsService eftPaymentAccountsService(@Qualifier("eftPaymentAccountsWebClient") final WebClient eftPaymentAccountsWebClient, ServiceUrlProperties properties){
		return new EFTPaymentAccountsService(eftPaymentAccountsWebClient, properties.getEftPaymentAccountsUrlProvider().getBaseUrl());
	}

	@Bean
	public QuoteReportService quoteReportService(
			@Qualifier("quoteReportWebClient") final WebClient quoteReportWebClient,
			final ServiceUrlProperties properties) {
		return new QuoteReportService(quoteReportWebClient, properties);
	}

	@Bean
	public CustomerAccountHelper customerAccountHelper(@Qualifier("customerAccountWebClient") final WebClient customerAccountWebClient, final ServiceUrlProperties properties, @Qualifier("auditLogHelper") final AuditLogHelper auditLogHelper) {
		return new CustomerAccountHelper(customerAccountWebClient, properties.getCustomerAccountUrlProvider().getBaseUrl(), auditLogHelper);
	}

	@Bean
	public OpportunityCreationHelper opportunityCreationHelper(
			final OpportunityRepoHelper opportunityRepoHelper,
			final OpportunityConstructor opportunityConstructor,
			final OpportunityConstructor businessOpportunityConstructor,
			final QuoteReportItemHelper quoteReportItemHelper,
			final Publisher publisher,
			final BookTransferServiceWebClient bookTransferService,
			final OpportunityEventHelper opportunityEventHelper,
			final CreationStrategyFactory creationStrategyFactory,
			final UploadEventService uploadEventService,
			final TransformationService transformationService,
			final SensitiveDataHelper sensitiveDataHelper,
			final AddressCleanseService addressCleanseService,
			final EFTPaymentAccountsService eftPaymentAccountsService,
			final CustomerAccountHelper customerAccountHelper
	) {
		return new OpportunityCreationHelper(opportunityRepoHelper, opportunityConstructor, businessOpportunityConstructor,
				quoteReportItemHelper, publisher, bookTransferService, opportunityEventHelper, creationStrategyFactory,
				uploadEventService, transformationService, sensitiveDataHelper, addressCleanseService,
				eftPaymentAccountsService, customerAccountHelper);
	}

	@Bean
	public AuditLogHelper auditLogHelper(final AuditLogWebClient auditLogWebClient){
		return new AuditLogHelper(auditLogWebClient);
	}

	@Bean
	public OpportunityEventHelper opportunityEventHelper(
			final OpportunityEventRepo opportunityEventRepo,
			final OpportunityUrlProvider opportunityUrlProvider,
			final AuditLogHelper auditLogHelper
	) {
		return new OpportunityEventHelper(
				opportunityEventRepo, opportunityUrlProvider, auditLogHelper);
	}

	@Bean
	public OpportunityRepoHelper opportunityRepoHelper(final OpportunityJpaRepository opportunityRepository,
													   final OpportunityJDBCRepo opportunityJDBCRepo) {
		return new OpportunityRepoHelper(opportunityRepository, opportunityJDBCRepo);
	}

	@Bean("homeEffectiveDateHelper")
	public UpdateEffectiveDateHelper updateHomeLeadEffectiveDateHelper(final OpportunityHelper opportunityHelper,
																	   final BookTransferService bookTransferService,
																	   final ExportHelper exportHelper,
																	   final OpportunityRepoHelper opportunityRepoHelper) {
		return new UpdateHomeLeadEffectiveDateHelper(opportunityHelper, bookTransferService, exportHelper, opportunityRepoHelper);
	}

	@Bean("trueLeadEffectiveDateHelper")
	public UpdateEffectiveDateHelper updateLeadEffectiveDateHelper(final OpportunityHelper opportunityHelper,
																   final BookTransferService bookTransferService,
																   final ExportHelper exportHelper,
																   final OpportunityRepoHelper opportunityRepoHelper) {
		return new UpdateLeadEffectiveDateHelper(opportunityHelper, bookTransferService, exportHelper, opportunityRepoHelper);
	}

	@Bean("nextTermEffectiveDateHelper")
	public UpdateEffectiveDateHelper updateNextTermEffectiveDateHelper(final OpportunityHelper opportunityHelper,
																	   final ExportHelper exportHelper,
																	   final OpportunityRepoHelper opportunityRepoHelper) {
		return new UpdateNextTermDateHelper(opportunityHelper, exportHelper, opportunityRepoHelper);
	}

	@Bean("setDateEffectiveDateHelper")
	public UpdateEffectiveDateHelper updateSetEffectiveDateHelper(final OpportunityHelper opportunityHelper,
																  final ExportHelper exportHelper,
																  final OpportunityRepoHelper opportunityRepoHelper) {
		return new UpdateSetEffectiveDateHelper(opportunityHelper, exportHelper, opportunityRepoHelper);
	}

	@Bean
	public ExportHelper exportHelper(EmailService emailSender,
									 EmailProperties emailProperties,
									 EnvironmentProperties environmentProperties) {
		return new ExportHelper(emailSender, emailProperties, environmentProperties);
	}

	@Bean
	public DownloadZipFileHelper downloadZipFileHelper(final BookTransferService bookTransferService,
													   final OpportunityRepoHelper opportunityRepoHelper) {
		return new DownloadZipFileHelper(bookTransferService, opportunityRepoHelper);
	}

	@Bean
	public BusinessOpportunityHelper businessOpportunityHelper(
			final QuoteReportItemHelper quoteReportItemHelper,
			final BookTransferService bookTransferService,
			final ReportGenerator reportGenerator,
			final ProcessResultService processResultService,
			final QuotingAdapterService quotingAdapterService,
			final TransformationService transformationService,
			final SensitiveDataHelper sensitiveDataHelper,
			final OpportunityRepoHelper opportunityRepoHelper,
			final QuotingGuidelineHelper quotingGuidelineHelper,
			final BTPaymentServiceHelper paymentServiceHelper,
			final CustomerAccountHelper customerAccountHelper,
			final ObjectMapper objectMapper) {
		return new BusinessOpportunityHelper(
				quoteReportItemHelper,
				bookTransferService,
				reportGenerator,
				processResultService,
				quotingAdapterService,
				transformationService,
				sensitiveDataHelper,
				opportunityRepoHelper,
				quotingGuidelineHelper,
				paymentServiceHelper,
				customerAccountHelper,
				objectMapper);
	}

	@Bean
	public OpportunityConstructor opportunityConstructor(final OpportunityHelper opportunityHelper,
														 final TransformationService transformationService) {
		return new OpportunityConstructor(opportunityHelper, transformationService);
	}

	@Bean("businessOpportunityConstructor")
	public OpportunityConstructor businessOpportunityConstructor(final BusinessOpportunityHelper businessOpportunityHelper,
																 final TransformationService transformationService) {
		return new OpportunityConstructor(businessOpportunityHelper, transformationService);
	}

	@Bean
	public UploadEventService uploadEventService(final ServiceUrlProperties properties) {
		return new UploadEventService(properties.getUploadServiceUrlProvider());
	}

	@Bean
	public FieldChangeHelper fieldChangeHelper(final OpportunityHelper opportunityHelper,
											   final QuoteReportItemHelper quoteReportItemHelper,
											   final UploadEventService uploadEventService,
											   final BookTransferService bookTransferService) {
		return new FieldChangeHelper(opportunityHelper, quoteReportItemHelper, uploadEventService, bookTransferService);
	}

	@Bean
	public CreationStrategyFactory creationStrategyFactory(final SensitiveDataHelper sensitiveDataHelper) {
		List<CreationStrategy> orderedStrategies = new ArrayList<>(Arrays.asList(
				// Split bulk XML by each LOB Rq tag. Must occur before all other strategies
				new SplitBulkCreationStrategy(sensitiveDataHelper),
				// Split motorcycles into separate Opportunities. Needs to happen before FifthVehicleCreationStrategy
				new MotorcycleCreationStrategy(),
				// Split into 4 or 8 vehicles per Opportunity
				new FifthVehicleCreationStrategy(enabled),
				// Split boats from home policies
				new BoatCreationStrategy(),
				// Split into 4 boats per Opportunity, this must run after BoatCreationStrategy but before SPQE
				new FifthBoatCreationStrategy(),
				// Split each location into its own DFIRE or HOME Opportunity if it has a corresponding dwelling node
				new PropertyCreationStrategy(),
				// Link SPQE Opportunities to previously created Opportunities. Needs to happen after all splitting strategies
				new SPQEPreviousOppsCreationStrategy()
		));
		return new CreationStrategyFactory(orderedStrategies);
	}

	@Bean
	public SensitiveDataHelper sensitiveDataHelper(final SensitiveDataServiceWebClient sensitiveDataServiceClient,
				   								final UploadPreprocessorWebClient uploadPreprocessorWebClient) {
		return new SensitiveDataHelper(sensitiveDataServiceClient, uploadPreprocessorWebClient, currentProfiles);
	}



	@Bean
	public LobGateway lobGateway() {
		return new LobInMemoryPersistence();
	}
}

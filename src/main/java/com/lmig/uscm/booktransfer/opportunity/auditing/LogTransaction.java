package com.lmig.uscm.booktransfer.opportunity.auditing;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Place annotation on any method to log a Splunk Transaction
 * If the method throws an exception the transaction will log as a failure
 * Otherwise it will log as a success
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LogTransaction {
    String value();
}

package com.lmig.uscm.booktransfer.opportunity.domain;

import java.util.ArrayList;
import java.util.List;

/**
 * Currently, non actionable refers to "non quotable"
 * However, we can reuse this in the future for more actions
 */
public class ActionableOpportunitiesResponse {
	private List<Integer> nonActionableOpps = new ArrayList<>();
	private List<Integer> actionableOpps = new ArrayList<>();
	
	public List<Integer> getNonActionableOpps() {
		return nonActionableOpps;
	}
	public void setNonActionableOpps(List<Integer> nonActionableOpps) {
		this.nonActionableOpps = nonActionableOpps;
	}
	public void addNonActionableOpp(Integer oppId) {
		this.nonActionableOpps.add(oppId);
	}
	public List<Integer> getActionableOpps() {
		return actionableOpps;
	}
	public void setActionableOpps(List<Integer> actionableOpps) {
		this.actionableOpps = actionableOpps;
	}
	public void addActionableOpp(Integer oppId) {
		this.actionableOpps.add(oppId);
	}
}

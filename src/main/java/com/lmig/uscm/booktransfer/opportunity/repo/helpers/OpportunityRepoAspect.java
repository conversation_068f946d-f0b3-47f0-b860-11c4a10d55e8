package com.lmig.uscm.booktransfer.opportunity.repo.helpers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityEventHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;

/**
 * Creates OpportunityEvent records after a method annotated with PublishOpportunityEventAnno is processed Specifically
 * for when we Save or Update an Opportunity
 */

@SuppressWarnings("unchecked")
@Aspect
@Component
@Slf4j
public class OpportunityRepoAspect {

	private final OpportunityEventHelper opportunityEventHelper;

	public OpportunityRepoAspect(final OpportunityEventHelper opportunityEventHelper) {
		this.opportunityEventHelper = Objects.requireNonNull(
				opportunityEventHelper, "OpportunityRepoAspect was created with a null OpportunityEventHelper");
	}

	/**
	 * PointCutter targeting OpportunityRepoHelper methods annotated with PublishOpportunityEventAnno Allows us to
	 * capture arguments before the method, and save OpportunityEvent records after finishing successfully
	 */
	@Around(value = "@annotation(com.lmig.uscm.booktransfer.opportunity.repo.helpers.PublishOpportunityEventAnno)")
	public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
		// CREATE, UPDATE, or CREATED_OR_UPDATED
		String annotationParam = getAnnotationEventType(joinPoint);

		List<Opportunity> oppsFromArgs = new ArrayList<>();
		if(annotationParam.equals(PublishOpportunityEventAnno.CREATED_OR_UPDATED_EVENT)) {
			// if we don't know which event Type it is we need to rely on the Opps passed to the method to verify
			oppsFromArgs = getAnyOppsFromMethodArgs(joinPoint.getArgs());
		}

		// run the actual method. This will return a List<Opportunity> or a lone Opportunity that have been saved
		final Object returnValue = joinPoint.proceed();

		// Save an OpportunityEvent for every Opportunity returned from the repo method annotated
		// Our methods are interfaced to return 1 or more Opportunities.
		try {
			if (returnValue instanceof List) {
				List<Opportunity> oppsList = (ArrayList<Opportunity>) convertObjectToList(returnValue);
				// filter opps for personal linetype as we do not want to create opportunityEvents for small commercial opportunities
				oppsList = oppsList.stream().filter(o -> !LineType.Business.equals(o.getLineType()))
						.collect(Collectors.toList());

				if(!oppsList.isEmpty()) {
					processMultipleOppEvents(oppsList, annotationParam, oppsFromArgs);
				}
			} else {

				Opportunity savedOpportunity = (Opportunity) returnValue;
				// Do not create opportunityEvents for Small commercial opportunities by returning here and skipping processOpportunityEvent
				if(savedOpportunity == null || LineType.Business.equals(savedOpportunity.getLineType())) {
					return returnValue;
				}
				String oppEventType = annotationParam.equals(PublishOpportunityEventAnno.CREATED_OR_UPDATED_EVENT)
						? getEventTypeFromMethodArgs(oppsFromArgs, savedOpportunity)
						: annotationParam;
				try {
					opportunityEventHelper.processOpportunityEvent(savedOpportunity, oppEventType);
				} catch(Exception e) {
					// fail gracefully, logging handled with LoggingAspect.java -> LogTransaction Annotation
				}
			}
		} catch (Exception e) {
			log.error("Error PublishOpportunityEventAnno", e);
		}

		return returnValue;
	}

	protected void processMultipleOppEvents(
			final List<Opportunity> savedOpps,
			String eventType,
			List<Opportunity> oppsFromArgs
	) {
		try {
			opportunityEventHelper.processOpportunityEvent(
					savedOpps,
					savedOpps.stream().map(opp -> eventType.equals(PublishOpportunityEventAnno.CREATED_OR_UPDATED_EVENT)
							? getEventTypeFromMethodArgs(oppsFromArgs, opp)
							: eventType
					).collect(Collectors.toList()));
		} catch(Exception e) {
			// fail gracefully, logging handled with LoggingAspect.java -> LogTransaction Annotation
		}
	}

	/**
	 * If we have a matching Opportunity in the oppsFromArgs then we know this is an UPDATE event
	 * If we have no matching Opp, then we know this is a new Opp and its a SAVE.
	 *
	 * @param oppsFromArgs - any Opps that were passed to the method being called
	 * @param opportunity - the Opportunity that was saved or updated
	 * @return the eventType - UPDATE or CREATE
	 */
	protected String getEventTypeFromMethodArgs(List<Opportunity> oppsFromArgs, Opportunity opportunity) {
		return isSavedOppIdPresentInMethodArgs(oppsFromArgs, opportunity) ? PublishOpportunityEventAnno.UPDATED_EVENT
				: PublishOpportunityEventAnno.CREATED_EVENT;
	}

	/**
	 * Retrieve any Opportunities from the parameters of the method
	 * We use the method params to verify if the eventType is a save or update
	 * depending on if the Opps being passed to the method have ids or not already
	 * <p>
	 * We need to make copies of the params to new Opportunity instances (hence the stream.map)
	 * otherwise the id will be altered when the opp is actually saved.
	 * <p>
	 * We expect either 1 or more Opportunities as a param to our save methods.
	 */
	protected List<Opportunity> getAnyOppsFromMethodArgs(Object[] methodArgs) {
		ArrayList<Opportunity> methodArgsOpps;
		if (methodArgs[0] instanceof List) {
			methodArgsOpps = (ArrayList<Opportunity>) convertObjectToList(methodArgs[0]);
		} else {
			methodArgsOpps = new ArrayList<>(Arrays.asList((Opportunity) methodArgs[0]));
		}

		return methodArgsOpps.stream().map(o -> new Opportunity(o.getOpportunityId()))
				.collect(Collectors.toList());
	}

	protected boolean isSavedOppIdPresentInMethodArgs(List<Opportunity> methodArgsOpps, Opportunity savedOpportunity) {
		List<Opportunity> matchingOpp = methodArgsOpps.stream()
				.filter(o -> o.getOpportunityId() == savedOpportunity.getOpportunityId()).collect(Collectors.toList());
		return !matchingOpp.isEmpty();
	}

	/**
	 * Retrieves the EventType from annotation valid values:(CREATED, UPDATED, or CREATED_OR_UPDATED_EVENT)
	 */
	protected String getAnnotationEventType(final ProceedingJoinPoint proceedingJoinPoint) {
		final PublishOpportunityEventAnno publishOppEventAnnotation =
				getMethodFromJointPoint(proceedingJoinPoint).getAnnotation(PublishOpportunityEventAnno.class);

		if (publishOppEventAnnotation != null && StringUtils.isNotEmpty(publishOppEventAnnotation.value())) {
			return publishOppEventAnnotation.value();
		}

		// if no value specified then default to CREATED_OR_UPDATED_EVENT
		return PublishOpportunityEventAnno.CREATED_OR_UPDATED_EVENT;
	}

	protected Method getMethodFromJointPoint(final ProceedingJoinPoint proceedingJoinPoint) {
		final MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
		return methodSignature.getMethod();
	}

	protected static List<?> convertObjectToList(Object obj) {
		List<?> list = new ArrayList<>();
		if (obj.getClass().isArray()) {
			list = Arrays.asList((Object[]) obj);
		} else if (obj instanceof Collection) {
			list = new ArrayList<>((Collection<?>) obj);
		}
		return list;
	}

}

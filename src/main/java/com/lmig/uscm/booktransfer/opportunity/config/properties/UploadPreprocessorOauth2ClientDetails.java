package com.lmig.uscm.booktransfer.opportunity.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;

@ConfigurationProperties(prefix = "oppservice.oauth2.upload.preprocessor.client")
public class UploadPreprocessorOauth2ClientDetails extends ClientCredentialsResourceDetails {

}

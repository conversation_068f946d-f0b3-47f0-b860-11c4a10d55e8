package com.lmig.uscm.booktransfer.opportunity.config.properties;

import com.lmig.usconsumermarkets.booktransfer.security.starter.properties.WebClientConfigProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties("bt.webclient.quote-report")
public class QuoteReportOauth2ClientDetails extends WebClientConfigProperties {
    public QuoteReportOauth2ClientDetails() {
    }
}





package com.lmig.uscm.booktransfer.opportunity.client.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lmig.uscm.booktransfer.opportunity.domain.preview.Preview;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PreviewDataResponse {

	@JsonProperty(value = "previewBuilder")
	private Preview preview;

	// serializes as quoteResponse
	/**
	 * Serializes as "quoteResponse" to keep contract with UI
	 */
	@JsonProperty(value = "quoteResponse")
	private PreviewDataQuoteResults quoteResponse;

	private final List<String> missingData;

	public PreviewDataResponse() {
		this.missingData = new ArrayList<>();
	}

	public void addMissingDataPoint(String missingDataName) {
		if (!this.missingData.contains(missingDataName)) {
			this.missingData.add(missingDataName);
		}
	}

}

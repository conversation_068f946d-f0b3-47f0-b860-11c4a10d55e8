package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.xpath.XPathExpressionException;
import java.util.ArrayList;
import java.util.List;

public class FifthBoatCreationStrategy implements CreationStrategy {

    private static final Integer MAX_BOATS_PER_OPPORTUNITY = 4;

    public boolean shouldResolveCreationRequest(final OpportunityCreationRequest creationRequest) {
        try {
            int boatCount = AcordHelper.getWatercrafts(creationRequest.getUploadedACORD()).getLength();
            if (boatCount > MAX_BOATS_PER_OPPORTUNITY) {
                return true;
            }
        } catch (XPathExpressionException | NullPointerException e) {
            // Just eat it, eat it, just eat it, eat it, ooh
        }
        return false;
    }

    @Override
    public void resolveCreationBundles(final List<CreationStrategyBundle> creationBundles, final OpportunityCreationResponse creationResponse) {
        creationBundles.forEach(bundle -> resolveCreationBundle(bundle, creationResponse));
    }

    public void resolveCreationBundle(final CreationStrategyBundle creationBundle, final OpportunityCreationResponse creationResponse) {
        List<OpportunityCreationRequest> incomingCreationRequests = new ArrayList<>(creationBundle.getRequests());

        for (OpportunityCreationRequest creationRequest : incomingCreationRequests) {
            if (!shouldResolveCreationRequest(creationRequest)) {
                continue;
            }

            try {
                splitRequestsPerMaxWatercraft(creationBundle, creationRequest);
            } catch (XPathExpressionException e) {
                creationResponse.incrementFailedCreationCount();
            }
        }
    }

    private void splitRequestsPerMaxWatercraft(final CreationStrategyBundle creationBundle, final OpportunityCreationRequest creationRequest) throws XPathExpressionException {
        if (!creationRequest.isDerivedFromHome()) {
            creationRequest.setAsMasterOpp();
        }

        Document currRequestDoc = creationRequest.getUploadedACORD();
        NodeList allBoats = AcordHelper.getWatercrafts(currRequestDoc);

        // remove all boats from master document
        AcordHelper.removeAllWatercraft(currRequestDoc);

        int boatCount = allBoats.getLength();
        OpportunityCreationRequest emptyRequestToClone = creationRequest.clone();

        OpportunityCreationRequest currRequest = creationRequest;
        for (int i = 0; i < boatCount; i++) {
            if (i != 0 && i % MAX_BOATS_PER_OPPORTUNITY == 0) {
                currRequest = emptyRequestToClone.clone();
                // remove all boat accessories
                AcordHelper.removeAllWatercraftAccessories(currRequest.getUploadedACORD());
                // add to same bundle to link masterOppId after Opportunity creation
                creationBundle.addCreationRequest(currRequest);
            }
            // add boat to new ACORD
            Node boatNode = allBoats.item(i);
            Document addedRequestDoc = currRequest.getUploadedACORD();
            AcordHelper.addBoat(addedRequestDoc, boatNode);
        }

        // skip the first index if boat creation request is derived from a home opportunity because we don't want to
        // update the CurrentTermAmt xpath for home policies which is the first one in the bundle
        for (int i = !creationRequest.isDerivedFromHome() ? 0 : 1; i < creationBundle.getRequests().size(); i++) {
            OpportunityCreationRequest request = creationBundle.getCreationRequest(i);
            AcordHelper.setBoatCurrentTermAmount(request.getUploadedACORD());
        }
    }

    @Override
    public CreationStrategyName getStrategyName() {
        return CreationStrategyName.SplitUpEveryFiveBoats;
    }
}

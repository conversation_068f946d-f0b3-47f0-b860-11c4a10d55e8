package com.lmig.uscm.booktransfer.opportunity.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.BusinessAcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import com.uscm.lmig.booktransfer.processresult.client.ProcessResultService;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;

import javax.xml.transform.TransformerException;
import javax.xml.xpath.XPathExpressionException;

@Slf4j
public class BusinessOpportunityHelper extends OpportunityHelper {


	public BusinessOpportunityHelper(
			QuoteReportItemHelper quoteReportItemHelper,
			BookTransferService bookTransferService,
			ReportGenerator reportGenerator,
			ProcessResultService processResultService,
			QuotingAdapterService quotingAdapterService,
			TransformationService transformationService,
			SensitiveDataHelper sensitiveDataHelper,
			OpportunityRepoHelper opportunityRepoHelper,
			QuotingGuidelineHelper quotingGuidelineHelper,
			BTPaymentServiceHelper paymentServiceHelper,
			CustomerAccountHelper customerAccountHelper,
			ObjectMapper objectMapper) {
		super(
				quoteReportItemHelper,
				bookTransferService,
				reportGenerator,
				processResultService,
				quotingAdapterService,
				transformationService,
				sensitiveDataHelper,
				opportunityRepoHelper,
				quotingGuidelineHelper,
				paymentServiceHelper,
				customerAccountHelper,
			objectMapper);
	}

	/**
	 * Only method that extracts information from Opportunity.OriginalXML into other Opportunity fields
	 */
	@Override
	public Opportunity updateNewOpportunity(final OpportunityCreationRequest opportunityCreationRequest,
											final Opportunity opportunity,
											Document workingDoc) throws OpportunityException {
		translateBlAcordDataIntoOpportunityData(opportunity, workingDoc);
		// Must have non-null opportunity.data
		validateMissingData(opportunity);
		return opportunity;
	}

	private Opportunity translateBlAcordDataIntoOpportunityData(final Opportunity opportunity, final Document originalXml) {
		try {
			opportunity.setBusinessType(LineOfBusiness.determineLobForBusinessLineType(originalXml).name());
			opportunity.setState(BusinessAcordHelper.getRatingState(originalXml));
			opportunity.setEffectiveDate(AcordHelper.getEffectiveDt(originalXml));
			opportunity.setAgencyId(AcordHelper.getAgencyId(originalXml));
			String customerName = BusinessAcordHelper.getCustomerName(originalXml);
			opportunity.setCustomerName(customerName.substring(0, Math.min(customerName.length(), 1000)));
			opportunity.setPriorCarrierGuid(BusinessAcordHelper.getPriorPolicyNumber(originalXml));
			opportunity.setPriorPremium(AcordHelper.extractPriorPremium(originalXml));
			String naiccd = AcordHelper.getNaicCd(originalXml);
			if (StringUtils.isNotEmpty(naiccd) && naiccd.length() <= 10) {
				opportunity.setNAICCd(naiccd);
			}
			opportunity.setData(XmlHelper.getDocumentString(originalXml));
		} catch (XPathExpressionException | TransformerException e) {
			log.error("Could not extract data from Business Opportunity XML", e);
		}

		return opportunity;
	}
}

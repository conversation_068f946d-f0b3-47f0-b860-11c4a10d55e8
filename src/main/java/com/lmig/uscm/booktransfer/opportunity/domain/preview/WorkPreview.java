package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;

public class WorkPreview extends Preview {
	private String completeAddress;

	protected WorkPreview() {
		super();
	}

	public String getCompleteAddress() {
		return completeAddress;
	}

	public void setCompleteAddress(String completeAddress) {
		this.completeAddress = completeAddress;
	}

	@Override
	protected void setResponseCommonMissingData(Document originalXml, PreviewDataResponse response) {
		//No missing data to add yet
		return;
	}

	@Override
	public LineOfBusiness getLob() {
		return LineOfBusiness.WORK;
	}

	@Override
	protected void setLOBRequiredData(Document originalXml, PreviewDataResponse response)
			throws ParserConfigurationException, SAXException, XPathExpressionException, IOException {
		this.setCompleteAddress(buildAddress(AcordHelper.getLocationAddr(originalXml)));
	}

}

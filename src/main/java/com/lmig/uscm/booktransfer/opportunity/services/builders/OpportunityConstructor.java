/*
 * Copyright (C) 2018, Liberty Mutual Group
 *
 * Created on 11/23/18 by n0210477
 */
package com.lmig.uscm.booktransfer.opportunity.services.builders;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.TransformationService;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.springframework.lang.Nullable;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.time.LocalDateTime;

/**
 * Constructs Opportunity from XML
 */
public class OpportunityConstructor {
	private final OpportunityHelper opportunityHelper;
	private final TransformationService transformationService;

	public OpportunityConstructor(final OpportunityHelper opportunityHelper,
								  final TransformationService transformationService) {
		this.opportunityHelper = opportunityHelper;
		this.transformationService = transformationService;
	}

	/**
	 * Formats xml and trims white space so is easier to read and use through
	 * application lifecycle
	 *
	 * @param xmlString xml string to clean up
	 * @return String formatted as a document
	 */
	public Document formatAndConvertXMLFromString(String xmlString)
			throws IOException, SAXException, ParserConfigurationException {
		return XmlHelper.getDocument(XmlHelper.removeXmlProlog(xmlString));
	}

	/**
	 * Builds an Opportunity from xml
	 *
	 * @param opportunityRequest requested info needed to make the opportunity
	 * @return new opportunity
	 */
	public Opportunity buildInitialOpportunity(OpportunityCreationRequest opportunityRequest)
			throws Exception {
		Opportunity opportunity = new Opportunity();
		opportunity.setUploadEventID(opportunityRequest.getUploadEventID());
		opportunity.setBookTransferID(opportunityRequest.getBookTransferID());
		opportunity.setOriginalXML(XmlHelper.getDocumentString(opportunityRequest.getUploadedACORD()));
		opportunity.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		if (opportunityRequest.getMasterOppID() != null) {
			opportunity.setMasterOppID(String.valueOf(opportunityRequest.getMasterOppID()));
		}
		opportunity.setOppPriorCarrier(opportunityRequest.getPriorCarrier());
		opportunity.setTimestampUpload(LocalDateTime.now());
		opportunity.setLineType(validateOrDefaultLineType(opportunityRequest.getLineType()));
		opportunity.setNNumber(opportunityRequest.getNNumber());

		return opportunity;
	}

	/**
	 * Defaults LineType to "Personal" if null or "All"
	 */
	private LineType validateOrDefaultLineType(LineType lineType) {
		if (lineType == null || lineType.equals(LineType.All)) {
			// default to Personal
			return LineType.Personal;
		}
		return lineType;
	}

	/**
	 * Updates existing opportunity from the xml
	 */
	public Opportunity updateOpportunity(OpportunityCreationRequest opportunityCreationRequest, Opportunity opportunity, Document workingDoc)
			throws Exception {
		opportunity = opportunityHelper.updateNewOpportunity(opportunityCreationRequest, opportunity, workingDoc);
		opportunity.setData(XmlHelper.removeXmlProlog(opportunity.getData()));
		return opportunity;
	}

	/**
	 * Checks if the subcode on the opportunity is different in xml and updates
	 * accordingly
	 *
	 * @param opportunity opportunity to update
	 * @param xml to check for new subcode
	 * @return Opportunity with correct subcode
	 */
	public Opportunity checkForUpdatedSubCode(OpportunityCreationRequest opportunityCreationRequest, Opportunity opportunity, Document xml) throws Exception {
		return opportunityHelper.checkForUpdatedSubCode(opportunityCreationRequest, opportunity, xml);
	}

	/**
	 * Formats the opportunity for use with rules engine
	 *
	 * @param xmlDoc                     doc to update
	 * @param opportunityCreationRequest info to update doc with
	 * @return updated document
	 */

	public Document formatXmlForOpportunity(Document xmlDoc, OpportunityCreationRequest opportunityCreationRequest)
			throws Exception {
		xmlDoc = AcordHelper.defaultInsuredOrPrincipalPersonName(xmlDoc);
		AcordHelper.addCommercialName(xmlDoc);
		AcordHelper.defaultProducerInfo(xmlDoc);
		xmlDoc = OpportunityUtil.setAgentNumber(xmlDoc, opportunityCreationRequest.getSubCode(), opportunityCreationRequest.getLineType());

		xmlDoc = orderInsureds(xmlDoc);

		return xmlDoc;
	}

	/**
	 * This orders the Insured/CoInsured in the proper order where the insured is
	 * first and the co insured is second
	 *
	 * @return Document
	 */
	public Document orderInsureds(Document xml) throws XPathExpressionException {
		NodeList insuredNodes = AcordHelper.getInsuredOrPrincipals(xml);

		if (insuredNodes.getLength() != 0 && !AcordHelper.isInsuredORPrincipalPrimary(insuredNodes, 0)) {
			moveInsuredAboveCoInsured(xml, insuredNodes, determineInsuredNodeLocation(insuredNodes));
		}

		return xml;
	}

	private Document moveInsuredAboveCoInsured(Document xml, NodeList insuredNodes, int insuredLocation) {
		if (insuredLocation == 0) { // If the location is 0 no insured exists
			return xml;
		}

		Node insuredOrPrincipal = insuredNodes.item(0).getParentNode();
		insuredOrPrincipal.insertBefore(insuredNodes.item(insuredLocation), insuredNodes.item(0));

		return xml;
	}

	private int determineInsuredNodeLocation(NodeList insuredOrPrincipalNodes) throws XPathExpressionException {
		int insuredLocation = 0; // Will return zero if it doesn't exist
		for (int i = 0; i < insuredOrPrincipalNodes.getLength(); i++) {
			if (AcordHelper.isInsuredORPrincipalPrimary(insuredOrPrincipalNodes, i)) {
				insuredLocation = i;
				break;
			}
		}
		return insuredLocation;
	}

	/**
	 * Runs rules against the passed Document and returns a different, resulting Document
	 */
	public Document runRules(Document xmlDoc, String packageId, @Nullable Integer oppId) {
		return transformationService.runRules(xmlDoc, packageId, oppId);
	}

}

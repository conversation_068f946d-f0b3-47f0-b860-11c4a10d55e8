package com.lmig.uscm.booktransfer.opportunity.client.domain;

import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO of OpportunityResult.
 * OpportunityResult is used in scheduler result service and partner-service and in SchedulerOutputMessage.class and ScheduleResult.class
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpportunityResultDTO {
    private Date exportDate;
    private Integer opportunityId;
    private String result;
    private String customerName;
    private Date effectiveDate;

    public OpportunityResultDTO(Integer opportunityId, OpportunityStatus opportunityStatus, Date effectiveDate) {
        this.opportunityId = opportunityId;
        this.result = opportunityStatus.getOppStatusValue();
        this.effectiveDate = effectiveDate;
        //to make it today
        this.exportDate = new Date();
    }
}

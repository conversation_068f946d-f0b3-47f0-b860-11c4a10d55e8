/*
 * Copyright (C) 2017, Liberty Mutual Group
 *
 * Created on Mar 28, 2017
 */

package com.lmig.uscm.booktransfer.opportunity.controller;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.common.annotations.VisibleForTesting;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityDetails;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.ScheduleRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OriginSource;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.SourceType;
import com.lmig.uscm.booktransfer.opportunity.domain.ActionableOpportunitiesResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.MoveOpportunitiesRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.PriorCarrierData;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequestForOppIds;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.EffectiveDateRange;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunityTopicException;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.ChangeEffectiveDateResponse;
import com.lmig.uscm.booktransfer.opportunity.repo.LobGateway;
import com.lmig.uscm.booktransfer.opportunity.services.DownloadZipFileHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityCreationHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate.UpdateEffectiveDateHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import com.uscm.lmig.booktransfer.processresult.client.domain.ProcessResultException;
import jakarta.persistence.EntityNotFoundException;

import com.lmig.uscm.booktransfer.opportunity.client.domain.DetokenizeRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.json.JSONObject;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lmig.uscm.booktransfer.opportunity.auditing.SplunkName;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunitySensitiveDataException;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.FieldChangeHelper;
import org.springframework.web.multipart.MultipartFile;

/**
 * New V3 Opportunity Controller will be used to Deprecate OpportunityController All methods will be locked down with
 * Security. Endpoints will be as truly RESTFUL as possible.
 * <p>
 * CREATE: POST v3/opportunity READS: GET is preferred. Can use POST when request body is needed. UPDATES: PUT - when
 * replacing the entire Opp, PATCH - when updating only select fields of the Opp DELETES:
 */
@CrossOrigin(origins = "*")
@RestController
@Component
@RequestMapping(value = "/v3/opportunity")
@Slf4j
public class OpportunityControllerV3 {
	private OpportunityRepoHelper opportunityRepoHelper;
	private OpportunityHelper opportunityHelper;

	private final DownloadZipFileHelper downloadZipFileHelper;
	private final FieldChangeHelper fieldChangeHelper;

	private Map<String, UpdateEffectiveDateHelper> updateEffectiveDateDynamicMap;
	private final OpportunityCreationHelper opportunityCreationHelper;
	private final LobGateway lobGateway;


	public OpportunityControllerV3(
			final OpportunityHelper opportunityHelper,
			final OpportunityRepoHelper opportunityRepoHelper,
			DownloadZipFileHelper downloadZipFileHelper,
			final FieldChangeHelper fieldChangeHelper,
			Map<String, UpdateEffectiveDateHelper> updateEffectiveDateDynamicMap,
			final OpportunityCreationHelper opportunityCreationHelper,
			final LobGateway lobGateway
	) {
		this.opportunityRepoHelper = opportunityRepoHelper;
		this.opportunityHelper = opportunityHelper;
		this.downloadZipFileHelper = downloadZipFileHelper;
		this.fieldChangeHelper = fieldChangeHelper;
		this.updateEffectiveDateDynamicMap = updateEffectiveDateDynamicMap;
		this.opportunityCreationHelper = opportunityCreationHelper;
		this.lobGateway = lobGateway;
	}

	/**
	 * CREATES - also @see ./OpportunityCreationController.java
	 */

	@Operation(summary = "create Opportunity", description = "Create an Opportunity")
	@ApiResponses(value = {@ApiResponse(responseCode = "201", description = "the Opportunity has been saved"),
			@ApiResponse(responseCode = "400", description = "the Opportunity has not been saved")})
	@PostMapping
	@SplunkName("CREATE OPPORTUNITY V3")
	public HttpStatus saveOpportunity(@RequestBody Opportunity opportunity) {
		opportunityRepoHelper.save(opportunity);
		return HttpStatus.CREATED;
	}

	/**
	 * READS
	 */

	@Operation(summary = "getOpportunityById")
	@GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("RETRIEVE OPPORTUNITY BY ID")
	public Opportunity getOpportunityById(@PathVariable("id") Integer id)  {
		return opportunityRepoHelper.findOpportunityById(id);
	}

	@Operation(summary = "getOpportunitiesByIds")
	@GetMapping(value = "/", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("GET OPPORTUNITIES BY IDS")
	public List<Opportunity> getOpportunitiesByIds(@RequestParam("ids") List<Integer> ids)  {
		return opportunityHelper.getAllOppsByOppIds(ids);
	}

	@Operation(summary = "Gets the opportunity by OppId",
			description = "For the given oppId the system will find the opportunity "
					+ "associated with the oppId and return data and the original xml")
	@PostMapping(value = "/getOpportunityForEdit/{id}")
	@SplunkName("RETRIEVE EDITABLE OPPORTUNITIES")
	public Opportunity getOpportunityForEdit(
			@PathVariable(value = "id") Integer oppID,
			@RequestBody DetokenizeRequest request
	) {
		return opportunityHelper.getOpportunityForEdit(oppID, request);
	}

	@Operation(summary = "Get sensitive, quote-ready Opportunity by Opportunity Id", description = "Pass Opp Id (Integer) as query param oppId")
	@SplunkName("RETRIEVE OPPORTUNITY BY ID")
	@PostMapping(value = "/findByOpportunityId")
	public ResponseEntity<Opportunity> findByOpportunityId(
			@RequestParam Integer oppId,
			@RequestBody DetokenizeRequest request
	) {
		Pair<Opportunity, List<String>> result = opportunityHelper.getQuotableOpportunity(
				oppId, request.isDetokenize(), request.getRequester());

		return ResponseEntity.ok()
				.header("Errors", result.getSecond().toArray(String[]::new))
				.header("Access-Control-Expose-Headers", "Errors")
				.body(result.getFirst());
	}

	@Operation(summary = "Gets the number of opportunities by numOfRecords",
			description = "For the given numOfRecords the system will find the top numOfRecords from the database")
	@GetMapping(value = "/getOpportunities")
	//TODO we need to remove asap as this does not work as expected in old envirmoment
	@SplunkName("RETRIEVE OPPORTUNITIES")
	public Page<Opportunity> getOpportunities(
			@Parameter(description = "numOfRecords that we want to return", required = true)
			@RequestParam(value = "numOfRecords") int numOfRecords,
			@Parameter(description = "lineType that we want to return (Personal or Business or All), default is Personal")
			@RequestParam(value = "lineType", required = false, defaultValue = LineType.LineTypeValues.PERSONAL) LineType lineType) {
		return opportunityRepoHelper.getPagedOpportunities(numOfRecords, lineType);
	}

	@Operation(summary = "Gets the opportunity by OppId",
			description = "For the given oppId the system will find the opportunity "
					+ "associated with the oppId and return data and the orginal xml")
	@GetMapping(value = "/getOpportunityForEdit")
	@SplunkName("RETRIEVE EDITABLE OPPORTUNITIES")
	public Opportunity getOpportunityForEdit(
			@Parameter(description = "oppId for the Opportunity we want to return", required = true)
			@RequestParam(value = "oppID") int oppID) {
		return opportunityHelper.getOpportunityForEdit(oppID);
	}

	/**
	 * Retrieve the Opportunity XML by Opportunity Id
	 * Allows detokenization of the XML if detokenize query param set to true
	 *
	 * @param detokenize detokenize the Xml or not
	 * @return Opportunity XML Document
	 */
	@Operation(summary = "getOpportunityXmlById")
	@GetMapping(value = "/{id}/xml", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("RETRIEVE OPPORTUNITY XML BY OppID")
	public String getOpportunityXmlById(
			@PathVariable("id") Integer id,
			@RequestParam(name = "detokenize", required = false, defaultValue = "false") boolean detokenize)
			throws EntityNotFoundException, OpportunitySensitiveDataException, OpportunityException {
		return opportunityHelper.getOpportunityXml(id, detokenize);
	}

	@Operation(summary = "Determines if list of ids is quotable or not",
			description = "Determines if list of ids is quotable or not")
	@PostMapping(value = "/getQuotableOpportunities", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("RETRIEVE QUOTABLE OPPORTUNITIES")
	public ActionableOpportunitiesResponse getQuotableOpportunities(@Parameter(description = "",
			required = true) @RequestBody List<Integer> oppIds) {
		// TODO: update logic to determine if BL is quotable or not
		return opportunityHelper.getQuotableOpportunities(oppIds);
	}

	/**
	 * Because of query param length limitations provide an endpoint to get all opportunities via POST body array of ids
	 * NOTE: this is likely to only be needed to be used api calls coming from browsers
	 *
	 * @param ids List Integer ids
	 * @return List Opportunity
	 */
	@Operation(summary = "getOpportunitiesByIds")
	@PostMapping(value = "/byIds", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("GET OPPORTUNITIES BY IDS")
	public List<Opportunity> getOpportunitiesByIdsPost(@RequestBody List<Integer> ids) {
		return opportunityHelper.getAllOppsByOppIds(ids);
	}

	@Operation(summary = "Get effective date range",
			description = "get the min and max effective dates fo the opportunities ")
	@PostMapping(value = "/effectiveDateRange", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("RETRIEVE EFFECTIVE DATA RANGE FOR OPPORTUNITIES")
	public EffectiveDateRange getEffectiveDateRange(@Parameter(description = "get effectivedate range for the opp ids",
			required = true) @RequestBody List<Integer> oppIds) {
		return opportunityRepoHelper.getCustomFilterOpportunitiesEffectiveDateRange(oppIds);
	}

	/**
	 * Grab the preview data from a process result item associated with an Opportunity ID
	 */
	@Operation(summary = "Get PreviewData", description = "Get all preview data for a specific opp ID")
	@GetMapping(value = "/getPreviewData")
	@SplunkName("RETRIEVE PREVIEW FOR OPPORTUNITY")
	public ResponseEntity<PreviewDataResponse> getPreviewData(
			@Parameter(description = "opportunityID that we are passing to find associated preview data", required = true)
			@RequestParam int oppId
	) throws Exception {
		// TODO: generate PreviewDataResponse for BL
		return ResponseEntity.status(HttpStatus.OK).body(opportunityHelper.getPreviewData(oppId));
	}

	@Operation(summary = "Get List of OpportunityError details by list of ids")
	@PostMapping(value = "/errorInfo")
	@SplunkName("RETRIEVE OPPORTUNITY QUOTE ERROR INFO")
	public ResponseEntity<List<OpportunityErrorInfo>> getOpportunitiesErrorInfo(@RequestBody List<Integer> opportunityIds) throws BookTransferException {
		return ResponseEntity.ok((opportunityHelper.getOpportunitiesErrorInfo(opportunityIds)));
	}

	@Operation(summary = "Gets a list of all opportunity prior carriers" + "and returns them in a list")
	@GetMapping(value = "/getOpportunityPriorCarriers")
	@SplunkName("RETRIEVE PRIOR CARRIERS")
	public List<String> getOpportunityPriorCarriers(@Parameter(description = "lineType that we want to return (Personal or Business or All), default is Personal")
													@RequestParam(value = "lineType", required = false, defaultValue = LineType.LineTypeValues.PERSONAL) LineType lineType) {
		// handle non-defaulted, incorrect lineType
		if (lineType == null) {
			lineType = LineType.Personal;
		}

		return opportunityRepoHelper.getOpportunityPriorCarriers(lineType);
	}

	@Operation(summary = "Get custom filter data",
			description = "Gets all opportunities that are in the oppId request as well as pagination request")
	@PostMapping(value = "/customFilterData")
	@SplunkName("CUSTOM FILTER REQUEST")
	public CustomFilterResponse customFilterData(
			@Parameter(description = "Opportunity ID we want to filter by", required = true)
			@RequestBody CustomFilterRequest cfRequest) {
		return opportunityHelper.getCustomFilterResponse(cfRequest);
	}

	@Operation(summary = "Get custom filter oppIds that we expect to return",
			description = "Gets all opportunity ids that meet the requested customer filters it ignores the pagination request ")
	@PostMapping(value = "/opportunityPageOppIds", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("RETRIEVE OPPORTUNITY IDS")
	public List<Integer> opportunityPageOppIds(@Parameter(description = "Opportunity ID we want to filter by",
			required = true) @RequestBody CustomFilterRequestForOppIds cfRequest) {
		if (cfRequest.getLineType() == null) {
			cfRequest.setLineType(LineType.All);
		}
		return opportunityRepoHelper.getCustomFilterOppIds(cfRequest);
	}

	@Operation(
			summary = "Get List of Opp details by list of salesforceCodes, list of LOBs, or start and end effective dates",
			description = "All fields optional except salesForceCodes")
	@PostMapping(value = "/opportunity-details")
	@SplunkName("RETRIEVE OPPORTUNITY DETAILS")
	public ResponseEntity<List<OpportunityDetails>> getOppDetails(@RequestBody OpportunityFilterRequest request)
			throws Exception {
		// default lineType to 'Personal' if none passed in
		if (request.getLineType() == null) {
			request.setLineType(LineType.Personal);
		}
		return ResponseEntity.ok(opportunityHelper.getOppDetails(request));
	}

	@PostMapping("/priorCarrierData")
	public ResponseEntity<List<PriorCarrierData>> getOpportunityPriorCarrierData(@RequestBody List<Integer> opportunityIds) {
		List<PriorCarrierData> priorCarrierData = opportunityHelper.findPriorCarrierData(opportunityIds);
		return ResponseEntity.ok(priorCarrierData);
	}

	@Operation(summary = "Get the booktransfer ids associated with opp ids",
			description = "return a map with oppid as key and bookid as value")
	@PostMapping(value = "/getBookIdsForOppIds", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("RETRIEVE BOOKTRANSFER IDS by OPPORTUNITY IDS")
	public ResponseEntity<Map<Integer, Integer>> getBookIdsForOppIds(@Parameter(
			description = "opportunity ids for which the bookids need to be retrieved") @RequestBody List<Integer> opportunityIds) {
		log.info("countOfOppIds - {}", opportunityIds.size());
		log.info("opportunityIds - {}", opportunityIds);

		Map<Integer, Integer> bookIdsForOppIds = opportunityRepoHelper.getBookIdsForOppIds(opportunityIds);

		return ResponseEntity.status(HttpStatus.OK).body(bookIdsForOppIds);
	}

	@Operation(summary = "Get Brushmap data for the opportunities", description = "return a CSV data")
	@PostMapping(value = "/getBrushMapData", produces = (MediaType.TEXT_PLAIN_VALUE))
	@SplunkName("RETRIEVE BRUSH MAP DATA")
	public ResponseEntity<String> getBrushMapData(
			@Parameter(description = "opportunityIds for which the brush map needs to be generated",
					required = true) @RequestBody List<Integer> opportunityIds) {
		log.info("opportunityIds - {}", opportunityIds);
		String brushMapData = opportunityHelper.buildOutBrushMapData(opportunityIds);
		return ResponseEntity.status(HttpStatus.OK).body(brushMapData);
	}

	@Operation(summary = "Get Customer List data for the opportunities", description = "return a CSV data")
	@PostMapping(value = "/getCustomerList", produces = (MediaType.TEXT_PLAIN_VALUE))
	@SplunkName("RETRIEVE CUSTOMER LIST")
	public ResponseEntity<String> getCustomerList(
			@Parameter(description = "opportunityIds for which to grab customer list data from",
					required = true) @RequestBody List<Integer> opportunityIds) {
		log.info("opportunityIds - {}", opportunityIds);
		String customerListCSV = opportunityHelper.buildCustomerListCSVAsString(opportunityIds);
		return ResponseEntity.status(HttpStatus.OK).body(customerListCSV);
	}

	@GetMapping("/lobs/all")
	public Map<String, List<String>> getAllLobs() {
		return lobGateway.getAllLobs();
	}

	/**
	 * UPDATES
	 * Patch is REST standard for partial updates Put is REST standard for full record updates
	 */

	@Operation(summary = "update opportunities via csv",
			description = "Update opportunities status, by passing \"status\" as middle value or move books, by passing \"sfdcid\", can be done this way by given the correct values via csv")
	@PatchMapping(value = "/file", produces = "text/csv")
	@SplunkName("UPDATE OPPORTUNITIES BY FILENAME")
	public ResponseEntity<String> updateOpportunitiesByFileName(
			@Parameter(description = "file", required = true) @RequestParam(value = "file") MultipartFile file)
			throws Exception {
		log.info("fileName - {}", file.getOriginalFilename());
		String statusChangeReport = fieldChangeHelper.updateOppAndQRI(file);
		return new ResponseEntity<>(statusChangeReport, HttpStatus.OK);
	}

	@Operation(summary = "changeOpportunitiesStatusForBL", description = "change opportunities status from one status to another")
	@PostMapping(value = "/changeBLOpportunitiesStatus")
	@SplunkName("CHANGE OPPORTUNITY STATUSES FOR BL")
	@Deprecated
	public ResponseEntity<String> changeOpportunitiesStatusForBL(
			@Parameter(description = "status", required = true) @RequestParam Integer status,
			@Parameter(description = "opportunityIds", required = true) @RequestBody List<Integer> opportunityIds) {
		log.info("Setting status {} on BL Opportunity ids - {}", status, opportunityIds);
		opportunityRepoHelper.updateOpportunitiesStatus(opportunityIds, status);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@Operation(summary = "changeOpportunitiesStatusWithLineType", description = "change opportunities status from one status to another")
	@PostMapping(value = "/changeOpportunitiesStatus/{lineType}")
	@SplunkName("CHANGE OPPORTUNITY STATUSES FOR BL and PL")
	public ResponseEntity<String> changeOpportunitiesStatusForLineType(
		@PathVariable("lineType") String lineType,
		@Parameter(description = "status", required = true) @RequestParam Integer status,
		@Parameter(description = "opportunityIds", required = true) @RequestBody List<Integer> opportunityIds) {
		log.info("Setting status {} on PL Opportunity ids - {}", status, opportunityIds);
		opportunityRepoHelper.updateOpportunitiesStatus(opportunityIds, status);
		if(LineType.Personal.name().equalsIgnoreCase(lineType)) {
			opportunityHelper.updateQRIForStatusChange(opportunityIds, status);
		}
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@Operation(summary = "changeOpportunitiesStatus", description = "change opportunities status from one status to another")
	@PostMapping(value = "/changeOpportunitiesStatus", produces = "text/csv")
	@SplunkName("CHANGE OPPORTUNITY STATUSES")
	public ResponseEntity<String> changeOpportunitiesStatus(
			@Parameter(description = "status", required = true) @RequestParam String status,
			@Parameter(description = "opportunityIds", required = true) @RequestBody List<Integer> opportunityIds)
			throws OpportunityException {
		log.info("countOfOppIds - {}", opportunityIds.size());
		log.info("opportunityIds - {}", opportunityIds);
		log.info("statusToChange - {}", status);
		String statusChangeReport = fieldChangeHelper.updateOppAndQRI(opportunityIds, "Status", status);

		return new ResponseEntity<>(statusChangeReport, HttpStatus.OK);
	}

	@Operation(summary = "Update edited XML given on the opportunity",
			description = "For the given opp it will take its data and update with the editeedxml")
	@ApiResponses(value = {@ApiResponse(responseCode = "200", description = "the xml is updated"),
			@ApiResponse(responseCode = "400", description = "the xml is not updated")})
	@PostMapping(value = "/updateOppXML")
	@SplunkName("UPDATE OPPORTUNITY XML")
	public HttpStatus updateOppXML(@Parameter(description = "Opportunity with Opp.data(xml) that we want to validate",
			required = true) @RequestBody Opportunity opportunity) throws Exception {
		opportunityHelper.updateOpportunityXML(opportunity);
		return HttpStatus.OK;
	}

	/**
	 * Downloads a zip file containing one or more xml files
	 *
	 * @return ResponseEntity
	 */
	@Operation(summary = "downloadZipFile", description = "Downloads a zip file containing one or more xml files")
	@PostMapping(value = "/downloadZipFile", produces = "application/zip")
	@SplunkName("DOWNLOAD OPPORTUNITY ZIP")
	public ResponseEntity<byte[]> downloadZipFile(@RequestBody List<Integer> oppList) throws Exception {
		log.info("oppIds: {}", oppList);

		HttpHeaders header = new HttpHeaders();
		// this will allow us to access the header prop at frontend with axios call
		header.add("Access-Control-Expose-Headers", "Content-Disposition");
		header.add("Content-Disposition", "inline; filename=" + downloadZipFileHelper.generateZipFileName());
		ByteArrayOutputStream output = downloadZipFileHelper.downloadZipFile(oppList);
		return new ResponseEntity<>(output.toByteArray(), header, HttpStatus.OK);
	}

	@PostMapping(value = "/validateXML", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("VALIDATE OPPORTUNITY XML")
	public ResponseEntity<String> validateXML(@Parameter(description = "Opportunity with Opp.data(xml) that we want to validate",
			required = true) @RequestBody Opportunity opportunity) {
		try {
			XmlHelper.getDocument(opportunity.getData());
			return ResponseEntity.status(HttpStatus.OK).body(null);
		} catch (Exception e) {
			HashMap<String, String> newMap = new HashMap<>();
			newMap.put("File cannot be saved. XML is not in proper XML format: ", e.getMessage().replace("\"", ""));
			JSONObject jsonObject = new JSONObject(newMap);
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(jsonObject.toString());
		}
	}
	@Operation(summary = "Given a schedule it will find all the oppIds that need to be quoted ",
			description = "Given that we have a schedule it will search the opp table for everything that is on the table")
	@PostMapping(value = "/getOppIdsForSchedule")
	@SplunkName("RETRIEVE OPPORTUNITIES FROM SCHEDULE")
	public ResponseEntity<Map<LineType, List<Integer>>>
	getOppIdsForSchedule(@Parameter(description = "Scheduler that we are searching the db for opps that relate to it",
			required = true) @RequestBody ScheduleRequest schedule) {
		return ResponseEntity.ok(opportunityRepoHelper.getOppIdsForSchedule(schedule));
	}

	@GetMapping(value = "/getLatestResultData")
	@SplunkName("GET LATEST RESULT DATA")
	public ResponseEntity<String> getLatestResultData(@RequestParam(value = "Opp", required = true) String opp)
			throws OpportunityException {
		try {
			// TODO: update to handle BL
			return ResponseEntity.status(HttpStatus.OK).body(opportunityHelper.maskAndBuildPartnerResult(opp));
		} catch (ProcessResultException e) {
			return ResponseEntity.status(HttpStatus.NO_CONTENT).body(e.getMessage());
		}
	}

	@Operation(summary = "Set the lead effective date",
			description = "For the given opps, update the effective date with the lead effective date")
	@PostMapping(value = "/setLeadEffectiveDate")
	@SplunkName("SET LEAD EFFECTIVE DATE")
	public ResponseEntity<ChangeEffectiveDateResponse> setLeadEffectiveDateV2(
			@Parameter(description = "startDate") @RequestParam(value = "startDate") String startDate,
			@Parameter(description = "leadType") @RequestParam(value = "leadType") String leadType,
			@Parameter(description = "email") @RequestParam(value = "email") String email,
			@Parameter(description = "opportunity ids") @RequestBody List<Integer> opportunityIds) throws Exception {

		UpdateEffectiveDateHelper updateEffectiveDateHelper = getUpdateEffectiveDateHelperByName(leadType);
		ChangeEffectiveDateResponse res =
				updateEffectiveDateHelper.setLeadEffectiveDate(opportunityIds, startDate, email);

		return ResponseEntity.status(HttpStatus.OK).body(res);
	}

	private UpdateEffectiveDateHelper getUpdateEffectiveDateHelperByName(String leadType) {
		if (leadType.equalsIgnoreCase("home")) {
			leadType = "homeEffectiveDateHelper";
			// old aqe option can be removed when killed
		} else if (leadType.equalsIgnoreCase("trueLead")) {
			leadType = "trueLeadEffectiveDateHelper";
		} else if (leadType.equalsIgnoreCase("True Lead")) {
			leadType = "trueLeadEffectiveDateHelper";
		} else if (leadType.equalsIgnoreCase("Next Term")) {
			leadType = "nextTermEffectiveDateHelper";
		} else if (leadType.equalsIgnoreCase("Set Date")) {
			leadType = "setDateEffectiveDateHelper";
		}

		UpdateEffectiveDateHelper updateEffectiveDateHelper = updateEffectiveDateDynamicMap.get(leadType);
		if (updateEffectiveDateHelper == null) {
			throw new IllegalArgumentException("Invalid leadType");
		}
		return updateEffectiveDateHelper;

	}

	@PostMapping(value = "/reupload", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("RE UPLOAD OPPORTUNITIES")
	public ResponseEntity<String> reuploadOpportunities(@RequestBody List<Integer> oppIds) {
		opportunityCreationHelper.reuploadOpportunities(oppIds);
		return ResponseEntity.ok("Opportunities Re-Uploaded");
	}

	@PostMapping(value = "/moveOpportunities")
	@SplunkName("MOVE OPPORTUNITIES")
	public ResponseEntity<String> moveOpportunities(@RequestBody MoveOpportunitiesRequest opportunitiesToMove)
			throws OpportunityException {
		return new ResponseEntity<>(fieldChangeHelper.updateOppAndQRI(
				opportunitiesToMove.getOpportunityIDs(), "SFDCID", opportunitiesToMove.getSFDCID()), HttpStatus.OK);
	}

	@PostMapping(value = "/SPQE", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("CREATE OPPORTUNITY FOR SPQE")
	public ResponseEntity<OpportunityCreationResponse> createOpportunityForSPQE(
		@RequestBody OpportunityCreationRequest opportunityCreationRequest) throws OpportunityTopicException, OpportunityException {
		opportunityCreationRequest.setOriginSource(OriginSource.SPQE);
		opportunityCreationRequest.setSourceType(SourceType.CSV_MAPPING);
		opportunityHelper.addLobToMasterOppSPQEMeta(opportunityCreationRequest.getMasterOppSPQEMeta());
		OpportunityCreationResponse opportunities =
			opportunityCreationHelper.createOpportunityFromOpportunityRequest(true, opportunityCreationRequest);

		return ResponseEntity.ok(opportunities);
	}

	@PostMapping(value="/create", produces = MediaType.APPLICATION_JSON_VALUE)
	@SplunkName("CREATE OPPORTUNITY")
	public ResponseEntity<OpportunityCreationResponse>
	createOpportunity(@RequestBody OpportunityCreationRequest opportunityCreationRequest) throws OpportunityTopicException, OpportunityException {
		// OriginSource and SourceType must be set in upload-manager as the only distinguish is the file name
		OpportunityCreationResponse opportunities =
			opportunityCreationHelper.createOpportunityFromOpportunityRequest(false, opportunityCreationRequest);

		return ResponseEntity.ok(opportunities);
	}

	@VisibleForTesting
	void setUpdateEffectiveDateDynamicMap(Map<String, UpdateEffectiveDateHelper> updateEffectiveDateDynamicMap) {
		this.updateEffectiveDateDynamicMap = updateEffectiveDateDynamicMap;
	}

	@VisibleForTesting
	void setOpportunityHelper(OpportunityHelper helper) {
		this.opportunityHelper = helper;
	}

	@VisibleForTesting
	void setOpportunityRepoHelper(OpportunityRepoHelper helper) {
		this.opportunityRepoHelper = helper;
	}

	OpportunityRepoHelper getOpportunityRepoHelper() {
		return this.opportunityRepoHelper;
	}
}

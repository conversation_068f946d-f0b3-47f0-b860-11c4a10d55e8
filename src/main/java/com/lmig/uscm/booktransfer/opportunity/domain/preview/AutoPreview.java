package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class AutoPreview extends Preview {
	private final List<VehicleInfo> vehicles;

	protected AutoPreview() {
		super();
		vehicles = new ArrayList<>(4);
	}

	@Override
	public LineOfBusiness getLob() {
		return LineOfBusiness.AUTOP;
	}

	public List<VehicleInfo> getVehicles() {
		return vehicles;
	}

	public void addVehicle(VehicleInfo vehicle) {
		vehicles.add(vehicle);
	}

	@Override
	protected void setLOBRequiredData(Document originalXml, PreviewDataResponse response)
			throws ParserConfigurationException, SAXException, XPathExpressionException, IOException {
		if (StringUtils.isBlank(AcordHelper.getExpirationDt(originalXml))) {
			response.addMissingDataPoint("ExpirationDate");
		}
		setDriverRequiredData(originalXml, response);
		setVehicleRequiredData(originalXml, response);

	}

	private void setVehicleRequiredData(Document originalXml, PreviewDataResponse response)
			throws XPathExpressionException {

		NodeList vehNodes = AcordHelper.getPersVehs(originalXml);
		for (int i = 0; i < vehNodes.getLength(); i++) {
			VehicleInfo vehicleInfo = new VehicleInfo();
			Node vehicle = vehNodes.item(i);
			vehicleInfo.setVehicleData(originalXml, vehicle);
			vehicleInfo.setRequiredData(vehicle, response);
			addVehicle(vehicleInfo);
		}
	}

	private void setDriverRequiredData(Document originalXml, PreviewDataResponse response) throws XPathExpressionException {
			NodeList driverNodes = AcordHelper.getPersDrivers(originalXml);
			for (int i = 0; i < driverNodes.getLength(); i++) {
				Node driverNode = driverNodes.item(i);
				DriverData driverData = new DriverData();
				driverData.setRequiredData(driverNode, response);
			}
	}

}

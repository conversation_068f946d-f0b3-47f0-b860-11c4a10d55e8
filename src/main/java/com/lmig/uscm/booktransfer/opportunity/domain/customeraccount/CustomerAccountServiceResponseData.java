package com.lmig.uscm.booktransfer.opportunity.domain.customeraccount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAccountServiceResponseData {
    private Integer statusCode;
    private String success;
    private String error;
    private String id;
    private SearchCriteria searchCriteria;
    private CustomerMetadata customerMetadata;
    private List<CustomerPolicy> customerPolicies;
    private List<EcliqAccount> ecliqAccountInfo;
}

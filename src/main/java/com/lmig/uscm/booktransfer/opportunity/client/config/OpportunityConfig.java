package com.lmig.uscm.booktransfer.opportunity.client.config;

import com.lmig.uscm.booktransfer.opportunity.client.OpportunityService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class OpportunityConfig {
	@Bean
	@ConditionalOnBean(name = "opportunityWebClient")
	public OpportunityService opportunityService(@Qualifier("opportunityWebClient") final WebClient webClient,
												 final OpportunityUrlProvider opportunityUrlProvider) {
		return new OpportunityService(webClient, opportunityUrlProvider);
	}

	@Bean
	@Profile({"local"})
	public OpportunityUrlProvider opportunityUrlProviderLocal() {
		return new OpportunityUrlProvider("http://localhost:8081");
	}

	@Bean
	@Primary
	@Profile({"dev", "development"})
	public OpportunityUrlProvider opportunityUrlProviderDev() {
		return new OpportunityUrlProvider("https://opportunityservice-development.pdc.np.paas.lmig.com");
	}

	@Bean
	@Primary
	@Profile({"test", "qa", "sandbox"})
	public OpportunityUrlProvider opportunityUrlProviderTest() {
		return new OpportunityUrlProvider("https://opportunityservice-test.pdc.np.paas.lmig.com");
	}

	@Bean
	@Primary
	@Profile({"staging"})
	public OpportunityUrlProvider opportunityUrlProviderStaging() {
		return new OpportunityUrlProvider("https://opportunityservice-staging.us-east-1.np.paas.lmig.com");
	}

	@Bean
	@Primary
	@Profile({"production", "prod"})
	public OpportunityUrlProvider opportunityUrlProviderProd() {
		return new OpportunityUrlProvider("https://opportunityservice.pdc.paas.lmig.com");
	}
}

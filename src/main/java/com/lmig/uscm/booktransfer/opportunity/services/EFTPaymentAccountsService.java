package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.EFTPaymentAccountsException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.EFTPaymentAccountsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientException;
import reactor.core.publisher.Mono;

@Slf4j
public class EFTPaymentAccountsService {

    private final WebClient eftPaymentAccountsWebClient;
    private final String url;

    public EFTPaymentAccountsService(WebClient eftPaymentAccountsWebClient, String url){
        this.eftPaymentAccountsWebClient = eftPaymentAccountsWebClient;
        this.url = url;
    }

    public EFTPaymentAccountsResponse getEFTPaymentAccountsDetails(final String paymentAccountId){
        log.info("Calling EFT Payment Accounts Service: {}", url);
        EFTPaymentAccountsResponse eftPaymentAccountsResponse = null;
        try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("requestSourceCode", "CBS");
                headers.add("userId", "OpportunityService");

                 eftPaymentAccountsResponse = eftPaymentAccountsWebClient.get()
                        .uri(url + "?paymentAccountId=" + paymentAccountId)
                        .headers(h->h.addAll(headers))
                        .retrieve()
                        .onStatus(HttpStatusCode::is4xxClientError, response -> response.bodyToMono(String.class)
                            .flatMap(errorBody -> {
                                log.error("Bad request: {}", errorBody);
                                return Mono.empty();}))
                        .onStatus(HttpStatusCode::is5xxServerError, response -> response.bodyToMono(String.class)
                                .flatMap(errorBody -> {
                                    log.error("Payment Service failure: {}", errorBody);
                                    return Mono.error(new EFTPaymentAccountsException("Payment Service failure"));
                                }))
                        .bodyToMono(EFTPaymentAccountsResponse.class)
                        .block();
            }catch(WebClientException e){
                log.error("Error finding EFT Payment Accounts for Payment Account ID: {}", paymentAccountId);
            }
        return  eftPaymentAccountsResponse;
    }
}
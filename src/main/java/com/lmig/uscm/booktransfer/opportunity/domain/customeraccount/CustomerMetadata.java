package com.lmig.uscm.booktransfer.opportunity.domain.customeraccount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerMetadata {
    private String btCode;
    private String customerName;
    private String address1;
    private String address2;
    private String city;
    private String state;
    private String zip;
}

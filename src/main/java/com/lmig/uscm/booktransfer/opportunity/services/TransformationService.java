package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.booktransfer.transformationservice.client.domain.AuditableAction;
import com.lmig.booktransfer.transformationservice.client.request.ExecutionRequest;
import com.lmig.booktransfer.transformationservice.client.domain.TransformationResult;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatusCode;
import org.springframework.lang.Nullable;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientException;
import org.w3c.dom.Document;
import reactor.core.publisher.Mono;

import javax.xml.transform.TransformerException;
import java.util.Optional;

@Slf4j
public class TransformationService {

	/**
	 * This webclient bean is created in bt-security-start library
	 * To create this bean we need to have `transformationClient` profile in active profiles
	 */
	private final WebClient transformationServiceWebClient;

	private final String transformationUrl;

	public TransformationService(final WebClient transformationServiceWebClient, final String url) {
		this.transformationServiceWebClient = transformationServiceWebClient;
		transformationUrl = url != null ? url : "";
	}

	/**
	 * This method is responsible for calling the service that executes rules. This method also swallows
	 * TransformerExceptions if there are issues transforming string to xml document.
	 *
	 * @return changed xml when service call is successful, the input xml when otherwise
	 */
	public Document runRules(Document xmlDoc, String packageId, @Nullable Integer oppId) {
		TransformationResult transformationResult = executePackage(xmlDoc, packageId, oppId);
		return transformationResult.getResultDocument();
	}

	/**
	 * Execute transformation package
	 *
	 * @param xml - ACORD document
	 * @param packageId - package id
	 * @return Latest {@link TransformationResult}
	 */
	public TransformationResult executePackage(final Document xml, final String packageId, @Nullable final Integer oppId) {
		String url = transformationUrl + "/package/execute";
		final ExecutionRequest request = new ExecutionRequest();
		request.setAuditableAction(AuditableAction.EXECUTE);
		request.setUserId("import");
		try {
			request.setXml(XmlHelper.getDocumentString(xml));

			request.setExecutableId(packageId);

			Optional<TransformationResult> transformationResultOptional = transformationServiceWebClient.post()
					.uri(url)
					.bodyValue(request)
					.retrieve()
					.onStatus(HttpStatusCode::is4xxClientError, response -> response.bodyToMono(String.class)
							.flatMap(errorBody -> {
								log.error("Bad request {}", errorBody);
								return Mono.empty();
							}))
					.onStatus(HttpStatusCode::is4xxClientError, response -> response.bodyToMono(String.class)
							.flatMap(errorBody -> {
								log.error("transformation service failure: {}", errorBody);
								return Mono.empty();
							}))
					.bodyToMono(TransformationResult.class)
					.blockOptional();
			if(transformationResultOptional.isPresent()) {
				return transformationResultOptional.get();
			}
		} catch (TransformerException e) {
			log.error(String.format("Error converting xml document to string for opp: %s", oppId), e);
		} catch (WebClientException e) {
			log.error(String.format("Error executing package id %s for opp %s", packageId, oppId), e);
		}

		TransformationResult defaultTransformationResult = new TransformationResult();
		defaultTransformationResult.setResultDocument(xml);
		return defaultTransformationResult;
	}
}

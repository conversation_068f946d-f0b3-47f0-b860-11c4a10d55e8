package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.masking.MaskHelper;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.utilityservice.DateUtils;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
public class DownloadZipFileHelper {

	@Value("${qni.url}")
	public String urlQni;
	private final OpportunityRepoHelper opportunityRepoHelper;
	private final BookTransferService bookTransferService;

	public DownloadZipFileHelper(final BookTransferService bookTransferService,
			final OpportunityRepoHelper opportunityRepoHelper) {
		this.bookTransferService = bookTransferService;
		this.opportunityRepoHelper = opportunityRepoHelper;
	}

	/**
	 * Creates a xml file and updates nodes values accordingly
	 *
	 * @return Document
	 */
	Document createEditedXmlFile(Opportunity opportunity, Integer sfdcid)
			throws ParserConfigurationException, SAXException, IOException, XPathExpressionException {

		String qniurl = urlQni;
		String lastPolicyGuid = opportunity.getLastPolicyGuid();

		Document doc = XmlHelper.getDocument(opportunity.getData());

		if (lastPolicyGuid == null || lastPolicyGuid.startsWith("000")) {
			lastPolicyGuid = "";
		} else {
			qniurl = qniurl + "&p=" + lastPolicyGuid;
		}

		Node persPolicyNode = XmlHelper.createNodeIfNotExist(doc, "PersPolicy", "", doc.getFirstChild());

		if (persPolicyNode != null) {
			XmlHelper.createNodeIfNotExist(doc, "SalesforceBookTransferName", String.valueOf(sfdcid), persPolicyNode);
		}

		Node quoteInfoNode = XmlHelper.createNodeIfNotExist(doc, "QuoteInfo", "", persPolicyNode);

		String opIdString = Integer.toString(opportunity.getOpportunityId());
		XmlHelper.createNodeIfNotExist(doc, "com.Safeco_AQEOpportunityID", opIdString, quoteInfoNode);

		if (StringUtils.isNotEmpty(lastPolicyGuid)) {

			XmlHelper.createNodeIfNotExist(doc, "CompanysQuoteNumber", lastPolicyGuid, quoteInfoNode);

			Node quoteURLnode = XmlHelper.getNodeFromDoc("//com_safeco_CompanyURL", doc);

			if (quoteURLnode != null) {
				quoteURLnode.setTextContent(qniurl);
				quoteURLnode.setNodeValue(qniurl);
			} else {
				Node newQuoteUrl = doc.createCDATASection(qniurl);
				newQuoteUrl.setNodeValue(qniurl);
				newQuoteUrl.setTextContent(qniurl);
				Node newQuoteURLNode = XmlHelper.createNodeIfNotExist(doc, "com_safeco_CompanyURL", "", quoteInfoNode);
				newQuoteURLNode.appendChild(newQuoteUrl);
			}
		}
		return doc;
	}

	/**
	 * Downloads a zip file containing one or more xml files
	 *
	 * @return ResponseEntity
	 */
	public ByteArrayOutputStream downloadZipFile(List<Integer> listOppIdIntegers)
			throws IOException, RuntimeException, BookTransferException {

		ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
		BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(byteArrayOutputStream);
		ZipOutputStream zipOutputStream = new ZipOutputStream(bufferedOutputStream);

		List<Opportunity> opportunityList;

		for (List<Integer> partition : ListUtils.partition(listOppIdIntegers, 500)) {
			opportunityList = opportunityRepoHelper.findByOpportunityIdIn(partition);

			// Map of bookId to book sfdcid
			Map<Integer, Integer> bookIdToSfdcid = getBookSfdcidsForOppList(opportunityList);

			zipOutputStream = writeXmlToZip(zipOutputStream, opportunityList, bookIdToSfdcid);
		}

		if (zipOutputStream != null) {
			zipOutputStream.finish();
			zipOutputStream.flush();
			IOUtils.closeQuietly(zipOutputStream);
		}

		return byteArrayOutputStream;
	}

	/**
	 * Gets the sfdcids for list of given Opportunities Returns a Map of each
	 * BookTransferId to it's corresponding sFDCID (Salesforce Id)
	 *
	 * @return Map of BookTransferId to it's corresponding sFDCID (Salesforce Id)
	 */
	public Map<Integer, Integer> getBookSfdcidsForOppList(List<Opportunity> opportunityList)
			throws BookTransferException {
		Set<Integer> bookTransferIds = new HashSet<>();
		for (Opportunity opp : opportunityList) {
			bookTransferIds.add(opp.getBookTransferID());
		}

		return bookTransferService.getSfdcidsForBookTransferIds(bookTransferIds);
	}

	ZipOutputStream writeXmlToZip(ZipOutputStream zipOutputStream, List<Opportunity> listOppList,
			Map<Integer, Integer> bookIdToSfdcid)
			throws RuntimeException {

		listOppList.forEach(opp -> {
			try {
				Integer sfdcid = bookIdToSfdcid.get(opp.getBookTransferID());

				String docString = opp.getData();
				if (LineType.Personal.equals(opp.getLineType())) {
					Document doc = createEditedXmlFile(opp, sfdcid);
					docString = XmlHelper.getDocumentString(doc);
				}
				// Mask NPPI for downloaded opps
				byte[] data = MaskHelper.maskDocumentOrNullify(docString, false, false).getBytes();

				String fileName = generateFileName(opp.getCustomerName(), opp.getOpportunityId(), opp.getEffectiveDate());
				zipOutputStream.putNextEntry(new ZipEntry(fileName));
				zipOutputStream.write(data, 0, data.length);
				zipOutputStream.closeEntry();

			} catch (Exception e) {
				log.error("Download Zip exception", e);
				throw new RuntimeException(e);
			}
		});

		return zipOutputStream;
	}

	public String generateFileName(String commercialName, int oppId, String effectiveDate) {
		return "BT_IVANS_" + fixEffectiveDate(effectiveDate) + "_" + oppId + "_" + fixCustomerName(commercialName)
				+ ".xml";
	}

	private String fixEffectiveDate(String effectiveDate) {
		if (StringUtils.isBlank(effectiveDate)) {
			// return current date if the opportunity effectivedate is empty or null
			return DateUtils.getSQLDateString(new Date());
		}

		return effectiveDate;
	}

	private String fixCustomerName(String commercialName) {
		if (StringUtils.isNotBlank(commercialName)) {
			// Remove characters other than alphanumeric, space and dot from customer name
			return commercialName.replaceAll("[^A-Za-z0-9\\.\\s]", "");
		}
		return "";
	}

	public String generateZipFileName() {
		return "BT_IVANS_" + new Date().getTime() + ".zip";
	}
}

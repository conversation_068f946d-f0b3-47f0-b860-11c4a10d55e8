package com.lmig.uscm.booktransfer.opportunity.config;

import com.lmig.idp.common.servlet.IdpSecurityAdapter;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;

@Configuration
@EnableWebSecurity
@Profile("local")
public class LocalSecurityConfig implements IdpSecurityAdapter {
    /*
    This enables access to all endpoints locally
     */
    @Override
    public void configure(HttpSecurity httpSecurity) throws Exception {
        httpSecurity.csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(requests -> requests.requestMatchers("/**").permitAll());
    }

}

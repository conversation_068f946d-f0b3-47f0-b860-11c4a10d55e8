package com.lmig.uscm.booktransfer.opportunity.client.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * The request object for opportunityService/getOppIds salesForceCodes is the
 * only required field
 * <p>
 * right now only used by systematic-qa
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class OpportunityFilterRequest {
	/**
	 * Required in the request, these are salesForceCodes but UI sends named as
	 * sfdcid
	 */
	private List<String> sfdcid;
	/**
	 * Optional in the request
	 */
	private List<String> lobs;
	/**
	 * Optional in the request
	 */
	private String startEffectiveDate;
	/**
	 * Optional in the request
	 */
	private String endEffectiveDate;
	/**
	 * Optional in the request
	 */
	private LineType lineType;

	public OpportunityFilterRequest() {
		this.lobs = new ArrayList<>();
	}
}

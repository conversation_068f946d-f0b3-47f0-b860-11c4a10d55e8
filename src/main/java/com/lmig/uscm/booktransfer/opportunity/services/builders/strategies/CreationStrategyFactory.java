package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class CreationStrategyFactory {

	private Map<CreationStrategyName, CreationStrategy> strategies;

	public CreationStrategyFactory(List<CreationStrategy> strategySet) {
		createStrategy(strategySet);
	}

	public CreationStrategy findStrategy(CreationStrategyName strategyName) {
		return strategies.get(strategyName);
	}

	private void createStrategy(List<CreationStrategy> strategySet) {
		strategies = new LinkedHashMap<>(); // LinkedHashMap maintains insertion order
		strategySet.forEach(
				strategy -> strategies.put(strategy.getStrategyName(), strategy));
	}

	public Collection<CreationStrategy> getAllStrategies() {
		return strategies.values();
	}

}

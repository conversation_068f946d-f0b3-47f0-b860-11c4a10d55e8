/*
 * Copyright (C) 2018, Liberty Mutual Group
 *
 * Created on 11/29/18 by n0210477
 */
package com.lmig.uscm.booktransfer.opportunity.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.lmig.uscm.booktransfer.opportunity.auditing.SplunkName;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.LargeTransferCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.FileContent;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityCreationHelper;
import com.lmig.uscm.booktransfer.opportunity.services.ZipFileHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import org.xml.sax.SAXException;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

@CrossOrigin
@RestController
@RequestMapping(value = "/opportunity")
@Slf4j
public class OpportunityCreationController {
	@Autowired
	private OpportunityCreationHelper opportunityCreationHelper;

	@Autowired
	private ObjectMapper objectMapper;

	private final Path UPLOAD_ROOT = Paths.get("uploads").toAbsolutePath();

	@Operation(summary = "Create opportunities from bulk xml or zip file in large transfer utility")
	@PostMapping(value = "/v3", consumes = "multipart/form-data")
	@SplunkName("CREATE OPPORTUNITY V3")
	public ResponseEntity<LargeTransferCreationResponse> createLargeTransferOpportunity(
		@Parameter(name = "file", required = true) @RequestParam MultipartFile file,
		@Parameter(name = "creationRequest", required = true) @RequestParam String creationRequest,
		@Parameter(name = "tePackageId", required = false) @RequestParam(required = false) String tePackageId
	) throws OpportunityException {
		log.info("POST - /opportunity/v3 - file: {}, creationRequest: {}, tePackageId: {}", file, creationRequest, tePackageId);
		OpportunityCreationRequest opportunityCreationRequest;

		try{
			opportunityCreationRequest = objectMapper.readValue(creationRequest, OpportunityCreationRequest.class);
			opportunityCreationRequest.setFileName(file.getOriginalFilename());
		} catch (JsonProcessingException e) {
			throw new OpportunityException(e);
		}

		// Extract MultipartFile in controller
		List<FileContent> uploadedContent = extractAllFileContents(file);
		// Async call to create opps. Do not block on the returned Future
		opportunityCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				uploadedContent, opportunityCreationRequest, tePackageId);

		return ResponseEntity.ok(new LargeTransferCreationResponse(uploadedContent.size()));
	}

	/**
	 * Extracts the content from the passed zipFile (whether it is compressed or not) or returns null
	 * <p>
	 * Note: This method must exist here as MultipartFile only binds to Controller
	 *
	 * @param zipFile compressed or not compressed file
	 * @return List of all found file content
	 * @throws OpportunityException when a non-null file cannot be read
	 */
	public List<FileContent> extractAllFileContents(final MultipartFile zipFile) throws OpportunityException {
		List<
				FileContent> allFilesContents = null;
		if (zipFile != null && StringUtils.isNotBlank(zipFile.getOriginalFilename())) {
			try {
				if (!Files.isDirectory(UPLOAD_ROOT)) {
					Files.createDirectory(UPLOAD_ROOT);
				}

				File file = new File(UPLOAD_ROOT.toString(), zipFile.getOriginalFilename());
				if (!file.createNewFile()) {
					throw new IOException("Failed to create new file " + file.getName());
				}

				zipFile.transferTo(file);

				allFilesContents = ZipFileHelper.readFileContents(file);
				opportunityCreationHelper.updateFileContents(allFilesContents);

				if (!file.delete()) {
					throw new IOException("Failed to delete file " + file.getName());
				}
			} catch (IOException e) {
				throw new OpportunityException("Could not read file for Opportunity creation", e);
			} catch (TransformerException | ParserConfigurationException | SAXException e) {
				throw new OpportunityException("Error processing XML for Opportunity creation", e);
			}
		}
		return allFilesContents;
	}

	@VisibleForTesting
	public void setObjectMapper(ObjectMapper objectMapper) {
		this.objectMapper = objectMapper;
	}
}

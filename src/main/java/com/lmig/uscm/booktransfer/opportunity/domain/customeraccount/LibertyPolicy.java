package com.lmig.uscm.booktransfer.opportunity.domain.customeraccount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LibertyPolicy {
    private String quoterComments;
    private String effectiveDate;
    private String lineOfBusiness;
    private String policyNumber;
    private Double premium;
    private String producer;
    private String status;
}

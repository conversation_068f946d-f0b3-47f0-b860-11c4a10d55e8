package com.lmig.uscm.booktransfer.opportunity.config;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.javatuples.Pair;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import org.springframework.context.annotation.Primary;

@Configuration
public class JacksonConfig {

    /**
     * This custom DateTimeFormatter is almost exactly similar to {@link DateTimeFormatter#ISO_LOCAL_DATE_TIME} with the
     * difference being it can handle both 'T' or ' ' as the delimiter between date and time sections.
     */
    private static final DateTimeFormatter CUSTOM_FORMATTER = new DateTimeFormatterBuilder()
            .parseCaseInsensitive()
            .append(DateTimeFormatter.ISO_LOCAL_DATE)
            .optionalStart()
            .appendLiteral('T')
            .optionalEnd()
            .optionalStart()
            .appendLiteral(' ')
            .optionalEnd()
            .append(DateTimeFormatter.ISO_LOCAL_TIME)
            .toFormatter();

    /**
     * Provides a {@link ObjectMapper} configured to handle several different {@link LocalDateTime} formats.
     *
     * <p>Note that in most cases a default {@link ObjectMapper} will meet your use cases.
     * This {@link ObjectMapper} is required should you run into {@link LocalDateTime} deserialization errors.</p>
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        return new ObjectMapper()
                .registerModule(new JavaTimeModule()
                        .addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer()))
                .addMixIn(Pair.class, MyPairMixin1.class)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    public static final class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(
                JsonParser jsonParser,
                DeserializationContext deserializationContext
        ) throws IOException {
            return LocalDateTime.parse(jsonParser.getValueAsString(), CUSTOM_FORMATTER);
        }
    }

    // NOTE: This mixin is required post the removal of domain-service's KeyValue class
    // https://github.com/lmigtech/domain-service/blob/master/src/main/java/com/lmig/uscm/booktransfer/Domain/KeyValue.java
    // This class was effectively a Pair<String, String> and jackson could natively serialize/deserialize it.
    // If in the future we need to serialize/deserialize Pairs with different generics (other than <String, String>)
    // then define additional mixins with matching contracts below and add them to the ObjectMapper Bean.
    // For additional context, found this mixin here https://github.com/FasterXML/jackson-databind/issues/2020
    @JsonIgnoreProperties
    abstract static class MyPairMixin1 {
        @JsonCreator
        @SuppressWarnings("rawtypes")
        public static Pair with(
                @JsonProperty("key") Object value0,
                @JsonProperty("value") Object value1
        ) {
            return Pair.with((String)value0, (String)value1);
        }
    }
}

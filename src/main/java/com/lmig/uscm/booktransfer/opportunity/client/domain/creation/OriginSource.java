package com.lmig.uscm.booktransfer.opportunity.client.domain.creation;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

public enum OriginSource {
	SLIM(OriginSourceValues.SLIM), // B2B Gateway
	AQE(OriginSourceValues.AQE), // Automated Quote Entry
	SPQE(OriginSourceValues.SPQE), // Single Page Quote Entry
	LTU(OriginSourceValues.LTU), // Large Transfer Utility page within admin-tools UI
	UNKNOWN(OriginSourceValues.UNKNOWN); // who's asking?

	private final String value;

	OriginSource(final String originSource) {
		this.value = originSource;
	}

	public String getValue() {
		return this.value;
	}

	public static OriginSource find(String sourceType) {
		String defaultVal = Optional.ofNullable(sourceType).orElse(OriginSource.OriginSourceValues.UNKNOWN);
		return Arrays.stream(OriginSource.values())
				.filter(type -> StringUtils.equals(type.getValue(), defaultVal))
				.findFirst()
				.orElse(OriginSource.UNKNOWN);
	}

	public static final class OriginSourceValues {
		public static final String SLIM = "SLIM";
		public static final String AQE = "AQE";
		public static final String SPQE = "SPQE";
		public static final String LTU = "LTU";
		public static final String UNKNOWN = "UNKNOWN";

		private OriginSourceValues() {
			throw new IllegalStateException("Utility class");
		}
	}
}

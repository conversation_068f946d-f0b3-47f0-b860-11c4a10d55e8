package com.lmig.uscm.booktransfer.opportunity.repo.mappers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityXmlData;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class OpportunityXmlDataMapper implements RowMapper<OpportunityXmlData> {

    @Override
    public OpportunityXmlData mapRow(ResultSet resultSet, int i) throws SQLException {
        OpportunityXmlData item = OpportunityXmlData.builder()
          .opportunityId(resultSet.getInt("OpportunityId"))
          .xmlData(resultSet.getString("Data"))
          .bookTransferId(resultSet.getInt("BookTransferId"))
          .build();

        final LineType databaseLineType = OpportunityUtil.defaultPersonalOrConvertStringToLineType(resultSet.getString("LineType"));
        item.setLineType(databaseLineType);
        return item;
    }
}

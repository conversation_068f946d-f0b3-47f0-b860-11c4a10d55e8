package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.auditing.LogTransaction;
import com.lmig.uscm.booktransfer.opportunity.client.config.OpportunityUrlProvider;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.OpportunityEvent;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityEventRepo;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.domain.TransactionStatus;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.EndTransactionRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.MetaInfoRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.StartTransactionRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.TransactionEventRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.response.TransactionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Used to coordinate publishing of OpportunityEvent / OpportunityCreationEvent (which is deprecated and will be
 * replaced eventually by Mongo, Kafka flow)
 */
@Slf4j
public class OpportunityEventHelper {
    public static final String ORIGIN = "origin";
    public static final String OPPORTUNITY_SERVICE = "Opportunity-Service";

    private final OpportunityUrlProvider opportunityUrlProvider;

    /**
     * Helper to call audit log rest api
     */
    private final AuditLogHelper auditLogHelper;

    /**
     * Saves OppEvent records to Mongo to publish to Kafka topic
     */
    private final OpportunityEventRepo opportunityEventRepo;

    public OpportunityEventHelper(
            final OpportunityEventRepo opportunityEventRepo,
            final OpportunityUrlProvider opportunityUrlProvider,
            final AuditLogHelper auditLogHelper
    ) {
        this.opportunityEventRepo = opportunityEventRepo;
        this.opportunityUrlProvider = opportunityUrlProvider;
        this.auditLogHelper = auditLogHelper;
    }

    /**
     * Save the OpportunityEvent
     * Mark method with LogTransaction Annotation so we can track via Splunk
     * Throw exceptions if encountered for logging
     */
    @LogTransaction("SAVE OPPORTUNITY EVENT")
    public OpportunityEvent processOpportunityEvent(Opportunity opportunity, String oppEventType) {
        return saveOpportunityEvent(new OpportunityEvent()
                .setOpportunityId(String.valueOf(opportunity.getOpportunityId()))
                .setCustomerAccountId(opportunity.getCustomerAccountId())
                .setEventStatus(oppEventType)
                .setLineType(String.valueOf(opportunity.getLineType()))
                .setOpportunityUrl(buildOpportunityUrl(String.valueOf(opportunity.getOpportunityId()))));
    }

    /**
     * Save the OpportunityEvent
     * Mark method with LogTransaction Annotation so we can track via Splunk
     * Throw exceptions if encountered for logging
     */
    @LogTransaction("SAVE OPPORTUNITY EVENT")
    public List<OpportunityEvent> processOpportunityEvent(List<Opportunity> opportunities, List<String> oppEventTypes) {
        return saveOpportunityEvents(IntStream.range(0, opportunities.size())
                .mapToObj(i -> new OpportunityEvent()
                        .setOpportunityId(String.valueOf(opportunities.get(i).getOpportunityId()))
                        .setCustomerAccountId(opportunities.get(i).getCustomerAccountId())
                        .setEventStatus(oppEventTypes.get(i))
                        .setLineType(String.valueOf(opportunities.get(i).getLineType()))
                        .setOpportunityUrl(buildOpportunityUrl(String.valueOf(opportunities.get(i).getOpportunityId())))
                ).collect(Collectors.toList()));
    }

    private OpportunityEvent saveOpportunityEvent(OpportunityEvent oppEvent) {
        TransactionResponse transactionResponse = startOrDefaultTransactionWithRetries(oppEvent);
        String transactionId = transactionResponse == null ? null : transactionResponse.getId();
        OpportunityEvent savedOppEvent;
        try {
            oppEvent.setTransactionId(transactionId);

            log.info("Saving OpportunityEvent type: {} for opportunityId: {}",
                    oppEvent.getEventStatus(), oppEvent.getOpportunityId());
            savedOppEvent = opportunityEventRepo.save(oppEvent);
            log.info("Saved OpportunityEvent opportunityId: {} eventId: {}",
                    oppEvent.getOpportunityId(), savedOppEvent.getId());
        } catch (Exception e) {
            log.error("Notifying team. Error Saving OpportunityEvent type: {} for opportunityId: {}",
                    oppEvent.getEventStatus(), oppEvent.getOpportunityId(), e);
            if (transactionResponse != null) {
                endTransactionFailedWithRetries(transactionId, e);
            }
            throw e;
        }

        log.info("Updating transaction OpportunityEvent status={} CorrelationId={} EntityId={}",
                savedOppEvent.getEventStatus(), savedOppEvent.getId(), oppEvent.getOpportunityId());
        if (transactionResponse != null) {
            addTransactionEventWithRetries(transactionId, savedOppEvent);
        }

        return savedOppEvent;
    }

    private List<OpportunityEvent> saveOpportunityEvents(List<OpportunityEvent> oppEvents) {
        List<String> opportunityEventIds = oppEventIds(oppEvents);

        List<TransactionResponse> transactionResponses = startOrDefaultTransactionsWithRetries(oppEvents);
        List<String> transactionIds = transactionResponses == null
                ? new ArrayList<>(Collections.nCopies(oppEvents.size(), null))
                : transactionResponseIds(transactionResponses);
        IntStream.range(0, oppEvents.size()).forEach(i -> oppEvents.get(i).setTransactionId(transactionIds.get(i)));

        List<OpportunityEvent> savedOppEvents;
        List<String> savedOppEventIds;
        try {
            log.info("Saving OpportunityEvent for opportunityIds: {}", opportunityEventIds);
            savedOppEvents = opportunityEventRepo.saveAll(oppEvents);
            savedOppEventIds = oppEventIds(savedOppEvents);
            log.info("Saved OpportunityEvent opportunityId: {} eventId: {}", opportunityEventIds, savedOppEventIds);
        } catch (Exception e) {
            log.error(
                    "Notifying team. Error Saving OpportunityEvent for opportunityId: {}",
                    opportunityEventIds, e);
            if (transactionResponses != null) {
                endTransactionsFailedWithRetries(transactionIds, e);
            }
            throw e;
        }

        log.info("Updating transaction OpportunityEvent status={} CorrelationId={} EntityId={}",
                savedOppEvents.get(0).getEventStatus(), savedOppEventIds, opportunityEventIds);
        if (transactionResponses != null) {
            addTransactionEventsWithRetries(transactionIds, savedOppEvents);
        }

        return List.of(); // savedOppEvents;
    }

    private void endTransactionFailedWithRetries(@Nullable String transactionId, Exception e) {
        // Not throwing error on failed audit-log connection since we are already in failed state
        OpportunityUtil.runFunctionWithRetriesWithDefault(
                inReq -> auditLogHelper.endTransactionEvent(transactionId, inReq),
                endTransactionRequest(e),
                null);
    }

    private void endTransactionsFailedWithRetries(List<String> transactionIds, Exception e) {
        // Not throwing error on failed audit-log connection since we are already in failed state
        OpportunityUtil.runFunctionWithRetriesWithDefault(
                auditLogHelper::batchEndTransactions,
                transactionIds.stream().collect(Collectors.toMap(id -> id, id -> endTransactionRequest(e))),
                null);
    }

    private void addTransactionEventWithRetries(String transactionId, OpportunityEvent savedOppEvent) {
        OpportunityUtil.runFunctionWithRetriesWithDefault(
                inReq -> auditLogHelper.addTransactionEvent(transactionId, inReq),
                transactionEventRequest(savedOppEvent),
                null);
    }

    private void addTransactionEventsWithRetries(List<String> transactionIds, List<OpportunityEvent> savedOppEvents) {
        assert transactionIds.size() == savedOppEvents.size() : String.format(
                "Received mismatched list sizes %s and %s", transactionIds.size(), savedOppEvents.size());
        OpportunityUtil.runFunctionWithRetriesWithDefault(
                auditLogHelper::batchAddTransactionEvents,
                IntStream.range(0, transactionIds.size()).boxed()
                        .collect(Collectors.toMap(
                                transactionIds::get,
                                i -> List.of(transactionEventRequest(savedOppEvents.get(i))))),
                null);
    }

    private TransactionResponse startOrDefaultTransactionWithRetries(OpportunityEvent oppEvent) {
        return OpportunityUtil.runFunctionWithRetriesWithDefault(
                auditLogHelper::startTransaction, startTransactionRequest(oppEvent), null);
    }

    private List<TransactionResponse> startOrDefaultTransactionsWithRetries(List<OpportunityEvent> oppEvents) {
        return OpportunityUtil.runFunctionWithRetriesWithDefault(
                auditLogHelper::batchStartTransactions,
                oppEvents.stream()
                        .map(OpportunityEventHelper::startTransactionRequest)
                        .collect(Collectors.toList()),
                null);
    }

    private String buildOpportunityUrl(String opportunityId) {
        return opportunityUrlProvider.getBaseUrl() + "/v3/opportunity/" + opportunityId;
    }

    private static List<String> oppEventIds(List<OpportunityEvent> events) {
        return events.stream().map(OpportunityEvent::getOpportunityId).collect(Collectors.toList());
    }

    private static List<String> transactionResponseIds(List<TransactionResponse> responses) {
        return responses.stream().map(TransactionResponse::getId).collect(Collectors.toList());
    }

    private static StartTransactionRequest startTransactionRequest(OpportunityEvent oppEvent) {
        return StartTransactionRequest.builder()
                .type(oppEvent.getEventStatus() + "_QUOTE_ENTRY")
                .metaInfo(MetaInfoRequest.builder()
                        .transactionMetaInfo(Map.of(
                                "opportunityId", oppEvent.getOpportunityId(),
                                ORIGIN, OPPORTUNITY_SERVICE))
                        .build())
                .build();
    }

    private static TransactionEventRequest transactionEventRequest(OpportunityEvent savedOppEvent) {
        return TransactionEventRequest.builder()
                .name("OPP_EVENT_SAVED")
                .status(TransactionStatus.PENDING)
                .metaInfo(MetaInfoRequest.builder()
                        .transactionMetaInfo(Map.of("oppEventId", savedOppEvent.getId()))
                        .eventMetaInfo(Map.of(ORIGIN, OPPORTUNITY_SERVICE))
                        .build())
                .build();
    }

    private static EndTransactionRequest endTransactionRequest(Exception e) {
        return EndTransactionRequest.builder()
                .status(TransactionStatus.FAILED)
                .events(List.of(TransactionEventRequest.builder()
                        .name("OPP_EVENT_SAVE_FAILED")
                        .status(TransactionStatus.FAILED)
                        .metaInfo(MetaInfoRequest.builder()
                                .eventMetaInfo(Map.of(ORIGIN, OPPORTUNITY_SERVICE, "StackTrace", e.getStackTrace()))
                                .build())
                        .build()))
                .build();
    }
}

---
idp:
  - provider: azure
    applications:
      - name: opportunity-service-azure-client-development
        client: true
        email_address: Quo<PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON>@LibertyMutual.com
  - type: oauth
    resources:
      - type: api
        name: oppsvc-d
        email_address: Quotabot<PERSON><PERSON><EMAIL>
        external_facing: false
        allowed_scopes:
          - opportunity.read
          - opportunity.create
          - opportunity.detokenize
        allowed_clients:
          - artifact_guid: 34027978-72ed-488b-80a1-36c50b6a1fa7
            description: Admin Tool
            environment_keys:
              - development
            scopes:
              - opportunity.read
              - opportunity.create
          - artifact_guid: b56d8525-9b82-4d2e-8197-1d60219c7c03
            description: AQE UI
            environment_keys:
              - development
            scopes:
              - opportunity.read
              - opportunity.create
              - opportunity.detokenize
          - artifact_guid: 387928df-cb52-4e69-bb85-319bdcccacd4
            description: bt-opp-to-quote-entry-service
            environment_keys:
              - development
            scopes:
              - opportunity.read
          - artifact_guid: 05c5c6c5-b4f4-4cf9-976a-c1bbdc5af7a4
            description: quoting-bl-adapter
            environment_keys:
              - development
            scopes:
              - opportunity.read
              - opportunity.detokenize
              - opportunity.create
          - artifact_guid: 0ee8e2e1-3d81-41d2-92af-1401e65ff416
            description: xml-viewer-services
            environment_keys:
              - development
            scopes:
              - opportunity.read
              - opportunity.detokenize
              - opportunity.create
          - artifact_guid: 7707a70e-5d83-40ba-a6e4-94738dc6c482
            description: bt-pl-rating-service
            environment_keys:
              - development
            scopes:
              - opportunity.read
              - opportunity.detokenize
          - artifact_guid: e76586dc-463e-44eb-ab65-b10db3156f55
            description: transformation-service-legacy
            environment_keys:
              - development
            scopes:
              - opportunity.read
              - opportunity.detokenize
          - artifact_guid: fe686a95-c2c9-4cc8-8376-c489b8b16d1a
            description: Transformation UI
            environment_keys:
              - development
            scopes:
              - opportunity.read
          - artifact_guid: b1a70601-985b-47c2-9ed0-7ca2f233f245
            description: Process Request Service
            environment_keys:
              - development
            scopes:
              - opportunity.read
          - artifact_guid: d0f835ec-d09c-4f4c-9699-803eff7c5a67
            description: Report Builder
            environment_keys:
              - development
            scopes:
              - opportunity.read
          - artifact_guid: c8f90543-3ce4-42a4-abc4-ed123e7697fd
            description: Quote Service Scheduler
            environment_keys:
              - development
            scopes:
              - opportunity.read
          - artifact_guid: 0cd4a63e-5d41-40cd-9d9e-dba73afa0174
            description: scheduler ui
            environment_keys:
              - development
            scopes:
              - opportunity.read
          - artifact_guid: 4b0697df-53e0-421d-9dd0-67b45a848868
            description: Upload Services
            environment_keys:
              - development
            scopes:
              - opportunity.create
          - artifact_guid: 9e3430e0-51a9-4478-b37a-9c2ec012a70e
            description: Coverage Compare
            environment_keys:
              - development
            scopes:
              - opportunity.read
          - artifact_guid: 2ee4a855-75c9-4be6-82b6-2596d42f6c3e
            description: SPQE XML Builder
            environment_keys:
              - development
            scopes:
              - opportunity.read
              - opportunity.detokenize
      - type: client
        name: oppClient-d

applications:
  - name: opportunityservice
    routes:
      - route: opportunityservice.pdc.paas.lmig.com
    buildpacks:
      - datadog_supply
      - java_current
    instances: 5
    memory: 2048M
    timeout: 180
    env:
      # sfStreamMongoProd - To create mongo client bean for production salesforce streaming events db
      # transformationClient - To create oauth webclient bean for transformation engine service
      SPRING_PROFILES_ACTIVE: "production,sfStreamMongoProd,transformationClient"
      JAVA_OPTS: "-Dlog4j2.formatMsgNoLookups=true -XX:MaxDirectMemorySize=64M"
      DD_SERVICE: ${forge.artifact.key}
      DD_VERSION: ${result.version}
      DD_ENV: ${forge.environment.key}
      DD_RUNTIME_METRICS_ENABLED: true
      DD_PROFILING_ENABLED: true
      DD_TRACE_ENABLED: true
      DD_JMXFETCH_ENABLED: true
      DD_TAGS:
        - team:quotabotz
      AZURE_AQE_DB_UN: ${secret.bt-shared-azure-sql-db-production.read-write.username}
      AZURE_AQE_DB_PW: ${secret.bt-shared-azure-sql-db-production.read-write.password}
      AZURE_AQE_DB_NAME: ${secret.bt-shared-azure-sql-db-production.read-write.database_name}
      AZURE_AQE_DB_PORT: ${secret.bt-shared-azure-sql-db-production.read-write.port}
      AZURE_AQE_DB_HOST: ${secret.bt-shared-azure-sql-db-production.read-write.host}
      QUOTE_ADAPTER_URL: ${secret.BlQuotingAPIs.quoteApi}
      QUOTE_ADAPTER_AUDIENCE: ${quoting-bl-adapter.audience}
      OPP_CLIENT_ID: ${secret.oppClient.id}
      OPP_CLIENT_SECRET: ${secret.oppClient.secret}
      JBP_CONFIG_OPEN_JDK_JRE: '{ jre: { version: 17.+}}'
      TRANSFORMATION_AUDIENCE: ${transformsvc.audience}
      SALESFORCE_MONGO_UN: ${secret.bt-salesforce-streaming-events-db-production.read-write.username}
      SALESFORCE_MONGO_PW: ${secret.bt-salesforce-streaming-events-db-production.read-write.password}
      AUDIENCE: ${oauth.api.id}
      SENSITIVE_API_ACCESS_TOKEN: ${sensitive-api.accessTokenUrl}
      SENSITIVE_API_SCOPES: ${sensitive-api.scopes}
      QUOTING_GUIDELINE_API_ACCESS_TOKEN: ${quoting-guideline-api-prod.accessTokenUrl}
      QUOTING_GUIDELINE_API_SCOPES: ${quoting-guideline-api-prod.scopes}
      AUDIT_LOG_API_AUDIENCE: ${audit-log-api.accessTokenUrl}
      AUDIT_LOG_API_SCOPES: ${audit-log-api.scopes}
      BT_PAYMENT_SERVICE_API_ACCESS_TOKEN_URI: ${payment-svc-prod.accessTokenUrl}
      BT_PAYMENT_SERVICE_API_SCOPES: ${payment-svc-prod.scopes}
      BT_CUSTOMER_ACCOUNT_SERVICE_API_ACCESS_TOKEN_URI: ${book-transfer-customer-account-service.accessTokenUrl}
      BT_CUSTOMER_ACCOUNT_SERVICE_API_SCOPES: ${book-transfer-customer-account-service.scopes}
      ADDRESS_CLEANSE_CLIENT_ID: ${secret.opportunity-service-azure-client-production.id}
      ADDRESS_CLEANSE_CLIENT_SECRET: ${secret.opportunity-service-azure-client-production.secret}
      EFT_PAYMENT_CLIENT_ID: ${secret.apigeecreds-prd-cloud-foundry-prod-pdc-internal-production.client-id}
      EFT_PAYMENT_CLIENT_SECRET: ${secret.apigeecreds-prd-cloud-foundry-prod-pdc-internal-production.client-secret}
      BT_PROPERTY_INFO_SERVICE_API_ACCESS_TOKEN_URI: ${propertyinfosvc.accessTokenUrl}
      BT_PROPERTY_INFO_SERVICE_API_SCOPES: ${propertyinfosvc.scopes}
      QUOTE_REPORT_SERVICE_API_ACCESS_TOKEN_URI: ${quotereportsvc.accessTokenUrl}
      QUOTE_REPORT_SERVICE_API_SCOPES: ${quotereportsvc.scopes}
    services:
      - autoscaler
      - uploadqueue
    blue-green:
      health-check-endpoint: /health
    autoscaler:
      instance_limits:
        min: 2
        max: 5
      rules:
        - rule_type: cpu
          threshold:
            min: 25
            max: 60
        - rule_type: memory
          threshold:
            min: 25
            max: 60
      scheduled_limit_changes: [ ]

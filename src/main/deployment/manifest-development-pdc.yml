applications:
  - name: opportunityservice-development
    routes:
      - route: opportunityservice-development.pdc.np.paas.lmig.com
    buildpacks:
      - datadog_supply
      - java_current
    instances: 3
    memory: 1024M
    timeout: 180
    env:
      # sfStreamMongoDev - To create mongo client bean for dev salesforce streaming events db
      # transformationClient - To create oauth webclient bean for transformation engine service
      SPRING_PROFILES_ACTIVE: "dev,sfStreamMongoDev,transformationClient"
      JAVA_OPTS: "-Dlog4j2.formatMsgNoLookups=true"
      DD_SERVICE: ${forge.artifact.key}
      DD_VERSION: ${result.version}
      DD_ENV: ${forge.environment.key}
      DD_RUNTIME_METRICS_ENABLED: true
      DD_PROFILING_ENABLED: true
      DD_TRACE_ENABLED: true
      DD_JMXFETCH_ENABLED: true
      DD_TAGS:
        - team:quotabotz
      JBP_CONFIG_OPEN_JDK_JRE: '{ jre: { version: 17.+}}'
      AZURE_AQE_DB_UN: ${secret.bt-shared-azure-sql-db-development.read-write.username}
      AZURE_AQE_DB_PW: ${secret.bt-shared-azure-sql-db-development.read-write.password}
      AZURE_AQE_DB_NAME: ${secret.bt-shared-azure-sql-db-development.read-write.database_name}
      AZURE_AQE_DB_PORT: ${secret.bt-shared-azure-sql-db-development.read-write.port}
      AZURE_AQE_DB_HOST: ${secret.bt-shared-azure-sql-db-development.read-write.host}
      QUOTE_ADAPTER_URL: ${secret.BlQuotingAPIs.quoteApi}
      QUOTE_ADAPTER_AUDIENCE: ${quoting-bl-adapter.audience}
      OPP_CLIENT_ID: ${secret.oppClient-d.id}
      OPP_CLIENT_SECRET: ${secret.oppClient-d.secret}
      TRANSFORMATION_AUDIENCE: ${transformsvc-d.audience}
      SALESFORCE_MONGO_UN: ${secret.bt-salesforce-streaming-events-db-development.read-write.username}
      SALESFORCE_MONGO_PW: ${secret.bt-salesforce-streaming-events-db-development.read-write.password}
      AUDIENCE: ${oauth.api.id}
      SENSITIVE_API_ACCESS_TOKEN: ${sensitive-api-dev.accessTokenUrl}
      SENSITIVE_API_SCOPES: ${sensitive-api-dev.scopes}
      QUOTING_GUIDELINE_API_ACCESS_TOKEN: ${quoting-guideline-api-dev.accessTokenUrl}
      QUOTING_GUIDELINE_API_SCOPES: ${quoting-guideline-api-dev.scopes}
      AUDIT_LOG_API_AUDIENCE: ${audit-log-api-dev.accessTokenUrl}
      AUDIT_LOG_API_SCOPES: ${audit-log-api-dev.scopes}
      UPLOAD_PREPROCESSOR_ACCESS_TOKEN_URI: ${upload-preprocessor-api-dev.accessTokenUrl}
      UPLOAD_PREPROCESSOR_API_SCOPES: ${upload-preprocessor-api-dev.scopes}
      BT_PAYMENT_SERVICE_API_ACCESS_TOKEN_URI: ${payment-svc-dev.accessTokenUrl}
      BT_PAYMENT_SERVICE_API_SCOPES: ${payment-svc-dev.scopes}
      BT_CUSTOMER_ACCOUNT_SERVICE_API_ACCESS_TOKEN_URI: ${book-transfer-customer-account-service.accessTokenUrl}
      BT_CUSTOMER_ACCOUNT_SERVICE_API_SCOPES: ${book-transfer-customer-account-service.scopes}
      ADDRESS_CLEANSE_CLIENT_ID: ${secret.opportunity-service-azure-client-development.id}
      ADDRESS_CLEANSE_CLIENT_SECRET: ${secret.opportunity-service-azure-client-development.secret}
      EFT_PAYMENT_CLIENT_ID: ${secret.apigeecreds-dev-cloud-foundry-non-prod-pdc-internal-develop.client-id}
      EFT_PAYMENT_CLIENT_SECRET: ${secret.apigeecreds-dev-cloud-foundry-non-prod-pdc-internal-develop.client-secret}
      BT_PROPERTY_INFO_SERVICE_API_ACCESS_TOKEN_URI: ${propertyinfosvc.accessTokenUrl}
      BT_PROPERTY_INFO_SERVICE_API_SCOPES: ${propertyinfosvc.scopes}
      QUOTE_REPORT_SERVICE_API_ACCESS_TOKEN_URI: ${quotereportsvc.accessTokenUrl}
      QUOTE_REPORT_SERVICE_API_SCOPES: ${quotereportsvc.scopes}
    services:
      - autoscaler
      - uploadqueue
    blue-green:
      health-check-endpoint: /health
    autoscaler:
      instance_limits:
        min: 1
        max: 3
      rules:
        - rule_type: cpu
          threshold:
            min: 25
            max: 75
        - rule_type: memory
          threshold:
            min: 25
            max: 75
      scheduled_limit_changes: [ ]

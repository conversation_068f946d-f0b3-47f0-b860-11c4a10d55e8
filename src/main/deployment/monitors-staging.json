{"datadog_monitors": {"monitors": [{"name": "${appNameVariable} in ${environmentVariable} - has an abnormal change in throughput", "type": "query alert", "query": "avg(last_4h):anomalies(sum:trace.repository.operation.hits{env:${environmentVariable},service:${appNameVariable}}, 'basic', 3, direction='both', interval=60, alert_window='last_15m', seasonality='weekly', timezone='utc', count_default_zero='true') >= 1", "message": "${appNameVariable} throughput deviated too much from its usual value. \n@webhook-test-eventstream-api", "tags": ["team:<PERSON><PERSON><PERSON>"], "options": {"thresholds": {"critical": 1, "critical_recovery": 0}, "notify_audit": false, "require_full_window": false, "notify_no_data": false, "no_data_timeframe": 10, "renotify_interval": 0, "threshold_windows": {"trigger_window": "last_15m", "recovery_window": "last_15m"}, "locked": false, "silenced": {}, "include_tags": true, "escalation_message": ""}, "priority": null}, {"name": "${appNameVariable} in ${environmentVariable} - has a high average latency", "type": "query alert", "query": "avg(last_10m):( avg:trace.repository.operation{env:${environmentVariable},service:${appNameVariable}} ) > 0.5", "message": "${appNameVariable} average latency is too high. \n@webhook-test-eventstream-api", "tags": ["team:<PERSON><PERSON><PERSON>"], "options": {"thresholds": {"critical": 0.5, "warning": 0.3}, "notify_audit": false, "require_full_window": false, "notify_no_data": false, "renotify_interval": 0, "locked": false, "silenced": {}, "include_tags": true, "new_host_delay": 300, "escalation_message": ""}, "priority": null}, {"name": "${appNameVariable} in ${environmentVariable} - has a high error rate", "type": "query alert", "query": "sum(last_10m):( sum:trace.repository.operation.errors{env:${environmentVariable},service:${appNameVariable}}.as_count() / sum:trace.repository.operation.hits{env:${environmentVariable},service:${appNameVariable}}.as_count() ) > 0.05", "message": "${appNameVariable} error rate is too high. \n@webhook-test-eventstream-api", "tags": ["team:<PERSON><PERSON><PERSON>"], "options": {"thresholds": {"critical": 0.05, "warning": 0.01}, "notify_audit": false, "require_full_window": false, "notify_no_data": false, "renotify_interval": 0, "locked": false, "silenced": {}, "include_tags": true, "new_host_delay": 300, "escalation_message": ""}, "priority": null}, {"name": "${appNameVariable} in ${environmentVariable} - has a high p90 latency", "type": "query alert", "query": "avg(last_10m):p90:trace.repository.operation{env:${environmentVariable},service:${appNameVariable}} > 1", "message": "${appNameVariable} 90th percentile latency is too high. \n@webhook-test-eventstream-api", "tags": ["team:<PERSON><PERSON><PERSON>"], "options": {"thresholds": {"critical": 1, "warning": 0.8}, "notify_audit": false, "require_full_window": false, "notify_no_data": false, "renotify_interval": 0, "locked": false, "silenced": {}, "include_tags": true, "new_host_delay": 300, "escalation_message": ""}, "priority": null}, {"name": "${appNameVariable} in ${environmentVariable} - has new errors in the latest deployment on", "type": "event alert", "query": "events('priority:all tags:deployment_analysis,env:${environmentVariable},service:${appNameVariable}').rollup('count').last('15m') > 0", "message": "There were new errors detected in the latest deployment.\n\n{{event.text}} \n@webhook-test-eventstream-api", "tags": ["team:<PERSON><PERSON><PERSON>"], "options": {"notify_audit": false, "locked": false, "silenced": {}, "include_tags": true, "thresholds": {"critical": 0}, "notify_no_data": false, "renotify_interval": 0, "timeout_h": 0, "no_data_timeframe": 2, "escalation_message": ""}, "priority": null}]}}
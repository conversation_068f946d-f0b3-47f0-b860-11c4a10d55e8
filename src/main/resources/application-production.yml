ADDRESS_CLEANSE_API_OAUTH_SCOPES: 156662e8-d751-4935-8c65-63d319de09bb/.default
bl:
    default:
        uploadevent:
            id: 36456
booktransfer:
    defaultid: 6044
    url: https://book-transfer-service.pdc.paas.lmig.com
idp:
    ping:
        federate:
            jwk-signing-cert-url: https://lmidp.libertymutual.com/ext/oauth/x509/kid?v=apisigningcert
eighthcar:
    enabled: false
email:
    address: <EMAIL>
    url: https://emailservice.pdc.paas.lmig.com/sendemail
oppservice:
    oauth2:
        quoteadapter:
            client:
                access-token-uri: https://lmidp.libertymutual.com/as/token.oauth2?aud=https://${QUOTE_ADAPTER_AUDIENCE}
        transformation:
            client:
                access-token-uri: https://lmidp.libertymutual.com/as/token.oauth2?aud=https://${TRANSFORMATION_AUDIENCE}
        upload:
            preprocessor:
                client:
                    access-token-uri: https://lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
                    client-id: fsdfewhgfhf
                    client-secret: adsafdgfdhtrd
                    grant-type: client_credentials
                    scope: uploadpreprocessor.scrub
        eft:
            paymentaccounts:
                client:
                    access-token-uri: https://api.us.lmig.com/oauth/token
qni:
    url: https://safesite.safeco.com/Client/OpenActivity.aspx?l=o
service:
    book-transfer-url-provider:
        base-url: https://book-transfer-service.pdc.paas.lmig.com
        path: /
    payment-service-url-provider:
        base-url: https://bt-payment-service-production.us-east-1.paas.lmig.com
    quote-report-url-provider:
        base-url: https://quotereportservice.pdc.paas.lmig.com
        path: /v1/quote-report
    transformation-url-provider:
        base-url: https://transformation-service.us-east-1.paas.lmig.com
    upload-service-url-provider:
        base-url: https://uploadservices.pdc.paas.lmig.com
    address-cleanse-url-provider:
        base-url: https://api.us.lmig.com/reusify/address-cleanse
    eft-paymentaccounts-url-provider:
        base-url: https://api.us.lmig.com/fin/paymentaccounts/paymentaccounts
    customer-account-url-provider:
        base-url: https://0egbplpz64-vpce-00de98ed4ff2dc73e.execute-api.us-east-1.amazonaws.com/production/api
    property-info-url-provider:
        base-url: https://bt-property-info-service-production.us-east-1.paas.lmig.com
spipolicydata:
    url: https://spipolicydataservice.pdc.paas.lmig.com
spring:
    security:
      oauth2:
          client:
              provider:
                  transformation:
                      token-uri: https://lmidp.libertymutual.com/as/token.oauth2?aud=https://${TRANSFORMATION_AUDIENCE}

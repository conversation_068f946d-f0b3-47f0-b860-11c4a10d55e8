ADDRESS_CLEANSE_API_OAUTH_ACCESS_TOKEN_URL: https://login.microsoftonline.com/libertymutual.onmicrosoft.com/oauth2/v2.0/token

bl:
    default:
        uploadevent:
            id: 1
booktransfer:
    defaultid: 8407
bt:
    mongodb:
        password: ${SALESFORCE_MONGO_PW}
        username: ${SALESFORCE_MONGO_UN}
    webclient:
        transformation:
            timeout: 30000 #30s
            max-in-memory-size: ******** # 16MB
        quote-report:
            timeout: 30000 #30s
            max-in-memory-size: ******** # 16MB
        customer-account:
            timeout: 30000 #30s
            max-in-memory-size: ******** # 16MB
idp:
    ping:
        federate:
            audience: ${AUDIENCE}
            enabled: true
            jwk-signing-cert-url: https://test-lmidp.libertymutual.com/ext/oauth/x509/kid?v=apisigningcert
email:
    address: <EMAIL>
    url: https://emailservice-dev.pdc.np.paas.lmig.com/sendemail
opportunity:
    topic:
        arn: ${OPPORTUNITY_TOPIC_ARN}
oppservice:
    oauth2:
        auditlog:
            client:
                access-token-uri: ${AUDIT_LOG_API_AUDIENCE}
                client-id: ${OPP_CLIENT_ID}
                client-secret: ${OPP_CLIENT_SECRET}
                grant-type: client_credentials
                scope: ${AUDIT_LOG_API_SCOPES}
        bt:
            payment:
                service:
                    client:
                        access-token-uri: ${BT_PAYMENT_SERVICE_API_ACCESS_TOKEN_URI}
                        client-id: ${OPP_CLIENT_ID}
                        client-secret: ${OPP_CLIENT_SECRET}
                        grant-type: client_credentials
                        scope: ${BT_PAYMENT_SERVICE_API_SCOPES}
        quoteadapter:
            client:
                access-token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://${QUOTE_ADAPTER_AUDIENCE}
                client-id: ${OPP_CLIENT_ID}
                client-secret: ${OPP_CLIENT_SECRET}
                grant-type: client_credentials
                scope: lambda_execute
        quoting:
            guideline:
                client:
                    access-token-uri: ${QUOTING_GUIDELINE_API_ACCESS_TOKEN}
                    client-id: ${OPP_CLIENT_ID}
                    client-secret: ${OPP_CLIENT_SECRET}
                    grant-type: client_credentials
                    scope: ${QUOTING_GUIDELINE_API_SCOPES}
        sensitivedata:
            client:
                access-token-uri: ${SENSITIVE_API_ACCESS_TOKEN}
                client-id: ${OPP_CLIENT_ID}
                client-secret: ${OPP_CLIENT_SECRET}
                grant-type: client_credentials
                scope: ${SENSITIVE_API_SCOPES}
        upload:
            preprocessor:
                client:
                    access-token-uri: ${UPLOAD_PREPROCESSOR_ACCESS_TOKEN_URI}
                    client-id: ${OPP_CLIENT_ID}
                    client-secret: ${OPP_CLIENT_SECRET}
                    grant-type: client_credentials
                    scope: ${UPLOAD_PREPROCESSOR_API_SCOPES}
        addresscleanse:
            client:
                access-token-uri: ${ADDRESS_CLEANSE_API_OAUTH_ACCESS_TOKEN_URL}
                client-id: ${ADDRESS_CLEANSE_CLIENT_ID}
                client-secret: ${ADDRESS_CLEANSE_CLIENT_SECRET}
                grant-type: client_credentials
                scope: ${ADDRESS_CLEANSE_API_OAUTH_SCOPES}
        eft:
            paymentaccounts:
                client:
                    client-id: ${EFT_PAYMENT_CLIENT_ID}
                    client-secret: ${EFT_PAYMENT_CLIENT_SECRET}
                    grant-type: client_credentials
                    scope: read
proxy:
    port: 80
    url: app-proxy.lmig.com
qni:
    url: http://ecdev-sam-dev.apps.safeco.com/Client/DevML/Client/OpenActivity.aspx?l=o
server:
    compression:
        enabled: true
        mime-types: text/zip, text/xml, text/plain, text/css, text/javascript, application/javascript, application/json, application/zip
        min-response-size: 2048
    port: 8081
    tomcat:
        max-swallow-size: -1
service:
    book-transfer-url-provider:
        base-url: https://book-transfer-service-test.pdc.np.paas.lmig.com
        path: /
    quote-report-url-provider:
        base-url: https://quotereportservice-test.pdc.np.paas.lmig.com
        path: /v1/quote-report
    quoting-adapter-url-provider:
        base-url: ${QUOTE_ADAPTER_URL}
    transformation-url-provider:
        base-url: https://transformation-service-test.us-east-1.np.paas.lmig.com
    upload-service-url-provider:
        base-url: https://uploadservices-dev.pdc.np.paas.lmig.com
    address-cleanse-url-provider:
        base-url: https://api-tst.us.lmig.com/reusify/address-cleanse
    customer-account-url-provider:
        base-url: https://nbd6l2f4w0-vpce-074828fb930397a5a.execute-api.us-east-1.amazonaws.com/test/api
    property-info-url-provider:
        base-url: https://bt-property-info-service-test.us-east-1.np.paas.lmig.com

spring:
    security:
      oauth2:
        client:
            registration:
              transformation:
                  client-id: ${OPP_CLIENT_ID}
                  client-secret: ${OPP_CLIENT_SECRET}
                  authorization-grant-type: client_credentials
                  scope: transformation.execute
              customerAccount:
                  client-id: ${OPP_CLIENT_ID}
                  client-secret: ${OPP_CLIENT_SECRET}
                  authorization-grant-type: client_credentials
                  scope: ${BT_CUSTOMER_ACCOUNT_SERVICE_API_SCOPES}
              property-info:
                  client-id: ${OPP_CLIENT_ID}
                  client-secret: ${OPP_CLIENT_SECRET}
                  authorization-grant-type: client_credentials
                  scope: ${BT_PROPERTY_INFO_SERVICE_API_SCOPES}
              quote-report:
                  client-id: ${OPP_CLIENT_ID}
                  client-secret: ${OPP_CLIENT_SECRET}
                  authorization-grant-type: client_credentials
                  scope: ${QUOTE_REPORT_SERVICE_API_SCOPES}
            provider:
                transformation:
                    token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://${TRANSFORMATION_AUDIENCE}
                customerAccount:
                    token-uri: ${BT_CUSTOMER_ACCOUNT_SERVICE_API_ACCESS_TOKEN_URI}
                property-info:
                    token-uri: ${BT_PROPERTY_INFO_SERVICE_API_ACCESS_TOKEN_URI}
                quote-report:
                    token-uri: ${QUOTE_REPORT_SERVICE_API_ACCESS_TOKEN_URI}
    mvc:
        pathmatch:
            matching-strategy: ant_path_matcher
    datasource:
        driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
        tomcat:
            max-active: 50
            max-wait: 10000
            test-on-borrow: true
            test-while-idle: true
            validation-query: SELECT 1
        hikari:
            maximum-pool-size: 50 # Increase as needed based on load and DB capacity
            connection-timeout: 60000
        url: jdbc:sqlserver://${AZURE_AQE_DB_HOST}:${AZURE_AQE_DB_PORT};user=${AZURE_AQE_DB_UN};password=${AZURE_AQE_DB_PW};database=${AZURE_AQE_DB_NAME};encrypt=true;trustServerCertificate=false;hostNameInCertificate=*.database.windows.net;loginTimeout=30;sendStringParametersAsUnicode=false;
    jpa:
        database-platform: org.hibernate.dialect.SQLServer2012Dialect
        hibernate:
            ddl-auto: none
            naming:
                physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
            naming-strategy: org.hibernate.cfg.DefaultNamingStrategy
        properties:
            hibernate:
                jdbc:
                    time_zone: UTC
        show-sql: false
    rabbitmq:
        listener:
            simple:
                retry:
                    enabled: true
                    max-attempts: 3
    servlet:
        multipart:
            max-file-size: 100MB
            max-request-size: 100MB
            enabled: true
    sleuth:
        sampler:
            probability: 1.0
        traceId128: true
        enabled: true
    zipkin:
        sender:
            type: rabbit
transformation:
    import:
        packageId: 615f582a774964c78ef94d20
    validation:
        packageId: 615f583e774964c78ef94d21
workflow:
    url: https://workflow-dev.pdc.np.paas.lmig.com

logging:
    level:
        org.cloudfoundry.security: WARN
    # stops splunk from converting newlines in stacktraces into individual splunk events
    exception-conversion-word: "%replace(%wEx){'\n','\u2028'}%nopex"

ADDRESS_CLEANSE_API_OAUTH_SCOPES: 0e27f52c-2dc9-4d00-83d2-a2a301073376/.default
booktransfer:
    base-url: https://book-transfer-service-test.pdc.np.paas.lmig.com
eighthcar:
    enabled: false
email:
    url: https://emailservice-test.pdc.np.paas.lmig.com/sendemail
management:
    endpoint:
        health:
            show-details: always
qni:
    url: https://safesite.qa.safeco.com/Client/TACRC/Client/OpenActivity.aspx?l=o
service:
    payment-service-url-provider:
        base-url: https://bt-payment-service-test.us-east-1.np.paas.lmig.com
    transformation-url-provider:
        base-url: https://transformation-service-test.us-east-1.np.paas.lmig.com
    upload-service-url-provider:
        base-url: https://uploadservices-test.pdc.np.paas.lmig.com
    eft-paymentaccounts-url-provider:
        base-url: https://api-tst.us.lmig.com/fin/paymentaccounts/paymentaccounts
spipolicydata:
    url: https://spipolicydataservice-test.pdc.np.paas.lmig.com

oppservice:
    oauth2:
        eft:
            paymentaccounts:
                client:
                    access-token-uri: https://api-tst.us.lmig.com/oauth/token

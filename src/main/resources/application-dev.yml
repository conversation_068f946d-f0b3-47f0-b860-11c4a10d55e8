ADDRESS_CLEANSE_API_OAUTH_SCOPES: 0e27f52c-2dc9-4d00-83d2-a2a301073376/.default
booktransfer:
    url: https://book-transfer-service-development.pdc.np.paas.lmig.com
eighthcar:
    enabled: true
email:
    url: https://emailservice-dev.pdc.np.paas.lmig.com/sendemail
spipolicydata:
    url: https://spipolicydataservice-development.pdc.np.paas.lmig.com
management:
    endpoint:
        health:
            show-details: always
qni:
    url: http://ecdev-sam-dev.apps.safeco.com/Client/DevML/Client/OpenActivity.aspx?l=o
service:
    payment-service-url-provider:
        base-url: https://bt-payment-service-development.us-east-1.np.paas.lmig.com
    transformation-url-provider:
        base-url: https://transformation-service-development.us-east-1.np.paas.lmig.com
    upload-service-url-provider:
        base-url: https://uploadservices-dev.pdc.np.paas.lmig.com
    quote-report-url-provider:
        base-url: https://quotereportservice-dev.pdc.np.paas.lmig.com
    eft-paymentaccounts-url-provider:
        base-url: https://api-dev.us.lmig.com/fin/paymentaccounts/paymentaccounts
    customer-account-url-provider:
        base-url: https://tw48pmbo2c-vpce-0d261813c39cb7123.execute-api.us-east-1.amazonaws.com/development/api
    #book-transfer-service url
    book-transfer-url-provider:
        base-url: https://book-transfer-service-development.pdc.np.paas.lmig.com
oppservice:
    oauth2:
        eft:
            paymentaccounts:
                client:
                    access-token-uri: https://api-dev.us.lmig.com/oauth/token

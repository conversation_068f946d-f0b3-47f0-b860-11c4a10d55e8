<ACORD>
    <com.BookTransfer_errors>
        <Common>
            <PriorCarrier>Missing</PriorCarrier>
            <EffectiveDate>Missing</EffectiveDate>
        </Common>
    </com.BookTransfer_errors>
    <BookTransferMeta>
        <creation>
            <type>eLabelTranslation</type>
            <origin>AQE</origin>
        </creation>
    </BookTransferMeta>
    <InsuranceSvcRq>
        <PolicyRq>
            <Policy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber>X56288</PolicyNumber>
                    <LOBCd>AUTOB</LOBCd>
                    <NAICCd>14184</NAICCd>
                    <NameInfo>
                        <CommercialName>
                            <CommlName>ACUITY</CommlName>
                        </CommercialName>
                    </NameInfo>
                    <ContractTerm>
                        <ExpirationDt>2023-12-01</ExpirationDt>
                    </ContractTerm>
                    <OriginalInceptionDt>2022-12-01</OriginalInceptionDt>
                    <LengthTimeWithPreviousInsurer>
                        <NumUnits>11</NumUnits>
                        <UnitMeasurementCd>ANN</UnitMeasurementCd>
                    </LengthTimeWithPreviousInsurer>
                </OtherOrPriorPolicy>
                <QuoteInfo>
                    <CompanysQuoteNumber>12345678</CompanysQuoteNumber>
                </QuoteInfo>
            </Policy>
        </PolicyRq>
        <WorkCompPolicyQuoteInqRq>
            <TransactionRequestDt>2020-07-28</TransactionRequestDt>
            <Producer>
                <ProducerInfo>
                    <ProducerRoleCd>Agency</ProducerRoleCd>
                </ProducerInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Cook Maran &amp; Associates</CommercialName>
                        </CommlName>
                        <SupplementaryNameInfo>
                            <SupplementaryNameCd>PrimaryContact</SupplementaryNameCd>
                            <SupplementaryName>Melville Office Account</SupplementaryName>
                        </SupplementaryNameInfo>
                    </NameInfo>
                    <Addr>
                        <Addr1>40 Marcus Drive 3rd Floor</Addr1>
                        <City>Melville</City>
                        <StateProvCd>NY</StateProvCd>
                        <PostalCode>11747</PostalCode>
                    </Addr>
                </GeneralPartyInfo>
                <ProducerSubCode>MOFFI</ProducerSubCode>
            </Producer>
            <CommlPolicy>
                <ControllingStateProvCd>NY</ControllingStateProvCd>
                <MiscParty>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>CarrierInsurer</MiscPartyRoleCd>
                    </MiscPartyInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>New York State Insurance Fund</CommercialName>
                            </CommlName>
                        </NameInfo>
                    </GeneralPartyInfo>
                </MiscParty>
                <PolicyNumber>19958794</PolicyNumber>
                <PolicyStatusCd>NotQuotedNotBound</PolicyStatusCd>
                <LOBCd>Worker's Compensation</LOBCd>
                <ContractTerm>
                    <EffectiveDt>2019-12-29</EffectiveDt>
                    <ExpirationDt>2020-12-29</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                    </DurationPeriod>
                </ContractTerm>
                <BillingMethodCd>CAB or CPB</BillingMethodCd>
                <MiscParty>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>Inspection</MiscPartyRoleCd>
                    </MiscPartyInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Frank Eipper</CommercialName>
                            </CommlName>
                        </NameInfo>
                        <Communications>
                            <PhoneInfo>
                                <PhoneNumber>(*************</PhoneNumber>
                            </PhoneInfo>
                            <EmailInfo>
                                <EmailAddr><EMAIL></EmailAddr>
                            </EmailInfo>
                        </Communications>
                    </GeneralPartyInfo>
                </MiscParty>
                <MiscParty>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>Accounting</MiscPartyRoleCd>
                    </MiscPartyInfo>
                </MiscParty>
                <CommlPolicySupplement>
                    <OperationsDesc>MANUFACTURER/PLASTIC</OperationsDesc>
                    <LengthTimeInBusiness>
                        <NumUnits>38</NumUnits>
                    </LengthTimeInBusiness>
                </CommlPolicySupplement>
                <Loss>
                    <NoticeInformationPracticesInd>0</NoticeInformationPracticesInd>
                </Loss>
                <MiscParty>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>C</MiscPartyRoleCd>
                    </MiscPartyInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Amanda Felsten</CommercialName>
                            </CommlName>
                        </NameInfo>
                    </GeneralPartyInfo>
                </MiscParty>
                <CurrentTermAmt>
                    <Amt>1000.45</Amt>
                </CurrentTermAmt>
            </CommlPolicy>
            <InsuredOrPrincipal>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                </InsuredOrPrincipalInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <Surname>East</Surname>
                            <GivenName>East</GivenName>
                        </PersonName>
                        <CommlName>
                            <CommercialName>EastauisdhuiashduiahsiudhuiashdiuahsiudauishduiashdiuahsiudhiaushdiuashiduaiusdhiuasdiuahsuidaiushduiasdEastauisdhuiashduiahsiudhuiashdiuahsiudauishduiashdiuahsiudhiaushdiuashiduaiusdhiuasdiuahsuidaiushduiasdEastauisdhuiashduiahsiudhuiashdiuahsiudauishduiashdiuahsiudhiaushdiuashiduaiusdhiuasdiuahsuidaiushduiasdEastauisdhuiashduiahsiudhuiashdiuahsiudauishduiashdiuahsiudhiaushdiuashiduaiusdhiuasdiuahsuidaiushduiasdEastauisdhuiashduiahsiudhuiashdiuahsiudauishduiashdiuahsiudhiaushdiuashiduaiusdhiuasdiuahsuidaiushduiasdEastauisdhuiashduiahsiudhuiashdiuahsiudauishduiashdiuahsiudhiaushdiuashiduaiusdhiuasdiuahsuidaiushduiasdEastauisdhuiashduiahsiudhuiashdiuahsiudauishduiashdiuahsiudhiaushdiuashiduaiusdhiuasdiuahsuidaiushduiasdEastauisdhuiashduiahsiudhuiashdiuahsiudauishduiashdiuahsiudhiaushdiuashiduaiusdhiuasdiuahsuidaiushduiasdEastauisdhuiashduiahsiudhuiashdiuahsiudauishduiashdiuahsiudhiaushdiuashiduaiusdhiuasdiuahsuidaiushduiasdEastauisdhuiashduiahsiudhuiashdiuahsiudauishduiashdiuahsiudhiaushdiuashiduaiusdhiuasdiuahsuidaiushduiasd</CommercialName>
                        </CommlName>
                        <TaxIdentity>
                            <TaxId>*********</TaxId>
                        </TaxIdentity>
                    </NameInfo>
                    <Addr>
                        <Addr1>10 County Road 27</Addr1>
                        <City>Hampton</City>
                        <StateProvCd>NY</StateProvCd>
                        <PostalCode>11937</PostalCode>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Business</CommunicationUseCd>
                            <PhoneNumber>(*************</PhoneNumber>
                        </PhoneInfo>
                        <WebsiteInfo>
                            <WebsiteURL>www.ehmealsonwheels.org</WebsiteURL>
                        </WebsiteInfo>
                    </Communications>
                </GeneralPartyInfo>
                <BusinessInfo>
                    <SICCd>581200</SICCd>
                    <BusinessStartDt>1983-01-01</BusinessStartDt>
                    <OperationsDesc>Delivery hot meals to homebound individuals</OperationsDesc>
                </BusinessInfo>
                <ItemIdInfo>
                    <AgencyId>EASTHAM46</AgencyId>
                </ItemIdInfo>
            </InsuredOrPrincipal>
            <Location id="LOC1">
                <ItemIdInfo>
                    <AgencyId>1</AgencyId>
                </ItemIdInfo>
                <SubLocation>
                    <ItemIdInfo>
                        <AgencyId>1</AgencyId>
                    </ItemIdInfo>
                    <Addr>
                        <Addr1>33 Newtown Lane, Suite #205</Addr1>
                        <City>East Hampton</City>
                        <County>Suffolk</County>
                        <StateProvCd>NY</StateProvCd>
                        <PostalCode>11937</PostalCode>
                    </Addr>
                </SubLocation>
                <RiskLocationCd>IN</RiskLocationCd>
                <Addr>
                    <Addr2/>
                    <Addr1>33 Newtown Lane, Suite #205 Building #1 East Hampton</Addr1>
                    <City>Suffolk</City>
                    <StateProvCd>NY</StateProvCd>
                    <PostalCode>11937</PostalCode>
                </Addr>
            </Location>
            <CommlSubLocation idRef="LOC1">
                <BldgOccupancy>
                    <OperationsDesc>office</OperationsDesc>
                </BldgOccupancy>
            </CommlSubLocation>
            <Location id="LOC2">
                <ItemIdInfo>
                    <AgencyId>2</AgencyId>
                </ItemIdInfo>
                <SubLocation>
                    <Addr>
                        <Addr1>15 Lumber Ln.</Addr1>
                        <City>East Hampton</City>
                        <County>Suffolk</County>
                        <StateProvCd>NY</StateProvCd>
                        <PostalCode>11937</PostalCode>
                    </Addr>
                </SubLocation>
                <Addr>
                    <Addr2/>
                    <Addr1>15 Lumber Ln. East Hampton</Addr1>
                    <City>Suffolk</City>
                    <StateProvCd>NY</StateProvCd>
                    <PostalCode>11937</PostalCode>
                </Addr>
            </Location>
            <Producer>
                <ProducerInfo>
                    <ProducerRoleCd>Producer</ProducerRoleCd>
                </ProducerInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Leonard Scioscia</CommercialName>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
            </Producer>
            <WorkCompLineBusiness>
                <WorkCompRateState>
                    <StateProvCd>NA NY</StateProvCd>
                </WorkCompRateState>
                <CommlCoverage>
                    <CoverageCd>WCEL</CoverageCd>
                    <Limit>
                        <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        <FormatInteger>100000</FormatInteger>
                    </Limit>
                    <Limit>
                        <LimitAppliesToCd>DisPol</LimitAppliesToCd>
                        <FormatInteger>500000</FormatInteger>
                    </Limit>
                    <Limit>
                        <LimitAppliesToCd>DisEachEmpl</LimitAppliesToCd>
                        <FormatInteger>100000</FormatInteger>
                    </Limit>
                </CommlCoverage>
                <WorkCompIndividuals idref="individuals-loc1">
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Frank Falcone</CommercialName>
                        </CommlName>
                        <TitleRelationshipDesc>Treasurer</TitleRelationshipDesc>
                    </NameInfo>
                    <IncludedExcludedCd>E</IncludedExcludedCd>
                    <InclIndividualsEstAnnualRemunerationAmt>
                        <Amt>0</Amt>
                    </InclIndividualsEstAnnualRemunerationAmt>
                </WorkCompIndividuals>
                <WorkCompIndividuals idref="individuals-loc2">
                    <NameInfo>
                        <CommlName>
                            <CommercialName>David Tosher</CommercialName>
                        </CommlName>
                        <TitleRelationshipDesc>Vice President #2</TitleRelationshipDesc>
                    </NameInfo>
                    <IncludedExcludedCd>E</IncludedExcludedCd>
                    <InclIndividualsEstAnnualRemunerationAmt>
                        <Amt>0</Amt>
                    </InclIndividualsEstAnnualRemunerationAmt>
                </WorkCompIndividuals>
                <WorkCompIndividuals idref="individuals-loc3">
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Diane Antell</CommercialName>
                        </CommlName>
                        <TitleRelationshipDesc>Vice President #1</TitleRelationshipDesc>
                    </NameInfo>
                    <IncludedExcludedCd>E</IncludedExcludedCd>
                    <InclIndividualsEstAnnualRemunerationAmt>
                        <Amt>0</Amt>
                    </InclIndividualsEstAnnualRemunerationAmt>
                </WorkCompIndividuals>
                <WorkCompIndividuals idref="individuals-loc4">
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Barbara Scheerer</CommercialName>
                        </CommlName>
                        <TitleRelationshipDesc>Secretary</TitleRelationshipDesc>
                    </NameInfo>
                    <IncludedExcludedCd>E</IncludedExcludedCd>
                    <InclIndividualsEstAnnualRemunerationAmt>
                        <Amt>0</Amt>
                    </InclIndividualsEstAnnualRemunerationAmt>
                </WorkCompIndividuals>
                <IncludeExcludeState>
                    <StateProvCd>NA</StateProvCd>
                </IncludeExcludeState>
                <QuestionAnswer>
                    <Explanation>2)</Explanation>
                </QuestionAnswer>
            </WorkCompLineBusiness>
            <WorkCompRateState>
                <StateProvCd>NA</StateProvCd>
            </WorkCompRateState>
            <Producer>
                <ProducerInfo>
                    <ProducerRoleCd>Producer</ProducerRoleCd>
                </ProducerInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Melville Office Account</CommercialName>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
            </Producer>
            <RemarkText IdRef="PageNumber">1</RemarkText>
        </WorkCompPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
<ACORD>
    <SignonRq>
        <ClientDt>2016-05-07</ClientDt>
        <ClientApp>
            <Org>Applied Systems</Org>
            <Name>EPIC</Name>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID/>
        <HomePolicyQuoteInqRq>
            <RqUID/>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>36TRB</ContractNumber>
                    <ProducerSubCode>Book Transfer</ProducerSubCode>
                </ProducerInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName/>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId></AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>***********</TaxId>
                        </TaxIdentity>
                        <PersonName>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>1875 NW HARTFORD AVE</Addr1>
                        <Addr2/>
                        <Addr3/>
                        <City>BEND</City>
                        <StateProvCd>OR</StateProvCd>
                        <PostalCode>*********</PostalCode>
                        <CountryCd>USA</CountryCd>
                        <County/>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-1111121</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Business</CommunicationUseCd>
                            <PhoneNumber>******-1111121</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <PhoneNumber>******-1111121</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                        <EmailInfo>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt>1966-01-01</BirthDt>
                        <MaritalStatusCd>M</MaritalStatusCd>
                        <OccupationDesc/>
                        <LengthTimeEmployed>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <LengthTimeCurrentOccupation>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeCurrentOccupation>
                        <LengthTimeWithPreviousEmployer>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeWithPreviousEmployer>
                        <com.safeco_OccupationCd>Analyst</com.safeco_OccupationCd>
                        <com.safeco_IndustryCd>Insurance</com.safeco_IndustryCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt>1959-01-01</BirthDt>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <PolicyNumber></PolicyNumber>
                <CompanyProductCd/>
                <LOBCd>HOME</LOBCd>
                <NAICCd/>
                <ControllingStateProvCd/>
                <BillingAccountNumber/>
                <BillingMethodCd>CPB</BillingMethodCd>
                <CurrentTermAmt>
                </CurrentTermAmt>
                <PayorCd/>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber></PolicyNumber>
                    <LOBCd/>
                    <InsurerName>GuideOne</InsurerName>
                    <ContractTerm/>
                    <Coverage>
                        <Limit>
                            <FormatInteger/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Other</PolicyCd>
                    <PolicyNumber>M1111111</PolicyNumber>
                    <LOBCd>AUTO</LOBCd>
                    <InsurerName>Safeco</InsurerName>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <PaymentPlanCd/>
                    <MethodPaymentCd/>
                    <DepositAmt>
                        <Amt/>
                    </DepositAmt>
                </PaymentOption>
                <QuestionAnswer>
                    <QuestionCd>GENRL22</QuestionCd>
                    <YesNoCd/>
                    <Explanation/>
                </QuestionAnswer>
                <AssignedRiskFacilityCd/>
                <PersApplicationInfo>
                    <LengthTimePreviousAddr>
                        <DurationPeriod>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </DurationPeriod>
                    </LengthTimePreviousAddr>
                    <LengthTimeCurrentAddr>
                        <DurationPeriod>
                            <NumUnits>4</NumUnits>
                            <UnitMeasurementCd>month</UnitMeasurementCd>
                        </DurationPeriod>
                        <DurationPeriod>
                            <NumUnits>2</NumUnits>
                            <UnitMeasurementCd>year</UnitMeasurementCd>
                        </DurationPeriod>
                    </LengthTimeCurrentAddr>
                </PersApplicationInfo>
            </PersPolicy>
            <Location id="">
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <TaxCodeInfo>
                    <TaxCd/>
                </TaxCodeInfo>
                <FireDistrict>BEND</FireDistrict>
                <FireDistrictCd/>
                <com.safeco_FireDeptCity/>
                <com.safeco_FireDeptCounty/>
                <AdditionalInterest>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>WELLS FARGO HOME MORTGAGE #936</CommercialName>
                            </CommlName>
                        </NameInfo>
                        <Addr>
                            <AddrTypeCd>MailingAddress</AddrTypeCd>
                            <Addr1>ITS SUCCESSORS AND/OR ASSIGNS</Addr1>
                            <Addr2>PO BOX 100515</Addr2>
                            <Addr3/>
                            <City>FLORENCE</City>
                            <StateProvCd>SC</StateProvCd>
                            <PostalCode>29502</PostalCode>
                            <CountryCd>USA</CountryCd>
                            <County/>
                        </Addr>
                        <Communications>
                            <PhoneInfo>
                                <CommunicationUseCd>Business</CommunicationUseCd>
                                <PhoneNumber/>
                            </PhoneInfo>
                        </Communications>
                    </GeneralPartyInfo>
                    <AdditionalInterestInfo>
                        <NatureInterestCd>MORTG</NatureInterestCd>
                        <InterestRank>1</InterestRank>
                        <AccountNumberId>**********</AccountNumberId>
                    </AdditionalInterestInfo>
                </AdditionalInterest>
            </Location>
            <HomeLineBusiness>
                <LOBCd>HOME</LOBCd>
                <Dwell LocationRef="">
                    <PolicyTypeCd>03</PolicyTypeCd>
                    <PurchaseDt>2014-09-01</PurchaseDt>
                    <com.safeco_PackageSelection>D</com.safeco_PackageSelection>
                    <PurchasePriceAmt>
                        <Amt/>
                    </PurchasePriceAmt>
                    <ThermostaticallyControlledCentralHeatInd>1</ThermostaticallyControlledCentralHeatInd>
                    <com.safeco_BusinessOnPremisesCategory/>
                    <NumEmployeesFullTimeResidence/>
                    <NumEmployeesPartTimeResidence/>
                    <OilStorageTankLocationCd/>
                    <FuelLineLocCd/>
                    <Construction>
                        <EIFSInstalledYr/>
                        <InsurerConstructionClassCd/>
                        <FoundationCd>Crawl</FoundationCd>
                        <YearBuilt>2014</YearBuilt>
                        <NumUnits/>
                        <BldgCodeEffectivenessGradeCd/>
                        <BldgArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </BldgArea>
                        <NumStories/>
                        <BasementArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </BasementArea>
                        <RoofingMaterial>
                            <RoofMaterialCd>COMP</RoofMaterialCd>
                        </RoofingMaterial>
                        <StormShuttersCd/>
                        <ConstructionReasonCd/>
                        <AdditionArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </AdditionArea>
                        <ConstructionCd>F</ConstructionCd>
                        <com.safeco_SyntheticStuccoVerificationType/>
                    </Construction>
                    <DwellOccupancy>
                        <NumRooms/>
                        <NumApartments/>
                        <ResidenceTypeCd>DW</ResidenceTypeCd>
                        <OccupancyTypeCd>N</OccupancyTypeCd>
                        <DwellUseCd>1</DwellUseCd>
                        <LengthTimeRentedToOthers>
                            <NumUnits/>
                            <UnitMeasurementCd>WEE</UnitMeasurementCd>
                        </LengthTimeRentedToOthers>
                        <NumResidentsInHousehold/>
                    </DwellOccupancy>
                    <DwellRating>
                        <TerritoryCd/>
                        <PremiumGroup/>
                        <ECPremiumGroup/>
                        <FireECRate/>
                    </DwellRating>
                    <BldgProtection>
                        <NumFireDivisions/>
                        <NumUnitsInFireDivisions/>
                        <FireProtectionClassCd>03</FireProtectionClassCd>
                        <DistanceToFireStation>
                            <NumUnits>01</NumUnits>
                            <UnitMeasurementCd>mile</UnitMeasurementCd>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits>0500</NumUnits>
                            <UnitMeasurementCd>foot</UnitMeasurementCd>
                        </DistanceToHydrant>
                        <com.safeco_WindProtectionMeasures>
                            <YesNoCd/>
                        </com.safeco_WindProtectionMeasures>
                        <ProtectionDeviceSprinklerCd>N</ProtectionDeviceSprinklerCd>
                        <ProtectionDeviceBurglarCd/>
                        <ProtectionDeviceSmokeCd>SMOKE</ProtectionDeviceSmokeCd>
                        <com.safeco_AlarmAndSecurity>
                            <BurglarAlarmAgent/>
                            <BurglarAlarmDate/>
                            <ItemDefinition>
                                <Manufacturer/>
                            </ItemDefinition>
                        </com.safeco_AlarmAndSecurity>
                        <DoorLockCd>DEADB</DoorLockCd>
                        <FireExtinguisherInd>1</FireExtinguisherInd>
                    </BldgProtection>
                    <BldgImprovements>
                        <ExteriorPaintYear/>
                        <HeatingImprovementCd/>
                        <HeatingImprovementYear/>
                        <PlumbingImprovementCd>C</PlumbingImprovementCd>
                        <PlumbingImprovementYear>2014</PlumbingImprovementYear>
                        <RoofingImprovementCd>C</RoofingImprovementCd>
                        <RoofingImprovementYear>2014</RoofingImprovementYear>
                        <WiringImprovementCd>C</WiringImprovementCd>
                        <WiringImprovementYear>2014</WiringImprovementYear>
                    </BldgImprovements>
                    <DwellInspectionValuation>
                        <DwellStyleCd>Rambler</DwellStyleCd>
                        <EstimatedReplCostAmt>
                            <Amt/>
                        </EstimatedReplCostAmt>
                        <EvaluationMethodCd/>
                        <TotalArea>
                            <UnitMeasurementCd>square foot</UnitMeasurementCd>
                            <NumUnits>01674</NumUnits>
                        </TotalArea>
                        <GroundFloorArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </GroundFloorArea>
                        <HeatSourcePrimaryCd/>
                        <NumFamilies>1</NumFamilies>
                        <NumStoriesInDwellingCd>1</NumStoriesInDwellingCd>
                        <BathroomInfo>
                            <BathroomTypeCd>Full</BathroomTypeCd>
                            <NumBathrooms>2</NumBathrooms>
                        </BathroomInfo>
                        <BathroomInfo>
                            <BathroomTypeCd>Half</BathroomTypeCd>
                            <NumBathrooms>1</NumBathrooms>
                        </BathroomInfo>
                        <BathroomInfo>
                            <BathroomTypeCd>ThreeQuarter</BathroomTypeCd>
                            <NumBathrooms/>
                        </BathroomInfo>
                        <ExteriorWallMaterialInfo>
                            <ExteriorWallMaterialCd>Clapboard</ExteriorWallMaterialCd>
                        </ExteriorWallMaterialInfo>
                        <ExteriorWallMaterialInfo>
                            <ExteriorWallMaterialCd/>
                        </ExteriorWallMaterialInfo>
                        <FireplaceInfo>
                            <NumHearths/>
                            <NumChimneys/>
                        </FireplaceInfo>
                        <FireplaceInfo>
                            <FireplaceTypeCd/>
                            <NumFireplaces/>
                        </FireplaceInfo>
                        <GarageInfo>
                            <NumVehs/>
                            <GarageTypeCd/>
                            <SurfaceArea>
                                <NumUnits/>
                                <UnitMeasurementCd>FTK</UnitMeasurementCd>
                            </SurfaceArea>
                        </GarageInfo>
                        <PorchInfo>
                            <SurfaceArea>
                                <NumUnits/>
                                <UnitMeasurementCd>FTK</UnitMeasurementCd>
                            </SurfaceArea>
                            <BreezewayTypeCd/>
                        </PorchInfo>
                        <PorchInfo>
                            <SurfaceArea>
                                <NumUnits/>
                                <UnitMeasurementCd>FTK</UnitMeasurementCd>
                            </SurfaceArea>
                            <PorchTypeCd/>
                        </PorchInfo>
                        <MarketValueAmt/>
                        <FinishedAtticArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </FinishedAtticArea>
                        <TotalLivingArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </TotalLivingArea>
                    </DwellInspectionValuation>
                    <SwimmingPool>
                        <ApprovedFenceInd/>
                        <AboveGroundInd/>
                    </SwimmingPool>
                    <Coverage>
                        <CoverageCd>DWELL</CoverageCd>
                        <Limit>
                        </Limit>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct/>
                            <DeductibleTypeCd/>
                            <DeductibleAppliesToCd>WindHail</DeductibleAppliesToCd>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>EQPBK</CoverageCd>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PIHOM</CoverageCd>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Y</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LOU</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>AALMT</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OS</CoverageCd>
                        <Limit>
                            <FormatInteger>0035500</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PL</CoverageCd>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FDC</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>IHBUS</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LAC</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PP</CoverageCd>
                        <Limit>
                            <FormatInteger>266250</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_EDC</CoverageCd>
                        <Limit>
                            <FormatPct>50</FormatPct>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BOLAW</CoverageCd>
                        <Limit>
                            <FormatPct>50</FormatPct>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FVREP</CoverageCd>
                        <Option>
                            <OptionCd>Yes</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FRAUD</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ROOF</CoverageCd>
                        <Option>
                            <OptionCd/>
                            <OptionValue/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SEWER</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WATER</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ERQK</CoverageCd>
                        <Deductible>
                            <FormatPct/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNJWF</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CCSV</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>AASPC</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SPPP</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SPKCR</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SECUR</CoverageCd>
                        <Option>
                            <OptionCd>N</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BARCR</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WINDX</CoverageCd>
                        <Option>
                            <OptionCd>N</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FREEZ</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SINK</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>IFPL</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MIN</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>HDACA</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LLF</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>THFTB</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SUMP</CoverageCd>
                        <CoverageDesc>Water Back-up and Sump Overflow</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <Form>
                            <FormNumber/>
                        </Form>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BOLEX</CoverageCd>
                        <CoverageDesc>HIG Building Ordinance and Law Extension of Coverage (H447)</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <Form>
                            <FormNumber/>
                        </Form>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ACCT</CoverageCd>
                        <CoverageDesc>Multi Policy Credit</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <Form>
                            <FormNumber>CR ACCOUNT</FormNumber>
                        </Form>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MLDLI</CoverageCd>
                        <CoverageDesc>Mold Liability</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <Form>
                            <FormNumber/>
                        </Form>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>APN</CoverageCd>
                        <CoverageDesc>Advantage Plus</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <Form>
                            <FormNumber/>
                        </Form>
                    </Coverage>
                    <QuestionAnswer>
                        <QuestionCd>GENRL15</QuestionCd>
                        <YesNoCd>N</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL20</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL28</QuestionCd>
                        <YesNoCd/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL32</QuestionCd>
                        <YesNoCd/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL16</QuestionCd>
                        <YesNoCd>N</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME08</QuestionCd>
                        <YesNoCd/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME06</QuestionCd>
                        <YesNoCd/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME07</QuestionCd>
                        <YesNoCd/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME02</QuestionCd>
                        <YesNoCd>N</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>com.safeco_BusinessIncidental</QuestionCd>
                        <YesNoCd>Y</YesNoCd>
                    </QuestionAnswer>
                    <DwellStateSupplement>
                        <DistanceToOceanOrOtherBodyWater>
                            <NumUnits/>
                        </DistanceToOceanOrOtherBodyWater>
                        <SeacoastOrOtherBodyWaterProximityCd>TidalWater</SeacoastOrOtherBodyWaterProximityCd>
                    </DwellStateSupplement>
                    <InspectionInfo>
                        <InspectionCd>com.safeco_AgentOut</InspectionCd>
                        <InspectionDt>2014-09-04</InspectionDt>
                    </InspectionInfo>
                    <OtherHeatSourcePrimaryDesc/>
                    <NumEmployeesFullTimeResidence/>
                    <AnimalExposureInfo>
                        <BiteHistoryInd>0</BiteHistoryInd>
                        <DogTypeCd>NO</DogTypeCd>
                        <AnimalTypeCd>N</AnimalTypeCd>
                    </AnimalExposureInfo>
                    <HeatingUnitInfo>
                        <HeatingUnitCd>C</HeatingUnitCd>
                        <FuelTypeCd/>
                    </HeatingUnitInfo>
                    <AreaTypeSurroundingsCd>IN</AreaTypeSurroundingsCd>
                    <HousekeepingConditionCd>GD</HousekeepingConditionCd>
                </Dwell>
                <ResidenceBusiness LocationRef="">
                    <ClassCd/>
                    <LegalEntityCd/>
                    <GeneralLiabilityCd/>
                    <NAICSCd/>
                    <NatureBusinessCd/>
                    <BusinessArea>
                        <NumUnits/>
                        <UnitMeasurementCd>FTK</UnitMeasurementCd>
                    </BusinessArea>
                    <AnnualSalesAmt>
                        <Amt/>
                    </AnnualSalesAmt>
                    <TotalPayrollAmt>
                        <Amt/>
                    </TotalPayrollAmt>
                    <NumVisitors/>
                    <NumEmployeesPartTime/>
                    <NumEmployeesFullTime/>
                    <BusinessHoursStartTime/>
                    <BusinessHoursCloseTime/>
                    <NumLosses/>
                    <BusinessDesc/>
                    <TaxIdentity>
                        <TaxIdTypeCd>FEIN</TaxIdTypeCd>
                        <TaxId/>
                    </TaxIdentity>
                </ResidenceBusiness>
                <Watercraft>
                    <Horsepower>
                        <NumUnits/>
                    </Horsepower>
                    <Length>
                        <UnitMeasurementCd>foot</UnitMeasurementCd>
                        <NumUnits/>
                    </Length>
                    <com.safeco_OperatorAgesDesc/>
                    <NumUnits/>
                </Watercraft>
                <QuestionAnswer>
                    <QuestionCd>GENRL06</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
            </HomeLineBusiness>
            <RemarkText/>
        </HomePolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
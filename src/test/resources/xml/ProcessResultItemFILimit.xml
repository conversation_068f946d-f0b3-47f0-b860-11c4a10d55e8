<?xml-stylesheet type="text/xsl" href='http://ecdev-tac.safeco.com/BookTransfer/TACML/WebAPI/scripts/xslt/BoatXMLView.xslt'?>
<ACORD>
    <SignonRs>
        <ClientDt>2016-06-03</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>BookSmart</Org>
            <Name>BookTransfer</Name>
        </ClientApp>
        <SignonTransport>
            <SignonRoleCd>Agent</SignonRoleCd>
            <CustId>
                <SPName>com.agencyport</SPName>
                <CustLoginId>default</CustLoginId>
            </CustId>
        </SignonTransport>
    </SignonRs>
    <Status>
        <StatusCd>0</StatusCd>
        <StatusDesc/>
    </Status>
    <InsuranceSvcRs>
        <RqUID>f7b053d2-c2b3-4c5b-8d55-2c6b92a57777</RqUID>
        <PersAutoPolicyQuoteInqRs>
            <com.safeco_TransactionType>QuoteRq</com.safeco_TransactionType>
            <RqUID>41b513bc-bb09-42dc-9455-528c14d17ad2</RqUID>
            <TransactionRequestDt>2016-06-03</TransactionRequestDt>
            <TransactionEffectiveDt>2015-07-10</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ProducerSubCode>WW</ProducerSubCode>
                    <ContractNumber>897404</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>FJPNCB3</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Fierro Guadalupe</CommercialName>
                        </CommlName>
                        <PersonName>
                            <Surname>FIERRO</Surname>
                            <GivenName>GUADALUPE</GivenName>
                        </PersonName>
                    </NameInfo>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-3391325</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <GenderCd>F</GenderCd>
                        <BirthDt>1975-11-11</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <LOBCd>AUTOP</LOBCd>
                <com.safeco_Esign>N</com.safeco_Esign>
                <com.safeco_PropertyCrossSell>
                    <com.safeco_PropertyAddressScrubYN>Y</com.safeco_PropertyAddressScrubYN>
                </com.safeco_PropertyCrossSell>
                <ContractTerm>
                    <EffectiveDt>2015-05-10</EffectiveDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                        <UnitMeasurementCd>Months</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>OTHER</InsurerName>
                    <LOBCd>Auto</LOBCd>
                    <ContractTerm>
                        <ExpirationDt>05/10/2016</ExpirationDt>
                    </ContractTerm>
                    <LengthTimeWithPreviousInsurer>
                        <NumUnits>24</NumUnits>
                        <UnitMeasurementCd>Months</UnitMeasurementCd>
                    </LengthTimeWithPreviousInsurer>
                    <Coverage>
                        <CoverageCd/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <com.safeco_PriorCarrierReportResult>NoHit</com.safeco_PriorCarrierReportResult>
                </OtherOrPriorPolicy>
                <CreditScoreInfo>
                    <CreditScore>640</CreditScore>
                    <CreditScoreDt>2015-07-10</CreditScoreDt>
                </CreditScoreInfo>
                <QuestionAnswer>
                    <QuestionCd>Current_Insurance_Value</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>CI</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Cancelled_Declined?</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Excluded_Vehicle_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>All_Vehicles_Covered_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Credit_Accept_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Accept_Contract_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>002</Explanation>
                </QuestionAnswer>
                <PersApplicationInfo>
                    <InsuredOrPrincipal>
                        <GeneralPartyInfo>
                            <Addr>
                                <AddrTypeCd>StreetAddress</AddrTypeCd>
                                <Addr1>1025 5TH ST</Addr1>
                                <City>DEL NORTE</City>
                                <StateProvCd>CO</StateProvCd>
                                <PostalCode>81132</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                    </InsuredOrPrincipal>
                    <ResidenceOwnedRentedCd>OWNED</ResidenceOwnedRentedCd>
                    <NumResidentsInHousehold>1</NumResidentsInHousehold>
                    <NumVehsInHousehold>1</NumVehsInHousehold>
                    <ResidenceTypeCd>DW</ResidenceTypeCd>
                </PersApplicationInfo>
                <DriverVeh>
                    <UsePct>100</UsePct>
                </DriverVeh>
                <CurrentTermAmt>
                    <Amt>109875.85</Amt>
                </CurrentTermAmt>
                <OtherOrPriorPolicy>
                    <ContractTerm>
                        <ExpirationDt>05/10/2016</ExpirationDt>
                    </ContractTerm>
                    <LOBCd>AUTOP</LOBCd>
                    <PolicyCd>Prior</PolicyCd>
                    <com.safeco_PriorCarrierReportResult>NoHit</com.safeco_PriorCarrierReportResult>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <PaymentPlanCd>Monthly</PaymentPlanCd>
                    <com.safeco_RecurringPaymentMethod>ByMail</com.safeco_RecurringPaymentMethod>
                    <NumPayments>10</NumPayments>
                    <MethodPaymentCd>CreditCard,EFT</MethodPaymentCd>
                    <DepositAmt>
                        <Amt>62.49</Amt>
                        <CurCd>USD</CurCd>
                    </DepositAmt>
                    <InstallmentInfo>
                        <InstallmentAmt>
                            <Amt>68.24</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentAmt>
                        <InstallmentFeeAmt>
                            <Amt>5.00</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentFeeAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <PaymentPlanCd>Budget</PaymentPlanCd>
                    <com.safeco_RecurringPaymentMethod>AutoDeduct</com.safeco_RecurringPaymentMethod>
                    <NumPayments>3</NumPayments>
                    <MethodPaymentCd>CreditCard,EFT</MethodPaymentCd>
                    <DepositAmt>
                        <Amt>174.48</Amt>
                        <CurCd>USD</CurCd>
                    </DepositAmt>
                    <InstallmentInfo>
                        <InstallmentAmt>
                            <Amt>174.47</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentAmt>
                        <InstallmentFeeAmt>
                            <Amt>2.00</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentFeeAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <PaymentPlanCd>Budget</PaymentPlanCd>
                    <com.safeco_RecurringPaymentMethod>CreditCard</com.safeco_RecurringPaymentMethod>
                    <NumPayments>3</NumPayments>
                    <MethodPaymentCd>CreditCard,EFT</MethodPaymentCd>
                    <DepositAmt>
                        <Amt>177.48</Amt>
                        <CurCd>USD</CurCd>
                    </DepositAmt>
                    <InstallmentInfo>
                        <InstallmentAmt>
                            <Amt>177.47</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentAmt>
                        <InstallmentFeeAmt>
                            <Amt>5.00</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentFeeAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <PaymentPlanCd>Budget</PaymentPlanCd>
                    <com.safeco_RecurringPaymentMethod>ByMail</com.safeco_RecurringPaymentMethod>
                    <NumPayments>3</NumPayments>
                    <MethodPaymentCd>CreditCard,EFT</MethodPaymentCd>
                    <DepositAmt>
                        <Amt>177.48</Amt>
                        <CurCd>USD</CurCd>
                    </DepositAmt>
                    <InstallmentInfo>
                        <InstallmentAmt>
                            <Amt>177.47</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentAmt>
                        <InstallmentFeeAmt>
                            <Amt>5.00</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentFeeAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <PaymentPlanCd>Full</PaymentPlanCd>
                    <com.safeco_RecurringPaymentMethod>ByMail,AutoDeduct,CreditCard</com.safeco_RecurringPaymentMethod>
                    <NumPayments/>
                    <MethodPaymentCd>CreditCard,EFT</MethodPaymentCd>
                    <DepositAmt>
                        <Amt>601.10</Amt>
                        <CurCd>USD</CurCd>
                    </DepositAmt>
                    <Description>Discount</Description>
                </PaymentOption>
                <PaymentOption>
                    <PaymentPlanCd>Monthly Checkless</PaymentPlanCd>
                    <com.safeco_RecurringPaymentMethod>AutoDeduct</com.safeco_RecurringPaymentMethod>
                    <NumPayments>11</NumPayments>
                    <MethodPaymentCd>CreditCard,EFT</MethodPaymentCd>
                    <DepositAmt>
                        <Amt>59.49</Amt>
                        <CurCd>USD</CurCd>
                    </DepositAmt>
                    <InstallmentInfo>
                        <InstallmentAmt>
                            <Amt>59.49</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentAmt>
                        <InstallmentFeeAmt>
                            <Amt>2.00</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentFeeAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <PaymentPlanCd>Monthly Checkless</PaymentPlanCd>
                    <com.safeco_RecurringPaymentMethod>CreditCard</com.safeco_RecurringPaymentMethod>
                    <NumPayments>11</NumPayments>
                    <MethodPaymentCd>CreditCard,EFT</MethodPaymentCd>
                    <DepositAmt>
                        <Amt>62.49</Amt>
                        <CurCd>USD</CurCd>
                    </DepositAmt>
                    <InstallmentInfo>
                        <InstallmentAmt>
                            <Amt>62.49</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentAmt>
                        <InstallmentFeeAmt>
                            <Amt>5.00</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentFeeAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <PaymentPlanCd>Semi-Annual</PaymentPlanCd>
                    <com.safeco_RecurringPaymentMethod>AutoDeduct</com.safeco_RecurringPaymentMethod>
                    <NumPayments>1</NumPayments>
                    <MethodPaymentCd>CreditCard,EFT</MethodPaymentCd>
                    <DepositAmt>
                        <Amt>302.55</Amt>
                        <CurCd>USD</CurCd>
                    </DepositAmt>
                    <InstallmentInfo>
                        <InstallmentAmt>
                            <Amt>302.55</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentAmt>
                        <InstallmentFeeAmt>
                            <Amt>2.00</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentFeeAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <PaymentPlanCd>Semi-Annual</PaymentPlanCd>
                    <com.safeco_RecurringPaymentMethod>CreditCard</com.safeco_RecurringPaymentMethod>
                    <NumPayments>1</NumPayments>
                    <MethodPaymentCd>CreditCard,EFT</MethodPaymentCd>
                    <DepositAmt>
                        <Amt>305.55</Amt>
                        <CurCd>USD</CurCd>
                    </DepositAmt>
                    <InstallmentInfo>
                        <InstallmentAmt>
                            <Amt>305.55</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentAmt>
                        <InstallmentFeeAmt>
                            <Amt>5.00</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentFeeAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <PaymentPlanCd>Semi-Annual</PaymentPlanCd>
                    <com.safeco_RecurringPaymentMethod>ByMail</com.safeco_RecurringPaymentMethod>
                    <NumPayments>1</NumPayments>
                    <MethodPaymentCd>CreditCard,EFT</MethodPaymentCd>
                    <DepositAmt>
                        <Amt>305.55</Amt>
                        <CurCd>USD</CurCd>
                    </DepositAmt>
                    <InstallmentInfo>
                        <InstallmentAmt>
                            <Amt>305.55</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentAmt>
                        <InstallmentFeeAmt>
                            <Amt>5.00</Amt>
                            <CurCd>USD</CurCd>
                        </InstallmentFeeAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <QuoteInfo>
                    <CompanysQuoteNumber>6cda3f50-0bea-46ab-a5b6-4695bea02232</CompanysQuoteNumber>
                    <com.safeco_CompanyClientID>5368c455-dd1f-4467-be05-5287b407b05f</com.safeco_CompanyClientID>
                    <com.safeco_CompanyURL id="A001">
                        https://safesite.qa.safeco.com/Client/TACRC/Client/OpenActivity.aspx?l=o&amp;p=6cda3f50-0bea-46ab-a5b6-4695bea02232
                    </com.safeco_CompanyURL>
                    <com.safeco_CompanyURLText id="A002">This is a link to the quote at the Safeco web site.
                    </com.safeco_CompanyURLText>
                    <com.safeco_CompanyURLAutoLaunch id="A003">1</com.safeco_CompanyURLAutoLaunch>
                    <com.safeco_FCRANotificationURL id="A004">
                        https://safesite.qa.safeco.com/Personal/TACRC/Consumer/Auto/VPReportMenu.aspx?P=6cda3f50-0bea-46ab-a5b6-4695bea02232&amp;ReportName=AdverseActionCreditNoticeForm&amp;modeid=18
                    </com.safeco_FCRANotificationURL>
                    <com.safeco_FCRANotificationURLText id="A005">This is a link to the consumers FCRA notification on
                        the safeco.com web site.
                    </com.safeco_FCRANotificationURLText>
                    <com.safeco_QuoteSummaryURL id="A006">
                        https://safesite.qa.safeco.com/Personal/TACRC/Consumer/Auto/VPReportMenu.aspx?P=6cda3f50-0bea-46ab-a5b6-4695bea02232&amp;ReportName=QuoteSummary&amp;modeid=18
                    </com.safeco_QuoteSummaryURL>
                    <com.safeco_QuoteSummaryURLText id="A007">This is a link to the Quote Summary.
                    </com.safeco_QuoteSummaryURLText>
                    <com.safeco_QuoteRq_PremiumTotal id="A008">689.90</com.safeco_QuoteRq_PremiumTotal>
                    <com.safeco_QuoteRq_PremiumTotalPIF id="A009">601.10</com.safeco_QuoteRq_PremiumTotalPIF>
                    <CurrentTermAmt>
                        <Amt id="A010">689.90</Amt>
                    </CurrentTermAmt>
                    <RateEffectiveDate id="A011">2015-05-10</RateEffectiveDate>
                    <com.safeco_QuoteRq_Product id="A012">PRF</com.safeco_QuoteRq_Product>
                    <com.safeco_QuoteRq_Market id="A013">050</com.safeco_QuoteRq_Market>
                    <com.safeco_QuoteRq_CovMarket id="A014">
                        BI:283,PD:265,COMP:252,COLL:269,MED:293,PIP:293,UM:256,UIM:256,UMPD:238
                    </com.safeco_QuoteRq_CovMarket>
                    <com.safeco_QuoteRq_Band id="A015">903</com.safeco_QuoteRq_Band>
                    <com.safeco_QuoteRq_Layer id="A016">006</com.safeco_QuoteRq_Layer>
                    <com.safeco_AdvancedQuotingDiscYN id="A017">N</com.safeco_AdvancedQuotingDiscYN>
                    <com.safeco_PolicyRemarks id="A018">Uninsured/Underinsured Motorist coverage is rejected or limits
                        selected are less than BI limits. A CO UM/UIM Coverage Option form will be required.***The
                        quoted total premium has been reduced for the following discounts: HomeOwner, Low Mileage*** We
                        could not find an exact VIN match based on the Model Year and Model entered in the rater. Please
                        verify and update your vehicle information in Safeco’s Quote and Issue system.
                    </com.safeco_PolicyRemarks>
                    <com.safeco_MarketingMessages>
                        <com.safeco_AgentMessage id="A019">Agent Message 1</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage id="A020">Safeco Motorcycle Enhancements!</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage id="A021">Come and
                            <a href="https://s3.amazonaws.com/Motorcycle_refresh_sizzle/MotorcycleRefresh_SIZZLE.html">
                                see what's in it for you.
                            </a>
                        </com.safeco_AgentMessage>
                        <com.safeco_AgentMessage id="A022">Agent Message 8</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage id="A023">Agent Message 9 - Updated</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage id="A024">Last Message</com.safeco_AgentMessage>
                        <com.safeco_ConsumerMessage id="A025">Consumer Message 1</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A026">Consumer Message 2 - update</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A027">Consumer Message 3</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A028">Consumer Message 5</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A029">Consumer Message 6</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A030">Consumer Message 7</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A031">Consumer Message 8</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A032">Consumer Message 9</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A033">Consumer Message 10- Updated</com.safeco_ConsumerMessage>
                    </com.safeco_MarketingMessages>
                    <com.safeco_AnnualAutoEligibleYN id="A038">Y</com.safeco_AnnualAutoEligibleYN>
                </QuoteInfo>
                <QuestionAnswer>
                    <QuestionCd>Current_Insurance_Value</QuestionCd>
                    <Explanation>CI</Explanation>
                </QuestionAnswer>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <PersDriver id="1">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>FIERRO</Surname>
                                <GivenName>GUADALUPE</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1975-11-11</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                            <com.safeco_IndustryCd>Construct/EnrgyTrds</com.safeco_IndustryCd>
                            <com.safeco_OccupationCd>Laborer/Worker</com.safeco_OccupationCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>1991-11-11</LicensedDt>
                            <DriversLicenseNumber>041330466</DriversLicenseNumber>
                            <StateProvCd>CO</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>BS</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh id="Veh1">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>TAURUS SES</Model>
                    <ModelYear>2002</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <TerritoryCd>265</TerritoryCd>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>00214.20</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>00198.80</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>00276.90</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>889.89</FullTermAmt>
                    <FullTermAmt>
                        <Amt>689.90</Amt>
                    </FullTermAmt>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location>
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>1025 5TH ST</Addr1>
                    <City>DEL NORTE</City>
                    <StateProvCd>CO</StateProvCd>
                    <PostalCode>81132</PostalCode>
                </Addr>
            </Location>
            <MsgStatus>
                <MsgStatusCd>SuccessWithInfo</MsgStatusCd>
                <ExtendedStatus>
                    <ExtendedStatusCd>Informational</ExtendedStatusCd>
                    <ExtendedStatusDesc>The policy premium with the Paid in Full discount is $601.10. The quote is based
                        on rating information you supplied and is subject to full underwriting review.
                    </ExtendedStatusDesc>
                </ExtendedStatus>
                <ExtendedStatus>
                    <ExtendedStatusCd>Informational</ExtendedStatusCd>
                    <ExtendedStatusDesc>Uninsured/Underinsured Motorist coverage is rejected or limits selected are less
                        than BI limits. A CO UM/UIM Coverage Option form will be required.***The quoted total premium
                        has been reduced for the following discounts: HomeOwner, Low Mileage*** We could not find an
                        exact VIN match based on the Model Year and Model entered in the rater. Please verify and update
                        your vehicle information in Safeco’s Quote and Issue system.
                    </ExtendedStatusDesc>
                </ExtendedStatus>
                <ExtendedStatus>
                    <ExtendedStatusCd>VerifyDataValue</ExtendedStatusCd>
                    <ExtendedStatusDesc>A default value was used for Prior Policy Duration.</ExtendedStatusDesc>
                </ExtendedStatus>
                <ExtendedStatus>
                    <ExtendedStatusCd>VerifyDataValue</ExtendedStatusCd>
                    <ExtendedStatusDesc>We were not able to find an insurance score for this client which may impact the
                        final rate. Please confirm the spelling of the Name and Address and the Birth Date and Social
                        Security Number.
                    </ExtendedStatusDesc>
                </ExtendedStatus>
                <ExtendedStatus>
                    <ExtendedStatusCd>VerifyDataValue</ExtendedStatusCd>
                    <ExtendedStatusDesc>BEO coverage was rated using limit/deductible of A for Vehicle Veh1.
                    </ExtendedStatusDesc>
                </ExtendedStatus>
            </MsgStatus>
            <com.safeco_CompanyURL id="A001">https://safesite.qa.safeco.com/Client/TACRC/Client/OpenActivity.aspx?l=o&amp;p=6cda3f50-0bea-46ab-a5b6-4695bea02232</com.safeco_CompanyURL>
            <com.safeco_CompanyURLText id="A002">This is a link to the quote at the Safeco web site.
            </com.safeco_CompanyURLText>
            <com.safeco_CompanyURLAutoLaunch id="A003">1</com.safeco_CompanyURLAutoLaunch>
            <com.safeco_QuoteRq_PremiumTotal id="A034">689.90</com.safeco_QuoteRq_PremiumTotal>
            <com.safeco_QuoteRq_PremiumTotalPIF id="A035">601.10</com.safeco_QuoteRq_PremiumTotalPIF>
            <CurrentTermAmt>
                <Amt id="A036">689.90</Amt>
            </CurrentTermAmt>
            <RateEffectiveDate id="A037">2015-05-10</RateEffectiveDate>
            <PolicySummaryInfo>
                <ItemIdInfo>
                    <InsuredId>6cda3f50-0bea-46ab-a5b6-4695bea02232</InsuredId>
                </ItemIdInfo>
                <FullTermAmt>
                    <Amt>00689.90</Amt>
                    <CurCd>USD</CurCd>
                </FullTermAmt>
                <PolicyStatusCd>QuotedNotBound</PolicyStatusCd>
            </PolicySummaryInfo>
        </PersAutoPolicyQuoteInqRs>
    </InsuranceSvcRs>
</ACORD>
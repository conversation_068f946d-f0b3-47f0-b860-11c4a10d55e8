<ACORD>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName>com.AMS</SPName>
                <CustPermId/>
                <CustLoginId><EMAIL></CustLoginId>
            </CustId>
            <CustPswd>
                <EncryptionTypeCd/>
                <Pswd/>
            </CustPswd>
        </SignonPswd>
        <ClientDt>11/28/2018 6:43 PM</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>AMS</Org>
            <Name>AMS 360</Name>
            <Version>2.0</Version>
        </ClientApp>
        <ProxyClient>
            <Org>Vertafore</Org>
            <Name>AgencyBookRoll</Name>
            <Version>V1.00</Version>
        </ProxyClient>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>2BEC24B8-984E-4198-A9E0-02AAA80E9849</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <RqUID>757A7981-53F2-4CBB-AA97-015DA0E91F78</RqUID>
            <TransactionRequestDt>2018-11-28T08:52:01</TransactionRequestDt>
            <TransactionEffectiveDt>2018-12-06</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ContractNumber/>
                    <ProducerRoleCd>Agency</ProducerRoleCd>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <Surname>LINSE</Surname>
                            <GivenName>JERRY</GivenName>
                        </PersonName>
                        <LegalEntityCd>IN</LegalEntityCd>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId/>
                        </TaxIdentity>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>1620 8TH AVE N</Addr1>
                        <City>GREAT FALLS</City>
                        <StateProvCd>MT</StateProvCd>
                        <PostalCode>*********</PostalCode>
                    </Addr>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt>1953-08-01</BirthDt>
                        <OccupationDesc>Construction</OccupationDesc>
                        <MiscParty>
                            <GeneralPartyInfo>
                                <NameInfo>
                                    <CommlName>
                                        <CommercialName>Eddard Stark</CommercialName>
                                    </CommlName>
                                </NameInfo>
                                <Addr>
                                    <AddrTypeCd>MailingAddress</AddrTypeCd>
                                    <Addr1>2200 13TH STREET SOUTH</Addr1>
                                    <City>GREAT FALLS</City>
                                    <StateProvCd>MT</StateProvCd>
                                    <PostalCode>59404</PostalCode>
                                </Addr>
                            </GeneralPartyInfo>
                            <MiscPartyInfo>
                                <MiscPartyRoleCd>Employer</MiscPartyRoleCd>
                            </MiscPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy id="PolicyLevel">
                <PolicyNumber>69M4516</PolicyNumber>
                <CompanyProductCd>EMCC</CompanyProductCd>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>21415</NAICCd>
                <ContractTerm>
                    <EffectiveDt>2018-12-06</EffectiveDt>
                    <ExpirationDt>2019-12-06</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                        <UnitMeasurementCd>MON</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <BillingMethodCd>CPB</BillingMethodCd>
                <CurrentTermAmt>
                    <Amt>745.00</Amt>
                </CurrentTermAmt>
                <OriginalInceptionDt>2007-12-06</OriginalInceptionDt>
                <RateEffectiveDt>2018-12-06</RateEffectiveDt>
                <Form>
                    <FormNumber>0405</FormNumber>
                    <FormName>PRIVACY NOTICE</FormName>
                    <EditionDt>2018-01-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>IL7004</FormNumber>
                    <FormName>MUTUAL POLICY PROVISIONS</FormName>
                    <EditionDt>2016-09-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>IL8355MT</FormNumber>
                    <FormName>ID CARD</FormName>
                    <EditionDt>2007-02-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>IL8538</FormNumber>
                    <FormName>MONTANA BILLING NOTIFICATION</FormName>
                    <EditionDt>2006-01-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>MC7021</FormNumber>
                    <FormName>SUNNY DAY (SUSP OF COV ENDORSEMENT)</FormName>
                    <EditionDt>1991-12-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>MC7026</FormNumber>
                    <FormName>MOTORCYCLE HELMET COVERAGE</FormName>
                    <EditionDt>2004-09-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>MC7027</FormNumber>
                    <FormName>NON FACTORY ITEMS EXCLUSION ENDST</FormName>
                    <EditionDt>2001-08-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>MC8011</FormNumber>
                    <FormName>IMPORTANT NOTICE</FormName>
                    <EditionDt>2001-08-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP0001</FormNumber>
                    <FormName>PERSONAL AUTO POLICY</FormName>
                    <EditionDt>2005-01-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP0183</FormNumber>
                    <FormName>AMENDMENT OF POLICY PROVISIONS</FormName>
                    <EditionDt>2017-06-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP0309</FormNumber>
                    <FormName>SINGLE LIABILITY LIMIT</FormName>
                    <EditionDt>2005-01-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP0323</FormNumber>
                    <FormName>MISCELLANEOUS TYPE VEHICLE</FormName>
                    <EditionDt>2005-01-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP0401</FormNumber>
                    <FormName>SINGLE UNINSURED MOTORISTS LIMIT</FormName>
                    <EditionDt>1998-06-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP0402</FormNumber>
                    <FormName>SINGLE UNDERINSURED MOTORISTS</FormName>
                    <EditionDt>1998-06-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP1301</FormNumber>
                    <FormName>COV DAMAGE TO AUTO EXCLUSION</FormName>
                    <EditionDt>1999-12-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP1430</FormNumber>
                    <FormName>UNDERINSURED MOTORISTS COVERAGE</FormName>
                    <EditionDt>2017-06-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP7007</FormNumber>
                    <FormName>PERS AUTO POLICY QUICK REFERENCE</FormName>
                    <EditionDt>2005-01-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP7189</FormNumber>
                    <FormName>LOSS PAYABLE CLAUSE</FormName>
                    <EditionDt>1998-11-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PP7198</FormNumber>
                    <FormName>UM/UIM/MED ANTI STACKING</FormName>
                    <EditionDt>2008-08-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <LOBCd>AUTOP</LOBCd>
                    <InsurerName>None</InsurerName>
                </OtherOrPriorPolicy>
                <PersApplicationInfo>
                    <InsuredOrPrincipal/>
                </PersApplicationInfo>
                <ControllingStateProvCd>MT</ControllingStateProvCd>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>21415</NAICCd>
                <CompanyProductCd>EMCC</CompanyProductCd>
                <CurrentTermAmt>
                    <Amt>745.00</Amt>
                </CurrentTermAmt>
                <NetChangeAmt>
                    <Amt>745.00</Amt>
                </NetChangeAmt>
                <RateEffectiveDt>2018-12-06</RateEffectiveDt>
                <Coverage>
                    <CoverageCd>UMISG</CoverageCd>
                    <Limit>
                        <FormatInteger>50000</FormatInteger>
                        <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                    </Limit>
                    <CurrentTermAmt>
                        <Amt>27.00</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <Coverage>
                    <CoverageCd>UNDSG</CoverageCd>
                    <Limit>
                        <FormatInteger>50000</FormatInteger>
                    </Limit>
                    <CurrentTermAmt>
                        <Amt>32.00</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <Coverage>
                    <CoverageCd>MEDPM</CoverageCd>
                    <Limit>
                        <FormatInteger>500</FormatInteger>
                    </Limit>
                    <CurrentTermAmt>
                        <Amt>20.00</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <PersDriver id="D1">
                    <ItemIdInfo>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Dee</Surname>
                                <GivenName>Forrest</GivenName>
                            </PersonName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1953-011-01</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                            <OccupationDesc>Construction wo</OccupationDesc>
                        </PersonInfo>
                        <DriversLicense>
                            <LicensedDt>1990-11-11</LicensedDt>
                            <DriversLicenseNumber>5057341</DriversLicenseNumber>
                            <StateProvCd>MT</StateProvCd>
                        </DriversLicense>
                        <DriversLicense>
                            <LicensedDt>1990-11-11</LicensedDt>
                            <DriversLicenseNumber>Unknown</DriversLicenseNumber>
                        </DriversLicense>
                        <License>
                            <LicenseTypeCd>Driver</LicenseTypeCd>
                            <LicensedDt>1990-11-11</LicensedDt>
                            <LicensePermitNumber>1100619534111</LicensePermitNumber>
                            <StateProvCd>MT</StateProvCd>
                        </License>
                        <License>
                            <LicenseTypeCd>MotorcycleDriver</LicenseTypeCd>
                            <LicensedDt>1990-11-11</LicensedDt>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo id="PD1">
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>1</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="D2">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Reynolds</Surname>
                                <GivenName>Bilbo</GivenName>
                            </PersonName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1966-00-01</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc>Electrician/ele</OccupationDesc>
                        </PersonInfo>
                        <DriversLicense>
                            <LicensedDt>1982-11-30</LicensedDt>
                            <DriversLicenseNumber>8520282</DriversLicenseNumber>
                            <StateProvCd>MT</StateProvCd>
                        </DriversLicense>
                        <DriversLicense>
                            <LicensedDt>1982-11-30</LicensedDt>
                            <DriversLicenseNumber>Unknown</DriversLicenseNumber>
                        </DriversLicense>
                        <License>
                            <LicenseTypeCd>Driver</LicenseTypeCd>
                            <LicensedDt>1982-11-30</LicensedDt>
                            <LicensePermitNumber>1104619664130</LicensePermitNumber>
                            <StateProvCd>MT</StateProvCd>
                        </License>
                        <License>
                            <LicenseTypeCd>MotorcycleDriver</LicenseTypeCd>
                            <LicensedDt>1982-11-30</LicensedDt>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo id="PD2">
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="L1" RatedDriverRef="D1" id="V1">
                    <ItemIdInfo>
                        <AgencyId>0001</AgencyId>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>Harley-davidson</Manufacturer>
                    <Model>Soft Tail Classic</Model>
                    <ModelYear>2006</ModelYear>
                    <VehBodyTypeCd>MOPED</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>MT</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>13500</Amt>
                    </CostNewAmt>
                    <FullTermAmt>
                        <Amt>270.00</Amt>
                    </FullTermAmt>
                    <Displacement>
                        <NumUnits>1450</NumUnits>
                    </Displacement>
                    <RegistrationStateProvCd>MT</RegistrationStateProvCd>
                    <TerritoryCd>001</TerritoryCd>
                    <VehIdentificationNumber>1HD1JDB176Y069145</VehIdentificationNumber>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Montana Federal Cu</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>PO Box 5027</Addr1>
                                <City>Great Falls</City>
                                <StateProvCd>MT</StateProvCd>
                                <PostalCode>594035027</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LOSSP</NatureInterestCd>
                            <InterestIdNumber>001</InterestIdNumber>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <GaragingCd>G</GaragingCd>
                    <MultiCarDiscountInd>1</MultiCarDiscountInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <PrincipalOperatorInd>1</PrincipalOperatorInd>
                    <RateClassCd>3021</RateClassCd>
                    <ResidualMarketFacilityInd>0</ResidualMarketFacilityInd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>84.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>76.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>110.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L1" RatedDriverRef="D2" id="V2">
                    <ItemIdInfo>
                        <AgencyId>0002</AgencyId>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>Harley-davidson</Manufacturer>
                    <Model>Xlh883deluxe</Model>
                    <ModelYear>1988</ModelYear>
                    <VehBodyTypeCd>MOTORBIKE</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>MT</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>10000</Amt>
                    </CostNewAmt>
                    <FullTermAmt>
                        <Amt>92.00</Amt>
                    </FullTermAmt>
                    <Displacement>
                        <NumUnits>883</NumUnits>
                    </Displacement>
                    <RegistrationStateProvCd>MT</RegistrationStateProvCd>
                    <TerritoryCd>001</TerritoryCd>
                    <VehIdentificationNumber>1HD4CFM17JY122225</VehIdentificationNumber>
                    <GaragingCd>G</GaragingCd>
                    <MultiCarDiscountInd>1</MultiCarDiscountInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <PrincipalOperatorInd>1</PrincipalOperatorInd>
                    <RateClassCd>3020</RateClassCd>
                    <ResidualMarketFacilityInd>0</ResidualMarketFacilityInd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>GDD</CoverageCd>
                        <Deductible>
                            <FormatInteger>10000</FormatInteger>
                            <DeductibleTypeCd>PC</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>92.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L1" id="V3">
                    <ItemIdInfo>
                        <AgencyId>0003</AgencyId>
                        <InsurerId>0003</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>Harley-davidson</Manufacturer>
                    <Model>Fxst</Model>
                    <ModelYear>2006</ModelYear>
                    <VehBodyTypeCd>MOTORCYCLETHREEWHEEL</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>MT</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>10000</Amt>
                    </CostNewAmt>
                    <FullTermAmt>
                        <Amt>304.00</Amt>
                    </FullTermAmt>
                    <Displacement>
                        <NumUnits>1450</NumUnits>
                    </Displacement>
                    <RegistrationStateProvCd>MT</RegistrationStateProvCd>
                    <TerritoryCd>001</TerritoryCd>
                    <VehIdentificationNumber>1HD1BVB136Y087080</VehIdentificationNumber>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Montana Fcu</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>PO Box 5027</Addr1>
                                <City>Great Falls</City>
                                <StateProvCd>MT</StateProvCd>
                                <PostalCode>594035027</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LOSSP</NatureInterestCd>
                            <InterestIdNumber>002</InterestIdNumber>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <GaragingCd>G</GaragingCd>
                    <MultiCarDiscountInd>1</MultiCarDiscountInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <RateClassCd>3020</RateClassCd>
                    <ResidualMarketFacilityInd>0</ResidualMarketFacilityInd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>95.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>86.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>123.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L1" RatedDriverRef="D2" id="V4">
                    <ItemIdInfo>
                        <AgencyId>0002</AgencyId>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>Harley-davidson</Manufacturer>
                    <Model>Xlh883deluxe</Model>
                    <ModelYear>1988</ModelYear>
                    <VehBodyTypeCd>MOTORCYCLETWOWHEEL</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>MT</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>10000</Amt>
                    </CostNewAmt>
                    <FullTermAmt>
                        <Amt>92.00</Amt>
                    </FullTermAmt>
                    <Displacement>
                        <NumUnits>883</NumUnits>
                    </Displacement>
                    <RegistrationStateProvCd>MT</RegistrationStateProvCd>
                    <TerritoryCd>001</TerritoryCd>
                    <VehIdentificationNumber>1HD4CFM17JY122225</VehIdentificationNumber>
                    <GaragingCd>G</GaragingCd>
                    <MultiCarDiscountInd>1</MultiCarDiscountInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <PrincipalOperatorInd>1</PrincipalOperatorInd>
                    <RateClassCd>3020</RateClassCd>
                    <ResidualMarketFacilityInd>0</ResidualMarketFacilityInd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>GDD</CoverageCd>
                        <Deductible>
                            <FormatInteger>10000</FormatInteger>
                            <DeductibleTypeCd>PC</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>92.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L1" RatedDriverRef="D2" id="V5">
                    <ItemIdInfo>
                        <AgencyId>0002</AgencyId>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>Harley-davidson</Manufacturer>
                    <Model>Xlh883deluxe</Model>
                    <ModelYear>1988</ModelYear>
                    <VehBodyTypeCd>MOTORSCOOTER</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>MT</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>10000</Amt>
                    </CostNewAmt>
                    <FullTermAmt>
                        <Amt>92.00</Amt>
                    </FullTermAmt>
                    <Displacement>
                        <NumUnits>883</NumUnits>
                    </Displacement>
                    <RegistrationStateProvCd>MT</RegistrationStateProvCd>
                    <TerritoryCd>001</TerritoryCd>
                    <VehIdentificationNumber>1HD4CFM17JY122225</VehIdentificationNumber>
                    <GaragingCd>G</GaragingCd>
                    <MultiCarDiscountInd>1</MultiCarDiscountInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <PrincipalOperatorInd>1</PrincipalOperatorInd>
                    <RateClassCd>3020</RateClassCd>
                    <ResidualMarketFacilityInd>0</ResidualMarketFacilityInd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>GDD</CoverageCd>
                        <Deductible>
                            <FormatInteger>10000</FormatInteger>
                            <DeductibleTypeCd>PC</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>92.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L1" RatedDriverRef="D2" id="V6">
                    <ItemIdInfo>
                        <AgencyId>0002</AgencyId>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>Harley-davidson</Manufacturer>
                    <Model>Xlh883deluxe</Model>
                    <ModelYear>1988</ModelYear>
                    <VehBodyTypeCd>UTV</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>MT</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>10000</Amt>
                    </CostNewAmt>
                    <FullTermAmt>
                        <Amt>92.00</Amt>
                    </FullTermAmt>
                    <Displacement>
                        <NumUnits>883</NumUnits>
                    </Displacement>
                    <RegistrationStateProvCd>MT</RegistrationStateProvCd>
                    <TerritoryCd>001</TerritoryCd>
                    <VehIdentificationNumber>1HD4CFM17JY122225</VehIdentificationNumber>
                    <GaragingCd>G</GaragingCd>
                    <MultiCarDiscountInd>1</MultiCarDiscountInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <PrincipalOperatorInd>1</PrincipalOperatorInd>
                    <RateClassCd>3020</RateClassCd>
                    <ResidualMarketFacilityInd>0</ResidualMarketFacilityInd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>GDD</CoverageCd>
                        <Deductible>
                            <FormatInteger>10000</FormatInteger>
                            <DeductibleTypeCd>PC</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>92.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="L1">
                <ItemIdInfo>
                    <AgencyId>0001</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>1620 8th Ave N</Addr1>
                    <City>Great Falls</City>
                    <StateProvCd>MT</StateProvCd>
                    <PostalCode>*********</PostalCode>
                    <County>Cascade</County>
                </Addr>
                <CountyTownCd>070</CountyTownCd>
            </Location>
            <RemarkText IdRef="PolicyLevel">OPERATOR IS MEMBER OF HOUSEHOLD
                OPERATOR IS MEMBER OF HOUSEHOLD
                LICENSED FOR ROAD USE
                SADDLEBAGS/TRUNK, WINDSHIELD/FAIRING, LUGGAGE RACK, SPECIAL PAINTING COVERAGE(S)
                LICENSED FOR ROAD USE, TOURING BIKE
                LICENSED FOR ROAD USE
            </RemarkText>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
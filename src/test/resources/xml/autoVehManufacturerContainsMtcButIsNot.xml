<ACORD>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName>BCFTechnology.com</SPName>
                <CustPermId>test</CustPermId>
                <CustLoginId>test</CustLoginId>
            </CustId>
            <CustPswd>
                <EncryptionTypeCd>NONE</EncryptionTypeCd>
                <Pswd>test</Pswd>
            </CustPswd>
        </SignonPswd>
        <ClientDt>2022-03-02T11:31:54</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>AMS Services</Org>
            <Name>Progressive Insurance Com</Name>
            <Version>CMS</Version>
        </ClientApp>
        <ProxyClient>
            <Org>BCFTech</Org>
            <Name>TransmitXML</Name>
            <Version>V1.00</Version>
        </ProxyClient>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>a41db610-0a00-45ef-8f4a-fe447553d10b</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <RqUID>D7C91A6D-951E-4E13-A9FB-96BFBEF7F2D6</RqUID>
            <TransactionRequestDt>2022-02-24T19:23:42</TransactionRequestDt>
            <TransactionEffectiveDt>2022-02-24</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>LeDoux Insurance Agency, Inc.</CommercialName>
                        </CommlName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>StreetAddress</AddrTypeCd>
                        <Addr1>PO Box 2218</Addr1>
                        <City>Eugene</City>
                        <StateProvCd>OR</StateProvCd>
                        <PostalCode>97402</PostalCode>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Business</CommunicationUseCd>
                            <PhoneNumber>******-6835112</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Fax</PhoneTypeCd>
                            <CommunicationUseCd>Business</CommunicationUseCd>
                            <PhoneNumber>******-6838753</PhoneNumber>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>
                <ProducerInfo>
                    <ContractNumber>035444</ContractNumber>
                    <ProducerRoleCd>Agency</ProducerRoleCd>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>20034301</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <Surname>Cannon</Surname>
                            <GivenName>Jennifer</GivenName>
                        </PersonName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>A4JN52478</TaxId>
                        </TaxIdentity>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>227 Wintersage Circle</Addr1>
                        <City>Talent</City>
                        <StateProvCd>OR</StateProvCd>
                        <PostalCode>97540</PostalCode>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-6909965</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Cell</PhoneTypeCd>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <PhoneNumber>******-7012399</PhoneNumber>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <GenderCd>F</GenderCd>
                        <BirthDt isMasked="true">1990-**-**</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                        <OccupationDesc>Teacher</OccupationDesc>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy id="PolicyLevel">
                <PolicyNumber>939096054</PolicyNumber>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>10194</NAICCd>
                <ContractTerm>
                    <EffectiveDt>2021-12-04</EffectiveDt>
                    <ExpirationDt>2022-06-04</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>6</NumUnits>
                        <UnitMeasurementCd>MON</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <CurrentTermAmt>
                    <Amt>467.00</Amt>
                </CurrentTermAmt>
                <OriginalInceptionDt>2020-06-04</OriginalInceptionDt>
                <RenewalTerm>
                    <DurationPeriod>
                        <NumUnits>6</NumUnits>
                        <UnitMeasurementCd>MON</UnitMeasurementCd>
                    </DurationPeriod>
                </RenewalTerm>
                <PaymentOption>
                    <PaymentPlanCd>Mo</PaymentPlanCd>
                </PaymentOption>
                <PersApplicationInfo>
                    <InsuredOrPrincipal/>
                </PersApplicationInfo>
                <AccidentViolation DriverRef="D1">
                    <AccidentViolationDt>2016-11-03</AccidentViolationDt>
                    <AccidentViolationDesc>Not At fault Accident</AccidentViolationDesc>
                    <AccidentViolationRecordTypeCd>ACCNF</AccidentViolationRecordTypeCd>
                </AccidentViolation>
                <ControllingStateProvCd>OR</ControllingStateProvCd>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>com.safeco_MC</LOBCd>
                <NAICCd>10194</NAICCd>
                <CurrentTermAmt>
                    <Amt>467.00</Amt>
                </CurrentTermAmt>
                <RateEffectiveDt>2021-12-04</RateEffectiveDt>
                <PersDriver id="D1">
                    <ItemIdInfo>
                        <AgencyId>0001</AgencyId>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Cannon</Surname>
                                <GivenName>Jennifer</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>A4JN52478</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt isMasked="true">1974-**-**</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber isMasked="true">14228********</DriversLicenseNumber>
                            <StateProvCd>OR</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicenseTypeCd>Driver</LicenseTypeCd>
                            <LicensePermitNumber isMasked="true">14228********</LicensePermitNumber>
                            <StateProvCd>OR</StateProvCd>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo id="PD1">
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <DriverTypeCd>P</DriverTypeCd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="L1" RatedDriverRef="D1" id="V1">
                    <ItemIdInfo>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>ISUZU</Manufacturer>
                    <Model>RODEO</Model>
                    <ModelYear>2001</ModelYear>
                    <FullTermAmt>
                        <Amt>467.00</Amt>
                    </FullTermAmt>
                    <TerritoryCd>13</TerritoryCd>
                    <VehIdentificationNumber>4S2DM58WX14344638</VehIdentificationNumber>
                    <VehSymbolCd>ZU</VehSymbolCd>
                    <OdometerReading>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <RateClassCd>04</RateClassCd>
                    <VehUseCd>DW</VehUseCd>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="82091721-faf7-4c25-8acd-914e936d0c87">
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>94.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="e8a9f9c9-4eec-4e7c-ab93-d7056ee3e787">
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>65.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="1b8c1690-0ee1-4487-8fca-adecc226fdea">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Coverage</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>132.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="ce689ac7-b8f7-4b0c-8416-caeef8a33e35">
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>96.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="c4312555-0f92-49f5-b7ac-107d7fe74097">
                        <CoverageCd>UMPD</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Deductible bookTransferAssociatedNodeId="V1" btId="2819dacb-b48b-48bf-9071-dadaddd568c4">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>5.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="319742a0-b4b6-4cae-ab92-80df23bd812a">
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible bookTransferAssociatedNodeId="V1" btId="cfbbd4f0-3ba8-4f2b-97f9-4275a090638c">
                            <FormatInteger>100</FormatInteger>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>G</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt>21.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="d6f6a84a-1860-4036-85c0-0971bdc03095">
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible bookTransferAssociatedNodeId="V1" btId="a31b7854-0ff9-4bcb-a45b-d51772cb1526">
                            <FormatInteger>250</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>44.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="5cba5d1d-919c-4310-b8d5-b3a77de624b9">
                        <CoverageCd>TL</CoverageCd>
                        <CurrentTermAmt>
                            <Amt>5.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="5b995219-289d-4482-a787-26f9f1ab63d7">
                        <CoverageCd>RREIM</CoverageCd>
                        <Limit>
                            <FormatInteger>40</FormatInteger>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>1200</FormatInteger>
                            <LimitAppliesToCd>MaxAmount</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>5.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="6c8ac8ea-4d1e-44ff-b2f9-393692b89502">
                        <CoverageCd>SMP</CoverageCd>
                        <CoverageDesc>Multi Policy Discount</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="5be28701-8997-4f3b-a84b-74dabd06286d">
                        <CoverageCd>NP4</CoverageCd>
                        <CoverageDesc>New Business Length of Pop Discount 4</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="6e112106-d1cd-412c-8ac4-f2b1f5578cc2">
                        <CoverageCd>IPP</CoverageCd>
                        <CoverageDesc>Paperless Discount</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V1" btId="4079815c-0364-4334-8129-194c8f256c23">
                        <CoverageCd>SD3</CoverageCd>
                        <CoverageDesc>Three Year Safe Driving Discount</CoverageDesc>
                    </Coverage>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="L1">
                <ItemIdInfo>
                    <AgencyId>0001</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>227 Wintersage Circle</Addr1>
                    <City>Talent</City>
                    <StateProvCd>OR</StateProvCd>
                    <PostalCode>97540</PostalCode>
                </Addr>
            </Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
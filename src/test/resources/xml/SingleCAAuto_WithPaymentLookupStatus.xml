<ACORD>
    <BookTransferMeta>
        <creation>
            <type>xmlExtraction</type>
            <origin>SLIM</origin>
        </creation>
    </BookTransferMeta>
    <InsuranceSvcRq>
        <PersAutoPolicyQuoteInqRq>
            <TransactionRequestDt>2024-04-04</TransactionRequestDt>
            <TransactionEffectiveDt>2023-12-11</TransactionEffectiveDt>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>999999</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>3952941404353.000</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>MARTA BEACH</CommercialName>
                        </CommlName>
                        <PersonName>
                            <Surname>BEACH</Surname>
                            <GivenName>MARTA</GivenName>
                        </PersonName>
                        <TaxIdentity>
                            <TaxId>ABD-YG-7453</TaxId>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                        </TaxIdentity>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>6277 IVARENE AVE </Addr1>
                        <City>LOS ANGELES</City>
                        <StateProvCd>CA</StateProvCd>
                        <PostalCode>90068</PostalCode>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Cell</PhoneTypeCd>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <PhoneNumber>******-111-1111</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <GenderCd>F</GenderCd>
                        <BirthDt isMasked="true">1968-**-**</BirthDt>
                        <MaritalStatusCd>W</MaritalStatusCd>
                        <OccupationDesc>Retired</OccupationDesc>
                        <LengthTimeWithPreviousEmployer>
                            <NumUnits>0</NumUnits>
                            <UnitMeasurementCd>years</UnitMeasurementCd>
                        </LengthTimeWithPreviousEmployer>
                        <LengthTimeCurrentAddr>
                            <EffectiveDt>2019-04-04</EffectiveDt>
                        </LengthTimeCurrentAddr>
                        <EducationLevelCd>Doctorate</EducationLevelCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <PolicyNumber>AO226101838490</PolicyNumber>
                <CompanyProductCd>2</CompanyProductCd>
                <BroadLOBCd>P</BroadLOBCd>
                <LOBCd>AUTOP</LOBCd>
                <ControllingStateProvCd>CA</ControllingStateProvCd>
                <ContractTerm>
                    <EffectiveDt>2023-12-11</EffectiveDt>
                    <ExpirationDt>2024-12-11</ExpirationDt>
                </ContractTerm>
                <LanguageCd>en</LanguageCd>
                <MailingResponsibiltyCd>CO</MailingResponsibiltyCd>
                <OriginalInceptionDt>2022-12-11</OriginalInceptionDt>
                <PrintedDocumentsRequestedInd>1</PrintedDocumentsRequestedInd>
                <RateEffectiveDt>2023-12-11</RateEffectiveDt>
                <OtherOrPriorPolicy>
                    <PolicyNumber>H4V24357486970</PolicyNumber>
                    <LOBCd>HOME</LOBCd>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy/>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber>AOV24374471270</PolicyNumber>
                    <LOBCd>AUTOP</LOBCd>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <PaymentPlanCd>E</PaymentPlanCd>
                    <DayMonthDue>11</DayMonthDue>
                    <MethodPaymentCd>EFT</MethodPaymentCd>
                </PaymentOption>
                <Form>
                    <FormName>Amendment of Policy Provisions - California</FormName>
                    <FormNumber>AS1133 05 16</FormNumber>
                </Form>
                <Form>
                    <FormName>Nuclear, Bio-Chemical &amp; Mold Exclusion Endorsement</FormName>
                    <FormNumber>AS2228</FormNumber>
                </Form>
                <Form>
                    <FormName>Amendment of Policy Definitions</FormName>
                    <FormNumber>AS2344</FormNumber>
                </Form>
                <Form>
                    <FormName>Automobile Amendatory Endorsement</FormName>
                    <FormNumber>AS2259 05 16</FormNumber>
                </Form>
                <Form>
                    <FormName>Automatic Termination Endorsement</FormName>
                    <FormNumber>AS1046</FormNumber>
                </Form>
                <Form>
                    <FormName>Coverage For Damage To Your Auto Exclusion Endorsement</FormName>
                    <FormNumber>PP 13 01</FormNumber>
                </Form>
                <Form>
                    <FormName>Membership in Liberty Mutual Holding Company Inc.</FormName>
                    <FormNumber>2340e</FormNumber>
                </Form>
                <Form>
                    <FormName>Split Liability Limits</FormName>
                    <FormNumber>PP 03 09</FormNumber>
                </Form>
                <Form>
                    <FormName>Uninsured Motorist-CA</FormName>
                    <FormNumber>AS2127 05 18</FormNumber>
                </Form>
                <Form>
                    <FormName>Split Uninsured Motorists Limits - California</FormName>
                    <FormNumber>PP 04 92</FormNumber>
                </Form>
                <Form>
                    <FormName>Mexico Extension Endorsement</FormName>
                    <FormNumber>AS1006 09 18</FormNumber>
                </Form>
                <CurrentTermAmt>
                    <Amt>4612.75</Amt>
                </CurrentTermAmt>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>23035</NAICCd>
                <PersDriver>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>ANMAR CHRISTODOULO</CommercialName>
                            </CommlName>
                            <PersonName>
                                <Surname>BEACH</Surname>
                                <GivenName>MARTA</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxId>ABD-YG-7453</TaxId>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <DriversLicense>
                            <LicensedDt>1984-06-15</LicensedDt>
                            <DriversLicenseNumber isMasked="true">C8647********</DriversLicenseNumber>
                        </DriversLicense>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt isMasked="true">1968-**-**</BirthDt>
                            <MaritalStatusCd>W</MaritalStatusCd>
                            <OccupationDesc>Retired</OccupationDesc>
                            <EducationLevelCd>SomeCollegeNoDegree</EducationLevelCd>
                        </PersonInfo>
                        <License>
                            <LicensePermitNumber isMasked="true">C8647********</LicensePermitNumber>
                            <LicensedDt>1984-06-15</LicensedDt>
                            <DriversLicenseNumber isMasked="true">I0454********</DriversLicenseNumber>
                            <StateProvCd>IL</StateProvCd>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>LUBABA WOTTON</CommercialName>
                            </CommlName>
                            <PersonName>
                                <Surname>BACA</Surname>
                                <GivenName>SHANG</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxId>A58-E3-6557</TaxId>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <DriversLicense>
                            <LicensedDt>1973-05-12</LicensedDt>
                            <DriversLicenseNumber isMasked="true">C4536********</DriversLicenseNumber>
                        </DriversLicense>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt isMasked="true">1957-**-**</BirthDt>
                            <MaritalStatusCd>W</MaritalStatusCd>
                            <OccupationDesc>Retired</OccupationDesc>
                            <EducationLevelCd>Doctorate</EducationLevelCd>
                        </PersonInfo>
                        <License>
                            <LicensePermitNumber isMasked="true">C4536********</LicensePermitNumber>
                            <LicensedDt>1973-05-12</LicensedDt>
                            <DriversLicenseNumber isMasked="true">A1818********</DriversLicenseNumber>
                            <StateProvCd>CA</StateProvCd>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>0</DistantStudentInd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="L1" id="1">
                    <Manufacturer>LEXS</Manufacturer>
                    <Model>UX Series</Model>
                    <ModelYear>2023</ModelYear>
                    <VehBodyTypeCd>PP</VehBodyTypeCd>
                    <VehBodyTypeDesc>HCHBK 4D</VehBodyTypeDesc>
                    <VehLength>
                        <NumUnits>177.00</NumUnits>
                        <UnitMeasurementCd>inches</UnitMeasurementCd>
                    </VehLength>
                    <Width>
                        <NumUnits>72.40</NumUnits>
                        <UnitMeasurementCd>inches</UnitMeasurementCd>
                    </Width>
                    <AntiTheftDeviceInfo>
                        <AntiTheftDeviceCd>Audible Alarm</AntiTheftDeviceCd>
                    </AntiTheftDeviceInfo>
                    <AntiTheftDeviceInfo>
                        <AntiTheftDeviceCd>Passive Disabling</AntiTheftDeviceCd>
                    </AntiTheftDeviceInfo>
                    <CostNewAmt>
                        <Amt>46324.00</Amt>
                        <CurCd>USD</CurCd>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek>4</NumDaysDrivenPerWeek>
                    <EstimatedAnnualDistance>
                        <NumUnits>11400</NumUnits>
                        <UnitMeasurementCd>miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt>
                        <Amt>4612.75</Amt>
                    </FullTermAmt>
                    <Displacement>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>cc</UnitMeasurementCd>
                    </Displacement>
                    <Horsepower>
                        <NumUnits>181</NumUnits>
                        <UnitMeasurementCd>HP</UnitMeasurementCd>
                    </Horsepower>
                    <LeasedVehInd>0</LeasedVehInd>
                    <NumCylinders>4</NumCylinders>
                    <PurchaseDt>2022-12-10</PurchaseDt>
                    <VehIdentificationNumber>JTHX6JBH1P2141274</VehIdentificationNumber>
                    <Color>10</Color>
                    <GrossVehWeight>
                        <NumUnits>3483</NumUnits>
                        <UnitMeasurementCd>lbs</UnitMeasurementCd>
                    </GrossVehWeight>
                    <AntiLockBrakeCd>None</AntiLockBrakeCd>
                    <DaytimeRunningLightInd>0</DaytimeRunningLightInd>
                    <DistanceOneWay>
                        <NumUnits>16</NumUnits>
                        <UnitMeasurementCd>miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <MultiCarDiscountInd>0</MultiCarDiscountInd>
                    <NewVehInd>1</NewVehInd>
                    <NonOwnedVehInd>0</NonOwnedVehInd>
                    <OdometerReading>
                        <NumUnits>4953</NumUnits>
                        <UnitMeasurementCd>miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>DW</VehUseCd>
                    <Coverage bookTransferAssociatedNodeId="1" btId="d7d86a78-0c04-40e0-86cf-f30450b836e5">
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc>Property Damage-Single Limit</CoverageDesc>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="1" btId="ab85ba76-198a-4b3d-b65f-1dd73e0c1ca5">
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc>Liability Limit</CoverageDesc>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>1322.00</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="1" btId="dada5790-5811-4e3e-a9a3-772cf367c4b1">
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc>Comprehensive Deductible</CoverageDesc>
                        <Deductible bookTransferAssociatedNodeId="1" btId="ec95d03e-2cf6-4f06-b423-365f66bfa7e3">
                            <FormatInteger>500</FormatInteger>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>G</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt>392.00</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="1" btId="464900dd-304d-43c2-b680-0a49c74b8a70">
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc>Collision Deductible</CoverageDesc>
                        <Deductible bookTransferAssociatedNodeId="1" btId="b727f4d3-daba-4ec9-826e-553e6fd68770">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>2570.00</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="1" btId="45212abd-fec7-4225-ac65-9aa27f170af4">
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc>Med Pay Limit</CoverageDesc>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>33.00</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="1" btId="dbbf5d96-91a6-4be3-9253-17c96b19602e">
                        <CoverageCd>RR</CoverageCd>
                        <CoverageDesc>Better Car Replacement</CoverageDesc>
                        <Limit>
                            <FormatText>No</FormatText>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>0.00</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="1" btId="9157aad1-5306-4a98-acde-46b8487c6f1f">
                        <CoverageCd>EMPG</CoverageCd>
                        <CoverageDesc>Employee Parking Guard</CoverageDesc>
                        <Limit>
                            <FormatText>No</FormatText>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>0.00</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="1" btId="b5500acb-e6ef-4cc9-9a7f-29786843a48e">
                        <CoverageCd>EXMED</CoverageCd>
                        <CoverageDesc>Excess Med Pay Indicator</CoverageDesc>
                        <Limit>
                            <FormatText>No</FormatText>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>0.00</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="1" btId="9d2c649d-d05a-40a0-9297-2b455004e05e">
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc>Uninsured Limit</CoverageDesc>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>294.00</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="1" btId="2ab3e731-ce0b-4a6f-a64a-e49255eeba7d">
                        <CoverageCd>CWAIV</CoverageCd>
                        <CoverageDesc>Waiver of Coll Ded Indicator</CoverageDesc>
                        <Limit>
                            <FormatText>Yes</FormatText>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>0.00</Amt>
                            <CurCd>USD</CurCd>
                        </CurrentTermAmt>
                    </Coverage>
                    <OdometerReadingAsOfDt>2023-11-11</OdometerReadingAsOfDt>
                    <CollisionSymbolCd>39</CollisionSymbolCd>
                    <ComprehensiveOTCSymbolCd>44</ComprehensiveOTCSymbolCd>
                    <com.Safeco_BrandedTitleInd>0</com.Safeco_BrandedTitleInd>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="L1">
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>6277 IVARENE AVE </Addr1>
                    <City>LOS ANGELES</City>
                    <StateProvCd>CA</StateProvCd>
                    <PostalCode>90068</PostalCode>
                </Addr>
            </Location>
            <com.Safeco_MigrationInfo>
				<com.safeco_PaymentLookupStatus>Payment data does not exist</com.safeco_PaymentLookupStatus>
                <CustomerId>3602626353864</CustomerId>
                <EnterpriseId>********</EnterpriseId>
                <LibertyAffinityNumber>136588</LibertyAffinityNumber>
                <LibertyRepNumber>03S1</LibertyRepNumber>
                <LibertyAgreementId>3952941404353</LibertyAgreementId>
                <CombinedLibertyRepAffinityForCompensation>03S1 ******** 136588</CombinedLibertyRepAffinityForCompensation>
                <ReferralSourceCode>005</ReferralSourceCode>
                <RepOfficeCd>0493</RepOfficeCd>
                <com.safeco_InstrumentId>118753197</com.safeco_InstrumentId>
            </com.Safeco_MigrationInfo>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
<ACORD>
    <SignonRq>
        <ClientDt>2016-04-27</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>BookSmart</Org>
            <Name>AgencyPort</Name>
        </ClientApp>
        <SignonTransport>
            <SignonRoleCd>Agent</SignonRoleCd>
            <CustId>
                <SPName>com.agencyport</SPName>
                <CustLoginId>default</CustLoginId>
            </CustId>
        </SignonTransport>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>6F6ECD35BA124E68A42C4FF485D8EB1A</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <RqUID>f7b053d2-c2b3-4c5b-8d55-2c6b92a57777</RqUID>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ProducerSubCode>BUB</ProducerSubCode>
                    <ContractNumber>431683</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>M3DJZ33</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName/>
                        </CommlName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>***********</TaxId>
                        </TaxIdentity>
                        <PersonName>
                            <GivenName>Insured</GivenName>
                            <Surname>Unknown</Surname>
                        </PersonName>
                    </NameInfo>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-5856961</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                        </EmailInfo>
                    </Communications>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>22045 SW COLE CT</Addr1>
                        <City>TUALATIN</City>
                        <StateProvCd>OR</StateProvCd>
                        <PostalCode>97062-7038</PostalCode>
                    </Addr>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <GenderCd>F</GenderCd>
                        <BirthDt>1961-04-27</BirthDt>
                        <MaritalStatusCd>M</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <LOBCd>AUTO</LOBCd>
                <com.safeco_Esign>N</com.safeco_Esign>
                <ContractTerm>
                    <EffectiveDt>2017-05-29</EffectiveDt>
                    <ExpirationDt></ExpirationDt>
                </ContractTerm>
                <DurationPeriod>
                    <NumUnits>12</NumUnits>
                    <UnitMeasurementCd>Months</UnitMeasurementCd>
                </DurationPeriod>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>AIG</InsurerName>
                    <LOBCd>AUTO</LOBCd>
                    <ContractTerm>
                        <ExpirationDt>2016-08-10</ExpirationDt>
                    </ContractTerm>
                    <LengthTimeWithPreviousInsurer>
                        <NumUnits>96</NumUnits>
                        <UnitMeasurementCd>Months</UnitMeasurementCd>
                    </LengthTimeWithPreviousInsurer>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Other</PolicyCd>
                    <LOBCd>com.safeco_Renter</LOBCd>
                    <PolicyNumber>OD11111</PolicyNumber>
                </OtherOrPriorPolicy>
                <QuoteInfo>
                    <CompanysQuoteNumber/>
                    <ItemIdInfo>
                        <com.safeco_InsuredIPAddress>***********</com.safeco_InsuredIPAddress>
                    </ItemIdInfo>
                </QuoteInfo>
                <CreditScoreInfo>
                    <CreditScore>690</CreditScore>
                    <CreditScoreDt>2014-07-21</CreditScoreDt>
                </CreditScoreInfo>
                <QuestionAnswer>
                    <QuestionCd>Current_Insurance_Value</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>CI</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Cancelled_Declined?</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Excluded_Vehicle_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>All_Vehicles_Covered_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Credit_Accept_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Accept_Contract_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>002</Explanation>
                </QuestionAnswer>
                <QuoteInfo>
                    <CompanysQuoteNumber/>
                </QuoteInfo>
                <PersApplicationInfo>
                    <InsuredOrPrincipal>
                        <GeneralPartyInfo>
                            <Addr>
                                <AddrTypeCd>StreetAddress</AddrTypeCd>
                                <Addr1>5495 SW Watson Ave</Addr1>
                                <City>Beaverton</City>
                                <StateProvCd>OR</StateProvCd>
                                <PostalCode>97005</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                    </InsuredOrPrincipal>
                    <ResidenceOwnedRentedCd>RENTD</ResidenceOwnedRentedCd>
                    <NumResidentsInHousehold>2</NumResidentsInHousehold>
                    <NumVehsInHousehold>2</NumVehsInHousehold>
                    <ResidenceTypeCd>APT</ResidenceTypeCd>
                </PersApplicationInfo>
                <DriverVeh DriverRef="Drv1" VehRef="Veh1">
                    <UsePct>70</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="Drv2" VehRef="Veh1">
                    <UsePct>30</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="Drv2" VehRef="Veh2">
                    <UsePct>70</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="Drv1" VehRef="Veh2">
                    <UsePct>30</UsePct>
                </DriverVeh>
                <CurrentTermAmt>
                    <Amt>10917.99</Amt>
                </CurrentTermAmt>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTO</LOBCd>
                <PersDriver id="Drv1">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>PURCHETTE</Surname>
                                <GivenName>BOBBY</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt></BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <com.safeco_IndustryCd>OTHER</com.safeco_IndustryCd>
                            <com.safeco_OccupationCd>Other</com.safeco_OccupationCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>1977-04-27</LicensedDt>
                            <DriversLicenseNumber></DriversLicenseNumber>
                            <StateProvCd>IA</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>JC</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="Drv2">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Rubble</Surname>
                                <GivenName>Betty</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1957-03-30</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>1973-03-30</LicensedDt>
                            <DriversLicenseNumber>7777889</DriversLicenseNumber>
                            <StateProvCd>OR</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>SC</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV3">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>John</Surname>
                                <GivenName>Anna</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1961-07-08</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc/>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>*********</DriversLicenseNumber>
                            <StateProvCd>CT</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber>*********</LicensePermitNumber>
                            <StateProvCd>CT</StateProvCd>
                            <TotalNumLicensePoints/>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>0</MatureDriverInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV4">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Franklin</Surname>
                                <GivenName>Anna</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1965-07-10</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc/>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>*********</DriversLicenseNumber>
                            <StateProvCd>CT</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber>*********</LicensePermitNumber>
                            <StateProvCd>CT</StateProvCd>
                            <TotalNumLicensePoints/>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>0</MatureDriverInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV5">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Bob</Surname>
                                <GivenName>Peter</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1964-07-10</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc/>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>*********</DriversLicenseNumber>
                            <StateProvCd>CT</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber>*********</LicensePermitNumber>
                            <StateProvCd>CT</StateProvCd>
                            <TotalNumLicensePoints/>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>0</MatureDriverInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV6">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Anitha</Surname>
                                <GivenName>R</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1966-07-10</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc/>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>*********</DriversLicenseNumber>
                            <StateProvCd>CT</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber>*********</LicensePermitNumber>
                            <StateProvCd>CT</StateProvCd>
                            <TotalNumLicensePoints/>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>0</MatureDriverInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV7">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Magna</Surname>
                                <GivenName>T</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1960-07-10</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc/>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>*********</DriversLicenseNumber>
                            <StateProvCd>CT</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber>*********</LicensePermitNumber>
                            <StateProvCd>CT</StateProvCd>
                            <TotalNumLicensePoints/>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>0</MatureDriverInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV8">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Misra</Surname>
                                <GivenName>Sauda</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1961-07-10</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc/>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>*********</DriversLicenseNumber>
                            <StateProvCd>CT</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber>*********</LicensePermitNumber>
                            <StateProvCd>CT</StateProvCd>
                            <TotalNumLicensePoints/>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>0</MatureDriverInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV9">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Shan</Surname>
                                <GivenName>David</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1964-10-10</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc/>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>*********</DriversLicenseNumber>
                            <StateProvCd>CT</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber>*********</LicensePermitNumber>
                            <StateProvCd>CT</StateProvCd>
                            <TotalNumLicensePoints/>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>0</MatureDriverInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="LOC1" id="VEH17">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CIVIC QX</Model>
                    <ModelYear>2001</ModelYear>
                    <VehBodyTypeCd>PP</VehBodyTypeCd>
                    <VehBodyTypeDesc/>
                    <Registration>
                        <RegistrationTypeCd>LicensePlate</RegistrationTypeCd>
                        <RegistrationId/>
                        <StateProvCd>CT</StateProvCd>
                    </Registration>
                    <POLKRestraintDeviceCd>N</POLKRestraintDeviceCd>
                    <CostNewAmt>
                        <Amt/>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek/>
                    <EstimatedAnnualDistance>
                        <NumUnits>10000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt>
                        <Amt></Amt>
                    </FullTermAmt>
                    <Horsepower>
                        <NumUnits/>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <TerritoryCd>1130</TerritoryCd>
                    <VehIdentificationNumber></VehIdentificationNumber>
                    <VehSymbolCd>15</VehSymbolCd>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>WELLS FARGO DEALER SERVICES</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <AddrTypeCd>MailingAddress</AddrTypeCd>
                                <Addr1>Po Box 997517</Addr1>
                                <Addr2/>
                                <Addr3/>
                                <City>Sacramento</City>
                                <StateProvCd>CA</StateProvCd>
                                <PostalCode>95899</PostalCode>
                                <CountryCd>USA</CountryCd>
                                <County/>
                            </Addr>
                            <Communications>
                                <PhoneInfo>
                                    <PhoneTypeCd>Phone</PhoneTypeCd>
                                </PhoneInfo>
                                <EmailInfo>
                                    <EmailAddr/>
                                </EmailInfo>
                            </Communications>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LOSSP</NatureInterestCd>
                            <InterestRank>1</InterestRank>
                            <InterestIdNumber/>
                            <FinancedAmt>
                                <Amt/>
                            </FinancedAmt>
                            <ReasonDesc/>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <GrossVehWeight>
                        <NumUnits/>
                        <UnitMeasurementCd>LBR</UnitMeasurementCd>
                    </GrossVehWeight>
                    <CarpoolInd>0</CarpoolInd>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <MultiCarDiscountInd>1</MultiCarDiscountInd>
                    <LengthTimePerMonth>
                        <NumUnits/>
                        <UnitMeasurementCd>WEE</UnitMeasurementCd>
                    </LengthTimePerMonth>
                    <OdometerReading>
                        <NumUnits/>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <RateClassCd/>
                    <VehSalvageTitleNumber/>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>Y</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>46.00</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RREIM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>30</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>900</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>MaxAmount</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>12.00</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>177.00</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>G</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt>64.00</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>31.00</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>78.00</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>173.00</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ABS</CoverageCd>
                        <CoverageDesc>Anti-lock brake discount</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ACCT</CoverageCd>
                        <CoverageDesc>Account Credit</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PASSR</CoverageCd>
                        <CoverageDesc>Passive Restraint</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <CollisionSymbolCd/>
                    <ComprehensiveOTCSymbolCd/>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="LOC1">
                <ItemIdInfo>
                    <AgencyId>1</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>MailingAddress</AddrTypeCd>
                    <Addr1>22045 SW COLE CT</Addr1>
                    <City>TUALATIN</City>
                    <StateProvCd>OR</StateProvCd>
                    <PostalCode>97062-7038</PostalCode>
                    <Addr2/>
                </Addr>
            </Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
    <com.BookTransfer_errors>
        <Common>
            <Applicant>Missing</Applicant>
        </Common>
    </com.BookTransfer_errors>
</ACORD>
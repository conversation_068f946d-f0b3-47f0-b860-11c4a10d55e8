<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<ACORD>
  <InsuranceSvcRq>
    <RqUID>179f23d8-6c1f-44f3-9a61-6955cca4cbee</RqUID>
    <CommlInlandMarinePolicyQuoteInqRq>
      <BusinessPurposeTypeCd>RWL</BusinessPurposeTypeCd>
      <ItemIdInfo>
        <OtherIdentifier>
          <UID>7f50a659-b6ab-43b9-8230-e8ec60cbb1ca</UID>
        </OtherIdentifier>
      </ItemIdInfo>
      <TransactionRequestDt>2024-07-18T07:26:23.235-06:00</TransactionRequestDt>
      <TransactionEffectiveDt>2024-05-21</TransactionEffectiveDt>
      <Producer>
        <ContractNumber>G6982</ContractNumber>
        <ProducerSubCode>0000858</ProducerSubCode>
        <ProducerRoleCd>PROD</ProducerRoleCd>
      </Producer>
      <InsuredOrPrincipal>
        <Name>ALFRED W. DIORIO, RLS, INC. C/O ALFRED W. DIORIO</Name>
        <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>PO BOX 999</Addr1>
          <City>ASHAWAY</City>
          <StateProvCd>RI</StateProvCd>
          <PostalCode>*********</PostalCode>
          <CountryCd>US</CountryCd>
        </Addr>
        <LegalEntityCd>CP</LegalEntityCd>
        <SICCd>8713</SICCd>
        <NAICSCd>541370</NAICSCd>
        <InsuredOrPrincipalRoleCd>IN</InsuredOrPrincipalRoleCd>
      </InsuredOrPrincipal>
      <Policy>
        <PolicyNumber>0C79209</PolicyNumber>
        <CompanyProductCd>EMCC</CompanyProductCd>
        <LOBCd>INMRC</LOBCd>
        <NAICCd>21415</NAICCd>
        <ContractTerm>
          <EffectiveDt>2024-05-21</EffectiveDt>
          <ExpirationDt>2025-05-21</ExpirationDt>
        </ContractTerm>
        <BillingMethodCd>CPB</BillingMethodCd>
        <CurrentTermAmt>
          <Amt>1157</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>1157</Amt>
        </NetChangeAmt>
        <RateEffectiveDt>2024-05-21</RateEffectiveDt>
        <Form>
          <FormNumber>CL0100</FormNumber>
          <FormName>COMMON POLICY CONDITIONS AAIS</FormName>
          <EditionDt>1999-03-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CL0166</FormNumber>
          <FormName>AMENDATORY ENDORSEMENT RHODE ISLAND AAIS</FormName>
          <EditionDt>2013-09-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CL0600</FormNumber>
          <FormName>CERTIFIED TERRORISM LOSS</FormName>
          <EditionDt>2015-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CL0700</FormNumber>
          <FormName>VIRUS OR BACTERIA EXCLUSION</FormName>
          <EditionDt>2006-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CM7001A</FormNumber>
          <FormName>COMMERCIAL INLAND MARINE SCHEDULE</FormName>
          <EditionDt>1997-09-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CM7004</FormNumber>
          <FormName>QUICK REFERENCE AAIS</FormName>
          <EditionDt>2006-09-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CM7021</FormNumber>
          <FormName>LOSS PAYABLE ENDORSEMENT AAIS AND ISO</FormName>
          <EditionDt>2001-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CM8068</FormNumber>
          <FormName>ADVISORY NOTICE TO POLICYHOLDERS</FormName>
          <EditionDt>2019-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CM9905</FormNumber>
          <FormName>CANNABIS EXCLUSION</FormName>
          <EditionDt>2019-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL0017</FormNumber>
          <FormName>COMMON POLICY CONDITIONS</FormName>
          <EditionDt>1998-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL7004</FormNumber>
          <FormName>MUTUAL POLICY PROVISIONS</FormName>
          <EditionDt>2024-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL7131A</FormNumber>
          <FormName>COMM'L POLICY ENDORSEMENT SCHEDULE</FormName>
          <EditionDt>2001-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL7306</FormNumber>
          <FormName>EXCLUSION OF CERTAIN COMPUTER LOSSES</FormName>
          <EditionDt>1998-08-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL7600</FormNumber>
          <FormName>RHODE ISLAND COMPANY ELIMINATION</FormName>
          <EditionDt>2019-05-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL8383.2A</FormNumber>
          <FormName>DISCL PURSUANT TERRSM RISK INS. ACT</FormName>
          <EditionDt>2020-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IM2081</FormNumber>
          <FormName>AMENDATORY ENDORSEMENT- RHODE ISLAND</FormName>
          <EditionDt>2004-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IM7000</FormNumber>
          <FormName>CONTRACTOR'S EQUIPMENT COVERAGE</FormName>
          <EditionDt>2004-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <OtherOrPriorPolicy>
          <PolicyCd>Prior</PolicyCd>
          <PolicyNumber>0C79209</PolicyNumber>
          <LOBCd>INMRC</LOBCd>
          <NAICCd>21415</NAICCd>
          <NameInfo>
            <CommercialName>
              <CommlName>EMCC</CommlName>
            </CommercialName>
          </NameInfo>
        </OtherOrPriorPolicy>
        <QuestionAnswer>
          <QuestionCd>GENRL47</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <AuditInd>0</AuditInd>
      </Policy>
      <Location id="L0001">
        <ItemIdInfo>
          <InsurerId>1</InsurerId>
        </ItemIdInfo>
        <Addr>
          <AddrTypeCd>PhysicalRisk</AddrTypeCd>
          <Addr1>264 WOODVILLE RD</Addr1>
          <City>ASHAWAY</City>
          <StateProvCd>RI</StateProvCd>
          <PostalCode>028042901</PostalCode>
          <Country>United States</Country>
        </Addr>
        <CountyTownCd>050</CountyTownCd>
        <SubLocation id="L0001S01">
          <ItemIdInfo>
            <InsurerId>01</InsurerId>
          </ItemIdInfo>
        </SubLocation>
      </Location>
      <CommlInlandMarineLineBusiness>
        <LOBCd>INMRC</LOBCd>
        <CommlIMInfo id="001" LocationRef="L0001" SubLocationRef="L0001S01">
          <CommlIMClassCd>CONEQ</CommlIMClassCd>
          <ScheduledInd>1</ScheduledInd>
          <PropertyScheduleModifications>
            <NumUnits>4</NumUnits>
          </PropertyScheduleModifications>
          <Coverage>
            <CoverageCd>CONEQ</CoverageCd>
            <CoverageDesc>Contractors Equipment</CoverageDesc>
            <Limit>
              <FormatInteger>68052</FormatInteger>
              <LimitAppliesToCd>EachOccurrence</LimitAppliesToCd>
            </Limit>
            <Deductible>
              <FormatInteger>250</FormatInteger>
              <DeductibleAppliesToCd>PerOcc</DeductibleAppliesToCd>
            </Deductible>
            <CurrentTermAmt>
              <Amt>1157</Amt>
            </CurrentTermAmt>
            <CoinsurancePct>80</CoinsurancePct>
          </Coverage>
          <Coverage>
            <CoverageCd>TOBE</CoverageCd>
            <CoverageDesc>Employee Tools</CoverageDesc>
            <Limit>
              <FormatInteger>5000</FormatInteger>
              <ValuationCd>ACV</ValuationCd>
            </Limit>
          </Coverage>
          <Coverage>
            <CoverageCd>SPARE</CoverageCd>
            <CoverageDesc>Spare Parts and Fuel</CoverageDesc>
            <Limit>
              <FormatInteger>5000</FormatInteger>
            </Limit>
          </Coverage>
          <Coverage>
            <CoverageCd>RREIM</CoverageCd>
            <CoverageDesc>Rental Reimbursement</CoverageDesc>
            <Limit>
              <FormatInteger>5000</FormatInteger>
            </Limit>
          </Coverage>
          <Coverage>
            <CoverageCd>CNPOL</CoverageCd>
            <CoverageDesc>Pollutant Cleanup and Removal</CoverageDesc>
            <Limit>
              <FormatInteger>25000</FormatInteger>
            </Limit>
          </Coverage>
          <Coverage>
            <CoverageCd>NABPP</CoverageCd>
            <CoverageDesc>Newly Purchased Equipment</CoverageDesc>
          </Coverage>
          <Coverage>
            <CoverageCd>DEBRL</CoverageCd>
            <CoverageDesc>Additional Debris Removal Expenses</CoverageDesc>
            <Limit>
              <FormatInteger>5000</FormatInteger>
            </Limit>
          </Coverage>
          <PropertyItem>
            <ItemDefinition>
              <ItemIdInfo>
                <InsurerId>1</InsurerId>
              </ItemIdInfo>
              <ItemDesc>MULTISTATION INSTRUMENT KIT LEICA</ItemDesc>
            </ItemDefinition>
            <AdditionalInterest>
              <Name>First Western Bank &amp; Trust</Name>
              <Addr>
                <AddrTypeCd>MailingAddress</AddrTypeCd>
                <Addr1>100 Prairie Center Dr Ste 100</Addr1>
                <City>Eden Prairie</City>
                <StateProvCd>MN</StateProvCd>
                <PostalCode>*********</PostalCode>
              </Addr>
              <NatureInterestCd>LOSSP</NatureInterestCd>
              <InterestIdNumber>1</InterestIdNumber>
            </AdditionalInterest>
            <ValueInfo>
              <ValuationCd>RC</ValuationCd>
            </ValueInfo>
            <InsuranceAmt>
              <Amt>49805</Amt>
            </InsuranceAmt>
          </PropertyItem>
          <PropertyItem>
            <ItemDefinition>
              <ItemIdInfo>
                <InsurerId>2</InsurerId>
              </ItemIdInfo>
              <ItemDesc>3.5G FIELD CONTROLLER W/ COMM CAP LEICA</ItemDesc>
            </ItemDefinition>
            <ValueInfo>
              <ValuationCd>RC</ValuationCd>
            </ValueInfo>
            <InsuranceAmt>
              <Amt>7380</Amt>
            </InsuranceAmt>
          </PropertyItem>
          <PropertyItem>
            <ItemDefinition>
              <ItemIdInfo>
                <InsurerId>3</InsurerId>
              </ItemIdInfo>
              <ItemDesc>ROBOTIC ACCESSORY KIT W/ HARD CASE</ItemDesc>
            </ItemDefinition>
            <ValueInfo>
              <ValuationCd>RC</ValuationCd>
            </ValueInfo>
            <InsuranceAmt>
              <Amt>1400</Amt>
            </InsuranceAmt>
          </PropertyItem>
          <PropertyItem>
            <ItemDefinition>
              <ItemIdInfo>
                <InsurerId>4</InsurerId>
              </ItemIdInfo>
              <ItemDesc>PERF SMART ANTENNA GPS 3.75G MODEM/UHF</ItemDesc>
            </ItemDefinition>
            <ValueInfo>
              <ValuationCd>RC</ValuationCd>
            </ValueInfo>
            <InsuranceAmt>
              <Amt>9467</Amt>
            </InsuranceAmt>
          </PropertyItem>
        </CommlIMInfo>
        <CommlIMInfo id="002" LocationRef="L0001" SubLocationRef="L0001S01">
          <CommlIMClassCd>CONEQ</CommlIMClassCd>
          <CommlIMSubClassCd>LRFO</CommlIMSubClassCd>
          <ScheduledInd>0</ScheduledInd>
          <Coverage>
            <CoverageCd>CONEQ</CoverageCd>
            <CoverageDesc>Contr. Equip - Leased or Rented From Others</CoverageDesc>
            <Limit>
              <FormatInteger>25000</FormatInteger>
              <LimitAppliesToCd>ML</LimitAppliesToCd>
              <ValuationCd>RC</ValuationCd>
            </Limit>
            <Limit>
              <FormatInteger>25000</FormatInteger>
              <LimitAppliesToCd>EachOccurrence</LimitAppliesToCd>
              <ValuationCd>RC</ValuationCd>
            </Limit>
            <Deductible>
              <FormatInteger>250</FormatInteger>
            </Deductible>
          </Coverage>
        </CommlIMInfo>
      </CommlInlandMarineLineBusiness>
      <RemarkText>5BPI Occ 1 - TOTAL POLICY PREMIUM IS 1157.00</RemarkText>
      <RemarkText>6IMB Occ 1 - Loc 0001 CONEQ - 801 - Item 001 Description: MULTISTATION INSTRUMENT KIT LEICA6008279</RemarkText>
      <RemarkText>6IMB Occ 2 - Loc 0001 CONEQ - 801 - Item 002 Description: 3.5G FIELD CONTROLLER W/ COMM CAP LEICA6008278 CS15 367661</RemarkText>
      <RemarkText>6IMB Occ 3 - Loc 0001 CONEQ - 801 - Item 003 Description: ROBOTIC ACCESSORY KIT W/ HARD CASELEICA 6007429 503681</RemarkText>
      <RemarkText>6IMB Occ 4 - Loc 0001 CONEQ - 801 - Item 004 Description: PERF SMART ANTENNA GPS 3.75G MODEM/UHFLEICA 6008450 GS14 2804622</RemarkText>
      <RemarkText>5CVG Occ 4 - Loc 0001 CONEQ - 801 - RREIM: WAITING PERIOD (NUMBER OF HOURS): 72</RemarkText>
      <RemarkText>5CVG Occ 6 - Loc 0001 CONEQ - 801 - NEW: Percentage of Catastrophe Limit: 30</RemarkText>
    </CommlInlandMarinePolicyQuoteInqRq>
    <FarmPolicyQuoteInqRq>
      <RqUID>f80d4dda-6cff-4f50-b709-73e66d2f1501</RqUID>
      <BusinessPurposeTypeCd>NBQ</BusinessPurposeTypeCd>
      <TransactionRequestDt>2028-02-12</TransactionRequestDt>
      <TransactionEffectiveDt>2025-04-18T00:01:00-04:00</TransactionEffectiveDt>
      <Producer>
        <ContractNumber>9895663</ContractNumber>
        <ProducerSubCode>9895663</ProducerSubCode>
        <ProducerRoleCd>IN</ProducerRoleCd>
        <GeneralPartyInfo>
          <NameInfo>
            <CommlName>
              <CommercialName>Mitchelle Stark</CommercialName>
            </CommlName>
          </NameInfo>
        </GeneralPartyInfo>
      </Producer>
      <InsuredOrPrincipal>
        <GeneralPartyInfo>
          <NameInfo>
            <TaxIdentity>
              <TaxId>32-2312345</TaxId>
              <TaxIdTypeCd>FEIN</TaxIdTypeCd>
            </TaxIdentity>
            <CommlName>
              <CommercialName>TEST AUTOPOLICY1568297</CommercialName>
            </CommlName>
          </NameInfo>
        </GeneralPartyInfo>
        <ItemIdInfo>
          <InsurerId>1618433</InsurerId>
        </ItemIdInfo>
        <InsuredOrPrincipalRoleCd>PrimaryContact</InsuredOrPrincipalRoleCd>
        <LegalEntityCd>LL</LegalEntityCd>
        <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>2500 E VAN BUREN ST</Addr1>
          <City>PHOENIX</City>
          <StateProvCd>AZ</StateProvCd>
          <PostalCode>85008-6037</PostalCode>
          <County>MARICOPA</County>
        </Addr>
        <Communications>
          <EmailInfo>
            <EmailAddr><EMAIL></EmailAddr>
          </EmailInfo>
          <PhoneInfo>
            <PhoneNumber>+************</PhoneNumber>
            <PhoneTypeCd>Phone</PhoneTypeCd>
          </PhoneInfo>
        </Communications>
        <BusinessStartDt>2020-04-16</BusinessStartDt>
        <NAICSCd>25135</NAICSCd>
        <OperationsDesc>Demo</OperationsDesc>
      </InsuredOrPrincipal>
      <InsuredOrPrincipal>
        <GeneralPartyInfo>
          <NameInfo>
            <CommlName>
              <CommercialName>TEST USER 1</CommercialName>
            </CommlName>
          </NameInfo>
        </GeneralPartyInfo>
        <ItemIdInfo>
          <InsurerId>pc:1352</InsurerId>
        </ItemIdInfo>
        <InsuredOrPrincipalRoleCd>AINT</InsuredOrPrincipalRoleCd>
        <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>518 E BROAD ST</Addr1>
          <City>COLUMBUS</City>
          <StateProvCd>OH</StateProvCd>
          <PostalCode>43215-3901</PostalCode>
          <County>FRANKLIN</County>
        </Addr>
        <Communications>
          <EmailInfo>
            <EmailAddr><EMAIL></EmailAddr>
          </EmailInfo>
          <PhoneInfo>
            <PhoneNumber>+************</PhoneNumber>
            <PhoneTypeCd>Phone</PhoneTypeCd>
          </PhoneInfo>
        </Communications>
        <BusinessStartDt>2020-04-16</BusinessStartDt>
        <NAICSCd>25135</NAICSCd>
        <OperationsDesc>Demo</OperationsDesc>
      </InsuredOrPrincipal>
      <InsuredOrPrincipal>
        <GeneralPartyInfo>
          <NameInfo>
            <TaxIdentity>
              <TaxId>32-2312345</TaxId>
              <TaxIdTypeCd>FEIN</TaxIdTypeCd>
            </TaxIdentity>
            <CommlName>
              <CommercialName>TEST AUTOPOLICY1568297</CommercialName>
            </CommlName>
          </NameInfo>
        </GeneralPartyInfo>
        <ItemIdInfo>
          <InsurerId>1618433</InsurerId>
        </ItemIdInfo>
        <InsuredOrPrincipalRoleCd>BC</InsuredOrPrincipalRoleCd>
        <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>2500 E VAN BUREN ST</Addr1>
          <City>PHOENIX</City>
          <StateProvCd>AZ</StateProvCd>
          <PostalCode>85008-6037</PostalCode>
          <County>MARICOPA</County>
        </Addr>
        <Communications>
          <EmailInfo>
            <EmailAddr><EMAIL></EmailAddr>
          </EmailInfo>
          <PhoneInfo>
            <PhoneNumber>+************</PhoneNumber>
            <PhoneTypeCd>Phone</PhoneTypeCd>
          </PhoneInfo>
        </Communications>
        <BusinessStartDt>2020-04-16</BusinessStartDt>
        <NAICSCd>25135</NAICSCd>
        <OperationsDesc>Demo</OperationsDesc>
      </InsuredOrPrincipal>
      <Policy>
        <PolicyNumber>10695882FA</PolicyNumber>
        <RateEffectiveDt>2028-02-12</RateEffectiveDt>
        <NAICCd>25135</NAICCd>
        <ControllingStateProvCd>AZ</ControllingStateProvCd>
        <ContractTerm>
          <EffectiveDt>2025-04-18</EffectiveDt>
          <ExpirationDt>2026-04-18</ExpirationDt>
        </ContractTerm>
        <FarmRanchTypeCd>LSTCK</FarmRanchTypeCd>
        <BillingAccountNumber>100779638C</BillingAccountNumber>
        <BillingMethodCd>CPB</BillingMethodCd>
        <CurrentTermAmt>
          <Amt>5253.00</Amt>
        </CurrentTermAmt>
        <CustomerServicingCd>AgcySvc</CustomerServicingCd>
        <LanguageCd>en</LanguageCd>
        <MailingResponsibiltyCd>CompResp</MailingResponsibiltyCd>
        <Form>
          <FormNumber>ACORD38AZ</FormNumber>
          <FormDesc>Arizona Notice of Information Practices (Privacy)</FormDesc>
          <EditionDt>2003-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>ACORD61AZ</FormNumber>
          <FormDesc>Arizona Auto Supplement Uninsured/Underinsured Motorists Coverage Selection Form</FormDesc>
          <EditionDt>2020-07-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>APP-FA</FormNumber>
          <FormDesc>Application - Farm Auto Policy</FormDesc>
          <EditionDt>2018-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>AU303</FormNumber>
          <FormDesc>Notice Of The Policies And Practices Of The Disclosure Of Nonpublic Personal Information</FormDesc>
          <EditionDt>1924-03-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>AZ-SOR</FormNumber>
          <FormDesc>Arizona Summary of Rights</FormDesc>
          <EditionDt>2016-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>BA50-AZ</FormNumber>
          <FormDesc>Insurance Identification Card</FormDesc>
          <EditionDt>2015-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>BA 10 24</FormNumber>
          <FormDesc>Comprehensive Coverage Deductible</FormDesc>
          <EditionDt>2015-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 00 01</FormNumber>
          <FormDesc>Business Auto Coverage Form</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 01 75</FormNumber>
          <FormDesc>Arizona Changes</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 02 05</FormNumber>
          <FormDesc>Arizona Changes - Nonrenewal</FormDesc>
          <EditionDt>2014-05-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 20 01</FormNumber>
          <FormDesc>Lessor - Additional Insured And Loss Payee</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 20 33</FormNumber>
          <FormDesc>Autos Leased, Hired, Rented Or Borrowed With Drivers - Physical Damage Coverage</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 20 54</FormNumber>
          <FormDesc>Employee Hired Autos</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 20 55</FormNumber>
          <FormDesc>Fellow Employee Coverage</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 21 39</FormNumber>
          <FormDesc>Arizona Uninsured Motorists Coverage</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 21 40</FormNumber>
          <FormDesc>Arizona Underinsured Motorists Coverage</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 23 01</FormNumber>
          <FormDesc>Explosives</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 23 05</FormNumber>
          <FormDesc>Wrong Delivery Of Liquid Products</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 23 45</FormNumber>
          <FormDesc>Public Or Livery Passenger Conveyance And On-Demand Delivery Services Exclusion</FormDesc>
          <EditionDt>2016-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 23 84</FormNumber>
          <FormDesc>Exclusion Of Terrorism</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 23 94</FormNumber>
          <FormDesc>Silica Or Silica-Related Dust Exclusion For Covered Autos Exposure</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 99 03</FormNumber>
          <FormDesc>Auto Medical Payments Coverage</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 99 10</FormNumber>
          <FormDesc>Drive Other Car Coverage - Broadened Coverage For Named Individuals</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA 99 23</FormNumber>
          <FormDesc>Rental Reimbursement Coverage</FormDesc>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DECOV-FA</FormNumber>
          <FormDesc>Declarations - Farm Auto Policy</FormDesc>
          <EditionDt>2018-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>FA51</FormNumber>
          <FormDesc>Mobile Equipment Coverage</FormDesc>
          <EditionDt>2018-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>FR132</FormNumber>
          <FormDesc>Certification of Loss History</FormDesc>
          <EditionDt>2019-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>ILN014</FormNumber>
          <FormDesc>Arizona Fraud Statement</FormDesc>
          <EditionDt>2003-09-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL 00 03</FormNumber>
          <FormDesc>Calculation Of Premium</FormDesc>
          <EditionDt>2008-09-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL 00 17</FormNumber>
          <FormDesc>Common Policy Conditions</FormDesc>
          <EditionDt>1998-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL 00 21</FormNumber>
          <FormDesc>Nuclear Energy Liability Exclusion Endorsement (Broad Form)</FormDesc>
          <EditionDt>2008-09-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>QUOTE-FA</FormNumber>
          <FormDesc>Quote Proposal - Farm Auto Policy</FormDesc>
          <EditionDt>2018-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>SI 90 01</FormNumber>
          <FormDesc>Common Policy Jacket</FormDesc>
          <EditionDt>2022-05-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <OtherOrPriorPolicy>
          <PolicyCd>Prior</PolicyCd>
          <LOBCd>AUTOB</LOBCd>
          <NameInfo>
            <CommlName>
              <CommercialName>Allianz</CommercialName>
            </CommlName>
          </NameInfo>
        </OtherOrPriorPolicy>
        <PaymentOption>
          <NumPayments>12</NumPayments>
        </PaymentOption>
        <OriginalPolicyInceptionDt>2025-04-18</OriginalPolicyInceptionDt>
      </Policy>
      <Location id="L001">
        <Addr>
          <Addr1>2500 E VAN BUREN ST</Addr1>
          <City>PHOENIX</City>
          <StateProvCd>AZ</StateProvCd>
          <PostalCode>85008-6037</PostalCode>
          <County>MARICOPA</County>
        </Addr>
      </Location>
      <FarmLineBusiness>
        <LOBCd>CFRM</LOBCd>
        <NAICCd>25135</NAICCd>
        <Coverage>
          <CoverageCd>COLL</CoverageCd>
          <CoverageDesc>Covered Auto Liability Coverage</CoverageDesc>
          <Limit>
            <FormatInteger>1000000</FormatInteger>
          </Limit>
          <CurrentTermAmt>
            <Amt>3676.00</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageCd>HRDBD</CoverageCd>
          <CoverageDesc>Hired Auto Coverage</CoverageDesc>
        </Coverage>
        <Coverage>
          <CoverageDesc>Hired Auto Liability</CoverageDesc>
          <CurrentTermAmt>
            <Amt>75.00</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageCd>NOWND</CoverageCd>
          <CoverageDesc>Non-Owned Auto Coverage For Other Than Garage Risks</CoverageDesc>
          <CurrentTermAmt>
            <Amt>200.00</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageCd>AHIPD</CoverageCd>
          <CoverageDesc>Hired Auto Collision</CoverageDesc>
          <Deductible>
            <FormatInteger>1000</FormatInteger>
          </Deductible>
          <CurrentTermAmt>
            <Amt>18.00</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageCd>AHIPD</CoverageCd>
          <CoverageDesc>Hired Auto Other Than Collision</CoverageDesc>
          <Deductible>
            <FormatInteger>100</FormatInteger>
          </Deductible>
          <CurrentTermAmt>
            <Amt>13.00</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageCd>DOC</CoverageCd>
          <CoverageDesc>Drive Other Car (DOC) Broadened Cov for Named Individuals</CoverageDesc>
        </Coverage>
        <Coverage>
          <CoverageDesc>Drive Other Car (DOC) Liability</CoverageDesc>
          <Limit>
            <FormatInteger>1000000</FormatInteger>
          </Limit>
          <CurrentTermAmt>
            <Amt>150.00</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageDesc>Medical Payments (DOC)</CoverageDesc>
          <Limit>
            <FormatInteger>5000</FormatInteger>
          </Limit>
          <CurrentTermAmt>
            <Amt>6.00</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageCd>CPDOC</CoverageCd>
          <CoverageDesc>Other Than Collision (DOC)</CoverageDesc>
          <Deductible>
            <FormatInteger>1000</FormatInteger>
          </Deductible>
          <CurrentTermAmt>
            <Amt>18.00</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageDesc>Collision (DOC)</CoverageDesc>
          <Deductible>
            <FormatInteger>1000</FormatInteger>
          </Deductible>
          <CurrentTermAmt>
            <Amt>102.00</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageCd>AHIPH</CoverageCd>
          <CoverageDesc>Autos Leased, Hired, Rented Or Borrowed With Drivers - Physical Damage Coverage</CoverageDesc>
          <Limit>
            <FormatInteger>50000</FormatInteger>
          </Limit>
        </Coverage>
        <Coverage>
          <CoverageCd>AHIPD</CoverageCd>
          <CoverageDesc>Employee Hired Autos</CoverageDesc>
        </Coverage>
        <Coverage>
          <CoverageCd>FELIA</CoverageCd>
          <CoverageDesc>Fellow Employee</CoverageDesc>
        </Coverage>
        <Coverage>
          <CoverageDesc>Autos Leased, Hired, Rented Or Borrowed With Drivers - Physical Damage Coverage - Collision</CoverageDesc>
          <Deductible>
            <FormatInteger>1000</FormatInteger>
          </Deductible>
        </Coverage>
        <FarmLiabilityInfo>
          <Coverage>
            <CoverageCd>UMCSL</CoverageCd>
            <CoverageDesc>Uninsured Motorists Coverage</CoverageDesc>
            <Limit>
              <FormatInteger>1000000</FormatInteger>
            </Limit>
            <CurrentTermAmt>
              <Amt>112.00</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>UNCSL</CoverageCd>
            <CoverageDesc>Underinsured Motorists Coverage</CoverageDesc>
            <Limit>
              <FormatInteger>1000000</FormatInteger>
            </Limit>
            <CurrentTermAmt>
              <Amt>305.00</Amt>
            </CurrentTermAmt>
          </Coverage>
        </FarmLiabilityInfo>
        <Vehicle id="V001" LocationRef="L001">
          <QuestionAnswer>
            <QuestionText>How many miles is the vehicle regularly operated from its garaging location?</QuestionText>
          </QuestionAnswer>
          <QuestionAnswer>
            <QuestionText>License Plate</QuestionText>
          </QuestionAnswer>
          <QuestionAnswer>
            <QuestionText>Is this vehicle used for Business, Personal or both?</QuestionText>
          </QuestionAnswer>
          <ItemIdInfo>
            <InsurerId>473101</InsurerId>
          </ItemIdInfo>
          <Manufacturer>ACURA</Manufacturer>
          <Model>LEGEND</Model>
          <ModelYear>1993</ModelYear>
          <VehBodyTypeCd>TRUCKT</VehBodyTypeCd>
          <VehBodyTypeDesc>Light Truck</VehBodyTypeDesc>
          <CostNewAmt>
            <Amt>31200</Amt>
          </CostNewAmt>
          <VehIdentificationNumber>JH4KA7556PC018027</VehIdentificationNumber>
          <AdditionalInterest>
            <Name>TEST USER 1</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>518 E BROAD ST</Addr1>
              <City>COLUMBUS</City>
              <PostalCode>43215-3901</PostalCode>
              <StateProvCd>OH</StateProvCd>
            </Addr>
            <NatureInterestCd>AILL</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>161201</InterestIdNumber>
          </AdditionalInterest>
          <VehUseCd>OT</VehUseCd>
          <FleetInd Value="0"/>
          <PrimaryClassCd>011</PrimaryClassCd>
          <SecondaryClassCd>33</SecondaryClassCd>
          <Addr>
            <AddressTypeCd>GaragingAddress</AddressTypeCd>
            <City>PHOENIX</City>
            <StateProvCd>AZ</StateProvCd>
            <PostalCode>85008-6037</PostalCode>
          </Addr>
          <Coverage>
            <CoverageCd>RREIM</CoverageCd>
            <CoverageDesc>Rental Reimbursement</CoverageDesc>
            <CurrentTermAmt>
              <Amt>312.00</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>COLL</CoverageCd>
            <CoverageDesc>Collision</CoverageDesc>
            <Deductible>
              <FormatInteger>1000</FormatInteger>
            </Deductible>
            <CurrentTermAmt>
              <Amt>124.00</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>OTC</CoverageCd>
            <CoverageDesc>Other than Collision (Comprehensive Coverage)</CoverageDesc>
            <Deductible>
              <FormatInteger>1000</FormatInteger>
            </Deductible>
            <CurrentTermAmt>
              <Amt>71.00</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>MEDPM</CoverageCd>
            <CoverageDesc>Medical Payments</CoverageDesc>
            <Limit>
              <FormatInteger>1000</FormatInteger>
            </Limit>
            <CurrentTermAmt>
              <Amt>69.00</Amt>
            </CurrentTermAmt>
          </Coverage>
          <FullTermAmt>
            <Amt>2749.00</Amt>
          </FullTermAmt>
        </Vehicle>
        <Vehicle id="V002" LocationRef="L001">
          <QuestionAnswer>
            <QuestionText>How many miles is the vehicle regularly operated from its garaging location?</QuestionText>
          </QuestionAnswer>
          <QuestionAnswer>
            <QuestionText>License Plate</QuestionText>
          </QuestionAnswer>
          <QuestionAnswer>
            <QuestionText>Is this vehicle used for Business, Personal or both?</QuestionText>
          </QuestionAnswer>
          <ItemIdInfo>
            <InsurerId>473102</InsurerId>
          </ItemIdInfo>
          <Manufacturer>LINCOLN</Manufacturer>
          <Model>TOWN CAR</Model>
          <ModelYear>2007</ModelYear>
          <VehBodyTypeCd>PPRCF</VehBodyTypeCd>
          <VehBodyTypeDesc>Personal Use or Farm Use - Nonfleet Vehicle</VehBodyTypeDesc>
          <CostNewAmt>
            <Amt>42055</Amt>
          </CostNewAmt>
          <VehIdentificationNumber>1LNHM81W27Y615245</VehIdentificationNumber>
          <VehUseCd>OT</VehUseCd>
          <FleetInd Value="0"/>
          <PrimaryClassCd>738</PrimaryClassCd>
          <SecondaryClassCd>1</SecondaryClassCd>
          <Addr>
            <AddressTypeCd>GaragingAddress</AddressTypeCd>
            <City>PHOENIX</City>
            <StateProvCd>AZ</StateProvCd>
            <PostalCode>85008-6037</PostalCode>
          </Addr>
          <Coverage>
            <CoverageCd>MEDPM</CoverageCd>
            <CoverageDesc>Medical Payments</CoverageDesc>
            <Limit>
              <FormatInteger>1000</FormatInteger>
            </Limit>
            <CurrentTermAmt>
              <Amt>69.00</Amt>
            </CurrentTermAmt>
          </Coverage>
          <FullTermAmt>
            <Amt>1920.00</Amt>
          </FullTermAmt>
        </Vehicle>
        <Driver id="D001">
          <ItemIdInfo>
            <InsurerId>326501</InsurerId>
          </ItemIdInfo>
          <GivenName>ADELA</GivenName>
          <Surname>COULOMBE</Surname>
          <License>
            <LicenseTypeCd>Driver</LicenseTypeCd>
            <LicensePermitNumber>C54507865066</LicensePermitNumber>
            <StateProvCd>IL</StateProvCd>
          </License>
          <BirthDt>1944-06-25</BirthDt>
        </Driver>
      </FarmLineBusiness>
    </FarmPolicyQuoteInqRq>
    <CommlPkgPolicyQuoteInqRq>
      <RqUID>f434e27b-e81c-473e-9033-dc64f69201c3</RqUID>
      <BusinessPurposeTypeCd>PCH</BusinessPurposeTypeCd>
      <TransactionRequestDt>2024-05-14T12:40:04.891-06:00</TransactionRequestDt>
      <TransactionEffectiveDt>2024-03-01</TransactionEffectiveDt>
      <Producer>
        <ContractNumber>48821</ContractNumber>
        <ProducerRoleCd>PROD</ProducerRoleCd>
      </Producer>
      <InsuredOrPrincipal>
        <Name>Lajcak, LLC</Name>
        <LegalEntityCd>LL</LegalEntityCd>
        <TaxIdentity>
          <TaxIdTypeCd>FEIN</TaxIdTypeCd>
          <TaxId>*********</TaxId>
        </TaxIdentity>
        <SupplementaryNameInfo>
          <SupplementaryNameCd>DBA</SupplementaryNameCd>
          <Name>Wilderness Campground</Name>
        </SupplementaryNameInfo>
        <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>1801 Irish Rd</Addr1>
          <City>Dundee</City>
          <StateProvCd>MI</StateProvCd>
          <PostalCode>*********</PostalCode>
        </Addr>
        <Communications>
          <PhoneInfo>
            <PhoneTypeCd>Phone</PhoneTypeCd>
            <PhoneNumber>******-4747915</PhoneNumber>
          </PhoneInfo>
        </Communications>
        <InsuredOrPrincipalRoleCd>IN</InsuredOrPrincipalRoleCd>
        <SICCd>7033</SICCd>
        <NAICSCd>721211</NAICSCd>
        <OperationsDesc>RV (Recreational Vehicle) Park</OperationsDesc>
      </InsuredOrPrincipal>
      <Policy>
        <PolicyNumber>B034723</PolicyNumber>
        <PolicyVersion>2</PolicyVersion>
        <LOBCd>CPKGE</LOBCd>
        <NAICCd>15350</NAICCd>
        <ControllingStateProvCd>MI</ControllingStateProvCd>
        <ContractTerm>
          <EffectiveDt>2024-03-01</EffectiveDt>
          <ExpirationDt>2025-03-01</ExpirationDt>
        </ContractTerm>
        <BillingAccountNumber>**********</BillingAccountNumber>
        <BillingMethodCd>CPB</BillingMethodCd>
        <CurrentTermAmt>
          <Amt>13209</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>0</Amt>
        </NetChangeAmt>
        <CustomerServicingCd>AgcySvc</CustomerServicingCd>
        <LanguageCd>en</LanguageCd>
        <MailingResponsibiltyCd>CompResp</MailingResponsibiltyCd>
        <RenewalTerm>
          <DurationPeriod>
            <NumUnits>12</NumUnits>
            <UnitMeasurementCd>MON</UnitMeasurementCd>
          </DurationPeriod>
        </RenewalTerm>
        <Form>
          <FormNumber>NS0412</FormNumber>
          <FormName>VACANCY SEASONAL EXCEPTION</FormName>
          <EditionDt>2012-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCP01</FormNumber>
          <FormName>COMMERCIAL LINES POLICY DECLARATION</FormName>
          <EditionDt>2018-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCP02</FormNumber>
          <FormName>NAMED INSURED SCHEDULE</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCP03</FormNumber>
          <FormName>LOCATION SCHEDULE</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCP04</FormNumber>
          <FormName>FORMS SCHEDULE</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL0985</FormNumber>
          <FormName>DISCLOSURE PURSUANT TO TERRORISM RISK INSURANCE AC</FormName>
          <EditionDt>2020-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL0935Z</FormNumber>
          <FormName>EXCLUSION OF CERTAIN COMPUTER-RELATED LOSSES</FormName>
          <EditionDt>2002-07-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL0017Z</FormNumber>
          <FormName>COMMON POLICY CONDITIONS</FormName>
          <EditionDt>1998-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL0952Z</FormNumber>
          <FormName>CAP ON LOSSES FROM CERTIFIED ACTS OF TERRORISM</FormName>
          <EditionDt>2015-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL0021</FormNumber>
          <FormName>NUCLEAR ENERGY LIABILITY EXCLUSION ENDORSEMENT (BR</FormName>
          <EditionDt>2008-09-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB660</FormNumber>
          <FormName>TWO OR MORE COVERAGE FORMS OR POLICIES ISSUED BY U</FormName>
          <EditionDt>2020-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IL0286Z</FormNumber>
          <FormName>MICHIGAN CHANGES - CANCELLATION AND NONRENEWAL</FormName>
          <EditionDt>2017-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP0010</FormNumber>
          <FormName>BUILDING AND PERSONAL PROPERTY COVERAGE FORM</FormName>
          <EditionDt>2012-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP0030</FormNumber>
          <FormName>BUSINESS INCOME (AND EXTRA EXPENSE) COVERAGE FORM</FormName>
          <EditionDt>2012-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP0090</FormNumber>
          <FormName>COMMERCIAL PROPERTY CONDITIONS</FormName>
          <EditionDt>1988-07-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCF01</FormNumber>
          <FormName>COMMERCIAL PROPERTY COVERAGE DECLARATIONS</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCF03</FormNumber>
          <FormName>COMMERCIAL PROPERTY COVERAGE SCHEDULE</FormName>
          <EditionDt>2016-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCF07</FormNumber>
          <FormName>COMMERCIAL PROPERTY ENDORSEMENTS &amp; MISCELLANEOUS P</FormName>
          <EditionDt>2016-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCF08</FormNumber>
          <FormName>COMMERCIAL PROPERTY ADDITIONAL INTEREST SCHEDULE</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCF09</FormNumber>
          <FormName>COMMERCIAL PROPERTY FORMS SCHEDULE</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP0140</FormNumber>
          <FormName>EXCLUSION OF LOSS DUE TO VIRUS OR BACTERIA</FormName>
          <EditionDt>2006-07-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP0401</FormNumber>
          <FormName>BRANDS AND LABELS</FormName>
          <EditionDt>2000-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP1030</FormNumber>
          <FormName>CAUSES OF LOSS - SPECIAL FORM</FormName>
          <EditionDt>2017-09-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP1075</FormNumber>
          <FormName>CYBER INCIDENT EXCLUSION</FormName>
          <EditionDt>2020-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP1218</FormNumber>
          <FormName>LOSS PAYABLE PROVISIONS</FormName>
          <EditionDt>2012-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP1531</FormNumber>
          <FormName>ORDINANCE OR LAW - INCREASED PERIOD OF RESTORATION</FormName>
          <EditionDt>2017-09-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP1545</FormNumber>
          <FormName>UTILITY SERVICES - TIME ELEMENT</FormName>
          <EditionDt>2017-09-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB2186</FormNumber>
          <FormName>BUSINESS INCOME COVERAGE - ACTUAL LOSS SUSTAINED (</FormName>
          <EditionDt>2005-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB2906</FormNumber>
          <FormName>PROPERTY ADDITIONAL COVERAGES AND COVERAGE EXTENSI</FormName>
          <EditionDt>2020-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB34</FormNumber>
          <FormName>EQUIPMENT BREAKDOWN COVERAGE ENDORSEMENT</FormName>
          <EditionDt>2018-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB898</FormNumber>
          <FormName>YOUR BUSINESS PERSONAL PROPERTY AMENDMENT TENANT G</FormName>
          <EditionDt>2018-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB2843</FormNumber>
          <FormName>CYBER SUITE COVERAGE ENDORSEMENT (CLAIMS-MADE THIR</FormName>
          <EditionDt>2019-08-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB2846</FormNumber>
          <FormName>MICHIGAN CHANGES AMENDATORY ENDORSEMENT</FormName>
          <EditionDt>2019-08-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCGL01</FormNumber>
          <FormName>COMMERCIAL GENERAL LIABILITY COVERAGE DECLARATIONS</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCGL02</FormNumber>
          <FormName>COMMERCIAL GENERAL LIABILITY CLASSIFICATION SCHEDU</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCGL03</FormNumber>
          <FormName>COMMERCIAL GENERAL LIABILITY ENDORSEMENTS AND MISC</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCGL04</FormNumber>
          <FormName>COMMERCIAL GENERAL LIABILITY FORMS SCHEDULE</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG0001</FormNumber>
          <FormName>COMMERCIAL GENERAL LIABILITY COVERAGE FORM</FormName>
          <EditionDt>2013-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG2012</FormNumber>
          <FormName>ADDITIONAL INSURED - STATE OR GOVERNMENTAL AGENCY</FormName>
          <EditionDt>2019-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG2106</FormNumber>
          <FormName>EXCLUSION - ACCESS OR DISCLOSURE OF CONFIDENTIAL O</FormName>
          <EditionDt>2014-05-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG2109</FormNumber>
          <FormName>EXCLUSION UNMANNED AIRCRAFT</FormName>
          <EditionDt>2015-06-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG2147</FormNumber>
          <FormName>EMPLOYMENT-RELATED PRACTICES EXCLUSION</FormName>
          <EditionDt>2007-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG2170</FormNumber>
          <FormName>CAP ON LOSSES FROM CERTIFIED ACTS OF TERRORISM</FormName>
          <EditionDt>2015-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG2407</FormNumber>
          <FormName>PRODUCTS/COMPLETED OPERATIONS HAZARD REDEFINED (FO</FormName>
          <EditionDt>1996-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG2412</FormNumber>
          <FormName>BOATS</FormName>
          <EditionDt>1985-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG2416</FormNumber>
          <FormName>CANOES OR ROWBOATS</FormName>
          <EditionDt>2007-12-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>NS0273</FormNumber>
          <FormName>LIMITED FUNGI COVERAGE</FormName>
          <EditionDt>2008-07-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>NS0338</FormNumber>
          <FormName>TRAILER SPOTTING ENDORSEMENT</FormName>
          <EditionDt>2009-05-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB144</FormNumber>
          <FormName>VOLUNTARY PROPERTY DAMAGE COVERAGE</FormName>
          <EditionDt>1999-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB1460</FormNumber>
          <FormName>AMENDMENT - WHO IS AN INSURED</FormName>
          <EditionDt>2018-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB1958GL</FormNumber>
          <FormName>EXCLUSION - LEAD LIABILITY</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB2000GL</FormNumber>
          <FormName>PLUS PAK - LIABILITY</FormName>
          <EditionDt>2018-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB1392GL</FormNumber>
          <FormName>EXCLUSION - BIOMETRIC IDENTIFIERS OR BIOMETRIC DAT</FormName>
          <EditionDt>2023-02-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG0168</FormNumber>
          <FormName>MICHIGAN CHANGES</FormName>
          <EditionDt>2020-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DBA01</FormNumber>
          <FormName>BUSINESS AUTO COVERAGE DECLARATIONS</FormName>
          <EditionDt>2022-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DBA03</FormNumber>
          <FormName>BUSINESS AUTO HIRED OR BORROWED SCHEDULE</FormName>
          <EditionDt>2022-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DBA04</FormNumber>
          <FormName>BUSINESS AUTO NON-OWNED AUTO SCHEDULE</FormName>
          <EditionDt>2022-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DBA07</FormNumber>
          <FormName>BUSINESS AUTO FORMS SCHEDULE</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA2054</FormNumber>
          <FormName>EMPLOYEE HIRED AUTOS</FormName>
          <EditionDt>2020-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA2301</FormNumber>
          <FormName>EXPLOSIVES</FormName>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA0001</FormNumber>
          <FormName>BUSINESS AUTO COVERAGE FORM</FormName>
          <EditionDt>2020-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA0110</FormNumber>
          <FormName>MICHIGAN CHANGES</FormName>
          <EditionDt>2020-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA2131</FormNumber>
          <FormName>MICHIGAN UNINSURED MOTORISTS COVERAGE</FormName>
          <EditionDt>2013-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA0217</FormNumber>
          <FormName>MICHIGAN CHANGES - CANCELLATION AND NONRENEWAL</FormName>
          <EditionDt>2017-07-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCM02</FormNumber>
          <FormName>COMMERCIAL INLAND MARINE COVERAGE DECLARATIONS</FormName>
          <EditionDt>2017-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCM04</FormNumber>
          <FormName>COMMERCIAL INLAND MARINE DESCRIPTION DETAIL SCHEDU</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCM05</FormNumber>
          <FormName>COMMERCIAL INLAND MARINE ENDORSEMENTS AND MISCELLA</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DCM07</FormNumber>
          <FormName>COMMERCIAL INLAND MARINE FORMS SCHEDULE</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IM7000Z</FormNumber>
          <FormName>CONTRACTORS' EQUIPMENT COVERAGE</FormName>
          <EditionDt>2004-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CL0100</FormNumber>
          <FormName>COMMON POLICY CONDITIONS</FormName>
          <EditionDt>1999-03-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CL0600</FormNumber>
          <FormName>CERTIFIED TERRORISM LOSS</FormName>
          <EditionDt>2015-01-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CL0700</FormNumber>
          <FormName>VIRUS OR BACTERIA EXCLUSION</FormName>
          <EditionDt>2006-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IM7500Z</FormNumber>
          <FormName>SCHEDULED PROPERTY FLOATER</FormName>
          <EditionDt>2004-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB2198</FormNumber>
          <FormName>EXCLUSION OF CERTAIN COMPUTER - RELATED LOSSES</FormName>
          <EditionDt>1998-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CL0200</FormNumber>
          <FormName>AMENDATORY ENDORSEMENT - MICHIGAN</FormName>
          <EditionDt>1999-03-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IM2111Z</FormNumber>
          <FormName>AMENDATORY ENDORSEMENT MICHIGAN</FormName>
          <EditionDt>2020-06-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG2135</FormNumber>
          <FormName>EXCLUSION - COVERAGE C - MEDICAL PAYMENTS</FormName>
          <EditionDt>2001-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB3109GL</FormNumber>
          <FormName>EXCLUSION - DISCRIMINATION</FormName>
          <EditionDt>2023-02-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DBA05</FormNumber>
          <FormName>BUSINESS AUTO ENDORSEMENTS AND MISCELLANEOUS PREMI</FormName>
          <EditionDt>2014-04-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CA2377</FormNumber>
          <FormName>MICHIGAN PUBLIC OR LIVERY PASSENGER CONVEYANCE, TR</FormName>
          <EditionDt>2021-02-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB214</FormNumber>
          <FormName>MEMBERSHIP AND VOTING NOTICE</FormName>
          <EditionDt>2023-05-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP1036Y</FormNumber>
          <FormName>LIMITATIONS ON COVERAGE FOR ROOF SURFACING</FormName>
          <EditionDt>2012-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB2944</FormNumber>
          <FormName>UNMATCHED PROPERTY DAMAGE EXCLUSION</FormName>
          <EditionDt>2021-05-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CP0120</FormNumber>
          <FormName>MICHIGAN CHANGES</FormName>
          <EditionDt>2023-05-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CG4032</FormNumber>
          <FormName>EXCLUSION - PERFLUOROALKYL AND POLYFLUOROALKYL SUB</FormName>
          <EditionDt>2023-05-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB3119GL</FormNumber>
          <FormName>TOTAL LIQUOR LIABILITY EXCLUSION</FormName>
          <EditionDt>2024-02-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB1468GL</FormNumber>
          <FormName>EXCLUSION - ASBESTOS OR ASBESTOS PRODUCTS</FormName>
          <EditionDt>2024-02-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB687GL</FormNumber>
          <FormName>EXCLUSION - FIREWORKS</FormName>
          <EditionDt>2024-02-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>DPCS01</FormNumber>
          <FormName>POLICY CHANGE SUMMARY DECLARATIONS</FormName>
          <EditionDt>2022-02-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>WB3113</FormNumber>
          <FormName>EXCESS PROVISION</FormName>
          <EditionDt>2024-02-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <QuestionAnswer>
          <QuestionCd>GENRL34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL35</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL47</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL41</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL22</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL21</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL07</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL08</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL09</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL25</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL63</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL40</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL52</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>GENRL64</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>WORK10</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <MinPremAmt>
          <Amt>0</Amt>
        </MinPremAmt>
        <AuditInd>1</AuditInd>
      </Policy>
      <Location id="L0001">
        <ItemIdInfo>
          <InsurerId>1</InsurerId>
        </ItemIdInfo>
        <Addr>
          <AddrTypeCd>PhysicalRisk</AddrTypeCd>
          <Addr1>1350 Meanwell Rd</Addr1>
          <City>Dundee</City>
          <StateProvCd>MI</StateProvCd>
          <PostalCode>*********</PostalCode>
          <County>Monroe County</County>
        </Addr>
        <SubLocation id="L0001S001">
          <ItemIdInfo>
            <InsurerId>1</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>Campground</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>2</InterestIdNumber>
          </AdditionalInterest>
          <AdditionalInterest>
            <Name>Old National Bank ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>LOSSP</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>3</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
        <SubLocation id="L0001S002">
          <ItemIdInfo>
            <InsurerId>2</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>Main Office</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>4</InterestIdNumber>
          </AdditionalInterest>
          <AdditionalInterest>
            <Name>Old National Bank ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>LOSSP</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>5</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
        <SubLocation id="L0001S003">
          <ItemIdInfo>
            <InsurerId>3</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>Pavilion</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>6</InterestIdNumber>
          </AdditionalInterest>
          <AdditionalInterest>
            <Name>Old National Bank ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>LOSSP</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>7</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
        <SubLocation id="L0001S004">
          <ItemIdInfo>
            <InsurerId>4</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>Bath House</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>8</InterestIdNumber>
          </AdditionalInterest>
          <AdditionalInterest>
            <Name>Old National Bank ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>LOSSP</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>9</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
        <SubLocation id="L0001S005">
          <ItemIdInfo>
            <InsurerId>5</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>Stage</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>10</InterestIdNumber>
          </AdditionalInterest>
          <AdditionalInterest>
            <Name>Old National Bank ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>LOSSP</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>11</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
        <SubLocation id="L0001S006">
          <ItemIdInfo>
            <InsurerId>6</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>Cabin #1</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>12</InterestIdNumber>
          </AdditionalInterest>
          <AdditionalInterest>
            <Name>Old National Bank ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>LOSSP</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>13</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
        <SubLocation id="L0001S007">
          <ItemIdInfo>
            <InsurerId>7</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>Cabin #2</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>14</InterestIdNumber>
          </AdditionalInterest>
          <AdditionalInterest>
            <Name>Old National Bank ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>LOSSP</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>15</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
        <SubLocation id="L0001S008">
          <ItemIdInfo>
            <InsurerId>8</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>Cabin #3</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>16</InterestIdNumber>
          </AdditionalInterest>
          <AdditionalInterest>
            <Name>Old National Bank ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>LOSSP</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>17</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
        <SubLocation id="L0001S009">
          <ItemIdInfo>
            <InsurerId>9</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>RV Garage</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>18</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
        <SubLocation id="L0001S010">
          <ItemIdInfo>
            <InsurerId>10</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>Storage Garage</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>19</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
        <SubLocation id="L0001S011">
          <ItemIdInfo>
            <InsurerId>11</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>2020 Artic Wolf 5th Wheel #5ZT3CK1B1L0704436</SubLocationDesc>
          <AdditionalInterest>
            <Name>Huntington National Bank</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 182519</Addr1>
              <City>Columbus</City>
              <StateProvCd>OH</StateProvCd>
              <PostalCode>43218</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>20</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
      </Location>
      <Location id="L0002">
        <ItemIdInfo>
          <InsurerId>2</InsurerId>
        </ItemIdInfo>
        <Addr>
          <AddrTypeCd>PhysicalRisk</AddrTypeCd>
          <Addr1>1801 Irish Rd</Addr1>
          <City>Dundee</City>
          <StateProvCd>MI</StateProvCd>
          <PostalCode>*********</PostalCode>
          <County>Monroe County</County>
        </Addr>
        <SubLocation id="L0002S001">
          <ItemIdInfo>
            <InsurerId>1</InsurerId>
          </ItemIdInfo>
          <SubLocationDesc>Residence</SubLocationDesc>
          <AdditionalInterest>
            <Name>Old National Bank, ISAOA</Name>
            <Addr>
              <AddrTypeCd>MailingAddress</AddrTypeCd>
              <Addr1>PO Box 3728</Addr1>
              <City>Evansville</City>
              <StateProvCd>IN</StateProvCd>
              <PostalCode>47736</PostalCode>
            </Addr>
            <NatureInterestCd>MORTG</NatureInterestCd>
            <InterestRank>1</InterestRank>
            <InterestIdNumber>21</InterestIdNumber>
          </AdditionalInterest>
        </SubLocation>
      </Location>
      <CommlAutoLineBusiness>
        <LOBCd>AUTOB</LOBCd>
        <NAICCd>15350</NAICCd>
        <CurrentTermAmt>
          <Amt>207</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>0</Amt>
        </NetChangeAmt>
        <CommlAutoPolicyInfo>
          <DriverRecruitingMethodInd>0</DriverRecruitingMethodInd>
          <DriverTrainingInd>N</DriverTrainingInd>
          <LeaseVehsToOthersInd>0</LeaseVehsToOthersInd>
          <MVRVerificationInd>0</MVRVerificationInd>
          <VehMaintenanceInd>0</VehMaintenanceInd>
          <PrimaryRatingJurisdictionCd>MI</PrimaryRatingJurisdictionCd>
        </CommlAutoPolicyInfo>
        <CoveredAutoSymbol>
          <AutoMedicalPaymentsSymbolCd>8</AutoMedicalPaymentsSymbolCd>
          <AutoMedicalPaymentsSymbolCd>9</AutoMedicalPaymentsSymbolCd>
          <EffectiveDt>2024-03-01</EffectiveDt>
          <LiabilitySymbolCd>8</LiabilitySymbolCd>
          <LiabilitySymbolCd>9</LiabilitySymbolCd>
          <SpecifiedPerilsSymbolCd>7</SpecifiedPerilsSymbolCd>
          <UnderinsuredMotoristSymbolCd>8</UnderinsuredMotoristSymbolCd>
          <UnderinsuredMotoristSymbolCd>9</UnderinsuredMotoristSymbolCd>
          <UninsuredMotoristSymbolCd>8</UninsuredMotoristSymbolCd>
          <UninsuredMotoristSymbolCd>9</UninsuredMotoristSymbolCd>
        </CoveredAutoSymbol>
        <CommlRateState>
          <NAICCd>15350</NAICCd>
          <StateProvCd>MI</StateProvCd>
          <CommlAutoHiredInfo>
            <HiredLiabilityClassCd>6627</HiredLiabilityClassCd>
            <Coverage>
              <CoverageCd>CSL</CoverageCd>
              <CoverageDesc>Combined Single Limit</CoverageDesc>
              <Limit>
                <FormatInteger>1000000</FormatInteger>
              </Limit>
              <CurrentTermAmt>
                <Amt>41</Amt>
              </CurrentTermAmt>
            </Coverage>
            <HiredLiabilityCostInfo>
              <HiredLiabilityCostAmt>
                <Amt>0</Amt>
              </HiredLiabilityCostAmt>
            </HiredLiabilityCostInfo>
          </CommlAutoHiredInfo>
          <CommlAutoNonOwnedInfo>
            <NonOwnedInfo>
              <HiredLiabilityClassCd>6638</HiredLiabilityClassCd>
              <Coverage>
                <CoverageCd>NOEMP</CoverageCd>
                <CoverageDesc>Non Owned Employee</CoverageDesc>
                <Limit>
                  <FormatInteger>1000000</FormatInteger>
                </Limit>
                <CurrentTermAmt>
                  <Amt>166</Amt>
                </CurrentTermAmt>
              </Coverage>
            </NonOwnedInfo>
          </CommlAutoNonOwnedInfo>
        </CommlRateState>
        <TruckersSupplement>
          <ReceiptsDistanceUnits>
            <YearIdentifierCd>TwoAgo</YearIdentifierCd>
          </ReceiptsDistanceUnits>
          <ReceiptsDistanceUnits>
            <YearIdentifierCd>Last</YearIdentifierCd>
          </ReceiptsDistanceUnits>
          <ReceiptsDistanceUnits>
            <YearIdentifierCd>Last12</YearIdentifierCd>
          </ReceiptsDistanceUnits>
          <ReceiptsDistanceUnits>
            <YearIdentifierCd>Next</YearIdentifierCd>
          </ReceiptsDistanceUnits>
          <ReceiptsDistanceUnits>
            <YearIdentifierCd>Next12</YearIdentifierCd>
          </ReceiptsDistanceUnits>
        </TruckersSupplement>
      </CommlAutoLineBusiness>
      <GeneralLiabilityLineBusiness>
        <LOBCd>CGL</LOBCd>
        <LOBSubCd>NPO</LOBSubCd>
        <NAICCd>15350</NAICCd>
        <CurrentTermAmt>
          <Amt>5154</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>0</Amt>
        </NetChangeAmt>
        <AdditionalInterest>
          <Name>MI Department of Licensing and Regulatory Affairs</Name>
          <Addr>
            <AddrTypeCd>MailingAddress</AddrTypeCd>
            <Addr1>PO Box 30005</Addr1>
            <City>Lansing</City>
            <StateProvCd>MI</StateProvCd>
            <PostalCode>48909</PostalCode>
          </Addr>
          <NatureInterestCd>ASP</NatureInterestCd>
          <InterestRank>1</InterestRank>
          <InterestIdNumber>1</InterestIdNumber>
        </AdditionalInterest>
        <LiabilityInfo>
          <Coverage>
            <CoverageCd>EAOCC</CoverageCd>
            <CoverageDesc>Each Occurrence</CoverageDesc>
            <Limit>
              <FormatInteger>1000000</FormatInteger>
            </Limit>
          </Coverage>
          <Coverage>
            <CoverageCd>GENAG</CoverageCd>
            <CoverageDesc>General Aggregate</CoverageDesc>
            <Limit>
              <FormatInteger>2000000</FormatInteger>
            </Limit>
          </Coverage>
          <Coverage>
            <CoverageCd>PRDCO</CoverageCd>
            <CoverageDesc>Products and Completed Operations</CoverageDesc>
            <Limit>
              <FormatInteger>2000000</FormatInteger>
            </Limit>
            <CurrentTermAmt>
              <Amt>148</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>PLPAK</CoverageCd>
            <CoverageDesc>Plus Pak</CoverageDesc>
            <CurrentTermAmt>
              <Amt>50</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>FIRDM</CoverageCd>
            <CoverageDesc>Damage to Rented Premises</CoverageDesc>
            <Limit>
              <FormatInteger>300000</FormatInteger>
            </Limit>
          </Coverage>
          <Coverage>
            <CoverageCd>PIADV</CoverageCd>
            <CoverageDesc />
            <Limit>
              <FormatInteger>1000000</FormatInteger>
            </Limit>
          </Coverage>
          <Coverage>
            <CoverageCd>AMPPD</CoverageCd>
            <CoverageDesc />
            <CurrentTermAmt>
              <Amt>146</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>PREM</CoverageCd>
            <CoverageDesc />
            <CurrentTermAmt>
              <Amt>4956</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>OT</CoverageCd>
            <CoverageDesc />
            <CurrentTermAmt>
              <Amt>50</Amt>
            </CurrentTermAmt>
          </Coverage>
          <GeneralLiabilityClassification LocationRef="L0001" SubLocationRef="L0001S001">
            <ItemIdInfo>
              <InsurerId>1</InsurerId>
            </ItemIdInfo>
            <SublineCd>334</SublineCd>
            <Coverage>
              <CoverageCd>PREM</CoverageCd>
              <CoverageDesc>Premises Operations</CoverageDesc>
              <CurrentTermAmt>
                <Amt>4597</Amt>
              </CurrentTermAmt>
              <Rate>14.406</Rate>
            </Coverage>
            <Coverage>
              <CoverageCd>TRIA</CoverageCd>
              <CoverageDesc>Terrorism Coverage provided under TRIA (2002, USA) (certified acts)</CoverageDesc>
              <CurrentTermAmt>
                <Amt>18</Amt>
              </CurrentTermAmt>
            </Coverage>
            <ClassCd>10331</ClassCd>
            <ClassCdDesc>Campgrounds - Other than Not-For-Profit</ClassCdDesc>
            <Exposure>319075</Exposure>
            <TerritoryCd>505</TerritoryCd>
            <PremiumBasisCd>S</PremiumBasisCd>
          </GeneralLiabilityClassification>
          <GeneralLiabilityClassification LocationRef="L0001" SubLocationRef="L0001S001">
            <ItemIdInfo>
              <InsurerId>2</InsurerId>
            </ItemIdInfo>
            <SublineCd>334</SublineCd>
            <Coverage>
              <CoverageCd>PREM</CoverageCd>
              <CoverageDesc>Premises Operations</CoverageDesc>
              <CurrentTermAmt>
                <Amt>37</Amt>
              </CurrentTermAmt>
              <Rate>2.542</Rate>
            </Coverage>
            <ClassCd>45190</ClassCd>
            <ClassCdDesc>Hotels and Motels - with pools or beaches - less than four stories</ClassCdDesc>
            <Exposure>14503</Exposure>
            <TerritoryCd>505</TerritoryCd>
            <PremiumBasisCd>S</PremiumBasisCd>
          </GeneralLiabilityClassification>
          <GeneralLiabilityClassification LocationRef="L0001" SubLocationRef="L0001S001">
            <ItemIdInfo>
              <InsurerId>3</InsurerId>
            </ItemIdInfo>
            <SublineCd>334</SublineCd>
            <Coverage>
              <CoverageCd>PREM</CoverageCd>
              <CoverageDesc>Premises Operations</CoverageDesc>
              <CurrentTermAmt>
                <Amt>23</Amt>
              </CurrentTermAmt>
              <Rate>0.625</Rate>
            </Coverage>
            <Coverage>
              <CoverageCd>PRDCO</CoverageCd>
              <CoverageDesc>Products Completed Operations</CoverageDesc>
              <CurrentTermAmt>
                <Amt>2</Amt>
              </CurrentTermAmt>
              <Rate>0.053</Rate>
            </Coverage>
            <ClassCd>18435</ClassCd>
            <ClassCdDesc>Stores - food or drink - Other than Not-For-Profit</ClassCdDesc>
            <Exposure>36513</Exposure>
            <TerritoryCd>505</TerritoryCd>
            <PremiumBasisCd>S</PremiumBasisCd>
          </GeneralLiabilityClassification>
          <GeneralLiabilityClassification LocationRef="L0001" SubLocationRef="L0001S001">
            <ItemIdInfo>
              <InsurerId>4</InsurerId>
            </ItemIdInfo>
            <SublineCd>334</SublineCd>
            <Coverage>
              <CoverageCd>PREM</CoverageCd>
              <CoverageDesc>Premises Operations</CoverageDesc>
              <CurrentTermAmt>
                <Amt>40</Amt>
              </CurrentTermAmt>
              <Rate>39.971</Rate>
            </Coverage>
            <ClassCd>43754</ClassCd>
            <ClassCdDesc>Fishing Piers</ClassCdDesc>
            <Exposure>1</Exposure>
            <TerritoryCd>505</TerritoryCd>
          </GeneralLiabilityClassification>
          <GeneralLiabilityClassification LocationRef="L0001" SubLocationRef="L0001S001">
            <ItemIdInfo>
              <InsurerId>5</InsurerId>
            </ItemIdInfo>
            <SublineCd>334</SublineCd>
            <Coverage>
              <CoverageCd>PREM</CoverageCd>
              <CoverageDesc>Premises Operations</CoverageDesc>
              <CurrentTermAmt>
                <Amt>40</Amt>
              </CurrentTermAmt>
              <Rate>39.971</Rate>
            </Coverage>
            <ClassCd>40072</ClassCd>
            <ClassCdDesc>Beaches - bathing - not commercially operated</ClassCdDesc>
            <Exposure>1</Exposure>
            <TerritoryCd>505</TerritoryCd>
          </GeneralLiabilityClassification>
          <GeneralLiabilityClassification LocationRef="L0001" SubLocationRef="L0001S001">
            <ItemIdInfo>
              <InsurerId>6</InsurerId>
            </ItemIdInfo>
            <Coverage>
              <CoverageCd>PREM</CoverageCd>
              <CoverageDesc />
              <Rate>7.994</Rate>
            </Coverage>
            <ClassCd>10119</ClassCd>
            <ClassCdDesc>Boats - rented to others</ClassCdDesc>
            <Exposure>0</Exposure>
            <TerritoryCd>505</TerritoryCd>
            <PremiumBasisCd>S</PremiumBasisCd>
          </GeneralLiabilityClassification>
          <GeneralLiabilityClassification LocationRef="L0001" SubLocationRef="L0001S001">
            <ItemIdInfo>
              <InsurerId>7</InsurerId>
            </ItemIdInfo>
            <SublineCd>334</SublineCd>
            <Coverage>
              <CoverageCd>PREM</CoverageCd>
              <CoverageDesc>Premises Operations</CoverageDesc>
              <CurrentTermAmt>
                <Amt>200</Amt>
              </CurrentTermAmt>
              <Rate>199.856</Rate>
            </Coverage>
            <Coverage>
              <CoverageCd>TRIA</CoverageCd>
              <CoverageDesc>Terrorism Coverage provided under TRIA (2002, USA) (certified acts)</CoverageDesc>
              <CurrentTermAmt>
                <Amt>1</Amt>
              </CurrentTermAmt>
            </Coverage>
            <ClassCd>46671</ClassCd>
            <ClassCdDesc>Parks or Playgrounds</ClassCdDesc>
            <Exposure>1</Exposure>
            <TerritoryCd>505</TerritoryCd>
          </GeneralLiabilityClassification>
          <GeneralLiabilityClassification LocationRef="L0001" SubLocationRef="L0001S001">
            <ItemIdInfo>
              <InsurerId>8</InsurerId>
            </ItemIdInfo>
            <Coverage>
              <CoverageCd>PREM</CoverageCd>
              <CoverageDesc />
              <Rate>1.392</Rate>
            </Coverage>
            <ClassCd>16916</ClassCd>
            <ClassCdDesc>Restaurants - with sale of alcoholic beverages that are 30% or more of but less than 75% of the tota...</ClassCdDesc>
            <Exposure>0</Exposure>
            <TerritoryCd>505</TerritoryCd>
            <PremiumBasisCd>S</PremiumBasisCd>
          </GeneralLiabilityClassification>
        </LiabilityInfo>
        <StateSupplementInfo>
          <StateProvCd>MI</StateProvCd>
        </StateSupplementInfo>
      </GeneralLiabilityLineBusiness>
      <CommlPropertyLineBusiness>
        <LOBCd>PROP</LOBCd>
        <NAICCd>15350</NAICCd>
        <CurrentTermAmt>
          <Amt>7156</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>0</Amt>
        </NetChangeAmt>
        <PropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S001">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>67500</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>299</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S001">
            <SubjectInsuranceCd>PP</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>15000</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>97</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S001">
            <SubjectInsuranceCd>BUSIN</SubjectInsuranceCd>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>0</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>670</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>100</CoinsurancePct>
            </Coverage>
            <ValuationCd>ACT</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S002">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>151800</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>716</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S002">
            <SubjectInsuranceCd>PP</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>100000</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>480</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S003">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>51100</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>251</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S003">
            <SubjectInsuranceCd>PP</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>15000</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>101</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S004">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>90000</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>389</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S004">
            <SubjectInsuranceCd>PP</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>5000</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>36</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S005">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>16800</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>82</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S005">
            <SubjectInsuranceCd>PP</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>3000</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>22</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S006">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>10400</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>51</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S006">
            <SubjectInsuranceCd>PP</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>2500</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>18</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S007">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>10400</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>51</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S007">
            <SubjectInsuranceCd>PP</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>2500</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>18</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S008">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>10400</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>51</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S008">
            <SubjectInsuranceCd>PP</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>2500</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>18</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S009">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>10200</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>51</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S010">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>50600</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>270</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S010">
            <SubjectInsuranceCd>PP</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>75000</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>372</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0001" SubLocationRef="L0001S011">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>54100</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>264</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0002" SubLocationRef="L0002S001">
            <SubjectInsuranceCd>B</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>423900</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>1622</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <Coverage>
              <CoverageCd>INFL</CoverageCd>
              <CoverageDesc />
              <Option>
                <OptionTypeCd>NumV1</OptionTypeCd>
                <OptionValue>0040000000</OptionValue>
              </Option>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <CommlPropertyInfo LocationRef="L0002" SubLocationRef="L0002S001">
            <SubjectInsuranceCd>PP</SubjectInsuranceCd>
            <ItemValueAmt>
              <Amt>5000</Amt>
            </ItemValueAmt>
            <Coverage>
              <CoverageCd>SPC</CoverageCd>
              <CoverageDesc />
              <Deductible>
                <FormatInteger>2500</FormatInteger>
              </Deductible>
              <CurrentTermAmt>
                <Amt>42</Amt>
              </CurrentTermAmt>
              <CoinsurancePct>80</CoinsurancePct>
            </Coverage>
            <ValuationCd>RC</ValuationCd>
          </CommlPropertyInfo>
          <Coverage>
            <CoverageCd>ENHAN</CoverageCd>
            <CoverageDesc />
            <CurrentTermAmt>
              <Amt>450</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>EQPBK</CoverageCd>
            <CoverageDesc>Equipment Breakdown Coverage</CoverageDesc>
            <Limit>
              <FormatInteger>50000</FormatInteger>
            </Limit>
            <Deductible>
              <FormatInteger>250</FormatInteger>
            </Deductible>
            <CurrentTermAmt>
              <Amt>441</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>CYBER</CoverageCd>
            <CoverageDesc />
            <Deductible>
              <FormatInteger>1000</FormatInteger>
            </Deductible>
            <CurrentTermAmt>
              <Amt>294</Amt>
            </CurrentTermAmt>
          </Coverage>
        </PropertyInfo>
      </CommlPropertyLineBusiness>
      <CommlInlandMarineLineBusiness>
        <LOBCd>INMRC</LOBCd>
        <NAICCd>15350</NAICCd>
        <CurrentTermAmt>
          <Amt>692</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>0</Amt>
        </NetChangeAmt>
        <Coverage>
          <CoverageCd>TRIA</CoverageCd>
          <CoverageDesc>Terrorism Coverage provided under TRIA (2002, USA) (certified acts)</CoverageDesc>
          <CurrentTermAmt>
            <Amt>17</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageCd>CAT</CoverageCd>
          <CoverageDesc />
          <Limit>
            <FormatInteger>100000</FormatInteger>
          </Limit>
        </Coverage>
        <CommlIMInfo id="001" LocationRef="L0001" SubLocationRef="L0001S001">
          <CommlIMClassCd>CONEQ</CommlIMClassCd>
          <ScheduledInd>1</ScheduledInd>
          <Coverage>
            <CoverageCd>SPC</CoverageCd>
            <CoverageDesc />
            <Limit>
              <FormatInteger>1000</FormatInteger>
              <LimitAppliesToCd>ML</LimitAppliesToCd>
            </Limit>
            <Deductible>
              <FormatInteger>1000</FormatInteger>
            </Deductible>
            <CurrentTermAmt>
              <Amt>660</Amt>
            </CurrentTermAmt>
            <CoinsurancePct>100</CoinsurancePct>
          </Coverage>
          <Coverage>
            <CoverageCd>CAT</CoverageCd>
            <CoverageDesc />
            <Limit>
              <FormatInteger>100000</FormatInteger>
            </Limit>
          </Coverage>
          <Coverage>
            <CoverageCd>AUTOI</CoverageCd>
            <CoverageDesc>Automatic Increase - 5%</CoverageDesc>
          </Coverage>
          <PropertyItem>
            <ItemDefinition>
              <ItemIdInfo>
                <InsurerId>1</InsurerId>
              </ItemIdInfo>
              <ItemDesc>Miscellaneous equipment, any one item no</ItemDesc>
            </ItemDefinition>
            <ItemValueAmt>
              <Amt>100000</Amt>
            </ItemValueAmt>
            <ValueInfo>
              <ItemValueAmt>
                <Amt>100000</Amt>
              </ItemValueAmt>
              <ValuationCd>ACV</ValuationCd>
            </ValueInfo>
            <InsuranceAmt>
              <Amt>100000</Amt>
            </InsuranceAmt>
          </PropertyItem>
        </CommlIMInfo>
        <CommlIMInfo id="002" LocationRef="L0001" SubLocationRef="L0001S001">
          <CommlIMClassCd>MPFL</CommlIMClassCd>
          <ScheduledInd>1</ScheduledInd>
          <Coverage>
            <CoverageCd>SPC</CoverageCd>
            <CoverageDesc />
            <Limit>
              <FormatInteger>2500</FormatInteger>
            </Limit>
            <Deductible>
              <FormatInteger>500</FormatInteger>
            </Deductible>
            <CurrentTermAmt>
              <Amt>15</Amt>
            </CurrentTermAmt>
          </Coverage>
          <Coverage>
            <CoverageCd>CAT</CoverageCd>
            <CoverageDesc />
            <Limit>
              <FormatInteger>100000</FormatInteger>
            </Limit>
          </Coverage>
          <PropertyItem>
            <ItemDefinition>
              <ItemIdInfo>
                <InsurerId>2</InsurerId>
              </ItemIdInfo>
              <ItemDesc>1999 DS Gas Club Car MPFL SN AG9947-8257</ItemDesc>
            </ItemDefinition>
            <ItemValueAmt>
              <Amt>2500</Amt>
            </ItemValueAmt>
            <ValueInfo>
              <ItemValueAmt>
                <Amt>2500</Amt>
              </ItemValueAmt>
            </ValueInfo>
            <InsuranceAmt>
              <Amt>2500</Amt>
            </InsuranceAmt>
          </PropertyItem>
        </CommlIMInfo>
      </CommlInlandMarineLineBusiness>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S001">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Joisted Masonry</ConstructionCd>
          <YearBuilt>1989</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>1457</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>1457</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S002">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Frame</ConstructionCd>
          <YearBuilt>1975</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>1493</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>1493</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S003">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Frame</ConstructionCd>
          <YearBuilt>2000</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>1557</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>1557</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S004">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Joisted Masonry</ConstructionCd>
          <YearBuilt>1975</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>822</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>822</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S005">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Frame</ConstructionCd>
          <YearBuilt>2002</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>300</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>300</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S006">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Frame</ConstructionCd>
          <YearBuilt>2010</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>192</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>192</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S007">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Frame</ConstructionCd>
          <YearBuilt>2010</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>192</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>192</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S008">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Frame</ConstructionCd>
          <YearBuilt>2010</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>192</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>192</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S009">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Frame</ConstructionCd>
          <YearBuilt>2020</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>720</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>720</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S010">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Frame</ConstructionCd>
          <YearBuilt>2019</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>1040</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>1040</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0001" SubLocationRef="L0001S011">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Frame</ConstructionCd>
          <YearBuilt>2020</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <LocationUWInfo LocationRef="L0002" SubLocationRef="L0002S001">
        <InterestCd>OWNER</InterestCd>
        <Construction>
          <ConstructionCd>Frame</ConstructionCd>
          <YearBuilt>1995</YearBuilt>
          <BldgCodeEffectivenessGradeCd>99</BldgCodeEffectivenessGradeCd>
          <BldgArea>
            <NumUnits>2500</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </BldgArea>
          <NumStories>1</NumStories>
        </Construction>
        <BldgProtection>
          <ProtectionClassGradeCd>05</ProtectionClassGradeCd>
        </BldgProtection>
        <BldgOccupancy>
          <AreaOccupied>
            <NumUnits>2500</NumUnits>
            <UnitMeasurementCd>FTK</UnitMeasurementCd>
          </AreaOccupied>
        </BldgOccupancy>
        <QuestionAnswer>
          <QuestionCd>BOP32</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP33</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
        <QuestionAnswer>
          <QuestionCd>BOP34</QuestionCd>
          <YesNoCd>NO</YesNoCd>
        </QuestionAnswer>
      </LocationUWInfo>
      <RemarkText>Policy Activity - Added WB3113 Excess Provision</RemarkText>
      <RemarkText>5BPI01 Occ 1 - Customer Number: **********</RemarkText>
    </CommlPkgPolicyQuoteInqRq>
    <CrimePolicyQuoteInqRq>
      <BusinessPurposeTypeCd>RWL</BusinessPurposeTypeCd>
      <ItemIdInfo>
        <OtherIdentifier>
          <UID>b5e3e67a-4add-40f8-9389-aeac0365bb8a</UID>
        </OtherIdentifier>
      </ItemIdInfo>
      <TransactionRequestDt>2024-07-18T09:50:32.413-06:00</TransactionRequestDt>
      <TransactionEffectiveDt>2024-07-22</TransactionEffectiveDt>
      <Producer>
        <ContractNumber>92322023</ContractNumber>
        <ProducerSubCode>18315</ProducerSubCode>
        <ProducerRoleCd>PROD</ProducerRoleCd>
      </Producer>
      <InsuredOrPrincipal>
        <Name>PETER PARKER</Name>
        <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>123 Main Street</Addr1>
          <Addr3>123 MAIN ST</Addr3>
          <Addr4>123 MAIN ST</Addr4>
          <City>Elkhorn</City>
          <StateProvCd>NE</StateProvCd>
          <PostalCode>680222886</PostalCode>
          <CountryCd>USA</CountryCd>
        </Addr>
        <Communications>
          <PhoneInfo>
            <PhoneTypeCd>Phone</PhoneTypeCd>
            <PhoneNumber>******-8675309+1307135067</PhoneNumber>
          </PhoneInfo>
          <PhoneInfo>
            <PhoneTypeCd>Phone</PhoneTypeCd>
            <PhoneNumber>******-8675309+1307135067</PhoneNumber>
          </PhoneInfo>
          <PhoneInfo>
            <PhoneTypeCd>Phone</PhoneTypeCd>
            <PhoneNumber>******-8675309+1307135067</PhoneNumber>
          </PhoneInfo>
        </Communications>
        <LegalEntityCd>CP</LegalEntityCd>
        <SICCd>2759</SICCd>
        <NAICSCd>323113</NAICSCd>
        <OperationsDesc>Commercial Printing, Nec</OperationsDesc>
        <InsuredOrPrincipalRoleCd>IN</InsuredOrPrincipalRoleCd>
      </InsuredOrPrincipal>
      <Policy>
        <PolicyNumber>CRSNE0000018015</PolicyNumber>
        <LOBCd>CRIM</LOBCd>
        <NAICCd>99999</NAICCd>
        <ControllingStateProvCd>NE</ControllingStateProvCd>
        <ContractTerm>
          <EffectiveDt>2024-07-22</EffectiveDt>
          <ExpirationDt>2025-07-22</ExpirationDt>
        </ContractTerm>
        <BillingAccountNumber>***************</BillingAccountNumber>
        <BillingMethodCd>CPB</BillingMethodCd>
        <CurrentTermAmt>
          <Amt>250</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>250</Amt>
        </NetChangeAmt>
        <PayorCd>IN</PayorCd>
        <ShortTermPremiumMethodCd>P</ShortTermPremiumMethodCd>
        <Form>
          <FormNumber>CR 00 23</FormNumber>
          <FormName>Commercial Crime Policy (loss Sustained)</FormName>
          <EditionDt>2022-06-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CR 01 65</FormNumber>
          <FormName>Nebraska Changes - Actual Cash Value</FormName>
          <EditionDt>2022-06-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CR 02 28</FormNumber>
          <FormName>Nebraska Changes</FormName>
          <EditionDt>2022-06-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CR 07 30</FormNumber>
          <FormName>Exclusion Of Terrorism</FormName>
          <EditionDt>2022-06-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CR 20 20</FormNumber>
          <FormName>Calculation Of Premium</FormName>
          <EditionDt>2022-06-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CR 20 21</FormNumber>
          <FormName>Excl Of Certain Computer Related Losses</FormName>
          <EditionDt>2022-06-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CR DS 02</FormNumber>
          <FormName>Commercial Crime Policy Declarations</FormName>
          <EditionDt>2022-06-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>CO-DEC-1</FormNumber>
          <FormName>Common Policy Declarations</FormName>
          <EditionDt>2019-10-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>FRM-SCHD</FormNumber>
          <FormName>Schedule Of Forms and Endorsements</FormName>
          <EditionDt>2017-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>IPJ-305</FormNumber>
          <FormName>Policy Jacket</FormName>
          <EditionDt>2023-11-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <Form>
          <FormNumber>PRIVACYNOT</FormNumber>
          <FormName>Privacy Notice</FormName>
          <EditionDt>2022-02-01</EditionDt>
          <IterationNumber>1</IterationNumber>
        </Form>
        <OriginalPolicyInceptionDt>2020-07-22</OriginalPolicyInceptionDt>
      </Policy>
      <Location id="L0001">
        <ItemIdInfo>
          <InsurerId>1</InsurerId>
        </ItemIdInfo>
        <Addr>
          <AddrTypeCd>PhysicalRisk</AddrTypeCd>
          <Addr1>123 MAIN ST</Addr1>
          <Addr2>123 MAIN ST</Addr2>
          <Addr3>123 MAIN ST</Addr3>
          <Addr4>123 MAIN ST</Addr4>
          <StateProvCd>NE</StateProvCd>
        </Addr>
        <SubLocation id="L0001S001">
          <ItemIdInfo>
            <InsurerId>1</InsurerId>
          </ItemIdInfo>
          <Addr>
            <AddrTypeCd>PhysicalRisk</AddrTypeCd>
            <Addr1>123 MAIN ST</Addr1>
            <Addr2>123 MAIN ST</Addr2>
            <Addr3>123 MAIN ST</Addr3>
            <Addr4>123 MAIN ST</Addr4>
          </Addr>
        </SubLocation>
      </Location>
      <CrimeLineBusiness>
        <LOBCd>CRIM</LOBCd>
        <CrimeCoverageInfo LocationRef="L0001" SubLocationRef="L0001S001">
          <CrimeCoverageDetails>
            <Coverage>
              <CoverageCd>NOCOV</CoverageCd>
              <CoverageDesc>No Coverage at Location Level</CoverageDesc>
            </Coverage>
          </CrimeCoverageDetails>
        </CrimeCoverageInfo>
        <Coverage>
          <CoverageCd>THFT</CoverageCd>
          <Limit>
            <FormatInteger>50000</FormatInteger>
          </Limit>
          <Deductible>
            <FormatInteger>500</FormatInteger>
          </Deductible>
          <CurrentTermAmt>
            <Amt>223</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageCd>THFT</CoverageCd>
          <Limit>
            <FormatInteger>50000</FormatInteger>
          </Limit>
          <Deductible>
            <FormatInteger>500</FormatInteger>
          </Deductible>
          <CurrentTermAmt>
            <Amt>223</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage>
          <CoverageCd>APMP</CoverageCd>
          <CoverageDesc>Additional for Policy Minimum Premium</CoverageDesc>
          <CurrentTermAmt>
            <Amt>27</Amt>
          </CurrentTermAmt>
        </Coverage>
      </CrimeLineBusiness>
    </CrimePolicyQuoteInqRq>
  </InsuranceSvcRq>
</ACORD>
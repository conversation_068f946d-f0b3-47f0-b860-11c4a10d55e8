<ACORD>
<SignonRq>
<ClientDt>2019-03-05-08:00</ClientDt>
<ClientApp>
<Org>Safeco Book Transfer</Org>
<Name>SPQE</Name>
<Version id="V1.0">V1.0</Version>
</ClientApp>
</SignonRq>
<InsuranceSvcRq>
<HomePolicyQuoteInqRq>
<Producer>
<GeneralPartyInfo>
<NameInfo>
<CommlName>
<CommercialName>Book Transfer</CommercialName>
</CommlName>
</NameInfo>
</GeneralPartyInfo>
<ProducerInfo>
<ContractNumber>21222</ContractNumber>
<ProducerRoleCd />
<ProducerSubCode>Book Transfer</ProducerSubCode>
</ProducerInfo>
</Producer>
<InsuredOrPrincipal>
<InsuredOrPrincipalInfo>
<InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
</InsuredOrPrincipalInfo>
<GeneralPartyInfo>
<Addr>
<AddrTypeCd>MailingAddress</AddrTypeCd>
<Addr1>500 SHEFFIELD RD</Addr1>
<City>HOPKINSVILLE</City>
<StateProvCd>KY</StateProvCd>
<PostalCode>42240</PostalCode>
</Addr>
<NameInfo>
<CommlName>
<CommercialName>Erin</CommercialName>
</CommlName>
<PersonName>
<Surname>WOOD</Surname>
<GivenName>HAROLDRUTH</GivenName>
<OtherGivenName>O</OtherGivenName>
</PersonName>
</NameInfo>
<Communications>
<PhoneInfo>
<id>PHO1</id>
<PhoneNumber>2708865845</PhoneNumber>
<PhoneTypeCd>H</PhoneTypeCd>
</PhoneInfo>
</Communications>
</GeneralPartyInfo>
</InsuredOrPrincipal>
<InsuredOrPrincipal>
<InsuredOrPrincipalInfo>
<InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
</InsuredOrPrincipalInfo>
<GeneralPartyInfo>
<Communications>
<PhoneInfo>
<id>PHO1</id>
<PhoneNumber>************</PhoneNumber>
</PhoneInfo>
</Communications>
</GeneralPartyInfo>
</InsuredOrPrincipal>
<PersPolicy>
<PolicyNumber>1946023</PolicyNumber>
<ControllingStateProvCd>KY</ControllingStateProvCd>
<CurrentTermAmt>
<Amt>1373.9</Amt>
</CurrentTermAmt>
<PolicyNumber id="1946023">1946023</PolicyNumber>
<LOBCd>HOME</LOBCd>
</PersPolicy>
<HomeLineBusiness>
<PropertySchedule id="1946023">
<PropertyClassCd>JL</PropertyClassCd>
<ItemValueAmt>
<Amt>6439992550450250166001500</Amt>
</ItemValueAmt>
</PropertySchedule>
<Dwell>
<Coverage>
<CoverageCd>DWELL</CoverageCd>
<CoverageDesc>AMOUNT OF COVERAGE FOR DWELLING</CoverageDesc>
<CurrentTermAmt>
<Amt>845</Amt>
</CurrentTermAmt>
<Limit>
<FormatInteger>343300</FormatInteger>
</Limit>
<Deductible>
<DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
<FormatInteger>1000</FormatInteger>
</Deductible>
</Coverage>
<Coverage>
<CoverageCd>OS</CoverageCd>
<CoverageDesc>AMOUNT OF COVERAGE FOR OTHER STRUCTURES</CoverageDesc>
<CurrentTermAmt>
<Amt>0</Amt>
</CurrentTermAmt>
<Limit>
<FormatInteger>34330</FormatInteger>
</Limit>
<Deductible>
<FormatInteger>0</FormatInteger>
</Deductible>
</Coverage>
<DwellInspectionValuation>
<NumFamilies>1</NumFamilies>
<FireplaceInfo>
<NumFireplaces>0</NumFireplaces>
<NumChimneys>0</NumChimneys>
<FirePlaceTypeCd>None</FirePlaceTypeCd>
</FireplaceInfo>
</DwellInspectionValuation>
<Construction>
<YearBuilt>1992</YearBuilt>
<ConstructionCd>M</ConstructionCd>
</Construction>
<BldgProtection>
<DistanceToHydrant>
<UnitMeasurementCd>foot</UnitMeasurementCd>
<NumUnits>999</NumUnits>
</DistanceToHydrant>
<DistanceToFireStation>
<UnitMeasurementCd>mile</UnitMeasurementCd>
<NumUnits>5</NumUnits>
</DistanceToFireStation>
<FireProtectionClassCd>4</FireProtectionClassCd>
</BldgProtection>
<DwellOccupancy>
<OccupancyTypeCd>O</OccupancyTypeCd>
<DwellUseCd>1</DwellUseCd>
</DwellOccupancy>
</Dwell>
</HomeLineBusiness>
</HomePolicyQuoteInqRq>
</InsuranceSvcRq>
</ACORD>
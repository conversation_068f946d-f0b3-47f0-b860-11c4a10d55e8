<?xml-stylesheet type="text/xsl" href='http://vmrid-apiqia01/BookTransfer/DEVML/WebAPI/scripts/xslt/HomeXMLView.xslt'?>
<ACORD>
    <SignonRq>
        <ClientDt>2016-05-07</ClientDt>
        <ClientApp>
            <Org>Applied Systems</Org>
            <Name>EPIC</Name>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID/>
        <HomePolicyQuoteInqRq>
            <RqUID/>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>431683</ContractNumber>
                    <ProducerSubCode>Book Transfer</ProducerSubCode>
                </ProducerInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName/>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Mark Mouse</CommercialName>
                        </CommlName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>***********</TaxId>
                        </TaxIdentity>
                        <PersonName>
                            <GivenName>LUISA</GivenName>
                            <OtherGivenName/>
                            <Surname>COOPER</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>1259 E CAMBRIDGE ST</Addr1>
                        <Addr2/>
                        <Addr3/>
                        <City>SPRINGFIELD</City>
                        <StateProvCd>MO</StateProvCd>
                        <PostalCode>*********</PostalCode>
                        <CountryCd>USA</CountryCd>
                        <County/>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-1111121</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Business</CommunicationUseCd>
                            <PhoneNumber>******-1111121</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <PhoneNumber>******-1111121</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                        <EmailInfo>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt>1975-01-01</BirthDt>
                        <MaritalStatusCd>M</MaritalStatusCd>
                        <OccupationDesc/>
                        <LengthTimeEmployed>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <LengthTimeCurrentOccupation>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeCurrentOccupation>
                        <LengthTimeWithPreviousEmployer>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeWithPreviousEmployer>
                        <com.safeco_OccupationCd>Analyst</com.safeco_OccupationCd>
                        <com.safeco_IndustryCd>Insurance</com.safeco_IndustryCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <GivenName>JARED</GivenName>
                            <OtherGivenName/>
                            <Surname>COOPER</Surname>
                        </PersonName>
                        <CommlName>
                            <CommercialName>JARED COOPER</CommercialName>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt>1979-01-01</BirthDt>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <PolicyNumber/>
                <CompanyProductCd/>
                <LOBCd>HOME</LOBCd>
                <NAICCd/>
                <ControllingStateProvCd/>
                <ContractTerm>
                    <EffectiveDt>2017-09-18</EffectiveDt>
                    <ExpirationDt>2018-09-18</ExpirationDt>
                </ContractTerm>
                <BillingAccountNumber/>
                <BillingMethodCd>CPB</BillingMethodCd>
                <CurrentTermAmt>
                    <Amt>1194.00</Amt>
                </CurrentTermAmt>
                <PayorCd/>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber/>
                    <LOBCd/>
                    <InsurerName>USAA</InsurerName>
                    <ContractTerm/>
                    <Coverage>
                        <Limit>
                            <FormatInteger/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Other</PolicyCd>
                    <PolicyNumber/>
                    <LOBCd>AUTO</LOBCd>
                    <InsurerName/>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <PaymentPlanCd/>
                    <MethodPaymentCd/>
                    <DepositAmt>
                        <Amt/>
                    </DepositAmt>
                </PaymentOption>
                <QuestionAnswer>
                    <QuestionCd>GENRL22</QuestionCd>
                    <YesNoCd/>
                    <Explanation/>
                </QuestionAnswer>
                <AssignedRiskFacilityCd/>
                <PersApplicationInfo>
                    <LengthTimePreviousAddr>
                        <DurationPeriod>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </DurationPeriod>
                    </LengthTimePreviousAddr>
                    <LengthTimeCurrentAddr>
                        <DurationPeriod>
                            <NumUnits>7</NumUnits>
                            <UnitMeasurementCd>month</UnitMeasurementCd>
                        </DurationPeriod>
                        <DurationPeriod>
                            <NumUnits>51</NumUnits>
                            <UnitMeasurementCd>year</UnitMeasurementCd>
                        </DurationPeriod>
                    </LengthTimeCurrentAddr>
                </PersApplicationInfo>
            </PersPolicy>
            <Location id="001">
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>StreetAddress</AddrTypeCd>
                    <Addr1>1259 E CAMBRIDGE ST</Addr1>
                    <Addr2/>
                    <Addr3/>
                    <City>SPRINGFIELD</City>
                    <StateProvCd>MO</StateProvCd>
                    <PostalCode>*********</PostalCode>
                    <CountryCd>USA</CountryCd>
                    <County/>
                </Addr>
                <TaxCodeInfo>
                    <TaxCd/>
                </TaxCodeInfo>
                <FireDistrict>SPRINGFIELD</FireDistrict>
                <FireDistrictCd/>
                <com.safeco_FireDeptCity/>
                <com.safeco_FireDeptCounty/>
                <AdditionalInterest>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>ELIEZER OYOLA</CommercialName>
                            </CommlName>
                        </NameInfo>
                        <Addr>
                            <AddrTypeCd>MailingAddress</AddrTypeCd>
                            <Addr1>3355 N WILDAN CT</Addr1>
                            <Addr2/>
                            <Addr3/>
                            <City>SPRINGFIELD</City>
                            <StateProvCd>MO</StateProvCd>
                            <PostalCode>65803</PostalCode>
                            <CountryCd>USA</CountryCd>
                            <County/>
                        </Addr>
                        <Communications>
                            <PhoneInfo>
                                <CommunicationUseCd>Business</CommunicationUseCd>
                                <PhoneNumber/>
                            </PhoneInfo>
                        </Communications>
                    </GeneralPartyInfo>
                    <AdditionalInterestInfo>
                        <NatureInterestCd>MORTG</NatureInterestCd>
                        <InterestRank>2</InterestRank>
                        <AccountNumberId/>
                    </AdditionalInterestInfo>
                </AdditionalInterest>
                <AdditionalInterest>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>TELCOMM CREDITU UNION</CommercialName>
                            </CommlName>
                        </NameInfo>
                        <Addr>
                            <AddrTypeCd>MailingAddress</AddrTypeCd>
                            <Addr1>2155 E SUNSHINE ST</Addr1>
                            <Addr2/>
                            <Addr3/>
                            <City>SPRINGFIELD</City>
                            <StateProvCd>MO</StateProvCd>
                            <PostalCode>65804</PostalCode>
                            <CountryCd>USA</CountryCd>
                            <County/>
                        </Addr>
                        <Communications>
                            <PhoneInfo>
                                <CommunicationUseCd>Business</CommunicationUseCd>
                                <PhoneNumber/>
                            </PhoneInfo>
                        </Communications>
                    </GeneralPartyInfo>
                    <AdditionalInterestInfo>
                        <NatureInterestCd>MORT</NatureInterestCd>
                        <AccountNumberId/>
                    </AdditionalInterestInfo>
                </AdditionalInterest>
            </Location>
            <com.safeco_TransactionType>Ratecall1</com.safeco_TransactionType>
            <HomeLineBusiness>
                <LOBCd>HOME</LOBCd>
                <Dwell LocationRef="001">
                    <PolicyTypeCd>03</PolicyTypeCd>
                    <PurchaseDt>1965-06-01</PurchaseDt>
                    <com.safeco_PackageSelection>B</com.safeco_PackageSelection>
                    <PurchasePriceAmt>
                        <Amt/>
                    </PurchasePriceAmt>
                    <ThermostaticallyControlledCentralHeatInd>1</ThermostaticallyControlledCentralHeatInd>
                    <com.safeco_BusinessOnPremisesCategory/>
                    <NumEmployeesFullTimeResidence/>
                    <NumEmployeesPartTimeResidence/>
                    <OilStorageTankLocationCd/>
                    <FuelLineLocCd/>
                    <Construction>
                        <EIFSInstalledYr/>
                        <InsurerConstructionClassCd/>
                        <FoundationCd></FoundationCd>
                        <YearBuilt>1965</YearBuilt>
                        <NumUnits/>
                        <BldgCodeEffectivenessGradeCd/>
                        <BldgArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </BldgArea>
                        <NumStories/>
                        <BasementArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </BasementArea>
                        <RoofingMaterial>
                            <RoofMaterialCd></RoofMaterialCd>
                        </RoofingMaterial>
                        <StormShuttersCd/>
                        <ConstructionReasonCd/>
                        <AdditionArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </AdditionArea>
                        <ConstructionCd></ConstructionCd>
                        <com.safeco_SyntheticStuccoVerificationType/>
                    </Construction>
                    <DwellOccupancy>
                        <NumRooms/>
                        <NumApartments/>
                        <ResidenceTypeCd>DW</ResidenceTypeCd>
                        <OccupancyTypeCd>N</OccupancyTypeCd>
                        <DwellUseCd>1</DwellUseCd>
                        <LengthTimeRentedToOthers>
                            <NumUnits/>
                            <UnitMeasurementCd>WEE</UnitMeasurementCd>
                        </LengthTimeRentedToOthers>
                        <NumResidentsInHousehold/>
                    </DwellOccupancy>
                    <DwellRating>
                        <TerritoryCd/>
                        <PremiumGroup/>
                        <ECPremiumGroup/>
                        <FireECRate/>
                    </DwellRating>
                    <BldgProtection>
                        <NumFireDivisions/>
                        <NumUnitsInFireDivisions/>
                        <FireProtectionClassCd>02</FireProtectionClassCd>
                        <DistanceToFireStation>
                            <NumUnits>02</NumUnits>
                            <UnitMeasurementCd>mile</UnitMeasurementCd>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits>0025</NumUnits>
                            <UnitMeasurementCd>foot</UnitMeasurementCd>
                        </DistanceToHydrant>
                        <com.safeco_WindProtectionMeasures>
                            <YesNoCd/>
                        </com.safeco_WindProtectionMeasures>
                        <ProtectionDeviceSprinklerCd>N</ProtectionDeviceSprinklerCd>
                        <ProtectionDeviceBurglarCd/>
                        <ProtectionDeviceSmokeCd/>
                        <com.safeco_AlarmAndSecurity>
                            <BurglarAlarmAgent/>
                            <BurglarAlarmDate/>
                            <ItemDefinition>
                                <Manufacturer/>
                            </ItemDefinition>
                        </com.safeco_AlarmAndSecurity>
                        <DoorLockCd/>
                        <FireExtinguisherInd>0</FireExtinguisherInd>
                    </BldgProtection>
                    <BldgImprovements>
                        <ExteriorPaintYear/>
                        <HeatingImprovementCd/>
                        <HeatingImprovementYear/>
                        <PlumbingImprovementCd>C</PlumbingImprovementCd>
                        <PlumbingImprovementYear>1965</PlumbingImprovementYear>
                        <RoofingImprovementCd></RoofingImprovementCd>
                        <RoofingImprovementYear></RoofingImprovementYear>
                        <WiringImprovementCd>C</WiringImprovementCd>
                        <WiringImprovementYear>1965</WiringImprovementYear>
                    </BldgImprovements>
                    <DwellInspectionValuation>
                        <AirConditioningCd>com.safeco_None</AirConditioningCd>
                        <DwellStyleCd>Rambler</DwellStyleCd>
                        <EstimatedReplCostAmt>
                            <Amt/>
                        </EstimatedReplCostAmt>
                        <EvaluationMethodCd/>
                        <TotalArea>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                            <NumUnits>1499</NumUnits>
                        </TotalArea>
                        <GroundFloorArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </GroundFloorArea>
                        <HeatSourcePrimaryCd/>
                        <NumFamilies>1</NumFamilies>
                        <NumStoriesInDwellingCd>1</NumStoriesInDwellingCd>
                        <BathroomInfo>
                            <BathroomTypeCd>Full</BathroomTypeCd>
                            <NumBathrooms>02</NumBathrooms>
                        </BathroomInfo>
                        <BathroomInfo>
                            <BathroomTypeCd>Half</BathroomTypeCd>
                            <NumBathrooms>1</NumBathrooms>
                        </BathroomInfo>
                        <BathroomInfo>
                            <BathroomTypeCd>ThreeQuarter</BathroomTypeCd>
                            <NumBathrooms/>
                        </BathroomInfo>
                        <ExteriorWallMaterialInfo>
                            <ExteriorWallMaterialCd></ExteriorWallMaterialCd>
                        </ExteriorWallMaterialInfo>
                        <ExteriorWallMaterialInfo>
                            <ExteriorWallMaterialCd/>
                        </ExteriorWallMaterialInfo>
                        <FireplaceInfo>
                            <NumHearths/>
                            <NumChimneys/>
                        </FireplaceInfo>
                        <FireplaceInfo>
                            <FireplaceTypeCd/>
                            <NumFireplaces/>
                        </FireplaceInfo>
                        <GarageInfo>
                            <NumVehs/>
                            <GarageTypeCd/>
                            <SurfaceArea>
                                <NumUnits/>
                                <UnitMeasurementCd>FTK</UnitMeasurementCd>
                            </SurfaceArea>
                        </GarageInfo>
                        <PorchInfo>
                            <SurfaceArea>
                                <NumUnits/>
                                <UnitMeasurementCd>FTK</UnitMeasurementCd>
                            </SurfaceArea>
                            <BreezewayTypeCd/>
                        </PorchInfo>
                        <PorchInfo>
                            <SurfaceArea>
                                <NumUnits/>
                                <UnitMeasurementCd>FTK</UnitMeasurementCd>
                            </SurfaceArea>
                            <PorchTypeCd/>
                        </PorchInfo>
                        <MarketValueAmt/>
                        <FinishedAtticArea>
                            <NumUnits/>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </FinishedAtticArea>
                        <TotalLivingArea>
                            <NumUnits></NumUnits>
                            <UnitMeasurementCd>square foot</UnitMeasurementCd>
                        </TotalLivingArea>
                        <OtherFeaturesInfo>
                            <OtherFeaturesDesc>com.safeco_none</OtherFeaturesDesc>
                        </OtherFeaturesInfo>
                    </DwellInspectionValuation>
                    <SwimmingPool>
                        <ApprovedFenceInd/>
                        <AboveGroundInd/>
                    </SwimmingPool>
                    <Coverage>
                        <CoverageCd>DWELL</CoverageCd>
                        <Limit>
                            <FormatInteger>166000</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>1000</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>1</FormatPct>
                            <DeductibleTypeCd/>
                            <DeductibleAppliesToCd>WindHail</DeductibleAppliesToCd>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>EQPBK</CoverageCd>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PIHOM</CoverageCd>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LOU</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>3000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>AALMT</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OS</CoverageCd>
                        <Limit>
                            <FormatInteger>0016600</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PL</CoverageCd>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FDC</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>IHBUS</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LAC</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PP</CoverageCd>
                        <Limit>
                            <FormatInteger>124500</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_EDC</CoverageCd>
                        <Limit>
                            <FormatPct/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BOLAW</CoverageCd>
                        <Limit>
                            <FormatPct/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FVREP</CoverageCd>
                        <Option>
                            <OptionCd>Y</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FRAUD</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ROOF</CoverageCd>
                        <Option>
                            <OptionCd/>
                            <OptionValue/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SEWER</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WATER</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ERQK</CoverageCd>
                        <Deductible>
                            <FormatPct/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNJWF</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CCSV</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>AASPC</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SPPP</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SPKCR</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SECUR</CoverageCd>
                        <Option>
                            <OptionCd>Y</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BARCR</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WINDX</CoverageCd>
                        <Option>
                            <OptionCd>N</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FREEZ</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SINK</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>IFPL</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MIN</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>HDACA</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LLF</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>THFTB</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SUMP</CoverageCd>
                        <CoverageDesc>Water Back-up and Sump Overflow</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <Form>
                            <FormNumber/>
                        </Form>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BOLEX</CoverageCd>
                        <CoverageDesc>HIG Building Ordinance and Law Extension of Coverage (H447)</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <Form>
                            <FormNumber/>
                        </Form>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ACCT</CoverageCd>
                        <CoverageDesc>Multi Policy Credit</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <Form>
                            <FormNumber>CR ACCOUNT</FormNumber>
                        </Form>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MLDLI</CoverageCd>
                        <CoverageDesc>Mold Liability</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <Form>
                            <FormNumber/>
                        </Form>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>APN</CoverageCd>
                        <CoverageDesc>Advantage Plus</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <Form>
                            <FormNumber/>
                        </Form>
                    </Coverage>
                    <QuestionAnswer>
                        <QuestionCd>GENRL15</QuestionCd>
                        <YesNoCd>N</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL20</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL28</QuestionCd>
                        <YesNoCd/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL32</QuestionCd>
                        <YesNoCd/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL16</QuestionCd>
                        <YesNoCd>N</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME08</QuestionCd>
                        <YesNoCd/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME06</QuestionCd>
                        <YesNoCd/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME07</QuestionCd>
                        <YesNoCd/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME02</QuestionCd>
                        <YesNoCd>N</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>com.safeco_BusinessIncidental</QuestionCd>
                        <YesNoCd>Y</YesNoCd>
                    </QuestionAnswer>
                    <DwellStateSupplement>
                        <DistanceToOceanOrOtherBodyWater>
                            <NumUnits/>
                        </DistanceToOceanOrOtherBodyWater>
                        <SeacoastOrOtherBodyWaterProximityCd>TidalWater</SeacoastOrOtherBodyWaterProximityCd>
                    </DwellStateSupplement>
                    <InspectionInfo>
                        <InspectionCd>com.safeco_AgentOut</InspectionCd>
                        <InspectionDt>2015-09-28</InspectionDt>
                    </InspectionInfo>
                    <OtherHeatSourcePrimaryDesc/>
                    <NumEmployeesFullTimeResidence/>
                    <AnimalExposureInfo>
                        <BiteHistoryInd>0</BiteHistoryInd>
                        <DogTypeCd>NO</DogTypeCd>
                        <AnimalTypeCd>N</AnimalTypeCd>
                    </AnimalExposureInfo>
                    <HeatingUnitInfo>
                        <HeatingUnitCd>P</HeatingUnitCd>
                        <FuelTypeCd/>
                    </HeatingUnitInfo>
                    <AreaTypeSurroundingsCd>IN</AreaTypeSurroundingsCd>
                    <HousekeepingConditionCd>GD</HousekeepingConditionCd>
                </Dwell>
                <ResidenceBusiness LocationRef="">
                    <ClassCd/>
                    <LegalEntityCd/>
                    <GeneralLiabilityCd/>
                    <NAICSCd/>
                    <NatureBusinessCd/>
                    <BusinessArea>
                        <NumUnits/>
                        <UnitMeasurementCd>FTK</UnitMeasurementCd>
                    </BusinessArea>
                    <AnnualSalesAmt>
                        <Amt/>
                    </AnnualSalesAmt>
                    <TotalPayrollAmt>
                        <Amt/>
                    </TotalPayrollAmt>
                    <NumVisitors/>
                    <NumEmployeesPartTime/>
                    <NumEmployeesFullTime/>
                    <BusinessHoursStartTime/>
                    <BusinessHoursCloseTime/>
                    <NumLosses/>
                    <BusinessDesc/>
                    <TaxIdentity>
                        <TaxIdTypeCd>FEIN</TaxIdTypeCd>
                        <TaxId/>
                    </TaxIdentity>
                </ResidenceBusiness>
                <Watercraft>
                    <Horsepower>
                        <NumUnits/>
                    </Horsepower>
                    <Length>
                        <UnitMeasurementCd>foot</UnitMeasurementCd>
                        <NumUnits/>
                    </Length>
                    <com.safeco_OperatorAgesDesc/>
                    <NumUnits/>
                </Watercraft>
                <QuestionAnswer>
                    <QuestionCd>GENRL06</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
            </HomeLineBusiness>
        </HomePolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
<ACORD>
    <SignonRq>
        <ClientDt>2016-03-04</ClientDt>
        <ClientApp>
            <Org>Applied Systems</Org>
            <Name>TAM</Name>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>41b513bc-bb09-42dc-9455-528c14d17ad2</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <com.safeco_TransactionType>Ratecall1</com.safeco_TransactionType>
            <RqUID>41b513bc-bb09-42dc-9455-528c14d17ad2</RqUID>
            <TransactionRequestDt>2015-07-10T08:11:37</TransactionRequestDt>
            <TransactionEffectiveDt>2015-07-10</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ProducerSubCode>WW</ProducerSubCode>
                    <ContractNumber>897404</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>FJPNCB3</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Percy Baratheon</CommercialName>
                        </CommlName>
                        <PersonName>
                            <Surname>FIERRO</Surname>
                            <GivenName>GUADALUPE</GivenName>
                        </PersonName>
                    </NameInfo>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-1111121</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <GenderCd>F</GenderCd>
                        <BirthDt>1975-11-11</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <LOBCd>AUTOP</LOBCd>
                <com.safeco_Esign>N</com.safeco_Esign>
                <com.safeco_PropertyCrossSell>
                    <com.safeco_PropertyAddressScrubYN>Y</com.safeco_PropertyAddressScrubYN>
                </com.safeco_PropertyCrossSell>
                <ContractTerm>
                    <EffectiveDt>2015-05-10</EffectiveDt>
                    <ExpirationDt>2016-05-10</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                        <UnitMeasurementCd>Months</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>Other</InsurerName>
                    <LOBCd>Auto</LOBCd>
                    <ContractTerm>
                        <EffectiveDt>2015-05-10</EffectiveDt>
                        <ExpirationDt>2016-07-10</ExpirationDt>
                    </ContractTerm>
                    <LengthTimeWithPreviousInsurer>
                        <NumUnits>75</NumUnits>
                        <UnitMeasurementCd>Months</UnitMeasurementCd>
                    </LengthTimeWithPreviousInsurer>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>17.72</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </OtherOrPriorPolicy>
                <CreditScoreInfo>
                    <CreditScore>640</CreditScore>
                    <CreditScoreDt>2015-07-10</CreditScoreDt>
                </CreditScoreInfo>
                <QuestionAnswer>
                    <QuestionCd>Current_Insurance_Value</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>CI</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Cancelled_Declined?</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Excluded_Vehicle_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>All_Vehicles_Covered_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Credit_Accept_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Accept_Contract_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>002</Explanation>
                </QuestionAnswer>
                <PersApplicationInfo>
                    <InsuredOrPrincipal>
                        <GeneralPartyInfo>
                            <Addr>
                                <AddrTypeCd>StreetAddress</AddrTypeCd>
                                <Addr1>1025 5TH ST</Addr1>
                                <City>DEL NORTE</City>
                                <StateProvCd>CO</StateProvCd>
                                <PostalCode>81132</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                    </InsuredOrPrincipal>
                    <ResidenceOwnedRentedCd>OWNED</ResidenceOwnedRentedCd>
                    <NumResidentsInHousehold>1</NumResidentsInHousehold>
                    <NumVehsInHousehold>1</NumVehsInHousehold>
                    <ResidenceTypeCd>DW</ResidenceTypeCd>
                </PersApplicationInfo>
                <DriverVeh>
                    <UsePct>100</UsePct>
                </DriverVeh>
                <CurrentTermAmt>
                    <Amt>69875.55</Amt>
                </CurrentTermAmt>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <PersDriver id="1">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Sagan</Surname>
                                <GivenName>Gregor</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1980-01-01</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                            <com.safeco_IndustryCd>Construct/EnrgyTrds</com.safeco_IndustryCd>
                            <com.safeco_OccupationCd>Laborer/Worker</com.safeco_OccupationCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>1991-11-11</LicensedDt>
                            <DriversLicenseNumber>1234567</DriversLicenseNumber>
                            <StateProvCd>CO</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>BS</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh id="V1">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh id="V2">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh id="V3">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh id="V4">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh id="V5">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh id="V6">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location>
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>1025 5TH ST</Addr1>
                    <City>DEL NORTE</City>
                    <StateProvCd>CO</StateProvCd>
                    <PostalCode>81132</PostalCode>
                </Addr>
            </Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
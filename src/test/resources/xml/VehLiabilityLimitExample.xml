<ACORD>
    <SignonRq>
        <ClientDt>2018-08-09</ClientDt>
        <ClientApp>
            <Org>Applied Systems</Org>
            <Name>EPIC</Name>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>2faa35f61651eed6a168000ac103d1f0</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <com.safeco_TransactionType>QuoteRq</com.safeco_TransactionType>
            <RqUID>2faa35f61651eed6a167c73ac103d1f0</RqUID>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>03431885</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>MILLERCH36</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>BART EHINGER</CommercialName>
                        </CommlName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                        </TaxIdentity>
                        <PersonName>
                            <GivenName>BART</GivenName>
                            <Surname>EHINGER</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>715 Avocet Way</Addr1>
                        <Addr2>715 Avocet Way</Addr2>
                        <Addr3/>
                        <City>Arroyo Grande</City>
                        <StateProvCd>CA</StateProvCd>
                        <PostalCode>*********</PostalCode>
                        <CountryCd>USA</CountryCd>
                        <County>San Luis Obispo</County>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-3103005</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                        <EmailInfo>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <EmailAddr/>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <MaritalStatusCd>U</MaritalStatusCd>
                        <LengthTimeEmployed>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <LengthTimeCurrentOccupation>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeCurrentOccupation>
                        <LengthTimeWithPreviousEmployer>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeWithPreviousEmployer>
                        <MiscParty>
                            <MiscPartyInfo>
                                <MiscPartyRoleCd>Employer</MiscPartyRoleCd>
                            </MiscPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
                <LengthTimeKnownByAgentBroker>
                    <UnitMeasurementCd>ANN</UnitMeasurementCd>
                </LengthTimeKnownByAgentBroker>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <ItemIdInfo/>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>BART EHINGER</CommercialName>
                        </CommlName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                        </TaxIdentity>
                        <PersonName>
                            <GivenName>BART</GivenName>
                            <Surname>EHINGER</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>Bart Ehinger</Addr1>
                        <Addr2>715 Avocet Way</Addr2>
                        <Addr3/>
                        <City>Arroyo Grande</City>
                        <StateProvCd>CA</StateProvCd>
                        <PostalCode>93420</PostalCode>
                        <CountryCd>USA</CountryCd>
                        <County/>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-3103005</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                        <EmailInfo>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <EmailAddr/>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <MaritalStatusCd>U</MaritalStatusCd>
                        <LengthTimeEmployed>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <LengthTimeCurrentOccupation>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeCurrentOccupation>
                        <LengthTimeWithPreviousEmployer>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeWithPreviousEmployer>
                        <MiscParty>
                            <MiscPartyInfo>
                                <MiscPartyRoleCd>Employer</MiscPartyRoleCd>
                            </MiscPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
                <LengthTimeKnownByAgentBroker>
                    <UnitMeasurementCd>ANN</UnitMeasurementCd>
                </LengthTimeKnownByAgentBroker>
            </InsuredOrPrincipal>
            <PersPolicy>
                <PersApplicationInfo>
                    <ResidenceTypeCd>DW</ResidenceTypeCd>
                </PersApplicationInfo>
                <PolicyNumber>PPA0016303768</PolicyNumber>
                <CompanyProductCd/>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>19100</NAICCd>
                <ControllingStateProvCd>CA</ControllingStateProvCd>
                <ContractTerm>
                    <EffectiveDt>2019-08-01</EffectiveDt>
                    <ExpirationDt>2020-08-01</ExpirationDt>
                </ContractTerm>
                <BillingAccountNumber>**********</BillingAccountNumber>
                <BillingMethodCd>CPB</BillingMethodCd>
                <CurrentTermAmt>
                    <Amt>2455.76</Amt>
                </CurrentTermAmt>
                <PayorCd>IN</PayorCd>
                <Form>
                    <FormNumber>IN0000</FormNumber>
                    <FormName>PRIVACY STATEMENT</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2009-04-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>IN0002</FormNumber>
                    <FormName>EVIDENCE OF INSURANCE IS REQUIRED WITH REGISTRATIO</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>1997-03-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>IN0521</FormNumber>
                    <FormName>NOTICE OF INSURANCE INFO. PRACTICES &amp; SPECIAL GOOD</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>1994-07-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>IN0531</FormNumber>
                    <FormName>IMPORTANT NOTICE - EXPLANATION OF EFFECTS OF ACCID</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2013-09-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>IN0532</FormNumber>
                    <FormName>NOTICE OF LIMITS OF FUTURE COVERAGE</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>1991-01-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>IN0542</FormNumber>
                    <FormName>CONSUMER COMPLAINTS AND INFORMATION</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2008-03-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>IN0611</FormNumber>
                    <FormName>IMPORTANT INSURANCE INFORMATION</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2001-04-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>IN0664</FormNumber>
                    <FormName>IMPORTANT NOTICE - PROGRAM DESCRIPTIONS</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2003-02-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>IN0693</FormNumber>
                    <FormName>MOTOR VEHICLE ACCIDENT AND VIOLATION INFORMATION</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2010-01-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>IN1254</FormNumber>
                    <FormName>IMPORTANT NOTICE - AVAILABILITY OF SPECIAL DISCOUN</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2016-08-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>IN1462</FormNumber>
                    <FormName>IMPORTANT NOTICE - 3RD PARY DESIGNATION</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2016-01-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>AA0001</FormNumber>
                    <FormName>PERSONAL AUTO POLICY</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>1986-09-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>AA0001A</FormNumber>
                    <FormName>SIGNATURE PAGE</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2011-11-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>AA0007</FormNumber>
                    <FormName>CHANGES PROVISION</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>1992-11-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>AA0008A</FormNumber>
                    <FormName>ALLIED EXTRA COVERAGES</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2009-11-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>AA0060</FormNumber>
                    <FormName>SPECIAL PHYSICAL DAMAGE COVERAGE</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2000-09-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>AA0073</FormNumber>
                    <FormName>WAIVER OF COLLISION DEDUCTIBLE</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>1997-06-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>AA0169</FormNumber>
                    <FormName>AMENDMENT OF POLICY PROVISIONS-CALIFORNIA</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2014-10-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>AA0319</FormNumber>
                    <FormName>LEGAL RESPONSIBILITY</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>1989-01-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>AA0487</FormNumber>
                    <FormName>UNINSURED MOTORISTS COVERAGE - CALIFORNIA</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2009-11-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>AA1418</FormNumber>
                    <FormName>ROADSIDE ASSISTANCE COVERAGE ENDORSEMENT</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2012-02-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>PP0315</FormNumber>
                    <FormName>FULL SAFETY GLASS COVERAGE</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2006-03-01</EditionDt>
                </Form>
                <Form>
                    <FormNumber>SN0559C</FormNumber>
                    <FormName>HOW YOUR INSURANCE PREMIUM IS DEVELOPED</FormName>
                    <CopyrightOwnerCd/>
                    <CopyrightOwnerNameCd/>
                    <FormDesc/>
                    <EditionDt>2016-08-01</EditionDt>
                </Form>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber>PPA0016303768</PolicyNumber>
                    <LOBCd>AUTOP</LOBCd>
                    <InsurerName>AMCO INSURANCE CO.</InsurerName>
                    <ContractTerm/>
                    <Coverage>
                        <Limit>
                            <FormatInteger/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <PaymentPlanCd/>
                    <DayMonthDue/>
                    <MethodPaymentCd/>
                    <DepositAmt>
                        <Amt/>
                    </DepositAmt>
                </PaymentOption>
                <MiscParty>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>CHRISTINE MILLER</CommercialName>
                            </CommlName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>AdditionalInsured</MiscPartyRoleCd>
                    </MiscPartyInfo>
                </MiscParty>
                <AssignedRiskFacilityCd/>
                <AccidentViolation DriverRef="DRV1">
                    <AccidentViolationCd>A</AccidentViolationCd>
                    <AccidentViolationDt>2015-08-26</AccidentViolationDt>
                    <DamageTotalAmt>
                        <Amt/>
                    </DamageTotalAmt>
                    <NumSurchargePoints/>
                    <AccidentViolationDesc>THEFT/VANDALISM OF $500 OR MOR</AccidentViolationDesc>
                    <PlaceIncident/>
                    <AccidentViolationRecordTypeCd>THEFT</AccidentViolationRecordTypeCd>
                </AccidentViolation>
                <DriverVeh DriverRef="DRV1" VehRef="VEH2">
                    <UsePct>100</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV1" VehRef="VEH1">
                    <UsePct>90</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV2" VehRef="VEH1">
                    <UsePct>10</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV2" VehRef="VEH2">
                    <UsePct>100</UsePct>
                </DriverVeh>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <PersDriver id="DRV1">
                    <ItemIdInfo>
                        <AgencyId>1</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Ehinger</Surname>
                                <GivenName>Bart</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1962-11-12</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>********</DriversLicenseNumber>
                            <StateProvCd>CA</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber>********</LicensePermitNumber>
                            <StateProvCd>CA</StateProvCd>
                            <TotalNumLicensePoints/>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>0</MatureDriverInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV2">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Miller</Surname>
                                <GivenName>Christine</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1961-09-26</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>********</DriversLicenseNumber>
                            <StateProvCd>CA</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber>********</LicensePermitNumber>
                            <StateProvCd>CA</StateProvCd>
                            <TotalNumLicensePoints/>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <GoodDriverInd>0</GoodDriverInd>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>0</MatureDriverInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="LOC1" RatedDriverRef="DRV2" id="VEH1">
                    <ItemIdInfo>
                        <AgencyId>1</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>BMW</Manufacturer>
                    <Model>640I</Model>
                    <ModelYear>2012</ModelYear>
                    <VehBodyTypeCd>CONVT</VehBodyTypeCd>
                    <VehBodyTypeDesc/>
                    <Registration>
                        <RegistrationTypeCd>LicensePlate</RegistrationTypeCd>
                        <RegistrationId/>
                        <StateProvCd>CA</StateProvCd>
                    </Registration>
                    <POLKRestraintDeviceCd>N</POLKRestraintDeviceCd>
                    <CostNewAmt>
                        <Amt/>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek/>
                    <EstimatedAnnualDistance>
                        <NumUnits>7000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt>
                        <Amt>1307.2600</Amt>
                    </FullTermAmt>
                    <Horsepower>
                        <NumUnits/>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <TerritoryCd>998</TerritoryCd>
                    <VehIdentificationNumber>WBALW7C58CC618694</VehIdentificationNumber>
                    <VehSymbolCd>64</VehSymbolCd>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Corner Stone Real Estate</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <AddrTypeCd>MailingAddress</AddrTypeCd>
                                <Addr1>2665 Shell Beach Road Ste J</Addr1>
                                <Addr2/>
                                <Addr3/>
                                <City>Shell Beach</City>
                                <StateProvCd>CA</StateProvCd>
                                <PostalCode>93449</PostalCode>
                                <CountryCd>USA</CountryCd>
                                <County/>
                            </Addr>
                            <Communications>
                                <PhoneInfo>
                                    <PhoneTypeCd>Phone</PhoneTypeCd>
                                </PhoneInfo>
                                <EmailInfo>
                                    <EmailAddr/>
                                </EmailInfo>
                            </Communications>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>ADDIN</NatureInterestCd>
                            <InterestRank/>
                            <InterestIdNumber/>
                            <FinancedAmt>
                                <Amt/>
                            </FinancedAmt>
                            <ReasonDesc/>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <GrossVehWeight>
                        <NumUnits/>
                        <UnitMeasurementCd>LBR</UnitMeasurementCd>
                    </GrossVehWeight>
                    <CarpoolInd>0</CarpoolInd>
                    <DistanceOneWay>
                        <NumUnits/>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <MultiCarDiscountInd>1</MultiCarDiscountInd>
                    <LengthTimePerMonth>
                        <NumUnits/>
                        <UnitMeasurementCd>WEE</UnitMeasurementCd>
                    </LengthTimePerMonth>
                    <OdometerReading>
                        <NumUnits/>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>Y</AntiTheftDeviceCd>
                    <RateClassCd>G01710</RateClassCd>
                    <VehPerformanceCd>BASIC</VehPerformanceCd>
                    <VehSalvageTitleNumber/>
                    <VehUseCd>BU</VehUseCd>
                    <AirBagTypeCd>Y</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>245.28</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>586.42</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>G</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt>130.42</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CWAIV</CoverageCd>
                        <CoverageDesc/>
                        <CurrentTermAmt>
                            <Amt>5.96</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>10000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>19.62</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>137.92</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>95.64</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OEM</CoverageCd>
                        <CoverageDesc>Special loss settlement Original Equip Manufactured parts</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt>86.0000</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>GLASS</CoverageCd>
                        <CoverageDesc>Company Unique Coverage</CoverageDesc>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>G</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LONGV</CoverageCd>
                        <CoverageDesc>Longevity Credit</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ANTHF</CoverageCd>
                        <CoverageDesc>Anti-Theft</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>AIRBG</CoverageCd>
                        <CoverageDesc>Company Unique Coverage</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CWAIV</CoverageCd>
                        <CoverageDesc>Waiver of Collision Deductible</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt>5.9600</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ROAD</CoverageCd>
                        <CoverageDesc>POLICY</CoverageDesc>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>PL</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd>PL</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt>42.4800</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>GDD</CoverageCd>
                        <CoverageDesc>Good Driver Discount</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <CollisionSymbolCd/>
                    <ComprehensiveOTCSymbolCd/>
                </PersVeh>
                <PersVeh LocationRef="LOC1" RatedDriverRef="DRV1" id="VEH2">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>GMC</Manufacturer>
                    <Model>SIERRA DEN</Model>
                    <ModelYear>2014</ModelYear>
                    <VehBodyTypeCd>PU</VehBodyTypeCd>
                    <VehBodyTypeDesc/>
                    <Registration>
                        <RegistrationTypeCd>LicensePlate</RegistrationTypeCd>
                        <RegistrationId/>
                        <StateProvCd>CA</StateProvCd>
                    </Registration>
                    <POLKRestraintDeviceCd>N</POLKRestraintDeviceCd>
                    <CostNewAmt>
                        <Amt/>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek/>
                    <EstimatedAnnualDistance>
                        <NumUnits>8200</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt>
                        <Amt>1102.5000</Amt>
                    </FullTermAmt>
                    <Horsepower>
                        <NumUnits/>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <TerritoryCd>998</TerritoryCd>
                    <VehIdentificationNumber>3GTU2WEC7EG338421</VehIdentificationNumber>
                    <VehSymbolCd>31</VehSymbolCd>
                    <GrossVehWeight>
                        <NumUnits/>
                        <UnitMeasurementCd>LBR</UnitMeasurementCd>
                    </GrossVehWeight>
                    <CarpoolInd>0</CarpoolInd>
                    <DistanceOneWay>
                        <NumUnits/>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <MultiCarDiscountInd>1</MultiCarDiscountInd>
                    <LengthTimePerMonth>
                        <NumUnits/>
                        <UnitMeasurementCd>WEE</UnitMeasurementCd>
                    </LengthTimePerMonth>
                    <OdometerReading>
                        <NumUnits/>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>Y</AntiTheftDeviceCd>
                    <RateClassCd>G01110</RateClassCd>
                    <VehPerformanceCd>BASIC</VehPerformanceCd>
                    <VehSalvageTitleNumber/>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>Y</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>275.40</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>401.30</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>G</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt>88.00</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CWAIV</CoverageCd>
                        <CoverageDesc/>
                        <CurrentTermAmt>
                            <Amt>5.96</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>10000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>14.36</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>163.98</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>97.44</Amt>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OEM</CoverageCd>
                        <CoverageDesc>Special loss settlement Original Equip Manufactured parts</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt>56.0600</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>GLASS</CoverageCd>
                        <CoverageDesc>Company Unique Coverage</CoverageDesc>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>G</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LONGV</CoverageCd>
                        <CoverageDesc>Longevity Credit</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ANTHF</CoverageCd>
                        <CoverageDesc>Anti-Theft</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>AIRBG</CoverageCd>
                        <CoverageDesc>Company Unique Coverage</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CWAIV</CoverageCd>
                        <CoverageDesc>Waiver of Collision Deductible</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt>5.9600</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>GDD</CoverageCd>
                        <CoverageDesc>Good Driver Discount</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <CollisionSymbolCd/>
                    <ComprehensiveOTCSymbolCd/>
                </PersVeh>
                <QuestionAnswer>
                    <QuestionCd>GENRL22</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                    <Explanation/>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL26</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                    <Explanation/>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>AUTOP11</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                    <Explanation/>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>AUTOB07</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                    <Explanation/>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>AUTOP01</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                    <Explanation/>
                </QuestionAnswer>
            </PersAutoLineBusiness>
            <Location id="LOC1">
                <ItemIdInfo>
                    <AgencyId>1</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>715 AVOCET WAY</Addr1>
                    <Addr2>715 Avocet Way</Addr2>
                    <Addr3/>
                    <City>ARROYO GRANDE</City>
                    <StateProvCd>CA</StateProvCd>
                    <PostalCode>*********</PostalCode>
                    <CountryCd>USA</CountryCd>
                    <County>SAN LUIS OBISPO</County>
                </Addr>
            </Location>
            <RemarkText>Type Code: P</RemarkText>
            <RemarkText>Coverage Code: ACCT</RemarkText>
            <RemarkText>Coverage Code: STSR1 Full Term Premium: 3.52</RemarkText>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
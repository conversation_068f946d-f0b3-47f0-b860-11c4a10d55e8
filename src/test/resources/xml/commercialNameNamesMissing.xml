<ACORD>
    <SignonRq>
        <ClientDt>2016-05-07</ClientDt>
        <ClientApp>
            <Org>Applied Systems</Org>
            <Name>EPIC</Name>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq BypassScrubbing="SkipNaming">
        <RqUID/>
        <PersAutoPolicyQuoteInqRq>
            <RqUID/>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>23PBP</ContractNumber>
                    <ProducerSubCode>Book Transfer</ProducerSubCode>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                        </TaxIdentity>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>29 Fulton Ln</Addr1>
                        <Addr2/>
                        <Addr3/>
                        <City>Starkville</City>
                        <StateProvCd>MS</StateProvCd>
                        <PostalCode>39759-6228</PostalCode>
                        <CountryCd>USA</CountryCd>
                        <County/>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-1111121</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                        <EmailInfo>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <MaritalStatusCd/>
                        <OccupationDesc/>
                        <LengthTimeEmployed>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <LengthTimeCurrentOccupation>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeCurrentOccupation>
                        <LengthTimeWithPreviousEmployer>
                            <NumUnits/>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeWithPreviousEmployer>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <GivenName/>
                            <OtherGivenName/>
                            <Surname/>
                        </PersonName>
                    </NameInfo>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <PolicyNumber></PolicyNumber>
                <CompanyProductCd>APA</CompanyProductCd>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd/>
                <ControllingStateProvCd/>
                <ContractTerm>
                    <EffectiveDt>2016-05-28</EffectiveDt>
                    <ExpirationDt>2017-05-28</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                    </DurationPeriod>
                </ContractTerm>
                <BillingAccountNumber/>
                <BillingMethodCd>CPB</BillingMethodCd>
                <CurrentTermAmt>
                    <Amt>1334.00</Amt>
                </CurrentTermAmt>
                <PayorCd/>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber></PolicyNumber>
                    <LOBCd/>
                    <InsurerName>GuideOne</InsurerName>
                    <ContractTerm>
                        <ExpirationDt>2017-03-18</ExpirationDt>
                    </ContractTerm>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                    </Coverage>
                    <LengthTimeWithPreviousInsurer>
                        <NumUnits>12</NumUnits>
                    </LengthTimeWithPreviousInsurer>
                </OtherOrPriorPolicy>
                <PersApplicationInfo>
                    <ResidenceTypeCd>DW</ResidenceTypeCd>
                </PersApplicationInfo>
                <PaymentOption>
                    <PaymentPlanCd>OT</PaymentPlanCd>
                    <DayMonthDue/>
                    <MethodPaymentCd/>
                    <DepositAmt>
                        <Amt/>
                    </DepositAmt>
                </PaymentOption>
                <AssignedRiskFacilityCd/>
                <QuestionAnswer>
                    <QuestionCd>AUPMA01</QuestionCd>
                    <YesNoCd/>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>com.safeco_CollectedAndRemitted</QuestionCd>
                    <YesNoCd/>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Current_Insurance_Value</QuestionCd>
                    <Explanation>CI</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>AUPMA18</QuestionCd>
                    <YesNoCd/>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>AUPMA19</QuestionCd>
                    <YesNoCd/>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>com.safeco_AutoFraudConviction</QuestionCd>
                    <YesNoCd/>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>com.safeco_SeatingCapacity8</QuestionCd>
                    <YesNoCd/>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>AUPMA05</QuestionCd>
                    <YesNoCd/>
                    <Explanation/>
                </QuestionAnswer>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <Coverage>
                    <CoverageCd>ADDA</CoverageCd>
                    <Limit>
                        <FormatInteger/>
                    </Limit>
                    <Option>
                        <OptionCd/>
                    </Option>
                </Coverage>
                <Coverage>
                    <CoverageCd>ADDG</CoverageCd>
                    <Limit>
                        <FormatInteger/>
                        <LimitAppliesToCd>PerWeek</LimitAppliesToCd>
                    </Limit>
                    <Option>
                        <OptionCd/>
                    </Option>
                </Coverage>
                <Coverage>
                    <CoverageCd>EXNON</CoverageCd>
                    <Option>
                        <OptionCd/>
                    </Option>
                </Coverage>
                <Coverage>
                    <CoverageCd>TORT</CoverageCd>
                    <Option>
                        <OptionCd/>
                    </Option>
                </Coverage>
                <PersDriver id="DRV836802402">
                    <ItemIdInfo>
                        <AgencyId/>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Ketchum</Surname>
                                <GivenName>Tyrion</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1956-01-01</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc/>
                            <com.safeco_IndustryCd>Med/Soc Svcs/Relig</com.safeco_IndustryCd>
                            <com.safeco_OccupationCd>Other</com.safeco_OccupationCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>1234567</DriversLicenseNumber>
                            <LicensedDt>1972-06-01</LicensedDt>
                            <LicenseClassCd>VALID</LicenseClassCd>
                            <StateProvCd>MS</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber/>
                            <StateProvCd/>
                            <TotalNumLicensePoints/>
                            <LicensedDt/>
                            <FirstLicensedCurrentStateDt/>
                        </License>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseCrDate</QuestionCd>
                            <YesNoCd/>
                            <Explanation/>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>NO</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd/>
                            <Explanation>HSD</Explanation>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>GENRL22</QuestionCd>
                            <YesNoCd/>
                            <Explanation/>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Senior_PIP_Discount</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DefensiveDriverDt/>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <GoodDriverInd/>
                        <GoodStudentCd>Y</GoodStudentCd>
                        <MatureDriverInd/>
                        <DriverTypeCd>R</DriverTypeCd>
                        <FinancialResponsibilityFiling>
                            <com.safeco_FilingTypeCd>SR22</com.safeco_FilingTypeCd>
                            <FilingStatusCd>N</FilingStatusCd>
                            <FilingOriginalDate/>
                            <com.safeco_FilingEndDate/>
                            <StateProvCd>MS</StateProvCd>
                            <com.safeco_FilingCaseNumber/>
                        </FinancialResponsibilityFiling>
                        <MembershipInfo>
                            <MembershipId/>
                            <MembershipTypeCd/>
                        </MembershipInfo>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV346802407">
                    <ItemIdInfo>
                        <AgencyId/>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Dee</Surname>
                                <GivenName>Grey</GivenName>
                                <OtherGivenName/>
                                <TitlePrefix/>
                                <NameSuffix/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1959-01-01</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc/>
                            <com.safeco_IndustryCd>Med/Soc Svcs/Relig</com.safeco_IndustryCd>
                            <com.safeco_OccupationCd>Other</com.safeco_OccupationCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>7654521</DriversLicenseNumber>
                            <LicensedDt>1975-06-01</LicensedDt>
                            <LicenseClassCd>VALID</LicenseClassCd>
                            <StateProvCd>MS</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber/>
                            <StateProvCd/>
                            <TotalNumLicensePoints/>
                            <LicensedDt/>
                            <FirstLicensedCurrentStateDt/>
                        </License>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseCrDate</QuestionCd>
                            <YesNoCd/>
                            <Explanation/>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>NO</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd/>
                            <Explanation>HSD</Explanation>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>GENRL22</QuestionCd>
                            <YesNoCd/>
                            <Explanation/>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Senior_PIP_Discount</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DefensiveDriverDt/>
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <GoodDriverInd/>
                        <GoodStudentCd>Y</GoodStudentCd>
                        <MatureDriverInd/>
                        <DriverTypeCd>R</DriverTypeCd>
                        <FinancialResponsibilityFiling>
                            <com.safeco_FilingTypeCd>SR22</com.safeco_FilingTypeCd>
                            <FilingStatusCd>N</FilingStatusCd>
                            <FilingOriginalDate/>
                            <com.safeco_FilingEndDate/>
                            <StateProvCd>MS</StateProvCd>
                            <com.safeco_FilingCaseNumber/>
                        </FinancialResponsibilityFiling>
                        <MembershipInfo>
                            <MembershipId/>
                            <MembershipTypeCd/>
                        </MembershipInfo>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="Gar1" RatedDriverRef="" id="VEH1">
                    <ItemIdInfo>
                        <AgencyId/>
                    </ItemIdInfo>
                    <Manufacturer>CADI</Manufacturer>
                    <Model>CTS</Model>
                    <ModelYear>2005</ModelYear>
                    <VehBodyTypeCd/>
                    <VehBodyTypeDesc/>
                    <VehTypeCd/>
                    <MultiCarDiscountInd/>
                    <Registration>
                        <RegistrationTypeCd>LicensePlate</RegistrationTypeCd>
                        <RegistrationId/>
                        <RegistrationTypeId/>
                        <StateProvCd/>
                    </Registration>
                    <POLKRestraintDeviceCd/>
                    <CostNewAmt>
                        <Amt>30190</Amt>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek/>
                    <NumCylinders/>
                    <PurchaseDt>2005-03-18</PurchaseDt>
                    <VehInspectionStatusCd/>
                    <DaytimeRunningLightInd>1</DaytimeRunningLightInd>
                    <EstimatedAnnualDistance>
                        <NumUnits>1500</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt>
                        <Amt>120.00</Amt>
                    </FullTermAmt>
                    <Horsepower>
                        <NumUnits/>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <TerritoryCd/>
                    <VehIdentificationNumber>1G6DM56T350235684</VehIdentificationNumber>
                    <VehSymbolCd/>
                    <GrossVehWeight>
                        <NumUnits/>
                        <UnitMeasurementCd>LBR</UnitMeasurementCd>
                    </GrossVehWeight>
                    <CarpoolInd/>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NewVehInd>0</NewVehInd>
                    <LengthTimePerMonth>
                        <NumUnits/>
                        <UnitMeasurementCd>WEE</UnitMeasurementCd>
                    </LengthTimePerMonth>
                    <OdometerReading>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>J</AntiTheftDeviceCd>
                    <AntiLockBrakeCd>Y</AntiLockBrakeCd>
                    <RateClassCd/>
                    <VehPerformanceCd/>
                    <VehSalvageTitleNumber/>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>Driver/passenger front, side, head and rear curtain airbags</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDUM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OPTBI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_GPCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>APIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RREIM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>MaxAmount</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMSLA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMPDA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <Deductible>
                            <FormatInteger/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SORPE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LUSE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ADB</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd>N</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PPI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDSP</CoverageCd>
                        <CoverageDesc>Underinsured Split Limit (Policy Level Coverage)</CoverageDesc>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ANTHF</CoverageCd>
                        <CoverageDesc>Anti-Theft</CoverageDesc>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RPV</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SOPKG</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_CovLevel</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>B</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ABS</CoverageCd>
                        <CoverageDesc>Anti-lock brake discount</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <CollisionSymbolCd/>
                    <ComprehensiveOTCSymbolCd/>
                </PersVeh>
                <PersVeh LocationRef="Gar2" RatedDriverRef="" id="VEH2">
                    <ItemIdInfo>
                        <AgencyId/>
                    </ItemIdInfo>
                    <Manufacturer>TYTA</Manufacturer>
                    <Model>CAMRY L/LE/SE/XLE</Model>
                    <ModelYear>2014</ModelYear>
                    <VehBodyTypeCd/>
                    <VehBodyTypeDesc/>
                    <VehTypeCd/>
                    <MultiCarDiscountInd/>
                    <Registration>
                        <RegistrationTypeCd>LicensePlate</RegistrationTypeCd>
                        <RegistrationId/>
                        <RegistrationTypeId/>
                        <StateProvCd/>
                    </Registration>
                    <POLKRestraintDeviceCd/>
                    <CostNewAmt>
                        <Amt>22235</Amt>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek/>
                    <NumCylinders/>
                    <PurchaseDt>2014-03-18</PurchaseDt>
                    <VehInspectionStatusCd/>
                    <DaytimeRunningLightInd>1</DaytimeRunningLightInd>
                    <EstimatedAnnualDistance>
                        <NumUnits>1300</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt>
                        <Amt>492.00</Amt>
                    </FullTermAmt>
                    <Horsepower>
                        <NumUnits/>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <TerritoryCd/>
                    <VehIdentificationNumber>4T1BF1FK0EU340783</VehIdentificationNumber>
                    <VehSymbolCd/>
                    <GrossVehWeight>
                        <NumUnits/>
                        <UnitMeasurementCd>LBR</UnitMeasurementCd>
                    </GrossVehWeight>
                    <CarpoolInd/>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NewVehInd>0</NewVehInd>
                    <LengthTimePerMonth>
                        <NumUnits/>
                        <UnitMeasurementCd>WEE</UnitMeasurementCd>
                    </LengthTimePerMonth>
                    <OdometerReading>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>J</AntiTheftDeviceCd>
                    <AntiLockBrakeCd>Y</AntiLockBrakeCd>
                    <RateClassCd/>
                    <VehPerformanceCd/>
                    <VehSalvageTitleNumber/>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>DFr Psg Dual AB(Fsr),Dual HD Crtn A/B(Fr),Seat Mtd SD AB Fsr</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDUM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OPTBI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_GPCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>APIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RREIM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>MaxAmount</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMSLA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMPDA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <Deductible>
                            <FormatInteger/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SORPE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LUSE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ADB</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd>Yes</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PPI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDSP</CoverageCd>
                        <CoverageDesc>Underinsured Split Limit (Policy Level Coverage)</CoverageDesc>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ANTHF</CoverageCd>
                        <CoverageDesc>Anti-Theft</CoverageDesc>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RPV</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SOPKG</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_CovLevel</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>B</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ABS</CoverageCd>
                        <CoverageDesc>Anti-lock brake discount</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <CollisionSymbolCd/>
                    <ComprehensiveOTCSymbolCd/>
                </PersVeh>
                <PersVeh LocationRef="Gar3" RatedDriverRef="" id="VEH3">
                    <ItemIdInfo>
                        <AgencyId/>
                    </ItemIdInfo>
                    <Manufacturer>NSSN</Manufacturer>
                    <Model>ALTIMA</Model>
                    <ModelYear>2015</ModelYear>
                    <VehBodyTypeCd/>
                    <VehBodyTypeDesc/>
                    <VehTypeCd/>
                    <MultiCarDiscountInd/>
                    <Registration>
                        <RegistrationTypeCd>LicensePlate</RegistrationTypeCd>
                        <RegistrationId/>
                        <RegistrationTypeId/>
                        <StateProvCd/>
                    </Registration>
                    <POLKRestraintDeviceCd/>
                    <CostNewAmt>
                        <Amt>0</Amt>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek/>
                    <NumCylinders/>
                    <PurchaseDt>2015-03-18</PurchaseDt>
                    <VehInspectionStatusCd/>
                    <DaytimeRunningLightInd>0</DaytimeRunningLightInd>
                    <EstimatedAnnualDistance>
                        <NumUnits>5000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt>
                        <Amt>559.00</Amt>
                    </FullTermAmt>
                    <Horsepower>
                        <NumUnits/>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <TerritoryCd/>
                    <VehIdentificationNumber>1N4AL3AP5FN364358</VehIdentificationNumber>
                    <VehSymbolCd/>
                    <GrossVehWeight>
                        <NumUnits/>
                        <UnitMeasurementCd>LBR</UnitMeasurementCd>
                    </GrossVehWeight>
                    <CarpoolInd/>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NewVehInd>0</NewVehInd>
                    <LengthTimePerMonth>
                        <NumUnits/>
                        <UnitMeasurementCd>WEE</UnitMeasurementCd>
                    </LengthTimePerMonth>
                    <OdometerReading>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>J</AntiTheftDeviceCd>
                    <AntiLockBrakeCd>Y</AntiLockBrakeCd>
                    <RateClassCd/>
                    <VehPerformanceCd/>
                    <VehSalvageTitleNumber/>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>Driver/passenger front, side, head and rear curtain airbags</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDUM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OPTBI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_GPCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>APIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RREIM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>MaxAmount</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMSLA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMPDA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <Deductible>
                            <FormatInteger/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SORPE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LUSE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ADB</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd>Yes</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PPI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDSP</CoverageCd>
                        <CoverageDesc>Underinsured Split Limit (Policy Level Coverage)</CoverageDesc>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ANTHF</CoverageCd>
                        <CoverageDesc>Anti-Theft</CoverageDesc>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RPV</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SOPKG</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_CovLevel</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>B</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ABS</CoverageCd>
                        <CoverageDesc>Anti-lock brake discount</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <CollisionSymbolCd/>
                    <ComprehensiveOTCSymbolCd/>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>WELLS FARGO DEALER SERVICES</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <AddrTypeCd>MailingAddress</AddrTypeCd>
                                <Addr1>PO Box 997517</Addr1>
                                <Addr2/>
                                <Addr3/>
                                <City>Sacramento</City>
                                <StateProvCd>CA</StateProvCd>
                                <PostalCode>95899-7517</PostalCode>
                                <CountryCd>USA</CountryCd>
                                <County/>
                            </Addr>
                            <Communications>
                                <PhoneInfo>
                                    <CommunicationUseCd>Business</CommunicationUseCd>
                                    <PhoneNumber/>
                                </PhoneInfo>
                            </Communications>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LOSSP</NatureInterestCd>
                            <InterestRank/>
                            <AccountNumberId/>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                </PersVeh>
                <PersVeh LocationRef="Gar4" RatedDriverRef="" id="VEH6">
                    <ItemIdInfo>
                        <AgencyId/>
                    </ItemIdInfo>
                    <Manufacturer>HOND</Manufacturer>
                    <Model>ACCORD EX</Model>
                    <ModelYear>2003</ModelYear>
                    <VehBodyTypeCd/>
                    <VehBodyTypeDesc/>
                    <VehTypeCd/>
                    <MultiCarDiscountInd/>
                    <Registration>
                        <RegistrationTypeCd>LicensePlate</RegistrationTypeCd>
                        <RegistrationId/>
                        <RegistrationTypeId/>
                        <StateProvCd/>
                    </Registration>
                    <POLKRestraintDeviceCd/>
                    <CostNewAmt>
                        <Amt>0</Amt>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek/>
                    <NumCylinders/>
                    <PurchaseDt>2003-03-18</PurchaseDt>
                    <VehInspectionStatusCd/>
                    <DaytimeRunningLightInd>0</DaytimeRunningLightInd>
                    <EstimatedAnnualDistance>
                        <NumUnits>8000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt>
                        <Amt>125.00</Amt>
                    </FullTermAmt>
                    <Horsepower>
                        <NumUnits/>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <TerritoryCd/>
                    <VehIdentificationNumber>1HGCM66523A087309</VehIdentificationNumber>
                    <VehSymbolCd/>
                    <GrossVehWeight>
                        <NumUnits/>
                        <UnitMeasurementCd>LBR</UnitMeasurementCd>
                    </GrossVehWeight>
                    <CarpoolInd/>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NewVehInd>0</NewVehInd>
                    <LengthTimePerMonth>
                        <NumUnits/>
                        <UnitMeasurementCd>WEE</UnitMeasurementCd>
                    </LengthTimePerMonth>
                    <OdometerReading>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>J</AntiTheftDeviceCd>
                    <AntiLockBrakeCd>Y</AntiLockBrakeCd>
                    <RateClassCd/>
                    <VehPerformanceCd/>
                    <VehSalvageTitleNumber/>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>Driver/passenger front, side, head and rear curtain airbags</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDUM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OPTBI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_GPCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>APIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RREIM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>MaxAmount</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMSLA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMPDA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <Deductible>
                            <FormatInteger/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SORPE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LUSE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ADB</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd>N</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PPI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDSP</CoverageCd>
                        <CoverageDesc>Underinsured Split Limit (Policy Level Coverage)</CoverageDesc>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ANTHF</CoverageCd>
                        <CoverageDesc>Anti-Theft</CoverageDesc>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RPV</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SOPKG</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_CovLevel</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>B</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ABS</CoverageCd>
                        <CoverageDesc>Anti-lock brake discount</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <CollisionSymbolCd/>
                    <ComprehensiveOTCSymbolCd/>
                </PersVeh>
                <PersVeh LocationRef="Gar5" RatedDriverRef="" id="VEH5">
                    <ItemIdInfo>
                        <AgencyId/>
                    </ItemIdInfo>
                    <Manufacturer>FORD</Manufacturer>
                    <Model>MUSTANG</Model>
                    <ModelYear>2002</ModelYear>
                    <VehBodyTypeCd/>
                    <VehBodyTypeDesc/>
                    <VehTypeCd/>
                    <MultiCarDiscountInd/>
                    <Registration>
                        <RegistrationTypeCd>LicensePlate</RegistrationTypeCd>
                        <RegistrationId/>
                        <RegistrationTypeId/>
                        <StateProvCd/>
                    </Registration>
                    <POLKRestraintDeviceCd/>
                    <CostNewAmt>
                        <Amt>0</Amt>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek/>
                    <NumCylinders/>
                    <PurchaseDt>2002-03-18</PurchaseDt>
                    <VehInspectionStatusCd/>
                    <DaytimeRunningLightInd>0</DaytimeRunningLightInd>
                    <EstimatedAnnualDistance>
                        <NumUnits>5000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt>
                        <Amt>118.00</Amt>
                    </FullTermAmt>
                    <Horsepower>
                        <NumUnits/>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <TerritoryCd/>
                    <VehIdentificationNumber>1FAFP40492F119168</VehIdentificationNumber>
                    <VehSymbolCd/>
                    <GrossVehWeight>
                        <NumUnits/>
                        <UnitMeasurementCd>LBR</UnitMeasurementCd>
                    </GrossVehWeight>
                    <CarpoolInd/>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NewVehInd>0</NewVehInd>
                    <LengthTimePerMonth>
                        <NumUnits/>
                        <UnitMeasurementCd>WEE</UnitMeasurementCd>
                    </LengthTimePerMonth>
                    <OdometerReading>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>J</AntiTheftDeviceCd>
                    <AntiLockBrakeCd>Y</AntiLockBrakeCd>
                    <RateClassCd/>
                    <VehPerformanceCd/>
                    <VehSalvageTitleNumber/>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>Driver side airbag / passenger airbag</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDUM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OPTBI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_GPCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>APIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RREIM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>MaxAmount</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMSLA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMPDA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <Deductible>
                            <FormatInteger/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SORPE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LUSE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ADB</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd>N</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PPI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDSP</CoverageCd>
                        <CoverageDesc>Underinsured Split Limit (Policy Level Coverage)</CoverageDesc>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ANTHF</CoverageCd>
                        <CoverageDesc>Anti-Theft</CoverageDesc>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RPV</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SOPKG</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_CovLevel</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>B</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ABS</CoverageCd>
                        <CoverageDesc>Anti-lock brake discount</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <CollisionSymbolCd/>
                    <ComprehensiveOTCSymbolCd/>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="">
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>StreetAddress</AddrTypeCd>
                    <Addr1>29 Fulton Ln</Addr1>
                    <Addr2/>
                    <Addr3/>
                    <City>Starkville</City>
                    <StateProvCd>MS</StateProvCd>
                    <PostalCode>39759-6228</PostalCode>
                    <CountryCd>USA</CountryCd>
                    <County/>
                </Addr>
            </Location>
            <RemarkText/>
            <Location id="Gar1">
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>29 Fulton Ln</Addr1>
                    <Addr2/>
                    <Addr3/>
                    <City>Starkville</City>
                    <StateProvCd>MS</StateProvCd>
                    <PostalCode>39759-6228</PostalCode>
                    <CountryCd>USA</CountryCd>
                    <County/>
                </Addr>
            </Location>
            <Location id="Gar2">
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>29 Fulton Ln</Addr1>
                    <Addr2/>
                    <Addr3/>
                    <City>Starkville</City>
                    <StateProvCd>MS</StateProvCd>
                    <PostalCode>39759-6228</PostalCode>
                    <CountryCd>USA</CountryCd>
                    <County/>
                </Addr>
            </Location>
            <Location id="Gar3">
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>29 Fulton Ln</Addr1>
                    <Addr2/>
                    <Addr3/>
                    <City>Starkville</City>
                    <StateProvCd>MS</StateProvCd>
                    <PostalCode>39759-6228</PostalCode>
                    <CountryCd>USA</CountryCd>
                    <County/>
                </Addr>
            </Location>
            <Location id="Gar4">
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>29 Fulton Ln</Addr1>
                    <Addr2/>
                    <Addr3/>
                    <City>Starkville</City>
                    <StateProvCd>MS</StateProvCd>
                    <PostalCode>39759-6228</PostalCode>
                    <CountryCd>USA</CountryCd>
                    <County/>
                </Addr>
            </Location>
            <Location id="Gar5">
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>29 Fulton Ln</Addr1>
                    <Addr2/>
                    <Addr3/>
                    <City>Starkville</City>
                    <StateProvCd>MS</StateProvCd>
                    <PostalCode>39759-6228</PostalCode>
                    <CountryCd>USA</CountryCd>
                    <County/>
                </Addr>
            </Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
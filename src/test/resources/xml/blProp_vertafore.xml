<?xml version="1.0" encoding="UTF-8"?>
<?ACORD version="1.3.1"?>
<ACORD>
	<SignonRq>
		<SignonPswd>
			<CustId>
				<SPName>BCFTechnology.com</SPName>
				<CustPermId>test</CustPermId>
				<CustLoginId>test</CustLoginId>
			</CustId>
			<CustPswd>
				<EncryptionTypeCd>NONE</EncryptionTypeCd>
				<Pswd>test</Pswd>
			</CustPswd>
		</SignonPswd>
		<ClientDt>2018-11-09T08:28:05</ClientDt>
		<CustLangPref>en-US</CustLangPref>
		<ClientApp>
			<Org>AMS Services</Org>
			<Name>AMS360</Name>
			<Version>V1.3</Version>
		</ClientApp>
		<ProxyClient>
			<Org>BCFTech</Org>
			<Name>TransmitXML</Name>
			<Version>V1.00</Version>
		</ProxyClient>
	</SignonRq>
	<InsuranceSvcRq>
		<RqUID>031482BE-2E2B-4407-B282-9175BE02C593</RqUID>
		<CommlPropertyPolicyQuoteInqRq>
			<RqUID>2FF3E540-AE0D-4EAE-8C27-C4063928AA4B</RqUID>
			<TransactionRequestDt>2018-11-09T09:18:46</TransactionRequestDt>
			<TransactionEffectiveDt>2016-08-30</TransactionEffectiveDt>
			<CurCd>USD</CurCd>
			<Producer>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>Carrier Database</CommercialName>
						</CommlName>
					</NameInfo>
					<Addr>
						<AddrTypeCd>StreetAddress</AddrTypeCd>
						<Addr1>123 Main Street</Addr1>
						<Addr2>ADDR2</Addr2>
						<City>Bothell</City>
						<StateProvCd>WA</StateProvCd>
						<PostalCode>97202</PostalCode>
					</Addr>
					<Communications>
						<PhoneInfo>
							<PhoneTypeCd>Phone</PhoneTypeCd>
							<CommunicationUseCd>Business</CommunicationUseCd>
							<PhoneNumber>9999999999</PhoneNumber>
						</PhoneInfo>
					</Communications>
				</GeneralPartyInfo>
				<ProducerInfo>
					<ContractNumber/>
					<ProducerRoleCd>Agency</ProducerRoleCd>
				</ProducerInfo>
			</Producer>
			<InsuredOrPrincipal>
				<ItemIdInfo>
					<AgencyId>00012345</AgencyId>
				</ItemIdInfo>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>AMS Test</CommercialName>
						</CommlName>
						<LegalEntityCd>IN</LegalEntityCd>
					</NameInfo>
					<Addr>
						<AddrTypeCd>MailingAddress</AddrTypeCd>
						<Addr1>X</Addr1>
						<City>X</City>
						<StateProvCd>OH</StateProvCd>
						<PostalCode>45891</PostalCode>
					</Addr>
				</GeneralPartyInfo>
				<InsuredOrPrincipalInfo>
					<InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
				</InsuredOrPrincipalInfo>
			</InsuredOrPrincipal>
			<InsuredOrPrincipal>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>AMS Test</CommercialName>
						</CommlName>
					</NameInfo>
				</GeneralPartyInfo>
				<InsuredOrPrincipalInfo>
					<InsuredOrPrincipalRoleCd>DEC</InsuredOrPrincipalRoleCd>
				</InsuredOrPrincipalInfo>
			</InsuredOrPrincipal>
			<CommlPolicy id="PolicyLevel">
				<PolicyNumber>CLP 1338760</PolicyNumber>
				<LOBCd>PROP</LOBCd>
				<ContractTerm>
					<EffectiveDt>2016-08-30</EffectiveDt>
					<ExpirationDt>2017-08-30</ExpirationDt>
					<DurationPeriod>
						<NumUnits>12</NumUnits>
						<UnitMeasurementCd>MON</UnitMeasurementCd>
					</DurationPeriod>
				</ContractTerm>
				<BillingMethodCd>AB</BillingMethodCd>
				<CurrentTermAmt>
					<Amt>550.00</Amt>
				</CurrentTermAmt>
				<Form>
					<FormNumber>CP0010</FormNumber>
					<FormName>Building And Personal Property Coverage Form</FormName>
					<EditionDt>2012-10-01</EditionDt>
					<IterationNumber>001</IterationNumber>
				</Form>
				<Form>
					<FormNumber>IL0986</FormNumber>
					<FormName>Exclusion Of Certified Acts Of Terrorism Invo</FormName>
					<EditionDt>2015-01-01</EditionDt>
					<IterationNumber>002</IterationNumber>
				</Form>
				<Form>
					<FormNumber>CP0030</FormNumber>
					<FormName>Business Income (and Extra Expense) Coverage</FormName>
					<EditionDt>2012-10-01</EditionDt>
					<IterationNumber>003</IterationNumber>
				</Form>
				<Form>
					<FormNumber>14-3039</FormNumber>
					<FormName>Business Income And Extra Expense</FormName>
					<EditionDt>2011-02-01</EditionDt>
					<IterationNumber>004</IterationNumber>
				</Form>
				<Form>
					<FormNumber>CP1040</FormNumber>
					<FormName>Earthquake &amp; Volcanic Eruption End</FormName>
					<EditionDt>2012-10-01</EditionDt>
					<IterationNumber>005</IterationNumber>
				</Form>
				<Form>
					<FormNumber>IL0952</FormNumber>
					<FormName>Cap On Losses From Certified Acts Of Terroris</FormName>
					<EditionDt>2015-01-01</EditionDt>
					<IterationNumber>006</IterationNumber>
				</Form>
				<Form>
					<FormNumber>CP0123</FormNumber>
					<FormName>Ohio Changes</FormName>
					<EditionDt>2008-04-01</EditionDt>
					<IterationNumber>007</IterationNumber>
				</Form>
				<Form>
					<FormNumber>IL0244</FormNumber>
					<FormName>Oh-changes - Cancellation &amp; Nonrenewal</FormName>
					<EditionDt>2007-09-01</EditionDt>
					<IterationNumber>008</IterationNumber>
				</Form>
				<Form>
					<FormNumber>14-3467</FormNumber>
					<FormName>Cyberone Coverage</FormName>
					<EditionDt>2013-11-01</EditionDt>
					<IterationNumber>009</IterationNumber>
				</Form>
				<Form>
					<FormNumber>20-2375</FormNumber>
					<FormName>Notice To Policyholders - Nbcr Exclusion</FormName>
					<EditionDt>2015-03-01</EditionDt>
					<IterationNumber>010</IterationNumber>
				</Form>
				<Form>
					<FormNumber>20-2118</FormNumber>
					<FormName>Policyholder Disclosure Notice (terrorism Ins</FormName>
					<EditionDt>2015-03-01</EditionDt>
					<IterationNumber>011</IterationNumber>
				</Form>
				<Form>
					<FormNumber>20-1768</FormNumber>
					<FormName>Mutual Policy Conditions-applicable To Centra</FormName>
					<EditionDt>1991-08-01</EditionDt>
					<IterationNumber>012</IterationNumber>
				</Form>
				<Form>
					<FormNumber>20-1769</FormNumber>
					<FormName>Provisions Applicable To Central Mutual &amp; All</FormName>
					<EditionDt>1991-08-01</EditionDt>
					<IterationNumber>013</IterationNumber>
				</Form>
				<Form>
					<FormNumber>14-3065</FormNumber>
					<FormName>Central Premier(r) Property Extensions Covera</FormName>
					<EditionDt>2015-11-01</EditionDt>
					<IterationNumber>014</IterationNumber>
				</Form>
				<Form>
					<FormNumber>14-3347</FormNumber>
					<FormName>Data Compromise Coverage Response Exp &amp; Defen</FormName>
					<EditionDt>2013-11-01</EditionDt>
					<IterationNumber>015</IterationNumber>
				</Form>
				<Form>
					<FormNumber>14-3250</FormNumber>
					<FormName>Identity Recovery Coverage</FormName>
					<EditionDt>2008-07-01</EditionDt>
					<IterationNumber>016</IterationNumber>
				</Form>
				<Form>
					<FormNumber>CP0140</FormNumber>
					<FormName>Exclusion Of Loss Due To Virus Or Bacteria</FormName>
					<EditionDt>2006-07-01</EditionDt>
					<IterationNumber>017</IterationNumber>
				</Form>
				<Form>
					<FormNumber>14-2660</FormNumber>
					<FormName>Business Income - Coinsurance Amendatory Endo</FormName>
					<EditionDt>2002-04-01</EditionDt>
					<IterationNumber>018</IterationNumber>
				</Form>
				<Form>
					<FormNumber>CP0090</FormNumber>
					<FormName>Coml Prop Conditions</FormName>
					<EditionDt>1988-07-01</EditionDt>
					<IterationNumber>019</IterationNumber>
				</Form>
				<Form>
					<FormNumber>14-2012</FormNumber>
					<FormName>Quick Ref-coml Property Coverage Part</FormName>
					<EditionDt>1985-11-01</EditionDt>
					<IterationNumber>020</IterationNumber>
				</Form>
				<Form>
					<FormNumber>IL0017</FormNumber>
					<FormName>Common Policy Conditions</FormName>
					<EditionDt>1998-11-01</EditionDt>
					<IterationNumber>021</IterationNumber>
				</Form>
				<Form>
					<FormNumber>14-3181</FormNumber>
					<FormName>Cw Policyholder Notice-loss Due To Virus Or B</FormName>
					<EditionDt>2015-03-01</EditionDt>
					<IterationNumber>022</IterationNumber>
				</Form>
				<Form>
					<FormNumber>CP1030</FormNumber>
					<FormName>Causes Of Loss - Special Form</FormName>
					<EditionDt>2012-10-01</EditionDt>
					<IterationNumber>023</IterationNumber>
				</Form>
				<PaymentOption>
					<PaymentPlanCd>FL</PaymentPlanCd>
				</PaymentOption>
				<MiscParty>
					<GeneralPartyInfo>
						<NameInfo>
							<CommlName>
								<CommercialName>Tnow Test</CommercialName>
							</CommlName>
						</NameInfo>
					</GeneralPartyInfo>
					<MiscPartyInfo>
						<MiscPartyRoleCd>A</MiscPartyRoleCd>
					</MiscPartyInfo>
				</MiscParty>
				<CommlPolicySupplement>
					<OperationsDesc>Logging Sifs</OperationsDesc>
				</CommlPolicySupplement>
				<ControllingStateProvCd>OH</ControllingStateProvCd>
			</CommlPolicy>
			<Location id="L1">
				<ItemIdInfo>
					<AgencyId>0001</AgencyId>
				</ItemIdInfo>
				<Addr>
					<Addr1>X</Addr1>
					<City>X</City>
					<StateProvCd>OH</StateProvCd>
					<PostalCode>45891</PostalCode>
				</Addr>
				<SubLocation id="L1S1">
					<ItemIdInfo>
						<AgencyId>001</AgencyId>
					</ItemIdInfo>
					<SubLocationDesc>X</SubLocationDesc>
					<Addr>
						<Addr1>X</Addr1>
						<City>X</City>
						<StateProvCd>OH</StateProvCd>
						<PostalCode>45891</PostalCode>
					</Addr>
				</SubLocation>
			</Location>
			<CommlPropertyLineBusiness>
				<LOBCd>PROP</LOBCd>
				<CurrentTermAmt>
					<Amt>550.00</Amt>
				</CurrentTermAmt>
				<RateEffectiveDt>2016-08-30</RateEffectiveDt>
				<PropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>ACVGP</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>1</Amt>
						</ItemValueAmt>
						<ClassCd>0567</ClassCd>
						<CommlCoverage>
							<CoverageCd>ACVGP</CoverageCd>
							<Limit>
								<FormatInteger>1</FormatInteger>
								<ValuationCd>RC</ValuationCd>
							</Limit>
							<Deductible>
								<FormatInteger>1000</FormatInteger>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
							<CoverageDesc>Additional Covered Property</CoverageDesc>
							<Limit>
								<FormatInteger>1</FormatInteger>
							</Limit>
							<CurrentTermAmt>
								<Amt>3.00</Amt>
							</CurrentTermAmt>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>BEQK</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>1</Amt>
						</ItemValueAmt>
						<ClassCd>0567</ClassCd>
						<CommlCoverage>
							<CoverageCd>BEQK</CoverageCd>
							<Limit>
								<FormatInteger>1</FormatInteger>
							</Limit>
							<Deductible>
								<FormatInteger>5</FormatInteger>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
							<CoverageDesc>Earthquake - Building</CoverageDesc>
							<Limit>
								<FormatInteger>1</FormatInteger>
							</Limit>
							<CurrentTermAmt>
								<Amt>1.00</Amt>
							</CurrentTermAmt>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>BUSIN</SubjectInsuranceCd>
						<CommlCoverage>
							<CoverageCd>BUSIN</CoverageCd>
							<Deductible>
								<FormatInteger>72</FormatInteger>
								<DeductibleTypeCd>HR</DeductibleTypeCd>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>BLDG</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>1</Amt>
						</ItemValueAmt>
						<ClassCd>0567</ClassCd>
						<FloorArea>
							<NumUnits>1111</NumUnits>
							<UnitMeasurementCd>FTK</UnitMeasurementCd>
						</FloorArea>
						<CommlCoverage>
							<CoverageCd>BLDG</CoverageCd>
							<Limit>
								<FormatInteger>1</FormatInteger>
								<ValuationCd>RC</ValuationCd>
							</Limit>
							<Deductible>
								<FormatInteger>1000</FormatInteger>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
							<CoverageDesc>Building</CoverageDesc>
							<Limit>
								<FormatInteger>1</FormatInteger>
							</Limit>
							<CurrentTermAmt>
								<Amt>3.00</Amt>
							</CurrentTermAmt>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>BPP</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>1</Amt>
						</ItemValueAmt>
						<ClassCd>0567</ClassCd>
						<CommlCoverage>
							<CoverageCd>BPP</CoverageCd>
							<Limit>
								<FormatInteger>1</FormatInteger>
								<ValuationCd>RC</ValuationCd>
							</Limit>
							<Deductible>
								<FormatInteger>1000</FormatInteger>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
							<CoverageDesc>Contents (Personal Property)</CoverageDesc>
							<Limit>
								<FormatInteger>1</FormatInteger>
							</Limit>
							<CurrentTermAmt>
								<Amt>3.00</Amt>
							</CurrentTermAmt>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>PPEQK</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>2</Amt>
						</ItemValueAmt>
						<ClassCd>0567</ClassCd>
						<CommlCoverage>
							<CoverageCd>PPEQK</CoverageCd>
							<Limit>
								<FormatInteger>2</FormatInteger>
							</Limit>
							<Deductible>
								<FormatInteger>5</FormatInteger>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
							<CoverageDesc>Earthquake - Contents</CoverageDesc>
							<Limit>
								<FormatInteger>2</FormatInteger>
							</Limit>
							<CurrentTermAmt>
								<Amt>1.00</Amt>
							</CurrentTermAmt>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>PO</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>1</Amt>
						</ItemValueAmt>
						<ClassCd>0567</ClassCd>
						<CommlCoverage>
							<CoverageCd>PO</CoverageCd>
							<Limit>
								<FormatInteger>1</FormatInteger>
								<ValuationCd>RC</ValuationCd>
							</Limit>
							<Deductible>
								<FormatInteger>1000</FormatInteger>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
							<CoverageDesc>Personal Property of Others</CoverageDesc>
							<Limit>
								<FormatInteger>1</FormatInteger>
							</Limit>
							<CurrentTermAmt>
								<Amt>3.00</Amt>
							</CurrentTermAmt>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlCoverage>
						<CoverageCd>STS01</CoverageCd>
						<CoverageDesc>State Surcharge</CoverageDesc>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>TRIA</CoverageCd>
						<CoverageDesc>Terrorism</CoverageDesc>
						<CurrentTermAmt>
							<Amt>2.00</Amt>
						</CurrentTermAmt>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>APMP</CoverageCd>
						<CoverageDesc>Minimum Premiuim</CoverageDesc>
						<CurrentTermAmt>
							<Amt>310.00</Amt>
						</CurrentTermAmt>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>DATAC</CoverageCd>
						<CoverageDesc>Data Compromise</CoverageDesc>
						<Limit>
							<FormatInteger>50000</FormatInteger>
						</Limit>
						<Limit>
							<FormatInteger>50000</FormatInteger>
						</Limit>
						<Deductible>
							<FormatInteger>1000</FormatInteger>
						</Deductible>
						<CurrentTermAmt>
							<Amt>128.00</Amt>
						</CurrentTermAmt>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>IDRC</CoverageCd>
						<CoverageDesc>Identity Recovery</CoverageDesc>
						<Limit>
							<FormatInteger>15000</FormatInteger>
						</Limit>
						<Deductible>
							<FormatInteger>250</FormatInteger>
						</Deductible>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>CPVIR</CoverageCd>
						<CoverageDesc>Virus and Hacking</CoverageDesc>
						<Limit>
							<FormatInteger>50000</FormatInteger>
						</Limit>
						<Deductible>
							<FormatInteger>5000</FormatInteger>
						</Deductible>
						<CurrentTermAmt>
							<Amt>96.00</Amt>
						</CurrentTermAmt>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>EDPPC</CoverageCd>
						<CoverageDesc>Protection and Control Systems</CoverageDesc>
						<Limit>
							<FormatInteger>50000</FormatInteger>
						</Limit>
						<Limit>
							<FormatInteger>50000</FormatInteger>
						</Limit>
						<Deductible>
							<FormatInteger>5000</FormatInteger>
						</Deductible>
					</CommlCoverage>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>ACVGP</SubjectInsuranceCd>
							<ValuationCd>RC</ValuationCd>
							<ClassCd>0567</ClassCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>BEQK</SubjectInsuranceCd>
							<ClassCd>0567</ClassCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>BUSIN</SubjectInsuranceCd>
							<ValuationCd>ACT</ValuationCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>BLDG</SubjectInsuranceCd>
							<ValuationCd>RC</ValuationCd>
							<ClassCd>0567</ClassCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>BPP</SubjectInsuranceCd>
							<ValuationCd>RC</ValuationCd>
							<ClassCd>0567</ClassCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>PPEQK</SubjectInsuranceCd>
							<ClassCd>0567</ClassCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>PO</SubjectInsuranceCd>
							<ValuationCd>RC</ValuationCd>
							<ClassCd>0567</ClassCd>
						</StatementValuesInfo>
					</StatementValues>
				</PropertyInfo>
			</CommlPropertyLineBusiness>
			<CommlSubLocation LocationRef="L1" SubLocationRef="L1S1">
				<Construction>
					<ConstructionCd>F</ConstructionCd>
					<YearBuilt>1111</YearBuilt>
					<BldgArea>
						<NumUnits>1111</NumUnits>
						<UnitMeasurementCd>FTK</UnitMeasurementCd>
					</BldgArea>
					<NumStories>1</NumStories>
				</Construction>
				<BldgProtection>
					<FireProtectionClassCd>01</FireProtectionClassCd>
				</BldgProtection>
				<BldgOccupancy>
					<OccupancyDesc>X</OccupancyDesc>
				</BldgOccupancy>
			</CommlSubLocation>
		</CommlPropertyPolicyQuoteInqRq>
	</InsuranceSvcRq>
</ACORD>

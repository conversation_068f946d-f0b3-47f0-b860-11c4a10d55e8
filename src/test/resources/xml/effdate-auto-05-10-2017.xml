<?xml-stylesheet type="text/xsl" href='http://ecdev-tac.safeco.com/BookTransfer/TACML/WebAPI/scripts/xslt/AutoXMLView.xslt'?>
<ACORD>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName>com.AMS</SPName>
                <CustPermId/>
                <CustLoginId><EMAIL></CustLoginId>
            </CustId>
            <CustPswd>
                <EncryptionTypeCd/>
                <Pswd/>
            </CustPswd>
        </SignonPswd>
        <ClientDt>2/2/2017 2:23 PM</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>AMS</Org>
            <Name>AMS 360</Name>
            <Version>2.0</Version>
        </ClientApp>
        <ProxyClient>
            <Org>Vertafore</Org>
            <Name>AgencyBookRoll</Name>
            <Version>V1.00</Version>
        </ProxyClient>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>E26891C4-A0BD-4622-9C46-D385ABE2A55C</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <RqUID>2E360BC2-99D7-41BE-8E4D-C7CDBC136173</RqUID>
            <TransactionRequestDt>2017-02-02T12:08:48</TransactionRequestDt>
            <TransactionEffectiveDt>2016-08-21</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Multi-line Insurance Agency</CommercialName>
                        </CommlName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>StreetAddress</AddrTypeCd>
                        <Addr1>800 Yonkers Ave.</Addr1>
                        <City>Yonkers</City>
                        <StateProvCd>NY</StateProvCd>
                        <PostalCode>10704</PostalCode>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Business</CommunicationUseCd>
                            <PhoneNumber>******-9616666</PhoneNumber>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>
                <ProducerInfo>
                    <ContractNumber>431683</ContractNumber>
                    <ProducerRoleCd>Agency</ProducerRoleCd>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>00000865</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Ygritte Jang</CommercialName>
                        </CommlName>
                        <PersonName>
                            <GivenName>Ygritte</GivenName>
                            <Surname>Jang</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>13 N Evarts Ave #2a</Addr1>
                        <City>Elmsford</City>
                        <StateProvCd>NY</StateProvCd>
                        <PostalCode>10523</PostalCode>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-1111121</PhoneNumber>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Gerardo Figueroa</CommercialName>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>DEC</InsuredOrPrincipalRoleCd>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy id="PolicyLevel">
                <PolicyNumber>63579655</PolicyNumber>
                <CompanyProductCd>C---</CompanyProductCd>
                <LOBCd>AUTOP</LOBCd>
                <ContractTerm>
                    <EffectiveDt>2017-05-10</EffectiveDt>
                    <ExpirationDt>2018-11-10</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>6</NumUnits>
                        <UnitMeasurementCd>MON</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <BillingMethodCd>CPB</BillingMethodCd>
                <CurrentTermAmt>
                    <Amt>744.00</Amt>
                </CurrentTermAmt>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber>63579655</PolicyNumber>
                    <LOBCd>AUTOP</LOBCd>
                    <NAICCd>24260</NAICCd>
                    <InsurerName>Cincinnati Insurance</InsurerName>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <NumPayments>6</NumPayments>
                </PaymentOption>
                <MiscParty>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Essam Abdelkhalek</CommercialName>
                            </CommlName>
                        </NameInfo>
                        <Communications>
                            <PhoneInfo>
                                <PhoneTypeCd>Fax</PhoneTypeCd>
                                <PhoneNumber>******-9616633</PhoneNumber>
                            </PhoneInfo>
                            <PhoneInfo>
                                <PhoneTypeCd>Fax</PhoneTypeCd>
                                <PhoneNumber>******-9616633</PhoneNumber>
                            </PhoneInfo>
                            <EmailInfo>
                                <EmailAddr><EMAIL></EmailAddr>
                            </EmailInfo>
                            <EmailInfo>
                                <EmailAddr><EMAIL></EmailAddr>
                            </EmailInfo>
                        </Communications>
                    </GeneralPartyInfo>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>A</MiscPartyRoleCd>
                    </MiscPartyInfo>
                </MiscParty>
                <PersApplicationInfo>
                    <InsuredOrPrincipal/>
                </PersApplicationInfo>
                <AccidentViolation DriverRef="D1">
                    <AccidentViolationDt>2015-12-26</AccidentViolationDt>
                    <AccidentViolationDesc>not at fault accident</AccidentViolationDesc>
                    <AccidentViolationRecordTypeCd>ACC</AccidentViolationRecordTypeCd>
                </AccidentViolation>
                <DriverVeh DriverRef="D1" VehRef="V2"/>
                <ControllingStateProvCd>NY</ControllingStateProvCd>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <CompanyProductCd>C---</CompanyProductCd>
                <CurrentTermAmt>
                    <Amt>744.00</Amt>
                </CurrentTermAmt>
                <RateEffectiveDt>2016-08-21</RateEffectiveDt>
                <Coverage>
                    <CoverageCd>SD3</CoverageCd>
                    <CoverageDesc>3 YR SAFE DRIVING DISC</CoverageDesc>
                </Coverage>
                <Coverage>
                    <CoverageCd>SD5</CoverageCd>
                    <CoverageDesc>5 YR SAFE DRIVING DISC</CoverageDesc>
                </Coverage>
                <Coverage>
                    <CoverageCd>NP3</CoverageCd>
                    <CoverageDesc>NEW BUS LENGTH POP DISC3</CoverageDesc>
                </Coverage>
                <PersDriver id="D1">
                    <ItemIdInfo>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Jang</Surname>
                                <GivenName>Ygritte</GivenName>
                            </PersonName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1968-01-01</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationDesc>MGR-GEN OP</OccupationDesc>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>1234567</DriversLicenseNumber>
                            <StateProvCd>NY</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicenseTypeCd>Driver</LicenseTypeCd>
                            <LicensePermitNumber>149427329</LicensePermitNumber>
                            <StateProvCd>NY</StateProvCd>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo VehPrincipallyDrivenRef="V2" id="PD1">
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <GoodStudentCd>N</GoodStudentCd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="D2">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Spiegelman</Surname>
                                <GivenName>April</GivenName>
                            </PersonName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1962-01-01</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <OccupationClassCd>UN</OccupationClassCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber/>
                        </DriversLicense>
                    </DriverInfo>
                    <PersDriverInfo id="PD2">
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <GoodStudentCd>N</GoodStudentCd>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="L1" RatedDriverRef="D1" id="V2">
                    <ItemIdInfo>
                        <AgencyId>0002</AgencyId>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>NISSA</Manufacturer>
                    <Model>ALTIMA</Model>
                    <ModelYear>2016</ModelYear>
                    <FullTermAmt>
                        <Amt>744.00</Amt>
                    </FullTermAmt>
                    <TerritoryCd>97</TerritoryCd>
                    <VehIdentificationNumber>1N4AL3AP2GC115772</VehIdentificationNumber>
                    <VehSymbolCd>NS</VehSymbolCd>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Nissan-Infiniti Lt</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>PO Box 390889</Addr1>
                                <City>Minneapolis</City>
                                <StateProvCd>MN</StateProvCd>
                                <PostalCode>55439</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LIEN</NatureInterestCd>
                            <InterestRank>1</InterestRank>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Nissan-Infiniti Lt</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>PO Box 390889</Addr1>
                                <City>Minneapolis</City>
                                <StateProvCd>MN</StateProvCd>
                                <PostalCode>55439</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>AINT</NatureInterestCd>
                            <InterestRank>1</InterestRank>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>AR2</CoverageCd>
                        <CoverageDesc>AIRBAG DISC/DR &amp; PASS</CoverageDesc>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ABS</CoverageCd>
                        <CoverageDesc>Anti-Lock Brake Discount</CoverageDesc>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>AT1</CoverageCd>
                        <CoverageDesc>Anti-Theft Device 1 Disc</CoverageDesc>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc>Bodily injury limit(s)</CoverageDesc>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>195.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc>Collision</CoverageDesc>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>257.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc>Comprehensive</CoverageDesc>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>56.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>DAYLT</CoverageCd>
                        <CoverageDesc>Daytime Running Light Credit</CoverageDesc>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LAWFE</CoverageCd>
                        <CoverageDesc>Law Enforcement Fee</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt>5.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc>Medical payments</CoverageDesc>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>1.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OBEL</CoverageCd>
                        <CoverageDesc>Optional basic economic loss</CoverageDesc>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>0</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>3.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PIP</CoverageCd>
                        <CoverageDesc>PIP-Basic</CoverageDesc>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd>NO</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt>74.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc>Property damage-single limit</CoverageDesc>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>108.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RREIM</CoverageCd>
                        <CoverageDesc>Rental reimbursement</CoverageDesc>
                        <Limit>
                            <FormatInteger>50</FormatInteger>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>1500</FormatInteger>
                            <LimitAppliesToCd>MaxAmount</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>33.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <CoverageDesc>Towing and labor</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt>1.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc>Uninsured Motorist Liab / BI</CoverageDesc>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>11.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="L1">
                <ItemIdInfo>
                    <AgencyId>0001</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>13 N Evarts Ave #2A</Addr1>
                    <City>Elmsford</City>
                    <StateProvCd>NY</StateProvCd>
                    <PostalCode>10523</PostalCode>
                    <Addr2/>
                </Addr>
            </Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
<ACORD>
    <com.BookTransfer_errors>
        <Common>
            <PriorCarrier>Missing</PriorCarrier>
        </Common>
    </com.BookTransfer_errors>
    <SignonRq>
        <ClientDt>2019-10-23-07:00</ClientDt>
        <ClientApp>
            <Org>Safeco Book Transfer</Org>
            <Name>SPQE</Name>
            <Version id="V1.0">V1.0</Version>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <HomePolicyQuoteInqRq>
            <com.safeco_TransactionType>QuoteRq</com.safeco_TransactionType>
            <Location id="001">
                <Addr>
                    <AddrTypeCd>StreetAddress</AddrTypeCd>
                    <PostalCode>60654</PostalCode>
                    <StateProvCd>IL</StateProvCd>
                    <City>Chicago</City>
                    <Addr2>1405</Addr2>
                    <Addr1>600 N Kingsbury St</Addr1>
                </Addr>
            </Location>
            <Producer>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Banc Insurance Agency, Inc</CommercialName>
                        </CommlName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>StreetAddress</AddrTypeCd>
                        <Addr1>1 Hartfield Blvd</Addr1>
                        <Addr2>Suite 301</Addr2>
                        <City>East Windsor</City>
                        <StateProvCd>CT</StateProvCd>
                        <PostalCode>6088</PostalCode>
                    </Addr>
                </GeneralPartyInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Book Transfer</CommercialName>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
                <ProducerInfo>
                    <ContractNumber>897404</ContractNumber>
                    <ProducerRoleCd/>
                    <ProducerSubCode>Book Transfer</ProducerSubCode>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <GivenName>Sanchez</GivenName>
                            <Surname>Woodhouse</Surname>
                        </PersonName>
                        <CommlName>
                            <CommercialName>Sanchez Woodhouse</CommercialName>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <PersonInfo>
                        <BirthDt>1985-03-08</BirthDt>
                    </PersonInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>HUTCHEJA01</AgencyId>
                </ItemIdInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>600 N Kingsbury St</Addr1>
                        <Addr2>1405</Addr2>
                        <City>Chicago</City>
                        <StateProvCd>IL</StateProvCd>
                        <PostalCode>60654</PostalCode>
                    </Addr>
                    <NameInfo>
                        <PersonName>
                            <GivenName>Stewie</GivenName>
                            <Surname>Cyril</Surname>
                        </PersonName>
                        <TaxIdentity>
                            <TaxId>A9W-NE-0009</TaxId>
                        </TaxIdentity>
                        <CommlName>
                            <CommercialName>Stewie Cyril</CommercialName>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <PersonInfo>
                        <BirthDt>1984-08-20</BirthDt>
                        <MaritalStatusCd>M</MaritalStatusCd>
                    </PersonInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <CurrentTermAmt>
                    <Amt/>
                </CurrentTermAmt>
                <QuoteInfo>
                    <CampaignId>9091</CampaignId>
                </QuoteInfo>
                <ContractTerm>
                    <EffectiveDt>2020-04-04</EffectiveDt>
                    <ExpirationDt>2021-04-04</ExpirationDt>
                </ContractTerm>
                <PolicyNumber id="9">9</PolicyNumber>
                <LOBCd>HOME</LOBCd>
            </PersPolicy>
            <HomeLineBusiness>
                <Dwell LocationRef="001">
                    <DwellOccupancy>
                        <ResidenceTypeCd>CD</ResidenceTypeCd>
                    </DwellOccupancy>
                    <DwellInspectionValuation>
                        <TotalArea>
                            <UnitMeasurementCd>SF</UnitMeasurementCd>
                            <NumUnits>2500</NumUnits>
                        </TotalArea>
                        <NumFamilies>1</NumFamilies>
                        <FireplaceInfo>
                            <NumFireplaces>0</NumFireplaces>
                            <NumChimneys>0</NumChimneys>
                            <FirePlaceTypeCd>None</FirePlaceTypeCd>
                        </FireplaceInfo>
                    </DwellInspectionValuation>
                    <BldgProtection>
                        <DistanceToHydrant>
                            <UnitMeasurementCd>foot</UnitMeasurementCd>
                            <NumUnits>1000</NumUnits>
                        </DistanceToHydrant>
                        <DistanceToFireStation>
                            <UnitMeasurementCd>mile</UnitMeasurementCd>
                            <NumUnits>1</NumUnits>
                        </DistanceToFireStation>
                        <FireProtectionClassCd>1</FireProtectionClassCd>
                    </BldgProtection>
                    <Construction>
                        <YearBuilt>1924</YearBuilt>
                        <RoofingMaterial>
                            <RoofMaterialCd>A</RoofMaterialCd>
                        </RoofingMaterial>
                        <FoundationCd>N</FoundationCd>
                    </Construction>
                    <PolicyTypeCd>Condo</PolicyTypeCd>
                    <Coverage>
                        <CoverageCd>PP</CoverageCd>
                        <Limit>
                            <FormatInteger>30350</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PL</CoverageCd>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LAC</CoverageCd>
                        <Limit>
                            <FormatInteger>Erin</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SEWER</CoverageCd>
                        <Limit>
                            <FormatInteger>Stotch</FormatInteger>
                        </Limit>
                    </Coverage>
                </Dwell>
            </HomeLineBusiness>
        </HomePolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
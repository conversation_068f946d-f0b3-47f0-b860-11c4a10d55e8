<?xml-stylesheet type="text/xsl" href='http://ecdev-tac.safeco.com/BookTransfer/TACML/WebAPI/scripts/xslt/BoatXMLView.xslt'?>
<ACORD>
    <Status>
        <StatusCd>0</StatusCd>
        <StatusDesc/>
    </Status>
    <SignonRs>
        <ClientDt>2016-03-04</ClientDt>
        <ClientApp>
            <Org>Applied Systems</Org>
            <Name>TAM</Name>
        </ClientApp>
    </SignonRs>
    <InsuranceSvcRs>
        <HomePolicyQuoteInqRs>
            <MsgStatus>
                <MsgStatusCd>SuccessWithChanges</MsgStatusCd>
                <ExtendedStatus>
                    <ExtendedStatusCd>VerifyDataValue</ExtendedStatusCd>
                    <ExtendedStatusDesc>We were not able to find an insurance score for this client which may impact the
                        final rate. Please confirm the spelling of the Name and Address and the Birth Date and Social
                        Security Number.
                    </ExtendedStatusDesc>
                </ExtendedStatus>
                <ExtendedStatus>
                    <ExtendedStatusCd>Informational</ExtendedStatusCd>
                    <ExtendedStatusDesc>Quote was placed in Safeco Insurance Company of Oregon</ExtendedStatusDesc>
                </ExtendedStatus>
                <ChangeStatus IdRef="A027">
                    <ActionCd>A</ActionCd>
                    <ChangeDesc>Added QuickQuote/InsuredFullToBePaidAmt/Amt</ChangeDesc>
                </ChangeStatus>
            </MsgStatus>
            <TransactionResponseDt>2016-06-03</TransactionResponseDt>
            <PolicySummaryInfo>
                <PolicyStatusCd>QuotedNotBound</PolicyStatusCd>
                <FullTermAmt>
                    <Amt>498.00</Amt>
                </FullTermAmt>
            </PolicySummaryInfo>
            <com.safeco_CompanyURL id="A001"/>
            <ChangeStatus IdRef="A001">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added CompanyURL</ChangeDesc>
            </ChangeStatus>
            <com.safeco_CompanyURLText id="A002">This is a link to the quote at the Safeco web site.
            </com.safeco_CompanyURLText>
            <ChangeStatus IdRef="A002">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added CompanyURLText</ChangeDesc>
            </ChangeStatus>
            <com.safeco_CompanyURLAutoLaunch id="A003">1</com.safeco_CompanyURLAutoLaunch>
            <ChangeStatus IdRef="A003">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added CompanyURLAutoLaunch</ChangeDesc>
            </ChangeStatus>
            <com.safeco_QuoteRq_PremiumTotal id="A004">498.00</com.safeco_QuoteRq_PremiumTotal>
            <ChangeStatus IdRef="A004">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added QuoteRq_PremiumTotal</ChangeDesc>
            </ChangeStatus>
            <com.safeco_QuoteRq_PremiumTotalPIF id="A005">498.00</com.safeco_QuoteRq_PremiumTotalPIF>
            <ChangeStatus IdRef="A005">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added QuoteRq_PremiumTotalPIF</ChangeDesc>
            </ChangeStatus>
            <RqUID>b4573ed0-0559-11da-8ad9-198309056153</RqUID>
            <com.safeco_TransactionType>QuoteRQ</com.safeco_TransactionType>
            <TransactionRequestDt>2014-11-22T16:05:59</TransactionRequestDt>
            <TransactionEffectiveDt>2014-11-22</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>037686</ContractNumber>
                    <ProducerSubCode>A*</ProducerSubCode>
                </ProducerInfo>
            </Producer>
            <PersPolicy>
                <CreditScoreInfo>
                    <CSPolicyTypeCd>IBS</CSPolicyTypeCd>
                </CreditScoreInfo>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>Standard Other Carrier</InsurerName>
                    <ContractTerm>
                        <ExpirationDt>2017-03-14</ExpirationDt>
                    </ContractTerm>
                    <CreditScoreInfo>
                        <CSPolicyTypeCd>IBS</CSPolicyTypeCd>
                    </CreditScoreInfo>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Other</PolicyCd>
                    <InsurerName>Safeco</InsurerName>
                    <LOBCd>HOME</LOBCd>
                    <PolicyNumber>Y1111111</PolicyNumber>
                </OtherOrPriorPolicy>
                <ContractTerm>
                    <EffectiveDt>2016-05-14</EffectiveDt>
                </ContractTerm>
                <PersApplicationInfo>
                    <LengthTimeCurrentAddr>
                        <DurationPeriod>
                            <UnitMeasurementCd>year</UnitMeasurementCd>
                            <NumUnits>05</NumUnits>
                        </DurationPeriod>
                    </LengthTimeCurrentAddr>
                </PersApplicationInfo>
                <QuoteInfo>
                    <CompanysQuoteNumber>f00c6a12-e544-420b-b592-bf19f6dd9af6</CompanysQuoteNumber>
                    <com.safeco_CompanyClientID>0a169be8-3204-4838-b6da-1feb2a7d8d3b</com.safeco_CompanyClientID>
                    <com.safeco_MarketingMessages>
                        <com.safeco_AgentMessage>Safeco Motorcycle Enhancements!</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage>Come and
                            <a href="https://s3.amazonaws.com/Motorcycle_refresh_sizzle/MotorcycleRefresh_SIZZLE.html">
                                see what's in it for you.
                            </a>
                        </com.safeco_AgentMessage>
                        <com.safeco_AgentMessage>Agent Message 8</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage>Agent Message 9 - Updated</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage>Last Message</com.safeco_AgentMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 1</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 2 - update</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 3</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 5</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 6</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 7</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 8</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 9</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 10- Updated</com.safeco_ConsumerMessage>
                    </com.safeco_MarketingMessages>
                    <Description id="A001"/>
                    <InsuredFullToBePaidAmt id="A027">
                        <Amt>498.00</Amt>
                        <CurCd>USD</CurCd>
                    </InsuredFullToBePaidAmt>
                    <QuoteURL id="A001"/>
                    <com.safeco_QuoteSummaryURL id="A003">
                        https://safesite.qa.safeco.com/tac/Consumer/Home/ReportDisplay.aspx?PolicyGuid=f00c6a12-e544-420b-b592-bf19f6dd9af6&amp;ReportName=QuoteSummary
                    </com.safeco_QuoteSummaryURL>
                    <com.safeco_QuoteRq_Market id="A040">050</com.safeco_QuoteRq_Market>
                    <ChangeStatus IdRef="A040">
                        <ActionCd>A</ActionCd>
                        <ChangeDesc>Added com.safeco_QuoteRq_Market</ChangeDesc>
                    </ChangeStatus>
                    <com.safeco_QuoteRq_PremiumTotal id="A041">498.00</com.safeco_QuoteRq_PremiumTotal>
                    <ChangeStatus IdRef="A041">
                        <ActionCd>A</ActionCd>
                        <ChangeDesc>Added QuoteRq_PremiumTotal</ChangeDesc>
                    </ChangeStatus>
                    <com.safeco_QuoteRq_PremiumTotalPIF id="A042">498.00</com.safeco_QuoteRq_PremiumTotalPIF>
                    <ChangeStatus IdRef="A042">
                        <ActionCd>A</ActionCd>
                        <ChangeDesc>Added QuoteRq_PremiumTotalPIF</ChangeDesc>
                    </ChangeStatus>
                </QuoteInfo>
                <CurrentTermAmt>
                    <Amt>12280.85</Amt>
                </CurrentTermAmt>
                <PaymentOption>
                    <DownPaymentPct>100.00</DownPaymentPct>
                    <NumPayments>1</NumPayments>
                    <DepositMethodPaymentCd>CreditCard</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>FL</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>498.00</Amt>
                    </DepositAmt>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>25.00</DownPaymentPct>
                    <NumPayments>4</NumPayments>
                    <DepositMethodPaymentCd>CreditCard</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>QT</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>129.5</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>5.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>129.5</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>8.3</DownPaymentPct>
                    <NumPayments>12</NumPayments>
                    <DepositMethodPaymentCd>CreditCard</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>CK</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>46.5</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>5.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>46.5</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>100.00</DownPaymentPct>
                    <NumPayments>1</NumPayments>
                    <DepositMethodPaymentCd>EFT</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>FL</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>498.00</Amt>
                    </DepositAmt>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>25.00</DownPaymentPct>
                    <NumPayments>4</NumPayments>
                    <DepositMethodPaymentCd>EFT</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>QT</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>126.50</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>2.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>126.50</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>8.3</DownPaymentPct>
                    <NumPayments>12</NumPayments>
                    <DepositMethodPaymentCd>EFT</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>CK</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>43.50</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>2.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>43.50</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>100.00</DownPaymentPct>
                    <NumPayments>1</NumPayments>
                    <DepositMethodPaymentCd>com.safeco_COD</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>FL</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>498.00</Amt>
                    </DepositAmt>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>50.00</DownPaymentPct>
                    <NumPayments>2</NumPayments>
                    <DepositMethodPaymentCd>CreditCard</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>2E</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>254.00</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>0.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>254.00</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>50.00</DownPaymentPct>
                    <NumPayments>2</NumPayments>
                    <DepositMethodPaymentCd>EFT</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>2E</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>254.00</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>0.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>254.00</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>16.60</DownPaymentPct>
                    <NumPayments>11</NumPayments>
                    <DepositMethodPaymentCd>CreditCard</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>MO</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>46.5</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>5.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>46.5</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>16.60</DownPaymentPct>
                    <NumPayments>11</NumPayments>
                    <DepositMethodPaymentCd>EFT</DepositMethodPaymentCd>
                    <Description>498.00</Description>
                    <PaymentPlanCd>MO</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>43.50</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>2.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>43.50</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <CurrentTermAmt id="A028">
                    <Amt>498.00</Amt>
                    <CurCd>USD</CurCd>
                </CurrentTermAmt>
            </PersPolicy>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <GivenName>Fred</GivenName>
                            <OtherGivenName>W</OtherGivenName>
                            <Surname>Flintstone</Surname>
                        </PersonName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>***********</TaxId>
                        </TaxIdentity>
                    </NameInfo>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-3618982</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                    </Communications>
                    <Addr>
                        <AddrTypeCd>StreetAddress</AddrTypeCd>
                        <Addr1>22045 SW COLE CT</Addr1>
                        <City>TUALATIN</City>
                        <StateProvCd>OR</StateProvCd>
                        <PostalCode>97062-7038</PostalCode>
                    </Addr>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>FNI</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <MaritalStatusCd>M</MaritalStatusCd>
                        <BirthDt>1983-09-05</BirthDt>
                        <com.safeco_IndustryCd>Construct/EnrgyTrds</com.safeco_IndustryCd>
                        <com.safeco_OccupationCd>Laborer/Worker</com.safeco_OccupationCd>
                        <LengthTimeCurrentOccupation>
                            <NumUnits/>
                            <UnitMeasurementCd>Years</UnitMeasurementCd>
                        </LengthTimeCurrentOccupation>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <GivenName>Ashley</GivenName>
                            <Surname>Moore</Surname>
                        </PersonName>
                    </NameInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>John Smith &amp; Jane Doe</CommercialName>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <MaritalStatusCd>M</MaritalStatusCd>
                        <BirthDt>1989-05-09</BirthDt>
                        <com.safeco_IndustryCd>Business/Sales/Offi</com.safeco_IndustryCd>
                        <com.safeco_OccupationCd>CSR</com.safeco_OccupationCd>
                        <TitleRelationshipCd>SP</TitleRelationshipCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <Location id="001">
                <Addr>
                    <AddrTypeCd>StreetAddress</AddrTypeCd>
                    <Addr1>22045 SW COLE CT</Addr1>
                    <City>TUALATIN</City>
                    <StateProvCd>OR</StateProvCd>
                    <PostalCode>97062-7038</PostalCode>
                </Addr>
                <AdditionalInterest>
                    <AdditionalInterestInfo>
                        <NatureInterestCd>MORTSR</NatureInterestCd>
                        <InterestRank>1</InterestRank>
                    </AdditionalInterestInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>1ST MORTGAGEE</CommercialName>
                            </CommlName>
                        </NameInfo>
                    </GeneralPartyInfo>
                </AdditionalInterest>
            </Location>
            <ChangeStatus IdRef="M006">
                <ActionCd>M</ActionCd>
                <ChangeDesc>Changed com.safeco_PackageSelection
                    (ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq/HomeLineBusiness/Dwell/com.safeco_PackageSelection)
                </ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="M007">
                <ActionCd>M</ActionCd>
                <ChangeDesc>Changed TerritoryCd
                    (ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq/HomeLineBusiness/Dwell/Construction/TerritoryCd)
                </ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="M008">
                <ActionCd>M</ActionCd>
                <ChangeDesc>Changed NumStories
                    (ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq/HomeLineBusiness/Dwell/Construction/NumStories)
                </ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="M009">
                <ActionCd>M</ActionCd>
                <ChangeDesc>Changed ExteriorWallMaterialCd
                    (ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq/HomeLineBusiness/Dwell/DwellInspectionValuation/ExteriorWallMaterialInfo/ExteriorWallMaterialCd)
                </ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="D010">
                <ActionCd>D</ActionCd>
                <ChangeDesc>Deleted YesNoCd
                    (ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq/HomeLineBusiness/Dwell/QuestionAnswer[3]/YesNoCd)
                </ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="D011">
                <ActionCd>D</ActionCd>
                <ChangeDesc>Deleted NumUnits
                    (ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq/HomeLineBusiness/Dwell/BldgProtection/DistanceToFireStation/NumUnits)
                </ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="M012">
                <ActionCd>M</ActionCd>
                <ChangeDesc>Changed FireProtectionClassCd
                    (ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq/HomeLineBusiness/Dwell/BldgProtection/FireProtectionClassCd)
                </ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="D013">
                <ActionCd>D</ActionCd>
                <ChangeDesc>Deleted WiringImprovementCd
                    (ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq/HomeLineBusiness/Dwell/BldgImprovements/WiringImprovementCd)
                </ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="D014">
                <ActionCd>D</ActionCd>
                <ChangeDesc>Deleted WiringImprovementYear
                    (ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq/HomeLineBusiness/Dwell/BldgImprovements/WiringImprovementYear)
                </ChangeDesc>
            </ChangeStatus>
            <HomeLineBusiness>
                <Dwell LocationRef="001">
                    <com.safeco_PackageSelection id="M006">A</com.safeco_PackageSelection>
                    <PolicyTypeCd>03</PolicyTypeCd>
                    <Construction>
                        <YearBuilt>1957</YearBuilt>
                        <ConstructionCd>F</ConstructionCd>
                        <TerritoryCd id="M007">048</TerritoryCd>
                        <NumStories id="M008">1</NumStories>
                        <RoofingMaterial>
                            <RoofMaterialCd>ASPHS</RoofMaterialCd>
                        </RoofingMaterial>
                        <FoundationCd>Partial</FoundationCd>
                        <FoundationInfo>
                            <FoundationCd id="A033">1102</FoundationCd>
                            <MaterialPct id="A034">100</MaterialPct>
                        </FoundationInfo>
                        <RoofGeometryType>
                            <RoofGeometryTypeCd id="A035">15102</RoofGeometryTypeCd>
                            <MaterialPct id="A036">100</MaterialPct>
                        </RoofGeometryType>
                    </Construction>
                    <AreaTypeSurroundingsCd>IN</AreaTypeSurroundingsCd>
                    <HousekeepingConditionCd>E</HousekeepingConditionCd>
                    <Coverage>
                        <CoverageCd>DWELL</CoverageCd>
                        <Limit>
                            <FormatInteger>180000</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatPct/>
                            <DeductibleAppliesToCd>WindHail</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatInteger>1000</FormatInteger>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt id="A019">000432.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ERQK</CoverageCd>
                        <Deductible>
                            <FormatPct>10</FormatPct>
                            <LimitAppliesToCd>Percent</LimitAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SPPP</CoverageCd>
                        <Option>
                            <OptionCd>No</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OS</CoverageCd>
                        <Limit>
                            <FormatInteger>18000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PP</CoverageCd>
                        <Limit>
                            <FormatInteger>90000</FormatInteger>
                            <LimitAppliesToCd>Contents</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PL</CoverageCd>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt id="A017">000042.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <DwellInspectionValuation>
                        <TotalArea>
                            <NumUnits>1254</NumUnits>
                            <UnitMeasurementCd>square foot</UnitMeasurementCd>
                        </TotalArea>
                        <NumFamilies>1</NumFamilies>
                        <BathroomInfo>
                            <BathroomTypeCd>Full</BathroomTypeCd>
                            <NumBathrooms>1</NumBathrooms>
                            <ConstructionQualityCd id="A037">STCUST</ConstructionQualityCd>
                        </BathroomInfo>
                        <BathroomInfo>
                            <BathroomTypeCd>ThreeQuarter</BathroomTypeCd>
                            <NumBathrooms/>
                            <ConstructionQualityCd id="A038">STCUST</ConstructionQualityCd>
                        </BathroomInfo>
                        <BathroomInfo>
                            <BathroomTypeCd>Half</BathroomTypeCd>
                            <NumBathrooms>1</NumBathrooms>
                            <ConstructionQualityCd id="A039">STCUST</ConstructionQualityCd>
                        </BathroomInfo>
                        <ExteriorWallMaterialInfo>
                            <ExteriorWallMaterialCd id="M009">CementF</ExteriorWallMaterialCd>
                            <MaterialPct id="A026">100</MaterialPct>
                        </ExteriorWallMaterialInfo>
                        <DwellStyleCd>Rambler</DwellStyleCd>
                        <AirConditioningCd/>
                        <InteriorFinishInfo>
                            <InteriorComponentTypeCd/>
                        </InteriorFinishInfo>
                        <GarageInfo>
                            <GarageTypeCd id="A029">Frame</GarageTypeCd>
                            <NumVehs>2</NumVehs>
                            <NumGarages id="A030">1</NumGarages>
                        </GarageInfo>
                        <InteriorFinishInfo>
                            <InteriorComponentTypeCd>com.safeco_Molding</InteriorComponentTypeCd>
                            <MaterialTypeCd id="A031">140</MaterialTypeCd>
                            <MaterialPct id="A032"/>
                        </InteriorFinishInfo>
                    </DwellInspectionValuation>
                    <DwellOccupancy>
                        <OccupancyTypeCd>OWNER</OccupancyTypeCd>
                        <DwellUseCd>1</DwellUseCd>
                        <ResidenceTypeCd>DW</ResidenceTypeCd>
                    </DwellOccupancy>
                    <HeatingUnitInfo>
                        <FuelTypeCd>Gas</FuelTypeCd>
                        <HeatingUnitCd/>
                    </HeatingUnitInfo>
                    <InspectionInfo>
                        <InspectionCd>com.safeco_Internet</InspectionCd>
                    </InspectionInfo>
                    <PurchaseDt>2000-06-01</PurchaseDt>
                    <QuestionAnswer>
                        <QuestionCd>GENRL20</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL15</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL16</QuestionCd>
                        <YesNoCd id="D010"/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>com.safeco_BusinessIncidental</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <BldgProtection>
                        <DistanceToFireStation>
                            <NumUnits id="D011"/>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits>500</NumUnits>
                        </DistanceToHydrant>
                        <ProtectionDeviceBurglarCd/>
                        <ProtectionDeviceSprinklerCd/>
                        <FireProtectionClassCd id="M012">5</FireProtectionClassCd>
                    </BldgProtection>
                    <BldgImprovements>
                        <RoofingImprovementCd>C</RoofingImprovementCd>
                        <RoofingImprovementYear>2000</RoofingImprovementYear>
                        <WiringImprovementCd id="D013"/>
                        <WiringImprovementYear id="D014"/>
                    </BldgImprovements>
                    <Coverage>
                        <CoverageCd>com.safeco_EDC</CoverageCd>
                        <Limit>
                            <FormatPct>25</FormatPct>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>EQPBK</CoverageCd>
                        <Option>
                            <OptionCd>Yes</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt id="A018">000024.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <SwimmingPool>
                        <AboveGroundInd/>
                    </SwimmingPool>
                    <Coverage>
                        <CoverageCd>BOLAW</CoverageCd>
                        <Limit>
                            <FormatPct id="A015">10</FormatPct>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LAC</CoverageCd>
                        <Limit>
                            <FormatInteger id="A016">500</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LOU</CoverageCd>
                        <Limit>
                            <FormatText id="A023">24Months</FormatText>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ACVRS</CoverageCd>
                        <Option>
                            <OptionCd id="A024"/>
                        </Option>
                    </Coverage>
                </Dwell>
                <QuestionAnswer>
                    <QuestionCd>GENRL06</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                </QuestionAnswer>
                <InsurerName id="A020">Safeco Insurance Company of Oregon</InsurerName>
                <CompanyProductCd id="A022">HOM</CompanyProductCd>
                <NAICCd id="A025">11071</NAICCd>
            </HomeLineBusiness>
            <ChangeStatus IdRef="A015">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added FormatPct</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A016">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added FormatInteger</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A017">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added Amt</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A018">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added Amt</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A019">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added Amt</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A020">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added InsurerName</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A021">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added OtherFeaturesDesc</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A022">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added CompanyProductCd</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A023">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added FormatText</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A024">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added OptionCd</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A025">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added NAICCd</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A026">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added MaterialPct</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A029">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added GarageInfo/GarageTypeCd</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A030">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added GarageInfo/NumGarages</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A031">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added InteriorFinishInfo/MaterialTypeCd</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A032">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added InteriorFinishInfo/MaterialPct</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A033">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added FoundationInfo/FoundationCd</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A034">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added FoundationInfo/MaterialPct</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A035">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added RoofGeometryType/RoofGeometryTypeCd</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A036">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added RoofGeometryType/MaterialPct</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A037">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added BathroomInfo/ConstructionQualityCd</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A038">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added BathroomInfo/ConstructionQualityCd</ChangeDesc>
            </ChangeStatus>
            <ChangeStatus IdRef="A039">
                <ActionCd>A</ActionCd>
                <ChangeDesc>Added BathroomInfo/ConstructionQualityCd</ChangeDesc>
            </ChangeStatus>
        </HomePolicyQuoteInqRs>
        <RqUID>f7b053d2-c2b3-4c5b-8d55-2c6b92a57777</RqUID>
        <Status>
            <StatusCd>0</StatusCd>
            <StatusDesc/>
        </Status>
    </InsuranceSvcRs>
</ACORD>
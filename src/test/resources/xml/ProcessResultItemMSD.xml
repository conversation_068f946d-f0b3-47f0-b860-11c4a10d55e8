<?xml-stylesheet type="text/xsl" href='http://ecdev-tac.safeco.com/BookTransfer/TACML/WebAPI/scripts/xslt/BoatXMLView.xslt'?>
<ACORD>
    <Status>
        <StatusCd>0</StatusCd>
        <StatusDesc/>
    </Status>
    <SignonRs>
        <ClientDt>2016-03-04</ClientDt>
        <ClientApp>
            <Org>Applied Systems</Org>
            <Name>TAM</Name>
        </ClientApp>
    </SignonRs>
    <InsuranceSvcRs>
        <RqUID>f7b053d2-c2b3-4c5b-8d55-2c6b92a57777</RqUID>
        <HomePolicyQuoteInqRs>
            <TransactionResponseDt>2016-05-20</TransactionResponseDt>
            <PolicySummaryInfo>
                <PolicyStatusCd>NotQuotedNotBound</PolicyStatusCd>
            </PolicySummaryInfo>
            <RemarkText id="A001" IdRef="A001">The Safeco system has encountered a rating error. Please report to you
                company contact. (600)
            </RemarkText>
            <MsgStatus>
                <MsgStatusCd>Rejected</MsgStatusCd>
                <MsgStatusDesc>The Safeco system has encountered a rating error. Please report to you company contact.
                    (600)
                </MsgStatusDesc>
            </MsgStatus>
            <RqUID>b4573ed0-0559-11da-8ad9-198309056153</RqUID>
            <com.safeco_TransactionType>QuoteRQ</com.safeco_TransactionType>
            <TransactionRequestDt>2014-11-22T16:05:59</TransactionRequestDt>
            <TransactionEffectiveDt>2014-11-22</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>037686</ContractNumber>
                    <ProducerSubCode>A*</ProducerSubCode>
                </ProducerInfo>
            </Producer>
            <PersPolicy>
                <CreditScoreInfo>
                    <CSPolicyTypeCd>IBS</CSPolicyTypeCd>
                </CreditScoreInfo>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>Standard Other Carrier</InsurerName>
                    <ContractTerm>
                        <ExpirationDt>2017-03-14</ExpirationDt>
                    </ContractTerm>
                    <CreditScoreInfo>
                        <CSPolicyTypeCd>IBS</CSPolicyTypeCd>
                    </CreditScoreInfo>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Other</PolicyCd>
                    <InsurerName>Safeco</InsurerName>
                    <LOBCd>HOME</LOBCd>
                    <PolicyNumber>Y1111111</PolicyNumber>
                </OtherOrPriorPolicy>
                <ContractTerm>
                    <EffectiveDt>2016-05-14</EffectiveDt>
                </ContractTerm>
                <PersApplicationInfo>
                    <LengthTimeCurrentAddr>
                        <DurationPeriod>
                            <UnitMeasurementCd>year</UnitMeasurementCd>
                            <NumUnits>05</NumUnits>
                        </DurationPeriod>
                    </LengthTimeCurrentAddr>
                </PersApplicationInfo>
                <QuoteInfo>
                    <CompanysQuoteNumber>b34fe394-7ae4-4ed6-b7f8-a02bd3ae10a4</CompanysQuoteNumber>
                    <com.safeco_CompanyClientID>0a169be8-3204-4838-b6da-1feb2a7d8d3b</com.safeco_CompanyClientID>
                    <com.safeco_MarketingMessages>
                        <com.safeco_AgentMessage>Safeco Motorcycle Enhancements!</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage>Come and
                            <a href="https://s3.amazonaws.com/Motorcycle_refresh_sizzle/MotorcycleRefresh_SIZZLE.html">
                                see what's in it for you.
                            </a>
                        </com.safeco_AgentMessage>
                        <com.safeco_AgentMessage>Agent Message 8</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage>Agent Message 9 - Updated</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage>Last Message</com.safeco_AgentMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 1</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 2 - update</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 3</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 5</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 6</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 7</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 8</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 9</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage>Consumer Message 10- Updated</com.safeco_ConsumerMessage>
                    </com.safeco_MarketingMessages>
                    <Description id="A020"/>
                    <QuoteURL id="A020"/>
                    <com.safeco_QuoteSummaryURL id="A033">
                        https://safesite.qa.safeco.com/tac/Consumer/Home/ReportDisplay.aspx?PolicyGuid=b34fe394-7ae4-4ed6-b7f8-a02bd3ae10a4&amp;ReportName=QuoteSummary
                    </com.safeco_QuoteSummaryURL>
                    <com.safeco_QuoteRq_Market id="A034">050</com.safeco_QuoteRq_Market>
                    <ChangeStatus IdRef="A034">
                        <ActionCd>A</ActionCd>
                        <ChangeDesc>Added com.safeco_QuoteRq_Market</ChangeDesc>
                    </ChangeStatus>
                </QuoteInfo>
                <CurrentTermAmt>
                    <Amt>1280.65</Amt>
                </CurrentTermAmt>
                <PaymentOption>
                    <DownPaymentPct>100.00</DownPaymentPct>
                    <NumPayments>1</NumPayments>
                    <DepositMethodPaymentCd>CreditCard</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>FL</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>25.00</DownPaymentPct>
                    <NumPayments>4</NumPayments>
                    <DepositMethodPaymentCd>CreditCard</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>QT</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>0.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>0.00</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>16.60</DownPaymentPct>
                    <NumPayments>11</NumPayments>
                    <DepositMethodPaymentCd>CreditCard</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>MO</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>0.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>0.00</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>8.3</DownPaymentPct>
                    <NumPayments>12</NumPayments>
                    <DepositMethodPaymentCd>CreditCard</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>CK</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>0.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>0.00</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>100.00</DownPaymentPct>
                    <NumPayments>1</NumPayments>
                    <DepositMethodPaymentCd>EFT</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>FL</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>25.00</DownPaymentPct>
                    <NumPayments>4</NumPayments>
                    <DepositMethodPaymentCd>EFT</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>QT</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>0.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>0.00</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>16.60</DownPaymentPct>
                    <NumPayments>11</NumPayments>
                    <DepositMethodPaymentCd>EFT</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>MO</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>0.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>0.00</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>8.3</DownPaymentPct>
                    <NumPayments>12</NumPayments>
                    <DepositMethodPaymentCd>EFT</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>CK</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>0.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>0.00</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>100.00</DownPaymentPct>
                    <NumPayments>1</NumPayments>
                    <DepositMethodPaymentCd>com.safeco_COD</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>FL</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>50.00</DownPaymentPct>
                    <NumPayments>2</NumPayments>
                    <DepositMethodPaymentCd>CreditCard</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>2E</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>0.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>0.00</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <PaymentOption>
                    <DownPaymentPct>50.00</DownPaymentPct>
                    <NumPayments>2</NumPayments>
                    <DepositMethodPaymentCd>EFT</DepositMethodPaymentCd>
                    <Description>0.00</Description>
                    <PaymentPlanCd>2E</PaymentPlanCd>
                    <DepositAmt>
                        <Amt>0.00</Amt>
                    </DepositAmt>
                    <InstallmentFeeAmt>
                        <Amt>0.00</Amt>
                    </InstallmentFeeAmt>
                    <InstallmentInfo>
                        <InstallmentNumber>1</InstallmentNumber>
                        <InstallmentAmt>
                            <Amt>0.00</Amt>
                        </InstallmentAmt>
                    </InstallmentInfo>
                </PaymentOption>
                <CurrentTermAmt id="A021">
                    <Amt>0.00</Amt>
                    <CurCd>USD</CurCd>
                </CurrentTermAmt>
                <LOBCd>HOME</LOBCd>
            </PersPolicy>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <GivenName>Fred</GivenName>
                            <OtherGivenName>W</OtherGivenName>
                            <Surname>Flintstone</Surname>
                        </PersonName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>***********</TaxId>
                        </TaxIdentity>
                    </NameInfo>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-3618982</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                    </Communications>
                    <Addr>
                        <AddrTypeCd>StreetAddress</AddrTypeCd>
                        <Addr1>22045 SW COLE CT</Addr1>
                        <City>TUALATIN</City>
                        <StateProvCd>OR</StateProvCd>
                        <PostalCode>97062-7038</PostalCode>
                    </Addr>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>FNI</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <MaritalStatusCd>M</MaritalStatusCd>
                        <BirthDt>1983-09-05</BirthDt>
                        <com.safeco_IndustryCd>Construct/EnrgyTrds</com.safeco_IndustryCd>
                        <com.safeco_OccupationCd>Laborer/Worker</com.safeco_OccupationCd>
                        <LengthTimeCurrentOccupation>
                            <NumUnits/>
                            <UnitMeasurementCd>Years</UnitMeasurementCd>
                        </LengthTimeCurrentOccupation>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <GivenName>Ashley</GivenName>
                            <Surname>Moore</Surname>
                        </PersonName>
                    </NameInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>John Smith &amp; Jane Doe</CommercialName>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <MaritalStatusCd>M</MaritalStatusCd>
                        <BirthDt>1989-05-09</BirthDt>
                        <com.safeco_IndustryCd>Business/Sales/Offi</com.safeco_IndustryCd>
                        <com.safeco_OccupationCd>CSR</com.safeco_OccupationCd>
                        <TitleRelationshipCd>SP</TitleRelationshipCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <Location id="001">
                <Addr>
                    <AddrTypeCd>StreetAddress</AddrTypeCd>
                    <Addr1>22045 SW COLE CT</Addr1>
                    <City>TUALATIN</City>
                    <StateProvCd>OR</StateProvCd>
                    <PostalCode>97062-7038</PostalCode>
                </Addr>
                <AdditionalInterest>
                    <AdditionalInterestInfo>
                        <NatureInterestCd>MORTSR</NatureInterestCd>
                        <InterestRank>1</InterestRank>
                    </AdditionalInterestInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>1ST MORTGAGEE</CommercialName>
                            </CommlName>
                        </NameInfo>
                    </GeneralPartyInfo>
                </AdditionalInterest>
            </Location>
            <HomeLineBusiness>
                <Dwell LocationRef="001">
                    <com.safeco_PackageSelection id="M002">A</com.safeco_PackageSelection>
                    <PolicyTypeCd>03</PolicyTypeCd>
                    <Construction>
                        <YearBuilt>1957</YearBuilt>
                        <ConstructionCd>F</ConstructionCd>
                        <TerritoryCd id="M003">048</TerritoryCd>
                        <NumStories id="M004">1</NumStories>
                        <RoofingMaterial>
                            <RoofMaterialCd>ASPHS</RoofMaterialCd>
                        </RoofingMaterial>
                        <FoundationCd>Partial</FoundationCd>
                        <FoundationInfo>
                            <FoundationCd id="A026">1102</FoundationCd>
                            <MaterialPct id="A027">100</MaterialPct>
                        </FoundationInfo>
                        <RoofGeometryType>
                            <RoofGeometryTypeCd id="A028">15102</RoofGeometryTypeCd>
                            <MaterialPct id="A029">100</MaterialPct>
                        </RoofGeometryType>
                    </Construction>
                    <AreaTypeSurroundingsCd>IN</AreaTypeSurroundingsCd>
                    <HousekeepingConditionCd>E</HousekeepingConditionCd>
                    <Coverage>
                        <CoverageCd>DWELL</CoverageCd>
                        <Limit>
                            <FormatInteger>180000</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatPct/>
                            <DeductibleAppliesToCd>WindHail</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatInteger>1000</FormatInteger>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ERQK</CoverageCd>
                        <Deductible>
                            <FormatPct>10</FormatPct>
                            <LimitAppliesToCd>Percent</LimitAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SPPP</CoverageCd>
                        <Option>
                            <OptionCd>No</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OS</CoverageCd>
                        <Limit>
                            <FormatInteger>18000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PP</CoverageCd>
                        <Limit>
                            <FormatInteger>90000</FormatInteger>
                            <LimitAppliesToCd>Contents</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PL</CoverageCd>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <DwellInspectionValuation>
                        <TotalArea>
                            <NumUnits>1254</NumUnits>
                            <UnitMeasurementCd>square foot</UnitMeasurementCd>
                        </TotalArea>
                        <NumFamilies>1</NumFamilies>
                        <BathroomInfo>
                            <BathroomTypeCd>Full</BathroomTypeCd>
                            <NumBathrooms>1</NumBathrooms>
                            <ConstructionQualityCd id="A030">STCUST</ConstructionQualityCd>
                        </BathroomInfo>
                        <BathroomInfo>
                            <BathroomTypeCd>ThreeQuarter</BathroomTypeCd>
                            <NumBathrooms/>
                            <ConstructionQualityCd id="A031">STCUST</ConstructionQualityCd>
                        </BathroomInfo>
                        <BathroomInfo>
                            <BathroomTypeCd>Half</BathroomTypeCd>
                            <NumBathrooms>1</NumBathrooms>
                            <ConstructionQualityCd id="A032">STCUST</ConstructionQualityCd>
                        </BathroomInfo>
                        <ExteriorWallMaterialInfo>
                            <ExteriorWallMaterialCd id="M005">CementF</ExteriorWallMaterialCd>
                            <MaterialPct id="A017">100</MaterialPct>
                        </ExteriorWallMaterialInfo>
                        <DwellStyleCd>Rambler</DwellStyleCd>
                        <AirConditioningCd/>
                        <InteriorFinishInfo>
                            <InteriorComponentTypeCd/>
                        </InteriorFinishInfo>
                        <GarageInfo>
                            <GarageTypeCd id="A022">Frame</GarageTypeCd>
                            <NumVehs>2</NumVehs>
                            <NumGarages id="A023">1</NumGarages>
                        </GarageInfo>
                        <InteriorFinishInfo>
                            <InteriorComponentTypeCd>com.safeco_Molding</InteriorComponentTypeCd>
                            <MaterialTypeCd id="A024">140</MaterialTypeCd>
                            <MaterialPct id="A025"/>
                        </InteriorFinishInfo>
                    </DwellInspectionValuation>
                    <DwellOccupancy>
                        <OccupancyTypeCd>OWNER</OccupancyTypeCd>
                        <DwellUseCd>1</DwellUseCd>
                        <ResidenceTypeCd>DW</ResidenceTypeCd>
                    </DwellOccupancy>
                    <HeatingUnitInfo>
                        <FuelTypeCd>Gas</FuelTypeCd>
                        <HeatingUnitCd/>
                    </HeatingUnitInfo>
                    <InspectionInfo>
                        <InspectionCd>com.safeco_Internet</InspectionCd>
                    </InspectionInfo>
                    <PurchaseDt>2000-06-01</PurchaseDt>
                    <QuestionAnswer>
                        <QuestionCd>GENRL20</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL15</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL16</QuestionCd>
                        <YesNoCd id="D006"/>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>com.safeco_BusinessIncidental</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <BldgProtection>
                        <DistanceToFireStation>
                            <NumUnits id="D007"/>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits>500</NumUnits>
                        </DistanceToHydrant>
                        <ProtectionDeviceBurglarCd/>
                        <ProtectionDeviceSprinklerCd/>
                        <FireProtectionClassCd id="M008">5</FireProtectionClassCd>
                    </BldgProtection>
                    <BldgImprovements>
                        <RoofingImprovementCd>C</RoofingImprovementCd>
                        <RoofingImprovementYear>2000</RoofingImprovementYear>
                        <WiringImprovementCd id="D009"/>
                        <WiringImprovementYear id="D010"/>
                    </BldgImprovements>
                    <Coverage>
                        <CoverageCd>com.safeco_EDC</CoverageCd>
                        <Limit>
                            <FormatPct>25</FormatPct>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>EQPBK</CoverageCd>
                        <Option>
                            <OptionCd>Yes</OptionCd>
                        </Option>
                    </Coverage>
                    <SwimmingPool>
                        <AboveGroundInd/>
                    </SwimmingPool>
                    <Coverage>
                        <CoverageCd>LOU</CoverageCd>
                        <Limit>
                            <FormatText id="A011">24Months</FormatText>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BOLAW</CoverageCd>
                        <Limit>
                            <FormatPct id="A012">10</FormatPct>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ACVRS</CoverageCd>
                        <Option>
                            <OptionCd id="A013"/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LAC</CoverageCd>
                        <Limit>
                            <FormatInteger id="A015">500</FormatInteger>
                        </Limit>
                    </Coverage>
                </Dwell>
                <QuestionAnswer>
                    <QuestionCd>GENRL06</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                </QuestionAnswer>
                <InsurerName id="A014">Safeco Insurance Company of Oregon</InsurerName>
                <NAICCd id="A018">11071</NAICCd>
                <CompanyProductCd id="A019">HOM</CompanyProductCd>
            </HomeLineBusiness>
        </HomePolicyQuoteInqRs>
    </InsuranceSvcRs>
</ACORD>
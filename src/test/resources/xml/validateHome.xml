<?xml-stylesheet type="text/xsl" href='http://vmrid-apiqia01/BookTransfer/DEVML/WebAPI/scripts/xslt/HomeXMLView.xslt'?>
<ACORD>
    <SignonRq>
        <ClientDt>2016-05-12</ClientDt>
        <ClientApp>
            <Org>Applied Systems</Org>
            <Name>TAM</Name>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>64dbe31154a59706168000ac10369a00</RqUID>
        <HomePolicyQuoteInqRq>
            <RqUID>64dbe31154a59706167ff1ac10369a00</RqUID>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>897404</ContractNumber>
                    <ProducerSubCode>BSM</ProducerSubCode>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>BANAM01C-2</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Harvey smith</CommercialName>
                        </CommlName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>***********</TaxId>
                        </TaxIdentity>
                        <PersonName>
                            <GivenName>Harvey</GivenName>
                            <Surname>smith</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>45 Hancock St</Addr1>
                        <Addr2>45 Hancock St</Addr2>
                        <City>Boston</City>
                        <StateProvCd>MA</StateProvCd>
                        <PostalCode>02114</PostalCode>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>- -</PhoneNumber>
                            <EmailAddr>Unsubscribed</EmailAddr>
                        </PhoneInfo>
                        <PhoneInfo>
                            <CommunicationUseCd>Business</CommunicationUseCd>
                            <PhoneNumber>************</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr/>
                        </EmailInfo>
                        <EmailInfo>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <EmailAddr/>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt>1959-05-30</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                        <OccupationDesc>PUBLIC RELATIONS</OccupationDesc>
                        <LengthTimeEmployed>
                            <NumUnits>4</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <LengthTimeCurrentOccupation>
                            <NumUnits>0</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeCurrentOccupation>
                        <LengthTimeWithPreviousEmployer>
                            <NumUnits>0</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeWithPreviousEmployer>
                        <MiscParty>
                            <GeneralPartyInfo>
                                <NameInfo>
                                    <CommlName>
                                        <CommercialName>CINNINGHAM COMM.</CommercialName>
                                    </CommlName>
                                </NameInfo>
                                <Addr>
                                    <Addr1/>
                                    <City/>
                                    <StateProvCd/>
                                    <PostalCode/>
                                </Addr>
                            </GeneralPartyInfo>
                            <MiscPartyInfo>
                                <MiscPartyRoleCd>Employer</MiscPartyRoleCd>
                            </MiscPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName/>
                        </CommlName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                        </TaxIdentity>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1/>
                        <Addr2/>
                        <City/>
                        <StateProvCd/>
                        <PostalCode/>
                    </Addr>
                    <Communications>
                        <EmailInfo>
                            <EmailAddr/>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <MaritalStatusCd/>
                        <OccupationDesc/>
                        <LengthTimeEmployed>
                            <NumUnits>0</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <LengthTimeCurrentOccupation>
                            <NumUnits>0</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeCurrentOccupation>
                        <LengthTimeWithPreviousEmployer>
                            <NumUnits>0</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeWithPreviousEmployer>
                        <MiscParty>
                            <GeneralPartyInfo>
                                <NameInfo>
                                    <CommlName>
                                        <CommercialName/>
                                    </CommlName>
                                </NameInfo>
                                <Addr>
                                    <Addr1/>
                                    <Addr2/>
                                    <City/>
                                    <StateProvCd/>
                                    <PostalCode/>
                                </Addr>
                            </GeneralPartyInfo>
                            <MiscPartyInfo>
                                <MiscPartyRoleCd>Employer</MiscPartyRoleCd>
                            </MiscPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <PolicyNumber>76587400002</PolicyNumber>
                <LOBCd>HOME</LOBCd>
                <NAICCd>17000</NAICCd>
                <ContractTerm>
                    <EffectiveDt>2017-04-08</EffectiveDt>
                    <ExpirationDt>2017-11-25</ExpirationDt>
                </ContractTerm>
                <CurrentTermAmt>
                    <Amt>342</Amt>
                </CurrentTermAmt>
                <OtherInsuranceWithCompanyCd>Y</OtherInsuranceWithCompanyCd>
                <Loss>
                    <LossDesc/>
                    <TotalPaidAmt>
                        <Amt>0</Amt>
                    </TotalPaidAmt>
                    <LossCauseCd/>
                </Loss>
                <PersApplicationInfo>
                    <LengthTimePreviousAddr>
                        <DurationPeriod>
                            <NumUnits>0</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </DurationPeriod>
                    </LengthTimePreviousAddr>
                </PersApplicationInfo>
            </PersPolicy>
            <Location id="001">
                <ItemIdInfo>
                    <InsurerId>1</InsurerId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>45 HANCOCK ST</Addr1>
                    <Addr2/>
                    <City>BOSTON</City>
                    <StateProvCd>MA</StateProvCd>
                    <PostalCode>02114</PostalCode>
                    <County>SUFFOLK</County>
                </Addr>
                <RiskLocationCd>IN</RiskLocationCd>
                <FireDistrict>Boston</FireDistrict>
            </Location>
            <HomeLineBusiness>
                <LOBCd>HOME</LOBCd>
                <Dwell LocationRef="001">
                    <PolicyTypeCd></PolicyTypeCd>
                    <AreaTypeSurroundingsCd>IN</AreaTypeSurroundingsCd>
                    <Construction>
                        <ConstructionCd>MY</ConstructionCd>
                        <YearBuilt>1960</YearBuilt>
                        <NumUnits>1</NumUnits>
                        <RoofingMaterial>
                            <RoofMaterialCd/>
                        </RoofingMaterial>
                    </Construction>
                    <DwellOccupancy>
                        <NumApartments>0</NumApartments>
                        <ResidenceTypeCd>AP</ResidenceTypeCd>
                        <DwellUseCd>1</DwellUseCd>
                    </DwellOccupancy>
                    <DwellRating>
                        <TerritoryCd>011</TerritoryCd>
                    </DwellRating>
                    <BldgProtection>
                        <FireProtectionClassCd>001</FireProtectionClassCd>
                        <DistanceToFireStation>
                            <NumUnits>3</NumUnits>
                            <UnitMeasurementCd>SMI</UnitMeasurementCd>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits>500</NumUnits>
                            <UnitMeasurementCd>FOT</UnitMeasurementCd>
                        </DistanceToHydrant>
                        <DoorLockCd>DEADB</DoorLockCd>
                    </BldgProtection>
                    <BldgImprovements/>
                    <DwellInspectionValuation>
                        <AirConditioningCd>com.safeco_None</AirConditioningCd>
                        <EstimatedReplCostAmt>
                            <Amt>0</Amt>
                        </EstimatedReplCostAmt>
                        <HeatSourcePrimaryCd/>
                        <NumFamilies>1</NumFamilies>
                        <TotalArea>
                            <NumUnits>750</NumUnits>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </TotalArea>
                    </DwellInspectionValuation>
                    <SwimmingPool>
                        <ApprovedFenceInd>0</ApprovedFenceInd>
                    </SwimmingPool>
                    <Coverage>
                        <CoverageCd>PP</CoverageCd>
                        <Limit>
                            <FormatInteger></FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleAppliesToCd>WindHail</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleAppliesToCd>Theft</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LOU</CoverageCd>
                        <Limit>
                            <FormatInteger>6420</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>2000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>H0431</CoverageCd>
                        <CoverageDesc>HO 0431</CoverageDesc>
                        <Limit>
                            <FormatInteger>10000</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt3</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ALARM</CoverageCd>
                        <CoverageDesc>HO 0416</CoverageDesc>
                        <Limit>
                            <FormatInteger>4</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible>
                            <DeductibleTypeCd>PC</DeductibleTypeCd>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt3</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>REWAR</CoverageCd>
                        <CoverageDesc>Auto Rewards End</CoverageDesc>
                        <Limit>
                            <FormatInteger>25</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible>
                            <DeductibleTypeCd>PC</DeductibleTypeCd>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt3</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <TerritoryCd/>
                    </Coverage>
                    <InspectionInfo/>
                    <TotalInsurableReplCostAmt>
                        <Amt>0</Amt>
                    </TotalInsurableReplCostAmt>
                    <ResidenceEmployees>
                        <EmployeeStatusCd>FULL</EmployeeStatusCd>
                    </ResidenceEmployees>
                </Dwell>
                <QuestionAnswer>
                    <QuestionCd>GENRL20</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL06</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL16</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>HOME02</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL22</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
            </HomeLineBusiness>
            <RemarkText>Form #: HO 0004 HOMEOWNERS 4 CONTENTS BROAD FORM 04/01/19 91 Form #: HO 0416 PREMISES ALARM OR
                FIRE PROTECTION SYSTEM
            </RemarkText>
            <RemarkText/>
        </HomePolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
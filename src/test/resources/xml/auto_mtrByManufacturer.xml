<?xml version="1.0" encoding="utf-8"?>
<ACORD>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName>AMSServices.com</SPName>
                <CustPermId />
                <CustLoginId><EMAIL></CustLoginId>
            </CustId>
            <CustPswd>
                <EncryptionTypeCd>NONE</EncryptionTypeCd>
                <Pswd />
            </CustPswd>
        </SignonPswd>
        <ClientDt>11/3/2020 10:50 AM</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>AMS Services</Org>
            <Name>Transit</Name>
            <Version>V2.5.5</Version>
        </ClientApp>
        <ProxyClient>
            <Org>BCFTech</Org>
            <Name>TransmitXML</Name>
            <Version>V1.00</Version>
        </ProxyClient>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>527165BB-68FD-4D59-824E-02ECCFE6FF73</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <com.safeco_TransactionType>QuoteRq</com.safeco_TransactionType>
            <RqUID>37E3B18C-1970-4E1F-A29A-807692E7C236</RqUID>
            <TransactionRequestDt>2020-11-02T12:12:30</TransactionRequestDt>
            <TransactionEffectiveDt>2020-11-02</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Acrisure, LLC d/b/a: Atlantic Agency</CommercialName>
                        </CommlName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>StreetAddress</AddrTypeCd>
                        <Addr1>1469 Deer Park Avenue</Addr1>
                        <City>NORTH BABYLON</City>
                        <StateProvCd>NY</StateProvCd>
                        <PostalCode>11703</PostalCode>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Business</CommunicationUseCd>
                            <PhoneNumber>******-2447784</PhoneNumber>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>
                <ProducerInfo>
                    <ContractNumber>897404</ContractNumber>
                    <ProducerRoleCd>Agency</ProducerRoleCd>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>3877</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <Surname>THORN</Surname>
                            <GivenName>RONALD</GivenName>
                            <OtherGivenName>G</OtherGivenName>
                        </PersonName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                        </TaxIdentity>
                        <CommlName>
                            <CommercialName>Forrest Nye</CommercialName>
                        </CommlName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>7 CARA COURT</Addr1>
                        <City>DEER PARK</City>
                        <StateProvCd>NY</StateProvCd>
                        <PostalCode>11729</PostalCode>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-1111111</PhoneNumber>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <GenderCd>M</GenderCd>
                        <BirthDt>1962-11-01</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy id="PolicyLevel">
                <OtherOrPriorPolicy>
                    <PolicyNumber>MC42219320</PolicyNumber>
                    <PolicyCd>Prior</PolicyCd>
                </OtherOrPriorPolicy>
                <PolicyNumber>MC42219320</PolicyNumber>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>42919</NAICCd>
                <ContractTerm>
                    <EffectiveDt>2022-02-11</EffectiveDt>
                    <ExpirationDt>2023-02-11</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                        <UnitMeasurementCd>MON</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <CurrentTermAmt>
                    <Amt>859.00</Amt>
                </CurrentTermAmt>
                <OriginalInceptionDt>2017-01-11</OriginalInceptionDt>
                <RenewalTerm>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                        <UnitMeasurementCd>MON</UnitMeasurementCd>
                    </DurationPeriod>
                </RenewalTerm>
                <PersApplicationInfo>
                    <ResidenceTypeCd>DW</ResidenceTypeCd>
                    <InsuredOrPrincipal />
                </PersApplicationInfo>
                <ControllingStateProvCd>NY</ControllingStateProvCd>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>42919</NAICCd>
                <CurrentTermAmt>
                    <Amt>859.00</Amt>
                </CurrentTermAmt>
                <RateEffectiveDt>2020-02-11</RateEffectiveDt>
                <PersDriver id="D1">
                    <ItemIdInfo>
                        <AgencyId>0001</AgencyId>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Nye</Surname>
                                <GivenName>Forrest</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId />
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1962-09-01</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>7950195</DriversLicenseNumber>
                            <StateProvCd>NY</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicenseTypeCd>Driver</LicenseTypeCd>
                            <LicensePermitNumber>*********</LicensePermitNumber>
                            <StateProvCd>NY</StateProvCd>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo id="PD1">
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="001" RatedDriverRef="D1" id="V3">
                    <ItemIdInfo>
                        <InsurerId>0003</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HARLEY-DAVIDSON</Manufacturer>
                    <Model>FLTRXSE CVO ROAD GLI</Model>
                    <ModelYear>2019</ModelYear>
                    <VehTypeCd>OT</VehTypeCd>
                    <FullTermAmt>
                        <Amt>502.00</Amt>
                    </FullTermAmt>
                    <TerritoryCd>3</TerritoryCd>
                    <VehIdentificationNumber>1HD1TCL13KB951865</VehIdentificationNumber>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Eaglemark Savings Bk</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr2>PO Box 21750</Addr2>
                                <City>Carson City</City>
                                <StateProvCd>NV</StateProvCd>
                                <PostalCode>89721</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LIEN</NatureInterestCd>
                            <PayorInd>0</PayorInd>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <AntiLockBrakeCd>Y</AntiLockBrakeCd>
                    <OdometerReading>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="4277407c-e402-444a-865f-60b1cf886c9c">
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>80.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="9dee5e60-95dd-477f-842b-18a0909bbf83">
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>45.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="72ea2524-1b01-4f2b-97c7-c8155d8e4efc">
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="2d69c152-f1d3-4805-90a9-dd70b2219283">
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible bookTransferAssociatedNodeId="V3" btId="838ad1fc-f6c1-4a0e-b0aa-e11ef2d832c0">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>64.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="67537143-3002-4757-99ac-a64202e144cd">
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible bookTransferAssociatedNodeId="V3" btId="8dd8f8b4-2cf7-4ea8-a5e9-4efa9b235800">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>249.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="fad61128-25c4-442a-ae55-6d896a0e3f22">
                        <CoverageCd>ACCES</CoverageCd>
                        <CoverageDesc>Accessory Coverage</CoverageDesc>
                        <Limit>
                            <FormatInteger>3000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>1.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="c9783498-43a3-4866-9bb3-2c0c93d90c88">
                        <CoverageCd>UMSUM</CoverageCd>
                        <CoverageDesc>Supplementary Uninsured Motorist</CoverageDesc>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>61.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="99c3d3d0-5e82-4946-8904-309ef545b0f8">
                        <CoverageCd>PEPIP</CoverageCd>
                        <CoverageDesc>Pedestrian PIP</CoverageDesc>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>2.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="397886f8-a00a-4ffd-a3c3-dca4afbb1ae9">
                        <CoverageCd>AT1</CoverageCd>
                        <CoverageDesc>ANTI-THEFT DEVICE 1 DISCOUNT</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="c7179b8e-b4f8-4618-81c7-5b2ea6abb30d">
                        <CoverageCd>SPOUS</CoverageCd>
                        <CoverageDesc>Supplemental Spousal Liability</CoverageDesc>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="e6b81378-7a12-4930-bb85-a793cd815703">
                        <CoverageCd>CFD</CoverageCd>
                        <CoverageDesc>Claims Free</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="f94816e2-f4c0-4dc0-aad2-5ef345daf22e">
                        <CoverageCd>HON</CoverageCd>
                        <CoverageDesc>Homeowner Discount</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="3bd1037f-cf8c-42b7-b3e0-49f8f80197ff">
                        <CoverageCd>SMV</CoverageCd>
                        <CoverageDesc>Multi Vehicle Discount</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="c726321b-be4f-420a-9f73-59174f1bb2c3">
                        <CoverageCd>PIF</CoverageCd>
                        <CoverageDesc>Paid In Full</CoverageDesc>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="001" RatedDriverRef="D0" id="V4">
                    <ItemIdInfo>
                        <InsurerId>0004</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>Ski-Doo</Manufacturer>
                    <Model>FLFBS FAT BOY</Model>
                    <ModelYear>2020</ModelYear>
                    <FullTermAmt>
                        <Amt>357.00</Amt>
                    </FullTermAmt>
                    <TerritoryCd>3</TerritoryCd>
                    <VehIdentificationNumber>1HD1YRK19LBO46747</VehIdentificationNumber>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Eaglemark Savings Bk</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr2>PO Box 21750</Addr2>
                                <City>Carson City</City>
                                <StateProvCd>NV</StateProvCd>
                                <PostalCode>89721</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LIEN</NatureInterestCd>
                            <PayorInd>0</PayorInd>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <AntiLockBrakeCd>Y</AntiLockBrakeCd>
                    <OdometerReading>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="137c4bbf-9bd3-42d2-8466-08df9204aed2">
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>73.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="0a96b08c-811a-4a59-9950-4698de29094b">
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>39.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="a215e036-435a-4dc5-92fc-008f159cc251">
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="3fc82180-d377-4498-828e-64b2af0b2b53">
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible bookTransferAssociatedNodeId="V4" btId="e91d0765-d51f-46a7-af8f-be9069e04648">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>47.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="b50a7288-0ad0-4dca-83e4-5d5fad457834">
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible bookTransferAssociatedNodeId="V4" btId="fd045bce-70dd-4dc6-8f45-206fe1b51141">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>136.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="ee70e96d-76c3-46a2-a5e6-5d8f3e98f8ea">
                        <CoverageCd>ACCES</CoverageCd>
                        <CoverageDesc>Accessory Coverage</CoverageDesc>
                        <Limit>
                            <FormatInteger>3000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>1.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="95a73d8f-81a0-47ec-8ead-c19e635fa28a">
                        <CoverageCd>PEPIP</CoverageCd>
                        <CoverageDesc>Pedestrian PIP</CoverageDesc>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>2.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="20cc852a-0b53-4910-964f-06ccbf7c16c1">
                        <CoverageCd>SPOUS</CoverageCd>
                        <CoverageDesc>Supplemental Spousal Liability</CoverageDesc>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="da33a062-b3b5-4b8c-821d-568d3ab4c43a">
                        <CoverageCd>UMSUM</CoverageCd>
                        <CoverageDesc>Supplementary Uninsured Motorist</CoverageDesc>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>59.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="cbd0d8eb-7159-4871-acd4-27e9f3664e2d">
                        <CoverageCd>AT1</CoverageCd>
                        <CoverageDesc>ANTI-THEFT DEVICE 1 DISCOUNT</CoverageDesc>
                    </Coverage>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="001">
                <ItemIdInfo>
                    <AgencyId>0001</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr2 />
                    <Addr1>7 CARA COURT</Addr1>
                    <City>DEER PARK</City>
                    <StateProvCd>NY</StateProvCd>
                    <PostalCode>11729</PostalCode>
                </Addr>
            </Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
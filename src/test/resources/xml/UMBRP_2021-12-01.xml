<?xml version="1.0" encoding="utf-8"?>
<ACORD>
  <SignonRq>
    <SignonPswd>
      <CustId>
        <SPName>AMSServices.com</SPName>
        <CustPermId />
        <CustLoginId><EMAIL></CustLoginId>
      </CustId>
      <CustPswd>
        <EncryptionTypeCd>NONE</EncryptionTypeCd>
        <Pswd />
      </CustPswd>
    </SignonPswd>
    <ClientDt>9/4/2021 2:13 PM</ClientDt>
    <CustLangPref>en-US</CustLangPref>
    <ClientApp>
      <Org>AMS Services</Org>
      <Name>Transit</Name>
      <Version>V2.5.5</Version>
    </ClientApp>
    <ProxyClient>
      <Org>BCFTech</Org>
      <Name>TransmitXML</Name>
      <Version>V1</Version>
    </ProxyClient>
  </SignonRq>
  <InsuranceSvcRq>
    <RqUID>78759a15-4563-43e5-89bd-fedf1e5c2774</RqUID>
    <PersUmbrellaPolicyQuoteInqRq>
      <RqUID>63770D04-E178-4BC4-872B-1282F830A1F1</RqUID>
      <TransactionRequestDt>2021-08-30T09:23:27</TransactionRequestDt>
      <TransactionEffectiveDt>2020-12-01</TransactionEffectiveDt>
      <CurCd>USD</CurCd>
      <BroadLOBCd>P</BroadLOBCd>
      <Producer>
        <ProducerInfo>
          <ContractNumber />
          <ProducerRoleCd>Agency</ProducerRoleCd>
        </ProducerInfo>
      </Producer>
      <InsuredOrPrincipal>
        <GeneralPartyInfo>
          <NameInfo>
            <PersonName>
              <Surname>ANDREWS</Surname>
              <GivenName>MATTHEW</GivenName>
            </PersonName>
            <LegalEntityCd>IN</LegalEntityCd>
          </NameInfo>
          <Addr>
            <AddrTypeCd>MailingAddress</AddrTypeCd>
            <Addr1>9728 105TH ST</Addr1>
            <City>OZONE PARK</City>
            <StateProvCd>NY</StateProvCd>
            <PostalCode>11416</PostalCode>
          </Addr>
        </GeneralPartyInfo>
        <InsuredOrPrincipalInfo>
          <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
          <PersonInfo>
            <OccupationDesc>OTHER</OccupationDesc>
          </PersonInfo>
        </InsuredOrPrincipalInfo>
      </InsuredOrPrincipal>
      <PersPolicy id="PolicyLevel">
        <PolicyNumber>**********</PolicyNumber>
        <LOBCd>UMBRP</LOBCd>
        <NAICCd>19402</NAICCd>
        <ContractTerm>
          <EffectiveDt></EffectiveDt>
          <ExpirationDt>2021-12-01</ExpirationDt>
          <DurationPeriod>
            <NumUnits>12</NumUnits>
            <UnitMeasurementCd>MON</UnitMeasurementCd>
          </DurationPeriod>
        </ContractTerm>
        <BillingAccountNumber>*************</BillingAccountNumber>
        <BillingMethodCd>CAB</BillingMethodCd>
        <CurrentTermAmt>
          <Amt></Amt>
        </CurrentTermAmt>
        <PayorCd>IN</PayorCd>
        <RenewalTerm>
          <DurationPeriod>
            <NumUnits>12</NumUnits>
            <UnitMeasurementCd>MON</UnitMeasurementCd>
          </DurationPeriod>
        </RenewalTerm>
        <Form>
          <FormNumber>PCGCLNO</FormNumber>
          <FormName>Claims Notice</FormName>
          <IterationNumber>001</IterationNumber>
        </Form>
        <Form>
          <FormNumber>PELAENY</FormNumber>
          <FormName>Amendatory Endorsement-New York</FormName>
          <IterationNumber>001</IterationNumber>
        </Form>
        <Form>
          <FormNumber>PELDEC1</FormNumber>
          <FormName>Personal Excess Liability Declarations Page</FormName>
          <IterationNumber>001</IterationNumber>
        </Form>
        <Form>
          <FormNumber>PELSCHED</FormNumber>
          <FormName>Sched of Underlying Ins</FormName>
          <IterationNumber>001</IterationNumber>
        </Form>
        <Form>
          <FormNumber>PELOFAC</FormNumber>
          <FormName>US Treasury Dept Notice</FormName>
          <IterationNumber>001</IterationNumber>
        </Form>
        <Form>
          <FormNumber>PCGDESNOTN</FormNumber>
          <FormName>IMPORTANT NOTICE FOR SENIOR POLICYHOLDERS</FormName>
          <IterationNumber>001</IterationNumber>
        </Form>
        <Form>
          <FormNumber>PELEPLI</FormNumber>
          <FormName>Employment Practices Liability Exclusion</FormName>
          <IterationNumber>001</IterationNumber>
        </Form>
        <Form>
          <FormNumber>PCGPRIVNOT</FormNumber>
          <FormName>Privacy and Data Security Notice</FormName>
          <IterationNumber>001</IterationNumber>
        </Form>
        <Form>
          <FormNumber>91222PCG</FormNumber>
          <FormName>Policyholder Notice-Producer Compensation</FormName>
          <IterationNumber>001</IterationNumber>
        </Form>
        <Form>
          <FormNumber>PCGFEENOTI</FormNumber>
          <FormName>Installment Fees</FormName>
          <IterationNumber>001</IterationNumber>
        </Form>
        <PersApplicationInfo>
          <InsuredOrPrincipal />
        </PersApplicationInfo>
        <ControllingStateProvCd>NY</ControllingStateProvCd>
      </PersPolicy>
      <Location id="L1">
        <ItemIdInfo>
          <AgencyId>0001</AgencyId>
        </ItemIdInfo>
        <Addr>
          <Addr1></Addr1>
          <City>OZONE PARK</City>
          <StateProvCd>NY</StateProvCd>
          <PostalCode>11416</PostalCode>
        </Addr>
      </Location>
      <PersUmbrellaLineBusiness>
        <LOBCd>UMBRP</LOBCd>
        <NAICCd>19402</NAICCd>
        <CurrentTermAmt>
          <Amt>1161</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>1161</Amt>
        </NetChangeAmt>
        <RateEffectiveDt>2020-12-01</RateEffectiveDt>
        <Coverage btId="5a0992db-74a4-4681-88da-1db7d27c294d">
          <CoverageCd>PCL</CoverageCd>
          <CoverageDesc>LocationRef: L1</CoverageDesc>
          <Limit>
            <FormatInteger>5000000</FormatInteger>
          </Limit>
          <Limit>
            <FormatInteger>5000000</FormatInteger>
          </Limit>
        </Coverage>
        <Coverage btId="9dc581ad-546e-4e94-aefc-c11e6ebf4b26">
          <CoverageCd>UMCSL</CoverageCd>
          <CoverageDesc>LocationRef: L1</CoverageDesc>
          <Limit>
            <FormatInteger>1000000</FormatInteger>
            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
          </Limit>
          <Limit>
            <FormatInteger>1000000</FormatInteger>
          </Limit>
          <CurrentTermAmt>
            <Amt>189</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage btId="8cc5e076-e9be-4413-91af-ee7364710ce0">
          <CoverageCd>UNDUM</CoverageCd>
          <CoverageDesc>LocationRef: L1</CoverageDesc>
          <Limit>
            <FormatInteger>1000000</FormatInteger>
            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
          </Limit>
          <Limit>
            <FormatInteger>1000000</FormatInteger>
          </Limit>
        </Coverage>
        <Coverage btId="2a4150f9-40ee-4f30-9ad3-ef552254d8a0">
          <CoverageCd>AUTO</CoverageCd>
          <CoverageDesc>LocationRef: L1</CoverageDesc>
          <CurrentTermAmt>
            <Amt>765</Amt>
          </CurrentTermAmt>
        </Coverage>
        <Coverage btId="a56a57b7-8386-41a5-be81-7af0211e49bc">
          <CoverageCd>RESID</CoverageCd>
          <CoverageDesc>LocationRef: L1</CoverageDesc>
          <CurrentTermAmt>
            <Amt>207</Amt>
          </CurrentTermAmt>
        </Coverage>
        <PersUmbrella>
          <TerritoryCd>7</TerritoryCd>
        </PersUmbrella>
        <RealEstate LocationRef="L1">
          <OccupancyTypeCd>OWNER</OccupancyTypeCd>
        </RealEstate>
        <BasicVehInfo LocationRef="L1" id="V1">
          <ItemIdInfo>
            <InsurerId>0001</InsurerId>
          </ItemIdInfo>
          <Manufacturer>Mazda</Manufacturer>
          <Model>CX-9</Model>
          <ModelYear>2019</ModelYear>
          <VehBodyTypeCd>PP</VehBodyTypeCd>
          <VehTypeCd>PP</VehTypeCd>
        </BasicVehInfo>
        <BasicDriverInfo id="D1">
          <ItemIdInfo>
            <InsurerId>0001</InsurerId>
          </ItemIdInfo>
          <GeneralPartyInfo>
            <NameInfo>
              <PersonName>
                <Surname>Andrews</Surname>
                <GivenName>Matthew</GivenName>
              </PersonName>
            </NameInfo>
          </GeneralPartyInfo>
          <DriverInfo>
            <PersonInfo>
              <BirthDt>1983-04-17</BirthDt>
            </PersonInfo>
            <DriversLicense>
              <DriversLicenseNumber>649445836</DriversLicenseNumber>
              <StateProvCd>NY</StateProvCd>
            </DriversLicense>
            <License>
              <LicenseTypeCd>Driver</LicenseTypeCd>
              <LicensePermitNumber>649445836</LicensePermitNumber>
              <StateProvCd>NY</StateProvCd>
            </License>
          </DriverInfo>
        </BasicDriverInfo>
        <BasicDriverInfo id="D2">
          <ItemIdInfo>
            <InsurerId>0002</InsurerId>
          </ItemIdInfo>
          <GeneralPartyInfo>
            <NameInfo>
              <PersonName>
                <Surname>Andrews</Surname>
                <GivenName>John</GivenName>
              </PersonName>
            </NameInfo>
          </GeneralPartyInfo>
          <DriverInfo>
            <PersonInfo>
              <BirthDt>1966-06-28</BirthDt>
            </PersonInfo>
            <DriversLicense>
              <DriversLicenseNumber>123456789</DriversLicenseNumber>
              <StateProvCd>NY</StateProvCd>
            </DriversLicense>
            <License>
              <LicenseTypeCd>Driver</LicenseTypeCd>
              <LicensePermitNumber>123456789</LicensePermitNumber>
              <StateProvCd>NY</StateProvCd>
            </License>
          </DriverInfo>
        </BasicDriverInfo>
        <BasicDriverInfo id="D3">
          <ItemIdInfo>
            <InsurerId>0003</InsurerId>
          </ItemIdInfo>
          <GeneralPartyInfo>
            <NameInfo>
              <PersonName>
                <Surname>Andrews</Surname>
                <GivenName>Larry</GivenName>
              </PersonName>
            </NameInfo>
          </GeneralPartyInfo>
          <DriverInfo>
            <PersonInfo>
              <BirthDt>1991-11-26</BirthDt>
            </PersonInfo>
            <DriversLicense>
              <DriversLicenseNumber>987654321</DriversLicenseNumber>
              <StateProvCd>NY</StateProvCd>
            </DriversLicense>
            <License>
              <LicenseTypeCd>Driver</LicenseTypeCd>
              <LicensePermitNumber>987654321</LicensePermitNumber>
              <StateProvCd>NY</StateProvCd>
            </License>
          </DriverInfo>
        </BasicDriverInfo>
      </PersUmbrellaLineBusiness>
    </PersUmbrellaPolicyQuoteInqRq>
  </InsuranceSvcRq>
</ACORD>
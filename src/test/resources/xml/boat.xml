<?xml-stylesheet type="text/xsl" href='http://ecdev-tac.safeco.com/BookTransfer/TACML/WebAPI/scripts/xslt/BoatXMLView.xslt'?>
<ACORD>
    <SignonRq>
        <ClientDt>2016-04-27</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>BookSmart</Org>
            <Name>AgencyPort</Name>
        </ClientApp>
        <SignonTransport>
            <SignonRoleCd>Agent</SignonRoleCd>
            <CustId>
                <SPName>com.agencyport</SPName>
                <CustLoginId>default</CustLoginId>
            </CustId>
        </SignonTransport>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>6F6ECD35BA124E68A42C4FF485D8EB1A</RqUID>
        <WatercraftPolicyQuoteInqRq>
            <RqUID>F0CFCC12F97540E6932043BDFAA96AED</RqUID>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>318429</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>CESNRA1C-3</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Ralph Cesnick</CommercialName>
                        </CommlName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>3425 N Dundale Rd</Addr1>
                        <City>Spruce</City>
                        <StateProvCd>MI</StateProvCd>
                        <PostalCode>48762-9526</PostalCode>
                        <County>Alcona</County>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>************</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Business</CommunicationUseCd>
                            <PhoneNumber/>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>

            </InsuredOrPrincipal>
            <PersPolicy>
                <PolicyNumber>K2503668</PolicyNumber>
                <CompanyProductCd>SAF</CompanyProductCd>
                <LOBCd>BOAT</LOBCd>
                <NAICCd>24740</NAICCd>
                <ContractTerm>
                    <EffectiveDt>2015-11-29</EffectiveDt>
                    <ExpirationDt>2016-11-29</ExpirationDt>
                </ContractTerm>
                <BillingAccountNumber>7211-2503668</BillingAccountNumber>
                <BillingMethodCd>CPB</BillingMethodCd>
                <PayorCd>IN</PayorCd>
                <NumLossesYrs>3</NumLossesYrs>
                <OtherOrPriorPolicy>
                    <PolicyCd>OIC</PolicyCd>
                    <PolicyNumber/>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <PaymentPlanCd/>
                </PaymentOption>
                <PersApplicationInfo/>
                <!--  <CurrentTermAmt>
                     <Amt>8500.00</Amt>
                 </CurrentTermAmt> -->
            </PersPolicy>
            <Location id="LOC1">
                <ItemIdInfo>
                    <AgencyId>1</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>SPRUCE MI 48762-9526</Addr1>
                </Addr>
            </Location>
            <WatercraftLineBusiness>
                <LOBCd>BOAT</LOBCd>
                <Watercraft StorageLocationRef="LOC1" id="BOAT1">
                    <ItemIdInfo>
                        <InsurerId>10</InsurerId>
                    </ItemIdInfo>
                    <WaterUnitTypeCd>2</WaterUnitTypeCd>
                    <ItemDefinition>
                        <Manufacturer>LUND BOATS 1700 ANGLER</Manufacturer>
                        <SerialIdNumber>LUNBR0041001</SerialIdNumber>
                        <ModelYear>2001</ModelYear>
                        <Registration>
                            <RegistrationTypeCd>VesselRegistration</RegistrationTypeCd>
                            <RegistrationId/>
                        </Registration>
                    </ItemDefinition>
                    <CostNewAmt>
                        <Amt>0</Amt>
                    </CostNewAmt>
                    <PurchaseDt>2005-06-01</PurchaseDt>
                    <PresentValueAmt>
                        <Amt>4000</Amt>
                    </PresentValueAmt>
                    <Length>
                        <NumUnits>16</NumUnits>
                        <UnitMeasurementCd>FOT</UnitMeasurementCd>
                    </Length>
                    <Speed>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>HM</UnitMeasurementCd>
                    </Speed>
                    <TerritoryCd/>
                    <PropulsionTypeCd>OUT</PropulsionTypeCd>
                    <HullMaterialTypeCd>MT</HullMaterialTypeCd>
                    <NameBoat/>
                    <LayUpPeriod>
                        <LayUpDuration/>
                    </LayUpPeriod>
                    <Coverage>
                        <CoverageCd>TRLR</CoverageCd>
                        <Limit>
                            <FormatInteger>700</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>100</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>6.9</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WATER</CoverageCd>
                        <Limit>
                            <FormatInteger>0</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>0</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>10.8</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>0</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>1</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WUCSL</CoverageCd>
                        <Limit>
                            <FormatInteger>0</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>0</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>0</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>EQUIP</CoverageCd>
                        <CoverageDesc>Equipment</CoverageDesc>
                        <Limit>
                            <FormatInteger>400</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>0</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ADEQP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>7.8</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <!-- <FullTermAmt>1560.23</FullTermAmt> -->
                </Watercraft>
                <Watercraft StorageLocationRef="LOC1" id="BOAT2">
                    <ItemIdInfo>
                        <InsurerId>10</InsurerId>
                    </ItemIdInfo>
                    <WaterUnitTypeCd>2</WaterUnitTypeCd>
                    <ItemDefinition>
                        <Manufacturer>LUND BOATS 1700 ANGLER</Manufacturer>
                        <SerialIdNumber>LUNBR0041001</SerialIdNumber>
                        <ModelYear>2001</ModelYear>
                        <Registration>
                            <RegistrationTypeCd>VesselRegistration</RegistrationTypeCd>
                            <RegistrationId/>
                        </Registration>
                    </ItemDefinition>
                    <CostNewAmt>
                        <Amt>0</Amt>
                    </CostNewAmt>
                    <PurchaseDt>2005-06-01</PurchaseDt>
                    <PresentValueAmt>
                        <Amt>4000</Amt>
                    </PresentValueAmt>
                    <Length>
                        <NumUnits>16</NumUnits>
                        <UnitMeasurementCd>FOT</UnitMeasurementCd>
                    </Length>
                    <Speed>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>HM</UnitMeasurementCd>
                    </Speed>
                    <TerritoryCd/>
                    <PropulsionTypeCd>OUT</PropulsionTypeCd>
                    <HullMaterialTypeCd>MT</HullMaterialTypeCd>
                    <NameBoat/>
                    <LayUpPeriod>
                        <LayUpDuration/>
                    </LayUpPeriod>
                    <Coverage>
                        <CoverageCd>TRLR</CoverageCd>
                        <Limit>
                            <FormatInteger>700</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>100</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>10.9</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WATER</CoverageCd>
                        <Limit>
                            <FormatInteger>0</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>0</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>10.8</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>0</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>1</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WUCSL</CoverageCd>
                        <Limit>
                            <FormatInteger>0</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>0</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>0</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>EQUIP</CoverageCd>
                        <CoverageDesc>Equipment</CoverageDesc>
                        <Limit>
                            <FormatInteger>400</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>5</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ADEQP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>7.8</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <!-- <FullTermAmt>1560.23</FullTermAmt> -->
                </Watercraft>
                <Watercraft StorageLocationRef="LOC1" id="BOAT3">
                    <ItemIdInfo>
                        <InsurerId>10</InsurerId>
                    </ItemIdInfo>
                    <WaterUnitTypeCd>2</WaterUnitTypeCd>
                    <ItemDefinition>
                        <Manufacturer>LUND BOATS 1700 ANGLER</Manufacturer>
                        <SerialIdNumber>LUNBR0041001</SerialIdNumber>
                        <ModelYear>2001</ModelYear>
                        <Registration>
                            <RegistrationTypeCd>VesselRegistration</RegistrationTypeCd>
                            <RegistrationId/>
                        </Registration>
                    </ItemDefinition>
                    <CostNewAmt>
                        <Amt>0</Amt>
                    </CostNewAmt>
                    <PurchaseDt>2005-06-01</PurchaseDt>
                    <PresentValueAmt>
                        <Amt>4000</Amt>
                    </PresentValueAmt>
                    <Length>
                        <NumUnits>16</NumUnits>
                        <UnitMeasurementCd>FOT</UnitMeasurementCd>
                    </Length>
                    <Speed>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>HM</UnitMeasurementCd>
                    </Speed>
                    <TerritoryCd/>
                    <PropulsionTypeCd>OUT</PropulsionTypeCd>
                    <HullMaterialTypeCd>MT</HullMaterialTypeCd>
                    <NameBoat/>
                    <LayUpPeriod>
                        <LayUpDuration/>
                    </LayUpPeriod>
                    <Coverage>
                        <CoverageCd>TRLR</CoverageCd>
                        <Limit>
                            <FormatInteger>700</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>100</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>10.9</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WATER</CoverageCd>
                        <Limit>
                            <FormatInteger>0</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>0</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>10.8</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>0</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>1</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WUCSL</CoverageCd>
                        <Limit>
                            <FormatInteger>0</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>0</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>0</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>EQUIP</CoverageCd>
                        <CoverageDesc>Equipment</CoverageDesc>
                        <Limit>
                            <FormatInteger>400</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>5</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ADEQP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>7.8</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <FullTermAmt>1560.23</FullTermAmt>
                </Watercraft>
                <WatercraftAccessory id="TRLR11" WatercraftRef="BOAT1">
                    <ItemIdInfo>
                        <AgencyId>14</AgencyId>
                    </ItemIdInfo>
                    <WaterUnitTypeCd>7</WaterUnitTypeCd>
                    <ItemDefinition>
                        <Manufacturer>SHORELAND'R</Manufacturer>
                        <SerialIdNumber>1MDAPLP131A169</SerialIdNumber>
                        <ModelYear>2001</ModelYear>
                    </ItemDefinition>
                    <CostNewAmt>
                        <Amt>0</Amt>
                    </CostNewAmt>
                    <WeightCapacity>
                        <NumUnits>0</NumUnits>
                    </WeightCapacity>
                    <NumTrailerAxles>0</NumTrailerAxles>
                </WatercraftAccessory>
                <PersDriver id="DRV1">
                    <ItemIdInfo>
                        <AgencyId>1</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>CESNICK</Surname>
                                <GivenName>RALPH</GivenName>
                                <OtherGivenName/>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1950-06-29</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>C252730108507</DriversLicenseNumber>
                            <StateProvCd>MI</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicensePermitNumber>C252730108507</LicensePermitNumber>
                            <StateProvCd>MI</StateProvCd>
                        </License>
                    </DriverInfo>
                </PersDriver>
                <DriverExperience DriverRef="DRV1">
                    <Manufacturer/>
                </DriverExperience>
            </WatercraftLineBusiness>
            <RemarkText>* Additional remarks are in notepad #006</RemarkText>
        </WatercraftPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
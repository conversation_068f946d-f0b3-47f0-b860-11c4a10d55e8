<ACORD>
    <BookTransferMeta>
        <NBDRelationship>
            testnbd
        </NBDRelationship>
    </BookTransferMeta>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName>com.agencyport</SPName>
                <CustLoginId>default</CustLoginId>
            </CustId>
            <CustPswd>
                <EncryptionTypeCd>NONE</EncryptionTypeCd>
                <Pswd>default</Pswd>
            </CustPswd>
        </SignonPswd>
        <ClientDt>2014-11-06T14:12:58</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>BookSmart</Org>
            <Name>AgencyPort</Name>
            <Version>*******</Version>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>65E83FD2-F139-02BE-5787-0C7D0A061CCC</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <RqUID>56C2735B-9CC8-C74C-F84E-009597939E62</RqUID>
            <TransactionRequestDt id="A6ACA1169A53606E0B3286D81BA6D5B27A">2014-11-06</TransactionRequestDt>
            <TransactionEffectiveDt id="A76AB420C69B02DD7F8A8F521EAAA27F8A">2014-10-18</TransactionEffectiveDt>
            <CurCd id="A5A6DA826B15648C692E4FC9956AC2B51A">USD</CurCd>
            <com.safeco_TransactionType id="A0CA380DE001CE21C765212B5828A11B5A">QuoteRQ</com.safeco_TransactionType>
            <Producer id="A5562809F4FDEEB74547BC492DE640700A">
                <GeneralPartyInfo id="A3BE948A169B5A13D7ACFB105748531BEA">
                    <NameInfo id="AA8EA5E87EDBEDA38FD7BDD5A6D838820A">
                        <CommlName id="A177B2FFE2DCD58EB9A50E7FC2F01F89AA">
                            <CommercialName id="A432A8717F22752E42140848722D77049A">Ridgebrook Insurance Services,
                                Inc.
                            </CommercialName>
                        </CommlName>
                    </NameInfo>
                    <Addr id="AF1548221A06F64C1C91B2D5031CC0F5FA">
                        <Addr1 id="A44147EB0CAE4CF9571681E88E8364E80A">909 Ridgebrook Road</Addr1>
                        <Addr2 id="AADA37EE1889CDD8A4EA2E809A0B3DB95A">Suite 116</Addr2>
                        <City id="AA5EF7E2F1E30BE8C5739DEB69DE617CAA">Sparks</City>
                        <StateProvCd id="A7462FDDF93F3E6A00A6A46DA3F5BAABFA">MD</StateProvCd>
                        <PostalCode id="A3E54246BA9604B755EAE9EF76918A757A">21152</PostalCode>
                    </Addr>
                    <Communications id="A53E8056EB2AEA2A00AFAA556E06E44BDA">
                        <PhoneInfo id="AC810858CBFB41629D1FB6F6012F8F9B4A">
                            <PhoneTypeCd id="A8512B46BD6BE4867BE843E684A5B4548A">Phone</PhoneTypeCd>
                            <CommunicationUseCd id="ADFA2EAC48D4AA3FDBB48E5D7B2238B55A">Business</CommunicationUseCd>
                            <PhoneNumber id="AEC8D51930E7C1033338FBEEE18B7C18CA">******-5953100</PhoneNumber>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>
                <ProducerInfo id="A0F679E4EED3BE9E1578F6AB313852475A">
                    <ContractNumber id="A25B9032203BC6225BE63EF2621398678A">552129</ContractNumber>
                    <ProducerSubCode id="AB00D111FECB35E6C90877B35ED74229AA">Book Transfer</ProducerSubCode>
                    <ProducerRoleCd id="AF089C26709AEF89073515613A99F15A8A">Agency</ProducerRoleCd>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal id="AACA700923991E35BAB0217E340858F51A">
                <ItemIdInfo id="A3DBEB72D0E3E980CA08D0F60C4B1D021A">
                    <AgencyId id="A9A11BB5914FF467B4C076D521935B513A">00010829</AgencyId>
                    <InsurerId id="A2F33C735868E679743FFBDDC43B538D1A">5023440</InsurerId>
                </ItemIdInfo>
                <GeneralPartyInfo id="A3C494719F64D5C74A187F180774B9F36A">
                    <NameInfo id="AB0F44AC80AB2CD507B291EDE7662ADE8A">
                        <LegalEntityCd id="A37C3D58B2A217294C0F59B1AB9258CF3A">IN</LegalEntityCd>
                        <TaxIdentity id="A47BCFEBAC21E118FFB61E5B939FE0EB2A">
                            <TaxIdTypeCd id="AA4F21D8574A0B3CCFBC1FBFA052D8E9CA">SSN</TaxIdTypeCd>
                            <TaxId id="A15665E5ED55846141E4C5B7DCBE5F0B7A">***********</TaxId>
                        </TaxIdentity>
                    </NameInfo>
                    <Addr id="AFA7EEEE04D091336576AE32170D53B84A">
                        <AddrTypeCd id="A572E1B779F39F40D6BC5C2549C275E48A">MailingAddress</AddrTypeCd>
                        <Addr1 id="A6F1A4CB138B754877D3E008286C71912A">211 MARION AVE</Addr1>
                        <Addr2 id="A1061C003E18D55E55C3DA125124AC433A">211 MARION AVE</Addr2>
                        <City id="AAA37D08D54C5CC538DCC7610F239BD77A">BALTIMORE</City>
                        <StateProvCd id="AE167F68FA8C2BD376068ACAF264D54BDA">MD</StateProvCd>
                        <PostalCode id="A7B5D41D58FFFD6546475580A7535F993A">21236-4209</PostalCode>
                        <County id="AFC47A84E67AA0160C7F5DD4E4C0E5552A">BALTI</County>
                    </Addr>
                    <Communications id="ACB4200360B7A6330A76DA3634352B96DA">
                        <PhoneInfo id="A79ABA6E758F85AF5BAB3FC6877679C7AA">
                            <PhoneTypeCd id="A943071990D4AFC0F33D7A504193B27F1A">Phone</PhoneTypeCd>
                            <CommunicationUseCd id="A97102AAF3898DB3363394156ADE2D313A">Home</CommunicationUseCd>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo id="AB8EC84F91591A7DBCBC0A4FA347CCDF8A">
                    <InsuredOrPrincipalRoleCd id="AC84A7EE2A849F603119FAC12E65161A7A">Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo id="A466331AD436C8791A459EB631C980082A">
                        <BirthDt id="A98EA311D5CA9B2AD999F24EB2A7056AAA">1966-05-14</BirthDt>
                        <MaritalStatusCd id="A7DEA6D0B1E72F40E3FE6311099CD1954A">M</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal id="ABC4FB193555135D16B587C6A94854B42A">
                <GeneralPartyInfo id="A339B3BF012CB3AC378BCB2E2FCD39654A">
                    <NameInfo id="ACB29A3E4EB07385FA5A8256A7DB411A7A">
                        <TaxIdentity id="A81D81F07643C36A6E0B0F125ACF0977EA">
                            <TaxIdTypeCd id="A538424B36C7DD926F47769A73C3F07A0A">SSN</TaxIdTypeCd>
                            <TaxId id="A8F033BE647BFC99456A6BD45DF456BD4A">***********</TaxId>
                        </TaxIdentity>
                    </NameInfo>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo id="A2FAFB8A85A20B563D70B00A583054CBEA">
                    <InsuredOrPrincipalRoleCd id="A72C61E8B607BDBB6F510D7CB2A4A7D70A">Coinsured
                    </InsuredOrPrincipalRoleCd>
                    <PersonInfo id="A3C4B972D568F1E3DF5A7E478988745C9A">
                        <BirthDt id="A69C3E315EFF5CB35613861F3A0384DF1A">1967-10-05</BirthDt>
                        <MaritalStatusCd id="A2F136DBA4A31B7768F676AAA5C19801CA">M</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal id="A3A5478FA4446A37864D50E0E3A7F9973A">
                <GeneralPartyInfo id="A388BCE3CFA0B8E4C081A1CD177DAF449A">
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo id="ABAABD57C8C3D5832FA66BFF8263CC86DA">
                    <InsuredOrPrincipalRoleCd id="AA29D47417A877E8E09DAC1A61E7FFC76A">DEC</InsuredOrPrincipalRoleCd>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy id="A4C2C1C5992B4480E8F54B04904051A90A">
                <PolicyNumber id="AEE00E7362E5085C5998059B53BEE2A48A">ad6c5062-8cde-4c7a-bd93-7eb8ee876b69
                </PolicyNumber>
                <LOBCd id="A292ADBF8869B05FE97E26D8D7AA9399DA">AUTOP</LOBCd>
                <ControllingStateProvCd id="A3455C7A20711183C82F45C586F70A3E3A">MD</ControllingStateProvCd>
                <ContractTerm id="A7B7393B17449E32AC44BA7936A6BCEE8A">
                    <DurationPeriod id="AEFD6A9B411871D5E6A1AB29C09340EBBA">
                        <NumUnits id="AE4A810895E2DD67E6CF3C6F8278CFE84A">12</NumUnits>
                    </DurationPeriod>
                </ContractTerm>
                <BillingAccountNumber>7211-2503668</BillingAccountNumber>
                <BillingMethodCd id="A8B4ED5735F1F71D5F38FA71657DC0223A">CPB</BillingMethodCd>
                <GroupId id="AB7A47DB06C5D396F73992C89AE7E0190A">Batch 3</GroupId>
                <Form id="A57EFFCF758C0689721D7A39974E3E9A2A">
                    <FormNumber id="**********************************">AP-MD</FormNumber>
                    <EditionDt id="A15AE9F61948DC88B311FA7D473802E7BA">2013-10-01</EditionDt>
                    <IterationNumber id="AF3CDD18618D9022FEC6930B86AD86527A">1</IterationNumber>
                </Form>
                <Form id="A386A64728DB1BDC73A9BF5FEC85250A6A">
                    <FormNumber id="**********************************">FORM SA</FormNumber>
                    <EditionDt id="A895F63967B16999D872FC08C83117E00A">2012-11-01</EditionDt>
                    <IterationNumber id="A327C86657B06263BBF38C5DE3C506F29A">2</IterationNumber>
                </Form>
                <Form id="**********************************">
                    <FormNumber id="A8E086AF36F2927A59F7CB93B6E62AB2BA">UF4806</FormNumber>
                    <EditionDt id="AE9CB536D18C32CB9E3FEF6B5D6FD6C4AA">2013-10-01</EditionDt>
                    <IterationNumber id="A57E5FF6EC2FECC445AF876ECBACAB721A">3</IterationNumber>
                </Form>
                <Form id="AF0A33E1BD229D631DD21C30BE87B102CA">
                    <FormNumber id="AE4696F8780FD7DC25C52F96ED4692672A">UF4839</FormNumber>
                    <EditionDt id="A4BAA86DE4F42DEC4B6DE53933A964422A">2008-04-01</EditionDt>
                    <IterationNumber id="AA0B13D1AB86707F472AFFCCC0F85ADB3A">4</IterationNumber>
                </Form>
                <Form id="A73E1FFC15031A9E1CBB11DCAFEE10274A">
                    <FormNumber id="A16443FFEF4B26CEAD64C1015CA708B05A">AFMU01</FormNumber>
                    <EditionDt id="AEF0227E900FFCB25AB0420724F454E5EA">2007-04-01</EditionDt>
                    <IterationNumber id="A317361E09D59836052169BF70A45A351A">5</IterationNumber>
                </Form>
                <Form id="A21040A14649ABC8D538FB64A18A31BF6A">
                    <FormNumber id="A723897354B98DB8A0E7CE276A912F1C8A">UF6850</FormNumber>
                    <EditionDt id="A61E5A1E05CC9D582B77EA485A4FB5FA0A">2013-10-01</EditionDt>
                    <IterationNumber id="AB7B1607B2F927535E6FF203BF0E72C93A">6</IterationNumber>
                </Form>
                <Form id="A84C3A9203AE01B41F1FA3F39330C110FA">
                    <FormNumber id="A5D3389983B384CFD2DBAADA0B2A11AAAA">AFMA02</FormNumber>
                    <EditionDt id="A5D51D45B386A0269B6FB53BDEF24C5B5A">2013-10-01</EditionDt>
                    <IterationNumber id="ACEC46DE6A2C56730A2921C681CD295EEA">7</IterationNumber>
                </Form>
                <Form id="A9DC7A0A671A642AE233D5F8A86F8024DA">
                    <FormNumber id="AC0D5089ED41584A5FA191935D459BAECA">AFMP01</FormNumber>
                    <EditionDt id="A0223F447DFBAF2D4D4AE6DA04D810A51A">2007-04-01</EditionDt>
                    <IterationNumber id="A9F49C716FA02D9439248E617EF57D2CCA">8</IterationNumber>
                </Form>
                <OtherOrPriorPolicy id="AEBF7FED9BC883CFE4A1F20926B40F4F1A">
                    <PolicyCd id="A99017C40FA9E70C983DF1A8FBD50C1C6A">Other</PolicyCd>
                    <LOBCd id="A31976651B132C837686AC8C8B48F338CA"/>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy id="AEB9B1AFC0252A97EC83E93B6507D35A7A">
                    <PolicyCd id="A33B7BB6BC81AF1F42505E03ECEE92961A">Other</PolicyCd>
                    <LOBCd id="A4684675BA641B5285846F2259A1CEC71A"/>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy id="A61BA49330FB5286A8C76F714FC73EAE1A">
                    <PolicyCd id="A2B009BA1B0633A833B31ACD33714C6CFA">Prior</PolicyCd>
                    <LOBCd id="AC6869CDFF3331B90C6785F126E26977EA">AUTOP</LOBCd>
                    <InsurerName id="ACCCC8AF1A83F7C16F1D7D2D1A0F71C66A">Geico</InsurerName>
                    <ContractTerm id="A423F20852C0E51C10DAEDEC99FFD8579A">
                        <ExpirationDt id="A5719802268EF7B912AC4A7D5A9E32300A">2015-03-16</ExpirationDt>
                    </ContractTerm>
                    <LengthTimeWithPreviousInsurer id="A3A0B43B732FF72545D8DA62BE2477E5EA">
                        <NumUnits id="AD48B72E12993179901108E00D7087242A">36</NumUnits>
                    </LengthTimeWithPreviousInsurer>
                    <Coverage id="A3926C73B82A01D9BE8CDDB5330211D2BA">
                        <CoverageCd id="AB75A4CEB218EF4532BE93501A646DDDEA">BI</CoverageCd>
                        <Limit id="AB3E693567D9450B84AA03FFCD3849182A">
                            <FormatInteger id="ABC56364EFF86908B27A67C5218C4FE83A"/>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy id="A97024DA7F6E6B4A44B00C71205B02BCFA">
                    <PolicyCd id="AFB754446E4B76610F94A56830CDC72BBA">Other</PolicyCd>
                    <LOBCd id="AF78882EAF2BC0EB4863B9D4C4DDF7971A"/>
                </OtherOrPriorPolicy>
                <PaymentOption id="A9FF3D57E028DD4F1340DE3D655EBBB8BA">
                    <PaymentPlanCd id="AD615DDD8023890247B5846960166CE95A">QT</PaymentPlanCd>
                    <DayMonthDue id="A66484CC148ED205E6D747DC427E06E5DA">0</DayMonthDue>
                    <NumPayments id="A63D93B435BF0C17C12EC6E9FE7792F1DA">1</NumPayments>
                </PaymentOption>
                <CreditScoreInfo id="A3ADB20077AC8E081B8EBDC685010B137A">
                    <CreditScore id="A9BCBAE1A3CBBDA4CAC1F61F839849433A"/>
                    <CreditScoreDt id="A3A691F00078AD515411C0A2178B7F260A"/>
                    <CSPolicyTypeCd id="AEDEAC9BD37DF43C96EDC1DB68FF3005BA">IBS</CSPolicyTypeCd>
                </CreditScoreInfo>
                <QuestionAnswer id="A50912D4F60C5E9076D19D667BFDBA99EA">
                    <QuestionCd id="AE9408441EBC2022DABC04FD7CF3055C3A">Current_Insurance_Value</QuestionCd>
                    <YesNoCd id="AA8A0752E51223549C0D1B0B2E0F78442A"/>
                    <Explanation id="A145C3ADFFE307B326D3C8E69A4C26937A">CI</Explanation>
                </QuestionAnswer>
                <PersApplicationInfo id="A0E8FFA53B5CB77CEC5CC1B150592AAA6A">
                    <NumVehsInHousehold id="AC7BEDE22374FFE54A631578F4A79C7CBA">1</NumVehsInHousehold>
                    <ResidenceTypeCd id="AC8390841CC1AC46593AEC58671C52299A">DW</ResidenceTypeCd>
                </PersApplicationInfo>
                <DriverVeh DriverRef="A9EAD80E1CCC255C2510B7F48CB4E8B50A" VehRef="A8933BE722C9809F2C8B9631AF20D272AA"
                           id="AD4D6C9A7742465F0941D2A7BF10658FBA">
                    <UsePct id="AFC57AA0CB6FA7EC314BE73AC1E22358EA">0</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="**********************************" VehRef="A8933BE722C9809F2C8B9631AF20D272AA"
                           id="A3E3B2E28CA0B7C79E7614EBAAD750FA3A">
                    <UsePct id="A47AF9DA9995ADEB622C071D7107950E1A">0</UsePct>
                </DriverVeh>
                <QuoteInfo id="A88FACD98E7EE0EA2B5DCD9B4CDC28F5CA">
                    <CompanysQuoteNumber>ad6c5062-8cde-4c7a-bd93-7eb8ee876b69</CompanysQuoteNumber>
                    <com.safeco_CompanyClientID>1391febe-1e9b-434e-93a6-dd7f4dd38992</com.safeco_CompanyClientID>
                    <com.safeco_CompanyURLAutoLaunch id="A003">1</com.safeco_CompanyURLAutoLaunch>
                    <com.safeco_QuoteRq_PremiumTotal id="A012">2776.00</com.safeco_QuoteRq_PremiumTotal>
                    <com.safeco_QuoteRq_PremiumTotalPIF id="A013">2536.60</com.safeco_QuoteRq_PremiumTotalPIF>
                    <CurrentTermAmt>
                        <Amt id="A014">2776.00</Amt>
                    </CurrentTermAmt>
                    <RateEffectiveDate id="A015">2016-04-15</RateEffectiveDate>
                    <com.safeco_QuoteRq_Product id="A016">STD</com.safeco_QuoteRq_Product>
                    <com.safeco_QuoteRq_Market id="A017">030</com.safeco_QuoteRq_Market>
                    <com.safeco_QuoteRq_CovMarket id="A018">
                        BI:283,PD:252,COMP:263,COLL:251,MED:278,PIP:278,UM:283,UIM:283,UMPD:251
                    </com.safeco_QuoteRq_CovMarket>
                    <com.safeco_QuoteRq_Band id="A019">903</com.safeco_QuoteRq_Band>
                    <com.safeco_QuoteRq_Layer id="A020">006</com.safeco_QuoteRq_Layer>
                    <com.safeco_AdvancedQuotingDiscYN id="A021">N</com.safeco_AdvancedQuotingDiscYN>
                    <com.safeco_PolicyRemarks id="A022">PM Prior Total Auto Premium: $776.00***The quoted total premium
                        has been reduced for the following discounts: HomeOwner, Violation Free, Coverage, Accident
                        Free, Anti-Theft, Low Mileage
                    </com.safeco_PolicyRemarks>
                    <com.safeco_MarketingMessages>
                        <com.safeco_AgentMessage id="A023">Agent Message 1</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage id="A024">Safeco Motorcycle Enhancements!</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage id="A025">Come and
                            <a href="https://s3.amazonaws.com/Motorcycle_refresh_sizzle/MotorcycleRefresh_SIZZLE.html">
                                see what's in it for you.
                            </a>
                        </com.safeco_AgentMessage>
                        <com.safeco_AgentMessage id="A026">Agent Message 8</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage id="A027">Agent Message 9 - Updated</com.safeco_AgentMessage>
                        <com.safeco_AgentMessage id="A028">Last Message</com.safeco_AgentMessage>
                        <com.safeco_ConsumerMessage id="A029">Consumer Message 1</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A030">Consumer Message 2 - update</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A031">Consumer Message 3</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A032">Consumer Message 5</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A033">Consumer Message 6</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A034">Consumer Message 7</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A035">Consumer Message 8</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A036">Consumer Message 9</com.safeco_ConsumerMessage>
                        <com.safeco_ConsumerMessage id="A037">Consumer Message 10- Updated</com.safeco_ConsumerMessage>
                    </com.safeco_MarketingMessages>
                    <com.safeco_AnnualAutoEligibleYN id="A042">N</com.safeco_AnnualAutoEligibleYN>
                    <com.safeco_AutoLogonURLText>This is a link to the quote at the Safeco web site with Auto Logon.
                    </com.safeco_AutoLogonURLText>
                    <com.safeco_AutoLogonURL>
                        https://safesite.qa.safeco.com/dpec/autologon.aspx?key=e61e473c-50ca-49e0-9441-044471d39cc0&amp;user=dab5reisvfzgdv452j3kxnf1&amp;OriginalURL=/Client/TINRC/Client/OpenActivity.aspx&amp;l=o&amp;p=ad6c5062-8cde-4c7a-bd93-7eb8ee876b69
                    </com.safeco_AutoLogonURL>
                    <com.safeco_QuoteSummaryURLText>This is a link to the Quote Summary.
                    </com.safeco_QuoteSummaryURLText>
                    <com.safeco_QuoteSummaryURL>
                        http://ecdev-spi-tin.apps.safeco.com/Personal/TINRC/Consumer/Auto/VPReportMenu.aspx?P=ad6c5062-8cde-4c7a-bd93-7eb8ee876b69&amp;ReportName=QuoteSummary&amp;modeid=18
                    </com.safeco_QuoteSummaryURL>
                    <com.safeco_FCRANotificationURLText>This is a link to the consumers FCRA notification on the
                        safeco.com web site.
                    </com.safeco_FCRANotificationURLText>
                    <com.safeco_FCRANotificationURL>
                        http://ecdev-spi-tin.apps.safeco.com/Personal/TINRC/Consumer/Auto/VPReportMenu.aspx?P=ad6c5062-8cde-4c7a-bd93-7eb8ee876b69&amp;ReportName=AdverseActionCreditNoticeForm&amp;modeid=18
                    </com.safeco_FCRANotificationURL>
                    <com.safeco_CompanyURLText>This is a link to the quote at the Safeco web site.
                    </com.safeco_CompanyURLText>
                    <com.safeco_CompanyURL>
                        http://ecdev-sam-tin.apps.safeco.com/Client/TINRC/Client/OpenActivity.aspx?l=o&amp;p=ad6c5062-8cde-4c7a-bd93-7eb8ee876b69
                    </com.safeco_CompanyURL>
                </QuoteInfo>
                <com.safeco_PropertyCrossSell>
                    <com.safeco_PropertyAccountCreditDiscountAmt>328.70</com.safeco_PropertyAccountCreditDiscountAmt>
                    <com.safeco_PropertyAddressEligibleYN>N</com.safeco_PropertyAddressEligibleYN>
                </com.safeco_PropertyCrossSell>
            </PersPolicy>
            <PersAutoLineBusiness id="ADAD10836E2BBB515011AA87496F6CF2FA">
                <LOBCd id="A454FDF7CDC7D7787A3DA4B838FE0142DA">AUTOP</LOBCd>
                <CurrentTermAmt id="A689D491571929B6449D1F6D79D733BDEA">
                    <Amt id="**********************************">776.00</Amt>
                </CurrentTermAmt>
                <PersDriver id="**********************************">
                    <ItemIdInfo id="**********************************">
                        <AgencyId id="AEE1CE9DA222ECC71CE8C56D7EBA714B7A">0002</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo id="A17CE6546C490066F7BACFA8062BA87A1A">
                        <NameInfo id="A38F18B47A136A4A7103AF784324288F0A">
                            <PersonName id="AD3C807C240036AE3E6785F14B51AD54EA">
                                <Surname id="AAB65965879DB828950FF59B4CADBE5F6A">Pusateri</Surname>
                                <GivenName id="A6D5024C989D37B54DD97570692E017C9A">Michael</GivenName>
                                <OtherGivenName id="A6AA08EDB05B8D7E65013E06B6E6690C4A">J</OtherGivenName>
                            </PersonName>
                            <TaxIdentity id="A660D00EEBBF4DF9830ABB5249AD8D9C9A">
                                <TaxIdTypeCd id="A09B1E8B11F611592BE4FE6C10C507FC5A">SSN</TaxIdTypeCd>
                                <TaxId id="A7F39474176F3EC00DA9E88921484CEBEA">***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo id="A9A29E03A25AED5770CAF4F11F0A7BD2DA">
                        <PersonInfo id="ABCE42281661E925A6EB75DBE0C865509A">
                            <GenderCd id="A2A7F21D1EED488C71EF244738E7FEEEBA">M</GenderCd>
                            <MaritalStatusCd id="A82BA112E9C16D9ACCF03A8473C8C7EBFA">M</MaritalStatusCd>
                            <com.safeco_OccupationCd id="AA54E7A40953BE501DF14ED179A76302FA">Sales-Retail/Whlsle
                            </com.safeco_OccupationCd>
                        </PersonInfo>
                        <DriversLicense id="AC57184D5145395675B1215B2530A5041A">
                            <LicensedDt id="A1FF6E55581BAABC8118B006701E1F0A3A">1982-05-15</LicensedDt>
                        </DriversLicense>
                        <License id="AE17B91EE4793D47597AAAD580699021AA">
                            <LicenseTypeCd id="AC17522E297B5C8A0D17E97E313CCF3EDA">Driver</LicenseTypeCd>
                            <LicensedDt id="A70E5B9379CE3471C1B6DC1BE1D9672DEA">1982-05-01</LicensedDt>
                            <LicensePermitNumber id="A334073518828328C05B6DF5053FECDC7A">P236603441366
                            </LicensePermitNumber>
                            <StateProvCd id="A97714601A649E5989B418C54AA82F1ADA">MD</StateProvCd>
                        </License>
                        <QuestionAnswer id="A60529552CA9B0D4DE61D784ACC4C27CDA">
                            <QuestionCd id="A21D27AE3C9BC3167D33BF3EAE4C9F42AA">Suspended_Revoked?</QuestionCd>
                            <YesNoCd id="A688C69F39944836843EFD8411F5DBBF9A">N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer id="A4CB56108833CCA5B796B78130F12D5E2A">
                            <QuestionCd id="A43B1733EA3CE46A592342A5E57BAAE32A">Education</QuestionCd>
                            <YesNoCd id="A324362B4D6FC0386F50901A96A3838D6A">YES</YesNoCd>
                            <Explanation id="A1FA2CC8D8D74C451A7746B7A1CAA6BAEA">BS</Explanation>
                        </QuestionAnswer>
                        <QuestionAnswer id="A4175D7EB21A26FC1B891F46A719A582EA">
                            <QuestionCd id="A4971EBB01E182C2FC7C5F50CDBEECA18A">Cancelled_Declined?</QuestionCd>
                            <YesNoCd id="A64220577C37BDB56806BE2B7F9AED7BFA">NO</YesNoCd>
                        </QuestionAnswer>
                    </DriverInfo>
                </PersDriver>
                <PersDriver id="A9EAD80E1CCC255C2510B7F48CB4E8B50A">
                    <ItemIdInfo id="A291C42C1965702A13EFCA39AC8BC0B0FA">
                        <AgencyId id="A0C4E904F35F198B5AD092EB717327862A">0001</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo id="A6180D1EDD99FFF10A25421C28F977B07A">
                        <NameInfo id="AF505178C77E715F24B2E77BED70277CDA">
                            <PersonName id="ADB01C7983221374B290D8CD1F232DF7CA">
                                <Surname id="ABC6A0752357200EB13E0A66A51029AA2A">Pusateri</Surname>
                                <GivenName id="A51B75E749C9F5536D311D8C61B33CC30A">Dorene</GivenName>
                                <OtherGivenName id="AD049F5A9C456E021CAF1B574E4502115A">L</OtherGivenName>
                            </PersonName>
                            <TaxIdentity id="A84018321879C93E8F81BEBC08AB3480BA">
                                <TaxIdTypeCd id="A693CDD08349E36419970DE62F0ADBAF5A">SSN</TaxIdTypeCd>
                                <TaxId id="AB92B25D1A9DC9CA331B7662A39E262F2A">***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo id="A83D0C189036389E2A363696F9794D386A">
                        <PersonInfo id="A46E12B55ECC09167F4F32B2DA0D93A5AA">
                            <GenderCd id="AB69C36E084B41AB726842D5ABDA47D9EA">F</GenderCd>
                            <MaritalStatusCd id="AE5AA292829623829BF6BDBC6BD0728D3A">M</MaritalStatusCd>
                            <com.safeco_OccupationCd id="A32904DE27B24A5E87BD3B2215BBF635CA">Sales-Retail/Whlsle
                            </com.safeco_OccupationCd>
                        </PersonInfo>
                        <DriversLicense id="A9EA83A3EA8C0BDF33ABA7EC89B5121FEA">
                            <LicensedDt id="A1C48DAB732DE98113D3BFBE3DF4A91D4A">1983-10-05</LicensedDt>
                        </DriversLicense>
                        <License id="AB1F3C7E26EAC4231275C6A8003878401A">
                            <LicenseTypeCd id="A6FE88E57F1A339641F9802ED7490A93AA">Driver</LicenseTypeCd>
                            <LicensedDt id="A1CE6586A42B6C5BE1255782888F5E1DBA">1984-10-01</LicensedDt>
                            <LicensePermitNumber id="AD2FFAAFCB1FD788F8374FB9247071ED4A">P236149522767
                            </LicensePermitNumber>
                            <StateProvCd id="A9BBEDFA734552C3D0273DC5E0F4F3396A">MD</StateProvCd>
                        </License>
                        <QuestionAnswer id="AF58A6CC475631F9A66DB9BDEB64903A9A">
                            <QuestionCd id="ACD1FA102321DA674D18F1804E28AE7E3A">Education</QuestionCd>
                            <YesNoCd id="A7F50F89AC7BF51B7E6F7BA67374C6E63A">YES</YesNoCd>
                            <Explanation id="A1095BB615314156E1DB37294821A75D8A">BS</Explanation>
                        </QuestionAnswer>
                        <QuestionAnswer id="AB6A82EC817570112E087A6A281ECD405A">
                            <QuestionCd id="AC11CFFB1599F469ED0E25E695D2D419EA">Cancelled_Declined?</QuestionCd>
                            <YesNoCd id="A1109CDA635C64DCD136230AF276DFD22A">NO</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer id="A739A8251A3B44D32AD9D5B85EBF79B28A">
                            <QuestionCd id="A19AB5512D5EC3E3463115602651698AFA">Suspended_Revoked?</QuestionCd>
                            <YesNoCd id="ABEEF4384A2820AB4191BE634BF42FACEA">N</YesNoCd>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo id="AD85D49689DEAD8E09205AB64169A9701A">
                        <DriverRelationshipToApplicantCd id="AF995AAFE40FF968114B68C45CBF2BC4CA">SP
                        </DriverRelationshipToApplicantCd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="**********************************">
                    <ItemIdInfo id="**********************************">
                        <AgencyId id="AEE1CE9DA222ECC71CE8C56D7EBA714B7A">0002</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo id="A17CE6546C490066F7BACFA8062BA87A1A">
                        <NameInfo id="A38F18B47A136A4A7103AF784324288F0A">
                            <PersonName id="AD3C807C240036AE3E6785F14B51AD54EA">
                                <Surname id="AAB65965879DB828950FF59B4CADBE5F6A">Pusateri</Surname>
                                <GivenName id="A6D5024C989D37B54DD97570692E017C9A">Lee</GivenName>
                                <OtherGivenName id="A6AA08EDB05B8D7E65013E06B6E6690C4A">J</OtherGivenName>
                            </PersonName>
                            <TaxIdentity id="A660D00EEBBF4DF9830ABB5249AD8D9C9A">
                                <TaxIdTypeCd id="A09B1E8B11F611592BE4FE6C10C507FC5A">SSN</TaxIdTypeCd>
                                <TaxId id="A7F39474176F3EC00DA9E88921484CEBEA">***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo id="A9A29E03A25AED5770CAF4F11F0A7BD2DA">
                        <PersonInfo id="ABCE42281661E925A6EB75DBE0C865509A">
                            <GenderCd id="A2A7F21D1EED488C71EF244738E7FEEEBA">M</GenderCd>
                            <MaritalStatusCd id="A82BA112E9C16D9ACCF03A8473C8C7EBFA">M</MaritalStatusCd>
                            <com.safeco_OccupationCd id="AA54E7A40953BE501DF14ED179A76302FA">Sales-Retail/Whlsle
                            </com.safeco_OccupationCd>
                        </PersonInfo>
                        <DriversLicense id="AC57184D5145395675B1215B2530A5041A">
                            <LicensedDt id="A1FF6E55581BAABC8118B006701E1F0A3A">1982-05-14</LicensedDt>
                        </DriversLicense>
                        <License id="AE17B91EE4793D47597AAAD580699021AA">
                            <LicenseTypeCd id="AC17522E297B5C8A0D17E97E313CCF3EDA">Driver</LicenseTypeCd>
                            <LicensedDt id="A70E5B9379CE3471C1B6DC1BE1D9672DEA">1988-05-01</LicensedDt>
                            <LicensePermitNumber id="A334073518828328C05B6DF5053FECDC7A">P236603441474
                            </LicensePermitNumber>
                            <StateProvCd id="A97714601A649E5989B418C54AA82F1ADA">MD</StateProvCd>
                        </License>
                        <QuestionAnswer id="A60529552CA9B0D4DE61D784ACC4C27CDA">
                            <QuestionCd id="A21D27AE3C9BC3167D33BF3EAE4C9F42AA">Suspended_Revoked?</QuestionCd>
                            <YesNoCd id="A688C69F39944836843EFD8411F5DBBF9A">N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer id="A4CB56108833CCA5B796B78130F12D5E2A">
                            <QuestionCd id="A43B1733EA3CE46A592342A5E57BAAE32A">Education</QuestionCd>
                            <YesNoCd id="A324362B4D6FC0386F50901A96A3838D6A">YES</YesNoCd>
                            <Explanation id="A1FA2CC8D8D74C451A7746B7A1CAA6BAEA">BS</Explanation>
                        </QuestionAnswer>
                        <QuestionAnswer id="A4175D7EB21A26FC1B891F46A719A582EA">
                            <QuestionCd id="A4971EBB01E182C2FC7C5F50CDBEECA18A">Cancelled_Declined?</QuestionCd>
                            <YesNoCd id="A64220577C37BDB56806BE2B7F9AED7BFA">NO</YesNoCd>
                        </QuestionAnswer>
                    </DriverInfo>
                </PersDriver>
                <PersDriver id="A9EAD80E1CCC255C2610B7F48CB4E8B60A">
                    <ItemIdInfo id="A291C42C1965702A13EFCA39AC8BC0B0FA">
                        <AgencyId id="A0C4E904F35F198B5AD092EB717327862A">0001</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo id="A6180D1EDD99FFF10A25421C28F977B07A">
                        <NameInfo id="AF505178C77E715F24B2E77BED70277CDA">
                            <PersonName id="ADB01C7983221374B290D8CD1F232DF7CA">
                                <Surname id="ABC6A0752357200EB13E0A66A51029AA2A">Lia</Surname>
                                <GivenName id="A51B75E749C9F5536D311D8C61B33CC30A">Dorene</GivenName>
                                <OtherGivenName id="AD049F5A9C456E021CAF1B574E4502115A">L</OtherGivenName>
                            </PersonName>
                            <TaxIdentity id="A84018321879C93E8F81BEBC08AB3480BA">
                                <TaxIdTypeCd id="A693CDD08349E36419970DE62F0ADBAF5A">SSN</TaxIdTypeCd>
                                <TaxId id="AB92B25D1A9DC9CA331B7662A39E262F2A">***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo id="A83D0C189036389E2A363696F9794D386A">
                        <PersonInfo id="A46E12B55ECC09167F4F32B2DA0D93A5AA">
                            <GenderCd id="AB69C36E084B41AB726842D5ABDA47D9EA">F</GenderCd>
                            <MaritalStatusCd id="AE5AA292829623829BF6BDBC6BD0728D3A">M</MaritalStatusCd>
                            <com.safeco_OccupationCd id="A32904DE27B24A5E87BD3B2215BBF635CA">Sales-Retail/Whlsle
                            </com.safeco_OccupationCd>
                        </PersonInfo>
                        <DriversLicense id="A9EA83A3EA8C0BDF33ABA7EC89B5121FEA">
                            <LicensedDt id="A1C48DAB732DE98113D3BFBE3DF4A91D4A">1983-10-05</LicensedDt>
                        </DriversLicense>
                        <License id="AB1F3C7E26EAC4231275C6A8003878401A">
                            <LicenseTypeCd id="A6FE88E57F1A339641F9802ED7490A93AA">Driver</LicenseTypeCd>
                            <LicensedDt id="A1CE6586A42B6C5BE1255782888F5E1DBA">1984-10-01</LicensedDt>
                            <LicensePermitNumber id="AD2FFAAFCB1FD788F8374FB9247071ED4A">P236149522782
                            </LicensePermitNumber>
                            <StateProvCd id="A9BBEDFA734552C3D0273DC5E0F4F3396A">MD</StateProvCd>
                        </License>
                        <QuestionAnswer id="AF58A6CC475631F9A66DB9BDEB64903A9A">
                            <QuestionCd id="ACD1FA102321DA674D18F1804E28AE7E3A">Education</QuestionCd>
                            <YesNoCd id="A7F50F89AC7BF51B7E6F7BA67374C6E63A">YES</YesNoCd>
                            <Explanation id="A1095BB615314156E1DB37294821A75D8A">BS</Explanation>
                        </QuestionAnswer>
                        <QuestionAnswer id="AB6A82EC817570112E087A6A281ECD405A">
                            <QuestionCd id="AC11CFFB1599F469ED0E25E695D2D419EA">Cancelled_Declined?</QuestionCd>
                            <YesNoCd id="A1109CDA635C64DCD136230AF276DFD22A">NO</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer id="A739A8251A3B44D32AD9D5B85EBF79B28A">
                            <QuestionCd id="A19AB5512D5EC3E3463115602651698AFA">Suspended_Revoked?</QuestionCd>
                            <YesNoCd id="ABEEF4384A2820AB4191BE634BF42FACEA">N</YesNoCd>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo id="AD85D49689DEAD8E09205AB64169A9901A">
                        <DriverRelationshipToApplicantCd id="AF995AAFE40FF968114B68C45CBF2BC4CA">SP
                        </DriverRelationshipToApplicantCd>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="A5B9D0A76BD5B5799EC295FB2FCB9DDF3A"
                         RatedDriverRef="A9EAD80E1CCC255C2510B7F48CB4E8B50A" id="A8933BE722C9809F2C8B9631AF20D272AA">
                    <ItemIdInfo id="AFE4E4957DE35490AA91EEF7C6DC25C21A">
                        <AgencyId id="A449A66AADDCEBCE9D5C0A9F6D4BA08F6A">0001</AgencyId>
                        <InsurerId id="A4BC1B0FDC4A330E1DDB9FC18314B1F7BA">0001</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer id="ACAA79F179A9375F8ADDFB068B297D92AA">FORD</Manufacturer>
                    <Model id="A968DE14B8E45D7305411AB8CF42360E0A">ESCAPE SE</Model>
                    <ModelYear id="ACD7B45B3FD0AC5C4E54CE45FC526D72EA">2013</ModelYear>
                    <VehBodyTypeCd id="ACE416A2B7080CC581EACEFCDC2EE49FAA">4WD</VehBodyTypeCd>
                    <Registration id="A1009FCECA1060ACC73C2409E979D02A5A">
                        <StateProvCd id="A8269A4852233BA649DE9A9523266A197A">MD</StateProvCd>
                    </Registration>
                    <AntiTheftDeviceInfo id="A3B95F3760EBF0029B886397607B52384A">
                        <AntiTheftDeviceCd id="A1582996935C767E7600073C271274458A">I</AntiTheftDeviceCd>
                    </AntiTheftDeviceInfo>
                    <NumDaysDrivenPerWeek id="A60C2C9479E64AE28E6CCAA8B8C7C5A6AA">5</NumDaysDrivenPerWeek>
                    <EstimatedAnnualDistance id="A0C5604C0051F17A2A4DB49402D64CC81A">
                        <NumUnits id="ABB0596757F297039B90C87BBE3ABFBBFA">2000</NumUnits>
                        <UnitMeasurementCd id="AA797A86B72B3493941B5653172E76ADFA">SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt id="A45CC0A7179519F559D3AEDD19A3B9BA6A">2012-12-01</PurchaseDt>
                    <TerritoryCd id="A94A2C08D5B12ABFF7FEDCE7ED028B7C4A">0M5</TerritoryCd>
                    <AntiLockBrakeCd id="A575C750AD859772DAEC2C41536135DEDA">2</AntiLockBrakeCd>
                    <CarpoolInd id="AAC1EFBCE750DE688A627D9E7F4112495A">1</CarpoolInd>
                    <DistanceOneWay id="A41553A4BA0901DC79322A627F7ED5466A">
                        <NumUnits id="A655EE203A20BFEE21D9A7D56B234FE96A">2</NumUnits>
                        <UnitMeasurementCd id="A2AEDA1F8318A3F1597506C91619183D4A">SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <MultiCarDiscountInd id="AF99B239925108186AD787BC3314475C6A">0</MultiCarDiscountInd>
                    <RateClassCd id="A9436A248C95E88799D87CB3EDC210FB1A">A1BS</RateClassCd>
                    <VehPerformanceCd id="A0B57C35D38A74A4508103569D9C72227A">BASIC</VehPerformanceCd>
                    <VehUseCd id="A0D50099D4DA224CCED478D35CB1368AAA">DW</VehUseCd>
                    <AirBagTypeCd id="ABED9605940A6171A96E4CCE40D284FB1A">FrontSide</AirBagTypeCd>
                    <Coverage id="AB67A9132D2D47C72866C05CB9BD38CF3A">
                        <CoverageCd id="A94BC84A61F99BD351707EB6B0BD7609FA">BI</CoverageCd>
                        <CoverageDesc id="A50F41B4E31F421744A91D8FC283E5631A">Bodily injury limit(s)</CoverageDesc>
                        <Limit id="A4F6A044C7ABE60F3B90D395F87EB9918A">
                            <FormatInteger id="A5219CAF77C1F8DC3820A1A090AEC858EA">100000</FormatInteger>
                            <LimitAppliesToCd id="A52DAD8BD89485D75D3549A9825CB0FCCA">PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit id="A91287BABB143C141B119DDB12149831EA">
                            <FormatInteger id="AE5FCADE19A9EB78C65D2F49906BACA85A">300000</FormatInteger>
                            <LimitAppliesToCd id="AD8E7CFDB356F6D05CF4BCEF170852E2DA">PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt id="A6814157AC266D72A8FE7E21EA7F4AD4BA">
                            <Amt id="A235C5CCDD50870398B1F3546935101FDA">193.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="AAB562732385DD890A3E650FA95994212A">
                        <CoverageCd id="A25CBC08D25C708A2CDEAB25784597E8EA">COLL</CoverageCd>
                        <CoverageDesc id="A3809401C5F8C1C98A188C37FF51CC04FA">Collision</CoverageDesc>
                        <Deductible id="A08A7C74C33D82CD7AA6AD23DF4681600A">
                            <FormatInteger id="A444DC4FF1B5EBBFC5D80DE9810ABE0AEA">500</FormatInteger>
                            <DeductibleTypeCd id="A471C13E2394EDCA75020C0208792EC3EA">FL</DeductibleTypeCd>
                        </Deductible>
                        <CurrentTermAmt id="AAA3C6094FA8525B646474D40C629BF3EA">
                            <Amt id="AA0ACDCC6F2F77ED34E054BC0D218AC3AA">217.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="AEF9BDD3955F39CF176DE49C7B92E64F0A">
                        <CoverageCd id="A1B0FADE66CB2033A806A172AEBFEA620A">COMP</CoverageCd>
                        <CoverageDesc id="A8994309F9A9EB3F9E6EE1F55E1660AF9A">Comprehensive</CoverageDesc>
                        <Deductible id="AA96C0FD9E38C82F72622A11C6023A5EAA">
                            <FormatInteger id="A45D20A0C80691407E816D338F28B2926A">100</FormatInteger>
                            <DeductibleTypeCd id="AF4D517FD1C6F75A98671387597859E2DA">FL</DeductibleTypeCd>
                        </Deductible>
                        <CurrentTermAmt id="A2276D09EDA7ED231463B26B7F87C1115A">
                            <Amt id="A087CF81AE5BFD2386A8BB708B40B113CA">134.00</Amt>
                        </CurrentTermAmt>
                        <CreditOrSurcharge id="A556BDB410A0634503F761C9F04DCF339A">
                            <CreditSurchargeCd id="A64799963B913F3FDD2CD89D5F29742FFA">ANTHF</CreditSurchargeCd>
                            <NumericValue id="AB8325C9890053D5C649EFB7B6C479D30A">
                                <FormatPct id="A8C6B3BD06BAFA4B7D3DBEADC315932A3A">1</FormatPct>
                            </NumericValue>
                        </CreditOrSurcharge>
                    </Coverage>
                    <Coverage id="ABD3449997F465FF1681E55C7CE35A706A">
                        <CoverageCd id="AA954EA38C991A8402DE0C27DE702EEE4A">PIP</CoverageCd>
                        <CoverageDesc id="AB723203A53F90BA2B8CF0F209CC7CFABA">PIP-Basic</CoverageDesc>
                        <Limit id="A2414B726E22390E7975FB41072CA960BA">
                            <FormatInteger id="AB75C4BF2BBA46724A9CB8EE97DD1A98CA">2500</FormatInteger>
                        </Limit>
                        <Option id="AB97AB5762301A4D4C278046F2ED317B2A">
                            <OptionTypeCd id="A9B6F518922BA5C805375B638371888C9A">Opt1</OptionTypeCd>
                        </Option>
                        <CurrentTermAmt id="A51BA12341588A98FFADBF6BEA2054909A">
                            <Amt id="A0CFE86CA6A18972EB7E375B51CB715E2A">43.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="A67E78D75A8F4E61D84BEC8029D21F65CA">
                        <CoverageCd id="A2A90A24425F6415F5EB4831279A3AB0FA">PD</CoverageCd>
                        <CoverageDesc id="A709A864C61E3391C6DE162D1B1F7F39CA">Property damage-single limit
                        </CoverageDesc>
                        <Limit id="A219A8EE5320DA42A811283B8A5ED1538A">
                            <FormatInteger id="A8B07E62E7F9215DBB72EF142E0FDCF06A">100000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt id="AB3312AB18598A6B328E70C609AEEF24EA">
                            <Amt id="AC825816D2D44FC01FB4D283B8559C068A">126.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="A3C62CE60B8449440E67906244A15700BA">
                        <CoverageCd id="A06A9C6D475B05529DDA0125097566B87A">TELCL</CoverageCd>
                        <CoverageDesc id="AC4EA3E85BB3EDFB47D932983F95FEB03A">TELCL</CoverageDesc>
                        <Limit id="AAFB23A1425770BFF7649EDAC043462EEA">
                            <FormatInteger id="A941E831E8621BD4201267C1FBB1B5887A">30</FormatInteger>
                        </Limit>
                        <Limit id="ABB83DCACB386F96001C948FE4C36407EA">
                            <FormatInteger id="A1588EEBA2E4824C4FC72C8DB31527C14A">1350</FormatInteger>
                        </Limit>
                        <CurrentTermAmt id="A1CEA14DCD3D030D8C736B9FE5C61DEBCA">
                            <Amt id="AE6C28F4FBBED0DB01FD39210CE609A12A">20.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="AB6C1231D2871B08C7282F4584C2C40B2A">
                        <CoverageCd id="AF2AAEF8D8489F169018F8B984C96C931A">RREIM</CoverageCd>
                        <CoverageDesc id="A3EB9AA5CFC134E1EDCE3AD428CCACC54A">TRANSP EXPENS CMP</CoverageDesc>
                        <CurrentTermAmt id="ABEA78AF7027CC68C4E19F617AA6384CFA">
                            <Amt id="A4352FB7CD4F7BE45C0BBB7E442F86729A">7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="AC10007537F6079D2199A0B0E444AAB6EA">
                        <CoverageCd id="A904E17D4C4A285A0A260D6E0FDADC60AA">UMPD</CoverageCd>
                        <CoverageDesc id="AE2D741E2977C6F255462EC1994555FA8A">Uninsured motorist property damage
                        </CoverageDesc>
                        <Limit id="AF9DC41440676C433299BFB4C62CBBF2AA">
                            <FormatInteger id="ACC9F6926BCD87760329A6A3A6CEF4368A">100000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt id="A24AD803EF65B63F69B24759E8B9E8FE7A">
                            <Amt id="AD1672F1A7A5B742A015511B2E5F71955A">36.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="A747D47E147892E59446F30CEEF02DA86A">
                        <CoverageCd id="A704CD6C20DDEAA6BDF55404688099714A">com.safeco_CovLevel</CoverageCd>
                        <Limit id="AC46CA13FCD2FFB13E79C5A24ADD77C69A">
                            <FormatInteger id="A3BE52C191093FA8B4A6F4DB1471304D3A">1</FormatInteger>
                        </Limit>
                        <Option id="AB30D594DA5EFC540816D038909502936A">
                            <OptionCd id="A974904AB7011167ACE456B956FE9A61DA">B</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage id="AF71F876DC53E7AEC72CA49ABFB2E7226A">
                        <CoverageCd id="A063C89783D19983ADD364A13DE62DF09A">UM</CoverageCd>
                        <Limit id="A578B24224A325B1D056A0DEB61E6A85DA">
                            <FormatInteger id="A5BC6D66B82C38E6BB9B03E534BAF1D8EA">100000</FormatInteger>
                            <LimitAppliesToCd id="AD0424E1D8943EC0647A38A00F0919EB6A">PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit id="ABC70E6436934160A03CB76B5A5E74453A">
                            <FormatInteger id="A0560BF2DBCABD1B52BFFD83C102731BFA">300000</FormatInteger>
                            <LimitAppliesToCd id="A8366DB4DC12A71FCDFD1B180F4071684A">PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage id="AD67D4D5ED27578C7E5193722E0EBBBDEA">
                        <CoverageCd id="A22DEAE052E19BF67D0714BE30A24DA66A">TL</CoverageCd>
                        <Option id="A121BC455D0652BF55890B930D1D8B1A5A">
                            <OptionCd id="A64C2706C6D1762BBC1B5D44CEE13F2A5A">Y</OptionCd>
                        </Option>
                    </Coverage>
                    <com.safeco_PriorVehiclePremium id="A8D163524E0B63A3A3B1CCC20E80C0AE9A">776.00
                    </com.safeco_PriorVehiclePremium>
                    <FullTermAmt></FullTermAmt>
                </PersVeh>
                <PersVeh LocationRef="A5B9D0A76BD5B5799EC295FB2FCB9DDF3A"
                         RatedDriverRef="A9EAD80E1CCC255C2510B7F48CB4E8B50A" id="A8933BE722C9809F2C8B9631AF20D272AA">
                    <ItemIdInfo id="AFE4E4957DE35490AA91EEF7C6DC25C21A">
                        <AgencyId id="A449A66AADDCEBCE9D5C0A9F6D4BA08F6A">0001</AgencyId>
                        <InsurerId id="A4BC1B0FDC4A330E1DDB9FC18314B1F7BA">0001</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer id="ACAA79F179A9375F8ADDFB068B297D92AA">FORD</Manufacturer>
                    <Model id="A968DE14B8E45D7305411AB8CF42360E0A">ESCAPE SE</Model>
                    <ModelYear id="ACD7B45B3FD0AC5C4E54CE45FC526D72EA">2013</ModelYear>
                    <VehBodyTypeCd id="ACE416A2B7080CC581EACEFCDC2EE49FAA">4WD</VehBodyTypeCd>
                    <Registration id="A1009FCECA1060ACC73C2409E979D02A5A">
                        <StateProvCd id="A8269A4852233BA649DE9A9523266A197A">MD</StateProvCd>
                    </Registration>
                    <AntiTheftDeviceInfo id="A3B95F3760EBF0029B886397607B52384A">
                        <AntiTheftDeviceCd id="A1582996935C767E7600073C271274458A">I</AntiTheftDeviceCd>
                    </AntiTheftDeviceInfo>
                    <NumDaysDrivenPerWeek id="A60C2C9479E64AE28E6CCAA8B8C7C5A6AA">5</NumDaysDrivenPerWeek>
                    <EstimatedAnnualDistance id="A0C5604C0051F17A2A4DB49402D64CC81A">
                        <NumUnits id="ABB0596757F297039B90C87BBE3ABFBBFA">2000</NumUnits>
                        <UnitMeasurementCd id="AA797A86B72B3493941B5653172E76ADFA">SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt id="A45CC0A7179519F559D3AEDD19A3B9BA6A">2012-12-01</PurchaseDt>
                    <TerritoryCd id="A94A2C08D5B12ABFF7FEDCE7ED028B7C4A">0M5</TerritoryCd>
                    <AntiLockBrakeCd id="A575C750AD859772DAEC2C41536135DEDA">2</AntiLockBrakeCd>
                    <CarpoolInd id="AAC1EFBCE750DE688A627D9E7F4112495A">1</CarpoolInd>
                    <DistanceOneWay id="A41553A4BA0901DC79322A627F7ED5466A">
                        <NumUnits id="A655EE203A20BFEE21D9A7D56B234FE96A">2</NumUnits>
                        <UnitMeasurementCd id="A2AEDA1F8318A3F1597506C91619183D4A">SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <MultiCarDiscountInd id="AF99B239925108186AD787BC3314475C6A">0</MultiCarDiscountInd>
                    <RateClassCd id="A9436A248C95E88799D87CB3EDC210FB1A">A1BS</RateClassCd>
                    <VehPerformanceCd id="A0B57C35D38A74A4508103569D9C72227A">BASIC</VehPerformanceCd>
                    <VehUseCd id="A0D50099D4DA224CCED478D35CB1368AAA">DW</VehUseCd>
                    <AirBagTypeCd id="ABED9605940A6171A96E4CCE40D284FB1A">FrontSide</AirBagTypeCd>
                    <Coverage id="AB67A9132D2D47C72866C05CB9BD38CF3A">
                        <CoverageCd id="A94BC84A61F99BD351707EB6B0BD7609FA">BI</CoverageCd>
                        <CoverageDesc id="A50F41B4E31F421744A91D8FC283E5631A">Bodily injury limit(s)</CoverageDesc>
                        <Limit id="A4F6A044C7ABE60F3B90D395F87EB9918A">
                            <FormatInteger id="A5219CAF77C1F8DC3820A1A090AEC858EA">100000</FormatInteger>
                            <LimitAppliesToCd id="A52DAD8BD89485D75D3549A9825CB0FCCA">PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit id="A91287BABB143C141B119DDB12149831EA">
                            <FormatInteger id="AE5FCADE19A9EB78C65D2F49906BACA85A">300000</FormatInteger>
                            <LimitAppliesToCd id="AD8E7CFDB356F6D05CF4BCEF170852E2DA">PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt id="A6814157AC266D72A8FE7E21EA7F4AD4BA">
                            <Amt id="A235C5CCDD50870398B1F3546935101FDA">193.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="AAB562732385DD890A3E650FA95994212A">
                        <CoverageCd id="A25CBC08D25C708A2CDEAB25784597E8EA">COLL</CoverageCd>
                        <CoverageDesc id="A3809401C5F8C1C98A188C37FF51CC04FA">Collision</CoverageDesc>
                        <Deductible id="A08A7C74C33D82CD7AA6AD23DF4681600A">
                            <FormatInteger id="A444DC4FF1B5EBBFC5D80DE9810ABE0AEA">500</FormatInteger>
                            <DeductibleTypeCd id="A471C13E2394EDCA75020C0208792EC3EA">FL</DeductibleTypeCd>
                        </Deductible>
                        <CurrentTermAmt id="AAA3C6094FA8525B646474D40C629BF3EA">
                            <Amt id="AA0ACDCC6F2F77ED34E054BC0D218AC3AA">217.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="AEF9BDD3955F39CF176DE49C7B92E64F0A">
                        <CoverageCd id="A1B0FADE66CB2033A806A172AEBFEA620A">COMP</CoverageCd>
                        <CoverageDesc id="A8994309F9A9EB3F9E6EE1F55E1660AF9A">Comprehensive</CoverageDesc>
                        <Deductible id="AA96C0FD9E38C82F72622A11C6023A5EAA">
                            <FormatInteger id="A45D20A0C80691407E816D338F28B2926A">100</FormatInteger>
                            <DeductibleTypeCd id="AF4D517FD1C6F75A98671387597859E2DA">FL</DeductibleTypeCd>
                        </Deductible>
                        <CurrentTermAmt id="A2276D09EDA7ED231463B26B7F87C1115A">
                            <Amt id="A087CF81AE5BFD2386A8BB708B40B113CA">134.00</Amt>
                        </CurrentTermAmt>
                        <CreditOrSurcharge id="A556BDB410A0634503F761C9F04DCF339A">
                            <CreditSurchargeCd id="A64799963B913F3FDD2CD89D5F29742FFA">ANTHF</CreditSurchargeCd>
                            <NumericValue id="AB8325C9890053D5C649EFB7B6C479D30A">
                                <FormatPct id="A8C6B3BD06BAFA4B7D3DBEADC315932A3A">1</FormatPct>
                            </NumericValue>
                        </CreditOrSurcharge>
                    </Coverage>
                    <Coverage id="ABD3449997F465FF1681E55C7CE35A706A">
                        <CoverageCd id="AA954EA38C991A8402DE0C27DE702EEE4A">PIP</CoverageCd>
                        <CoverageDesc id="AB723203A53F90BA2B8CF0F209CC7CFABA">PIP-Basic</CoverageDesc>
                        <Limit id="A2414B726E22390E7975FB41072CA960BA">
                            <FormatInteger id="AB75C4BF2BBA46724A9CB8EE97DD1A98CA">2500</FormatInteger>
                        </Limit>
                        <Option id="AB97AB5762301A4D4C278046F2ED317B2A">
                            <OptionTypeCd id="A9B6F518922BA5C805375B638371888C9A">Opt1</OptionTypeCd>
                        </Option>
                        <CurrentTermAmt id="A51BA12341588A98FFADBF6BEA2054909A">
                            <Amt id="A0CFE86CA6A18972EB7E375B51CB715E2A">43.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="A67E78D75A8F4E61D84BEC8029D21F65CA">
                        <CoverageCd id="A2A90A24425F6415F5EB4831279A3AB0FA">PD</CoverageCd>
                        <CoverageDesc id="A709A864C61E3391C6DE162D1B1F7F39CA">Property damage-single limit
                        </CoverageDesc>
                        <Limit id="A219A8EE5320DA42A811283B8A5ED1538A">
                            <FormatInteger id="A8B07E62E7F9215DBB72EF142E0FDCF06A">100000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt id="AB3312AB18598A6B328E70C609AEEF24EA">
                            <Amt id="AC825816D2D44FC01FB4D283B8559C068A">126.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="A3C62CE60B8449440E67906244A15700BA">
                        <CoverageCd id="A06A9C6D475B05529DDA0125097566B87A">TELCL</CoverageCd>
                        <CoverageDesc id="AC4EA3E85BB3EDFB47D932983F95FEB03A">TELCL</CoverageDesc>
                        <Limit id="AAFB23A1425770BFF7649EDAC043462EEA">
                            <FormatInteger id="A941E831E8621BD4201267C1FBB1B5887A">30</FormatInteger>
                        </Limit>
                        <Limit id="ABB83DCACB386F96001C948FE4C36407EA">
                            <FormatInteger id="A1588EEBA2E4824C4FC72C8DB31527C14A">1350</FormatInteger>
                        </Limit>
                        <CurrentTermAmt id="A1CEA14DCD3D030D8C736B9FE5C61DEBCA">
                            <Amt id="AE6C28F4FBBED0DB01FD39210CE609A12A">20.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="AB6C1231D2871B08C7282F4584C2C40B2A">
                        <CoverageCd id="AF2AAEF8D8489F169018F8B984C96C931A">RREIM</CoverageCd>
                        <CoverageDesc id="A3EB9AA5CFC134E1EDCE3AD428CCACC54A">TRANSP EXPENS CMP</CoverageDesc>
                        <CurrentTermAmt id="ABEA78AF7027CC68C4E19F617AA6384CFA">
                            <Amt id="A4352FB7CD4F7BE45C0BBB7E442F86729A">7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="AC10007537F6079D2199A0B0E444AAB6EA">
                        <CoverageCd id="A904E17D4C4A285A0A260D6E0FDADC60AA">UMPD</CoverageCd>
                        <CoverageDesc id="AE2D741E2977C6F255462EC1994555FA8A">Uninsured motorist property damage
                        </CoverageDesc>
                        <Limit id="AF9DC41440676C433299BFB4C62CBBF2AA">
                            <FormatInteger id="ACC9F6926BCD87760329A6A3A6CEF4368A">100000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt id="A24AD803EF65B63F69B24759E8B9E8FE7A">
                            <Amt id="AD1672F1A7A5B742A015511B2E5F71955A">36.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage id="A747D47E147892E59446F30CEEF02DA86A">
                        <CoverageCd id="A704CD6C20DDEAA6BDF55404688099714A">com.safeco_CovLevel</CoverageCd>
                        <Limit id="AC46CA13FCD2FFB13E79C5A24ADD77C69A">
                            <FormatInteger id="A3BE52C191093FA8B4A6F4DB1471304D3A">1</FormatInteger>
                        </Limit>
                        <Option id="AB30D594DA5EFC540816D038909502936A">
                            <OptionCd id="A974904AB7011167ACE456B956FE9A61DA">B</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage id="AF71F876DC53E7AEC72CA49ABFB2E7226A">
                        <CoverageCd id="A063C89783D19983ADD364A13DE62DF09A">UM</CoverageCd>
                        <Limit id="A578B24224A325B1D056A0DEB61E6A85DA">
                            <FormatInteger id="A5BC6D66B82C38E6BB9B03E534BAF1D8EA">100000</FormatInteger>
                            <LimitAppliesToCd id="AD0424E1D8943EC0647A38A00F0919EB6A">PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit id="ABC70E6436934160A03CB76B5A5E74453A">
                            <FormatInteger id="A0560BF2DBCABD1B52BFFD83C102731BFA">300000</FormatInteger>
                            <LimitAppliesToCd id="A8366DB4DC12A71FCDFD1B180F4071684A">PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage id="AD67D4D5ED27578C7E5193722E0EBBBDEA">
                        <CoverageCd id="A22DEAE052E19BF67D0714BE30A24DA66A">TL</CoverageCd>
                        <Option id="A121BC455D0652BF55890B930D1D8B1A5A">
                            <OptionCd id="A64C2706C6D1762BBC1B5D44CEE13F2A5A">Y</OptionCd>
                        </Option>
                    </Coverage>
                    <com.safeco_PriorVehiclePremium id="A8D163524E0B63A3A3B1CCC20E80C0AE9A">776.00
                    </com.safeco_PriorVehiclePremium>
                    <FullTermAmt></FullTermAmt>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location></Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
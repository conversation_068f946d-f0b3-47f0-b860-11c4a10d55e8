<ACORD>
    <SignonRq>
        <ClientDt>2016-04-27</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>BookSmart</Org>
            <Name>AgencyPort</Name>
        </ClientApp>
        <SignonTransport>
            <SignonRoleCd>Agent</SignonRoleCd>
            <CustId>
                <SPName>com.agencyport</SPName>
                <CustLoginId>default</CustLoginId>
            </CustId>
        </SignonTransport>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>6F6ECD35BA124E68A42C4FF485D8EB1A</RqUID>
        <PersUmbrellaPolicyQuoteInqRq>
            <RqUID>2B440158E3EF4E2FA22A332D6EAD4BA5</RqUID>
            <Producer>
                <ProducerInfo>
                    <ContractNumber></ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>GILBDO1C-8</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName></CommercialName>
                            <SupplementaryNameInfo>
                                <SupplementaryNameCd>ATTN</SupplementaryNameCd>
                                <SupplementaryName>Insured Attn</SupplementaryName>
                            </SupplementaryNameInfo>
                        </CommlName>
                        <PersonName>
                            <GivenName>Todd</GivenName>
                            <Surname>Smith</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>100 Main Street</Addr1>
                        <City>Park Forest</City>
                        <StateProvCd>PA</StateProvCd>
                        <PostalCode>60466</PostalCode>
                        <County>Cook</County>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <PhoneNumber>************</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <PhoneNumber>************</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr>primary@emailaddress</EmailAddr>
                        </EmailInfo>
                        <EmailInfo>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <EmailAddr>secondary@emailaddress</EmailAddr>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <OccupationDesc>Appl Occupation</OccupationDesc>
                        <LengthTimeEmployed>
                            <NumUnits>1</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <MiscParty>
                            <GeneralPartyInfo>
                                <NameInfo>
                                    <CommlName>
                                        <CommercialName>Appl Employer Name</CommercialName>
                                    </CommlName>
                                </NameInfo>
                                <Addr>
                                    <Addr1>Appl Employer Street</Addr1>
                                    <City>Appl Employer City</City>
                                    <StateProvCd>IN</StateProvCd>
                                    <PostalCode>10001-1111</PostalCode>
                                </Addr>
                            </GeneralPartyInfo>
                            <MiscPartyInfo>
                                <MiscPartyRoleCd>Employer</MiscPartyRoleCd>
                            </MiscPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <OccupationDesc>Co--Appl Occupation</OccupationDesc>
                        <LengthTimeEmployed>
                            <NumUnits>2</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <MiscParty>
                            <GeneralPartyInfo>
                                <NameInfo>
                                    <CommlName>
                                        <CommercialName>Co-Appl Employer Name</CommercialName>
                                    </CommlName>
                                </NameInfo>
                                <Addr>
                                    <Addr1>Co-Appl Employer Street</Addr1>
                                    <City>Co-Appl Employer City</City>
                                    <StateProvCd>HI</StateProvCd>
                                    <PostalCode>10002-2222</PostalCode>
                                </Addr>
                            </GeneralPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <PolicyNumber>PU-COMPLETE DATA</PolicyNumber>
                <CompanyProductCd>Company Plan</CompanyProductCd>
                <LOBCd>UMBRP</LOBCd>
                <NAICCd>39012</NAICCd>
                <ContractTerm>
                    <EffectiveDt></EffectiveDt>
                    <ExpirationDt>2018-03-21</ExpirationDt>
                </ContractTerm>
                <BillingAccountNumber>BILLING ACCT NUM</BillingAccountNumber>
                <BillingMethodCd>CPB</BillingMethodCd>
                <MailingResponsibiltyCd>CO</MailingResponsibiltyCd>
                <PayorCd>AIF</PayorCd>
                <NumLossesYrs>5</NumLossesYrs>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber>PRIOR-POLNUMBER</PolicyNumber>
                    <InsurerName>AIG</InsurerName>
                    <ContractTerm>
                        <ExpirationDt>2016-01-06</ExpirationDt>
                    </ContractTerm>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <PaymentPlanCd>BM</PaymentPlanCd>
                    <MethodPaymentCd id="PAYMETH1">DRAFT</MethodPaymentCd>
                </PaymentOption>
            </PersPolicy>
            <Location id="LOC7">
                <ItemIdInfo>
                    <AgencyId>1</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1></Addr1>
                    <City>loc 1 city</City>
                    <StateProvCd>AK</StateProvCd>
                    <PostalCode>11111-1111</PostalCode>
                </Addr>
                <LocationDesc>loc 1 desc 2</LocationDesc>
            </Location>
            <Location>
                <ItemIdInfo>
                    <AgencyId>2</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 2 street</Addr1>
                    <City>loc 2 city</City>
                    <StateProvCd>AL</StateProvCd>
                    <PostalCode>22222-2222</PostalCode>
                </Addr>
                <LocationDesc>loc 2 desc 2</LocationDesc>
            </Location>
            <Location id="LOC3">
                <ItemIdInfo>
                    <AgencyId>3</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 3 street</Addr1>
                    <City>loc 3 city</City>
                    <StateProvCd>AR</StateProvCd>
                    <PostalCode>33333-3333</PostalCode>
                </Addr>
                <LocationDesc>loc 3 desc 2</LocationDesc>
            </Location>
            <Location id="LOC4">
                <ItemIdInfo>
                    <AgencyId>4</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 4 street</Addr1>
                    <City>loc 4 city</City>
                    <StateProvCd>AZ</StateProvCd>
                    <PostalCode>44444-4444</PostalCode>
                </Addr>
                <LocationDesc>loc4desc</LocationDesc>
            </Location>
            <Location id="LOC5">
                <ItemIdInfo>
                    <AgencyId>5</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 5 street</Addr1>
                    <City>loc 5 city</City>
                    <StateProvCd>CA</StateProvCd>
                    <PostalCode>55555-5555</PostalCode>
                </Addr>
                <LocationDesc>loc 5 desc 2</LocationDesc>
            </Location>
            <Location id="LOC6">
                <ItemIdInfo>
                    <AgencyId>6</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 6 street</Addr1>
                    <City>loc 6 city</City>
                    <StateProvCd>CO</StateProvCd>
                    <PostalCode>66666-6666</PostalCode>
                </Addr>
                <LocationDesc>loc 6 desc 2</LocationDesc>
            </Location>
            <Location id="LOC7">
                <ItemIdInfo>
                    <AgencyId>7</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 7 street</Addr1>
                    <City>loc 7 city</City>
                    <StateProvCd>CT</StateProvCd>
                    <PostalCode>77777-7777</PostalCode>
                </Addr>
                <LocationDesc>loc 7 desc 2</LocationDesc>
            </Location>
        </PersUmbrellaPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
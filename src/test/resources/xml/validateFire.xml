<?xml-stylesheet type="text/xsl" href='http://vmrid-apiqia01/BookTransfer/DEVML/WebAPI/scripts/xslt/DFireXMLView.xslt'?>
<ACORD>
    <SignonRq>
        <ClientDt>2016-04-27</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>BookSmart</Org>
            <Name>AgencyPort</Name>
        </ClientApp>
        <SignonTransport>
            <SignonRoleCd>Agent</SignonRoleCd>
            <CustId>
                <SPName>com.agencyport</SPName>
                <CustLoginId>default</CustLoginId>
            </CustId>
        </SignonTransport>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>6F6ECD35BA124E68A42C4FF485D8EB1A</RqUID>
        <DwellFirePolicyQuoteInqRq>
            <RqUID>B05EBC1168984BBEBCBC6E25910A3699</RqUID>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>431683</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>GILBDO1C34</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Applicant Name</CommercialName>
                        </CommlName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>***********</TaxId>
                        </TaxIdentity>
                        <SupplementaryNameInfo>
                            <SupplementaryNameCd>ATTN</SupplementaryNameCd>
                            <SupplementaryName>Applicant Attan</SupplementaryName>
                        </SupplementaryNameInfo>
                        <PersonName>
                            <GivenName>Applicant</GivenName>
                            <Surname>Name</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1></Addr1>
                        <Addr2>Applicant Strt 2</Addr2>
                        <City>Applicant City</City>
                        <StateProvCd>AR</StateProvCd>
                        <PostalCode>60466</PostalCode>
                        <County>Appl County</County>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <PhoneNumber>************</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <PhoneNumber>************</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr>primary@emailaddress</EmailAddr>
                        </EmailInfo>
                        <EmailInfo>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <EmailAddr>secondary@emailaddress</EmailAddr>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt>1970-03-21</BirthDt>
                        <MaritalStatusCd>M</MaritalStatusCd>
                        <OccupationDesc>Appl Occupation</OccupationDesc>
                        <LengthTimeEmployed>
                            <NumUnits>11</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <LengthTimeCurrentOccupation>
                            <NumUnits>10</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeCurrentOccupation>
                        <LengthTimeWithPreviousEmployer>
                            <NumUnits>12</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeWithPreviousEmployer>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <PolicyNumber>DW-COMPLETE-DATA</PolicyNumber>
                <CompanyProductCd>Comp Plan</CompanyProductCd>
                <LOBCd>DFIRE</LOBCd>
                <NAICCd>39012</NAICCd>
                <ContractTerm>
                    <EffectiveDt>2017-03-05</EffectiveDt>
                    <ExpirationDt>2018-03-05</ExpirationDt>
                </ContractTerm>
                <BillingAccountNumber>ACCT NUMBER</BillingAccountNumber>
                <BillingMethodCd>CPB</BillingMethodCd>
                <CurrentTermAmt>
                    <Amt>0</Amt>
                </CurrentTermAmt>
                <MailingResponsibiltyCd>CO</MailingResponsibiltyCd>
                <PayorCd>MORTG</PayorCd>
                <Loss>
                    <Catastrophe>
                        <CatastropheCd>100</CatastropheCd>
                    </Catastrophe>
                    <CoverageCd>DWELL</CoverageCd>
                    <LossDt>2015-12-01</LossDt>
                    <LossDesc>Loc 1 Loss 1 Desc</LossDesc>
                    <TotalPaidAmt>
                        <Amt>1001</Amt>
                    </TotalPaidAmt>
                    <LossEnteredByCd>A</LossEnteredByCd>
                    <InDisputeInd>1</InDisputeInd>
                </Loss>
                <Loss>
                    <Catastrophe>
                        <CatastropheCd>21</CatastropheCd>
                    </Catastrophe>
                    <CoverageCd>PP</CoverageCd>
                    <LossDt>2015-11-01</LossDt>
                    <LossDesc>Loc 2 Loss 1 Desc</LossDesc>
                    <TotalPaidAmt>
                        <Amt>21001</Amt>
                    </TotalPaidAmt>
                    <LossEnteredByCd>A</LossEnteredByCd>
                    <InDisputeInd>1</InDisputeInd>
                </Loss>
                <Loss>
                    <Catastrophe>
                        <CatastropheCd>102</CatastropheCd>
                    </Catastrophe>
                    <CoverageCd>OS</CoverageCd>
                    <LossDt>2015-12-02</LossDt>
                    <LossDesc>Loc 1 Loss 2 Desc</LossDesc>
                    <TotalPaidAmt>
                        <Amt>1002</Amt>
                    </TotalPaidAmt>
                    <LossEnteredByCd>C</LossEnteredByCd>
                    <InDisputeInd>0</InDisputeInd>
                </Loss>
                <Loss>
                    <Catastrophe>
                        <CatastropheCd>22</CatastropheCd>
                    </Catastrophe>
                    <CoverageCd>LOU</CoverageCd>
                    <LossDt>2015-11-02</LossDt>
                    <LossDesc>Loc 2 Loss 2 Desc</LossDesc>
                    <TotalPaidAmt>
                        <Amt>22001</Amt>
                    </TotalPaidAmt>
                    <LossEnteredByCd>C</LossEnteredByCd>
                    <InDisputeInd>0</InDisputeInd>
                </Loss>
                <Loss>
                    <Catastrophe>
                        <CatastropheCd>103</CatastropheCd>
                    </Catastrophe>
                    <CoverageCd>PL</CoverageCd>
                    <LossDt>2015-12-03</LossDt>
                    <LossDesc>Loc 1 Loss 3 Desc</LossDesc>
                    <TotalPaidAmt>
                        <Amt>1003</Amt>
                    </TotalPaidAmt>
                    <LossEnteredByCd>A</LossEnteredByCd>
                    <InDisputeInd>1</InDisputeInd>
                </Loss>
                <Loss>
                    <Catastrophe>
                        <CatastropheCd>23</CatastropheCd>
                    </Catastrophe>
                    <CoverageCd>COV2</CoverageCd>
                    <LossDt>2015-11-03</LossDt>
                    <LossDesc>Loc 2 Loss 2 Desc</LossDesc>
                    <TotalPaidAmt>
                        <Amt>2203</Amt>
                    </TotalPaidAmt>
                    <LossEnteredByCd>A</LossEnteredByCd>
                    <InDisputeInd>0</InDisputeInd>
                </Loss>
                <NumLossesYrs>5</NumLossesYrs>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber>Loc 2 Prior Pol Num</PolicyNumber>
                    <InsurerName>AIG</InsurerName>
                    <ContractTerm>
                        <ExpirationDt>2015-11-26</ExpirationDt>
                    </ContractTerm>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <PaymentPlanCd id="PAYPLANCD1">OT</PaymentPlanCd>
                    <MethodPaymentCd>CYBER</MethodPaymentCd>
                </PaymentOption>
                <MiscParty>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>FINANCE COMPANY</CommercialName>
                            </CommlName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>FC</MiscPartyRoleCd>
                    </MiscPartyInfo>
                </MiscParty>
                <MiscParty>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Named Insured 1</CommercialName>
                            </CommlName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>AdditionalInsured</MiscPartyRoleCd>
                    </MiscPartyInfo>
                </MiscParty>
                <MiscParty>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Named Insured 2</CommercialName>
                            </CommlName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>AdditionalInsured</MiscPartyRoleCd>
                    </MiscPartyInfo>
                </MiscParty>
                <QuestionAnswer>
                    <QuestionCd>GENRL42</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>INMRP06</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>CPR92</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                </QuestionAnswer>
                <AssignedRiskFacilityCd>FACILI</AssignedRiskFacilityCd>
                <PersApplicationInfo>
                    <CurrentResidenceDt>2016-01-01</CurrentResidenceDt>
                    <Addr>
                        <AddrTypeCd>PreviousAddress</AddrTypeCd>
                        <Addr1>Location 2 Previous Street 1</Addr1>
                        <Addr2>Location 2 Previous Street 2</Addr2>
                        <City>Lcoation 2 Previous Ci</City>
                        <StateProvCd>IL</StateProvCd>
                        <PostalCode>60443</PostalCode>
                        <County>loc 2 prev county</County>
                    </Addr>
                    <LengthTimePreviousAddr>
                        <DurationPeriod>
                            <NumUnits>10</NumUnits>
                        </DurationPeriod>
                    </LengthTimePreviousAddr>
                </PersApplicationInfo>
            </PersPolicy>
            <Location id="001">
                <ItemIdInfo>
                    <AgencyId>1</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>StreetAddress</AddrTypeCd>
                    <Addr1>Loc if Diff Street 1</Addr1>
                    <Addr2>Loc if Diff Street 2</Addr2>
                    <City>Loc if Diff City</City>
                    <StateProvCd>AK</StateProvCd>
                    <PostalCode>22222-2222</PostalCode>
                    <County>Diff County</County>
                </Addr>
                <RiskLocationCd>IN</RiskLocationCd>
                <TaxCodeInfo>
                    <TaxCd>taxcd</TaxCd>
                </TaxCodeInfo>
                <AdditionalInterest>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Loc 1 Add Int 1 Name</CommercialName>
                                <IndexName>GMACCH1</IndexName>
                                <SupplementaryNameInfo>
                                    <SupplementaryNameCd>ATTN</SupplementaryNameCd>
                                    <SupplementaryName>Loc 1 Add Int 1 Attn</SupplementaryName>
                                </SupplementaryNameInfo>
                            </CommlName>
                        </NameInfo>
                        <Addr>
                            <AddrTypeCd>MailingAddress</AddrTypeCd>
                            <Addr1>Loc 1 Add Int 1 Strt 1</Addr1>
                            <Addr2>Loc 1 Add Int 1 Strt 2</Addr2>
                            <City>Loc 1 Add Int 1 City</City>
                            <StateProvCd>IL</StateProvCd>
                            <PostalCode>12222-1111</PostalCode>
                        </Addr>
                    </GeneralPartyInfo>
                    <AdditionalInterestInfo>
                        <NatureInterestCd>MORTG</NatureInterestCd>
                        <AccountNumberId>LOC1AOI1LOAN</AccountNumberId>
                        <BillFrequencyCd>R</BillFrequencyCd>
                    </AdditionalInterestInfo>
                </AdditionalInterest>
                <AdditionalInterest>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Loc 1 Add Int 2 Name</CommercialName>
                                <IndexName>UNICHI1</IndexName>
                                <SupplementaryNameInfo>
                                    <SupplementaryNameCd>ATTN</SupplementaryNameCd>
                                    <SupplementaryName>Loc 1 Add Int 2 Attn</SupplementaryName>
                                </SupplementaryNameInfo>
                            </CommlName>
                        </NameInfo>
                        <Addr>
                            <AddrTypeCd>MailingAddress</AddrTypeCd>
                            <Addr1>Loc 1 Add Int 2 Strt 1</Addr1>
                            <Addr2>Loc 1 Add Int 2 Strt 2</Addr2>
                            <City>Loc 1 Add Int 2 City</City>
                            <StateProvCd>IN</StateProvCd>
                            <PostalCode>13333-1111</PostalCode>
                        </Addr>
                    </GeneralPartyInfo>
                    <AdditionalInterestInfo>
                        <NatureInterestCd>LOSSP</NatureInterestCd>
                        <AccountNumberId>LOC1AOI2LOAN</AccountNumberId>
                        <BillFrequencyCd>R</BillFrequencyCd>
                        <CertificateFrequencyCd>R</CertificateFrequencyCd>
                    </AdditionalInterestInfo>
                </AdditionalInterest>
                <FireDistrict>Fire District</FireDistrict>
                <FireDistrictCd>97001</FireDistrictCd>
            </Location>
            <Location>
                <ItemIdInfo>
                    <AgencyId>2</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd>StreetAddress</AddrTypeCd>
                    <Addr1>Location 2 Street 1</Addr1>
                    <Addr2>Location 2 Street 2</Addr2>
                    <City>Location 2 City</City>
                    <StateProvCd>WY</StateProvCd>
                    <PostalCode>22222-2222</PostalCode>
                    <County>loc 2 county</County>
                </Addr>
                <RiskLocationCd>FD</RiskLocationCd>
                <TaxCodeInfo>
                    <TaxCd>TAXC2</TaxCd>
                </TaxCodeInfo>
                <AdditionalInterest>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Loc 2 AOI 1 Name</CommercialName>
                                <IndexName>HARCHI1</IndexName>
                                <SupplementaryNameInfo>
                                    <SupplementaryNameCd>ATTN</SupplementaryNameCd>
                                    <SupplementaryName>Loc 2 AOI 1 Attn</SupplementaryName>
                                </SupplementaryNameInfo>
                            </CommlName>
                        </NameInfo>
                        <Addr>
                            <AddrTypeCd>MailingAddress</AddrTypeCd>
                            <Addr1>Loc 2 AOI 1 Street 1</Addr1>
                            <Addr2>Loc 2 AOI 1 Street 2</Addr2>
                            <City>Loc 2 AOI 1 City</City>
                            <StateProvCd>OK</StateProvCd>
                            <PostalCode>60601-0100</PostalCode>
                        </Addr>
                    </GeneralPartyInfo>
                    <AdditionalInterestInfo>
                        <NatureInterestCd>LIEN</NatureInterestCd>
                        <AccountNumberId>L2AOI1LOAN</AccountNumberId>
                        <BillFrequencyCd>R</BillFrequencyCd>
                        <CertificateFrequencyCd>R</CertificateFrequencyCd>
                    </AdditionalInterestInfo>
                </AdditionalInterest>
                <AdditionalInterest>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Loc 2 AOI 2 Name</CommercialName>
                                <IndexName>FNBDET1</IndexName>
                                <SupplementaryNameInfo>
                                    <SupplementaryNameCd>ATTN</SupplementaryNameCd>
                                    <SupplementaryName>Loc 2 AOI 2 Attn</SupplementaryName>
                                </SupplementaryNameInfo>
                            </CommlName>
                        </NameInfo>
                        <Addr>
                            <AddrTypeCd>MailingAddress</AddrTypeCd>
                            <Addr1>Loc 2 AOI 2 Street 1</Addr1>
                            <Addr2>Loc 2 AOI 2 Street 2</Addr2>
                            <City>Loc 2 AOI 2 City</City>
                            <StateProvCd>MI</StateProvCd>
                            <PostalCode>48226</PostalCode>
                        </Addr>
                    </GeneralPartyInfo>
                    <AdditionalInterestInfo>
                        <NatureInterestCd id="ADINTLOC32">OT</NatureInterestCd>
                        <AccountNumberId>L2SOI2LOAN</AccountNumberId>
                        <BillFrequencyCd>R</BillFrequencyCd>
                        <CertificateFrequencyCd>R</CertificateFrequencyCd>
                    </AdditionalInterestInfo>
                </AdditionalInterest>
                <FireDistrict>Fire dist 2</FireDistrict>
                <FireDistrictCd>22000</FireDistrictCd>
            </Location>
            <DwellFireLineBusiness>
                <LOBCd>DFIRE</LOBCd>
                <Dwell LocationRef="001">
                    <PolicyTypeCd>BR</PolicyTypeCd>
                    <PurchaseDt>2010-04-01</PurchaseDt>
                    <PurchasePriceAmt>
                        <Amt>40004</Amt>
                    </PurchasePriceAmt>
                    <OilStorageTankLocationCd>UG</OilStorageTankLocationCd>
                    <FuelLineLocCd>U</FuelLineLocCd>
                    <WiringTypeCd>Copper</WiringTypeCd>
                    <WiringInspectedDt>2016-01-07</WiringInspectedDt>
                    <HousekeepingConditionCd>GD</HousekeepingConditionCd>
                    <Construction>
                        <ConstructionCd>F</ConstructionCd>
                        <EIFSInstalledYr>2001</EIFSInstalledYr>
                        <SidingCd>S</SidingCd>
                        <YearBuilt>1901</YearBuilt>
                        <FoundationCd>Closed</FoundationCd>
                        <BldgCodeEffectivenessGradeCd>grade</BldgCodeEffectivenessGradeCd>
                        <BldgArea>
                            <NumUnits>4001</NumUnits>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </BldgArea>
                        <BasementArea>
                            <NumUnits>2007</NumUnits>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </BasementArea>
                        <RoofingMaterial>
                            <RoofMaterialCd>ROOFM</RoofMaterialCd>
                        </RoofingMaterial>
                        <StormShuttersCd>A</StormShuttersCd>
                        <HeatingSystemServiceDt>2016-01-04</HeatingSystemServiceDt>
                        <ElectricalPanelCd>CIRC</ElectricalPanelCd>
                        <ElectricalStrength>
                            <NumUnits>31</NumUnits>
                            <UnitMeasurementCd>AMP</UnitMeasurementCd>
                        </ElectricalStrength>
                        <ConstructionReasonCd>R</ConstructionReasonCd>
                    </Construction>
                    <DwellOccupancy>
                        <NumRooms>10</NumRooms>
                        <NumApartments>6</NumApartments>
                        <ResidenceTypeCd>APT</ResidenceTypeCd>
                        <DwellUseCd>1</DwellUseCd>
                        <OccupancyTypeCd id="OCCTYPCDLOC2">TENAN</OccupancyTypeCd>
                        <VisibilityInd>1</VisibilityInd>
                        <VisibleToRoadInd>1</VisibleToRoadInd>
                        <DaytimeOccupancyInd>1</DaytimeOccupancyInd>
                        <LengthTimeRentedToOthers>
                            <NumUnits>40</NumUnits>
                            <UnitMeasurementCd>WEE</UnitMeasurementCd>
                        </LengthTimeRentedToOthers>
                        <NumResidentsInHousehold>11</NumResidentsInHousehold>
                    </DwellOccupancy>
                    <DwellRating>
                        <TerritoryCd>te1</TerritoryCd>
                        <OLAndTCd>PL</OLAndTCd>
                        <ClassSpecificRatedCd>CLR</ClassSpecificRatedCd>
                        <HomeRatingCreditCd>NMSKR</HomeRatingCreditCd>
                        <HomeRatingCreditCd>LIGHT</HomeRatingCreditCd>
                        <HomeRatingCreditCd>MANSE</HomeRatingCreditCd>
                        <HomeRatingCreditCd>OPTE</HomeRatingCreditCd>
                        <HomeRatingCreditCd>LOC1RATCR1</HomeRatingCreditCd>
                        <HomeRatingCreditCd>LOC1RATCR2</HomeRatingCreditCd>
                        <WindClassCd>SWR</WindClassCd>
                    </DwellRating>
                    <BldgProtection>
                        <NumFireDivisions>22</NumFireDivisions>
                        <NumUnitsInFireDivisions>23</NumUnitsInFireDivisions>
                        <FireProtectionClassCd>prot</FireProtectionClassCd>
                        <DistanceToFireStation>
                            <NumUnits>99</NumUnits>
                            <UnitMeasurementCd>SMI</UnitMeasurementCd>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits>98</NumUnits>
                            <UnitMeasurementCd>FOT</UnitMeasurementCd>
                        </DistanceToHydrant>
                        <FireExtinguisherInd>1</FireExtinguisherInd>
                        <ProtectionDeviceBurglarCd>LO</ProtectionDeviceBurglarCd>
                        <ProtectionDeviceFireCd>CN</ProtectionDeviceFireCd>
                        <ProtectionDeviceSmokeCd>DR</ProtectionDeviceSmokeCd>
                        <ProtectionDeviceSprinklerCd>PT</ProtectionDeviceSprinklerCd>
                        <DoorLockCd>DEADB</DoorLockCd>
                    </BldgProtection>
                    <BldgImprovements>
                        <HeatingImprovementCd>P</HeatingImprovementCd>
                        <HeatingImprovementYear>93</HeatingImprovementYear>
                        <PlumbingImprovementCd>C</PlumbingImprovementCd>
                        <PlumbingImprovementYear>92</PlumbingImprovementYear>
                        <RoofingImprovementCd>C</RoofingImprovementCd>
                        <RoofingImprovementYear>94</RoofingImprovementYear>
                        <WiringImprovementCd>P</WiringImprovementCd>
                        <WiringImprovementYear>91</WiringImprovementYear>
                    </BldgImprovements>
                    <DwellInspectionValuation>
                        <EstimatedReplCostAmt>
                            <Amt>40005</Amt>
                        </EstimatedReplCostAmt>
                        <HeatSourcePrimaryCd>E</HeatSourcePrimaryCd>
                        <HeatSourceSupplementalCd>G</HeatSourceSupplementalCd>
                        <NumFamilies>5</NumFamilies>
                        <FireplaceInfo>
                            <NumHearths>3</NumHearths>
                            <NumChimneys>2</NumChimneys>
                        </FireplaceInfo>
                        <FireplaceInfo>
                            <FireplaceTypeCd>MetalInsert</FireplaceTypeCd>
                            <NumFireplaces>1</NumFireplaces>
                        </FireplaceInfo>
                        <FireplaceInfo>
                            <FireplaceTypeCd>PreFabMetal</FireplaceTypeCd>
                            <NumFireplaces>4</NumFireplaces>
                        </FireplaceInfo>
                        <GarageInfo>
                            <SurfaceArea>
                                <NumUnits>4003</NumUnits>
                                <UnitMeasurementCd>FTK</UnitMeasurementCd>
                            </SurfaceArea>
                        </GarageInfo>
                        <MarketValueAmt>
                            <Amt>40001</Amt>
                        </MarketValueAmt>
                    </DwellInspectionValuation>
                    <SwimmingPool>
                        <AboveGroundInd>1</AboveGroundInd>
                        <ApprovedFenceInd>1</ApprovedFenceInd>
                        <DivingBoardInd>1</DivingBoardInd>
                        <SlideInd>1</SlideInd>
                    </SwimmingPool>
                    <Coverage>
                        <CoverageCd>DWELL</CoverageCd>
                        <Limit>
                            <FormatInteger></FormatInteger>
                        </Limit>
                        <Deductible>
                            <FormatInteger>200</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>20</FormatPct>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatInteger>201</FormatInteger>
                            <DeductibleTypeCd>FT</DeductibleTypeCd>
                            <DeductibleAppliesToCd>WindHail</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>21</FormatPct>
                            <DeductibleTypeCd>FT</DeductibleTypeCd>
                            <DeductibleAppliesToCd>WindHail</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatInteger>202</FormatInteger>
                            <DeductibleTypeCd>GR</DeductibleTypeCd>
                            <DeductibleAppliesToCd>Theft</DeductibleAppliesToCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>22</FormatPct>
                            <DeductibleTypeCd>GR</DeductibleTypeCd>
                            <DeductibleAppliesToCd>Theft</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OS</CoverageCd>
                        <Limit>
                            <FormatInteger>1003</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PP</CoverageCd>
                        <Limit>
                            <FormatInteger>1005</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LOU</CoverageCd>
                        <Limit>
                            <FormatInteger>1007</FormatInteger>
                            <ValuationCd>ACT</ValuationCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FRV</CoverageCd>
                        <Limit>
                            <FormatInteger>1011</FormatInteger>
                            <ValuationCd>ACT</ValuationCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ALEXP</CoverageCd>
                        <Limit>
                            <FormatInteger>1013</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PL</CoverageCd>
                        <Limit>
                            <FormatInteger>1015</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>1017</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>HURR</CoverageCd>
                        <Deductible>
                            <FormatInteger>203</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>23</FormatPct>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>HURRA</CoverageCd>
                        <Deductible>
                            <FormatInteger>204</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>24</FormatPct>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BLNKT</CoverageCd>
                        <Limit>
                            <FormatInteger>1009</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FVREP</CoverageCd>
                        <Limit>
                            <FormatPct>1019</FormatPct>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RCD</CoverageCd>
                        <Limit>
                            <FormatInteger>1021</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RCC</CoverageCd>
                        <Limit>
                            <FormatInteger>1023</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OTHDE1</CoverageCd>
                        <Deductible>
                            <FormatInteger>205</FormatInteger>
                            <DeductibleTypeCd>x1</DeductibleTypeCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>25</FormatPct>
                            <DeductibleTypeCd>x1</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OTHDE3</CoverageCd>
                        <Deductible>
                            <FormatInteger>207</FormatInteger>
                            <DeductibleTypeCd>x3</DeductibleTypeCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>27</FormatPct>
                            <DeductibleTypeCd>x3</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OTHDE4</CoverageCd>
                        <Deductible>
                            <FormatInteger>208</FormatInteger>
                            <DeductibleTypeCd>x4</DeductibleTypeCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>28</FormatPct>
                            <DeductibleTypeCd>x4</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OTHDE5</CoverageCd>
                        <Deductible>
                            <FormatInteger>209</FormatInteger>
                            <DeductibleTypeCd>x5</DeductibleTypeCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>29</FormatPct>
                            <DeductibleTypeCd>x5</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OTHDE6</CoverageCd>
                        <Deductible>
                            <FormatInteger>210</FormatInteger>
                            <DeductibleTypeCd>x6</DeductibleTypeCd>
                        </Deductible>
                        <Deductible>
                            <FormatPct>211</FormatPct>
                            <DeductibleTypeCd>x6</DeductibleTypeCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>THFTB</CoverageCd>
                        <Limit>
                            <FormatInteger>301</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WTRPR</CoverageCd>
                        <Limit>
                            <FormatInteger>303</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BOLAW</CoverageCd>
                        <Limit>
                            <FormatInteger>305</FormatInteger>
                            <LimitAppliesToCd>Aggregate</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>307</FormatInteger>
                            <LimitAppliesToCd>Increased</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Perc1</OptionTypeCd>
                            <OptionValue>80</OptionValue>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>DEBRL</CoverageCd>
                        <Limit>
                            <FormatInteger>308</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ERQK</CoverageCd>
                        <Deductible>
                            <FormatInteger>311</FormatInteger>
                        </Deductible>
                        <Deductible>
                            <FormatPct>10</FormatPct>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd>RET</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Perc1</OptionTypeCd>
                            <OptionValue>11</OptionValue>
                        </Option>
                        <TerritoryCd>EQT</TerritoryCd>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>FDC</CoverageCd>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>INFGD</CoverageCd>
                        <Option>
                            <OptionTypeCd>Perc1</OptionTypeCd>
                            <OptionValue>313</OptionValue>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LAC</CoverageCd>
                        <Limit>
                            <FormatInteger>315</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MIN</CoverageCd>
                        <CoverageDesc>Property Description</CoverageDesc>
                        <Limit>
                            <FormatInteger>401</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>M</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>AASPC</CoverageCd>
                        <Limit>
                            <FormatInteger>403</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SEWER</CoverageCd>
                        <Limit>
                            <FormatInteger>405</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>WINDX</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>Yes</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COV1</CoverageCd>
                        <CoverageDesc>Oth Cov 1 Description</CoverageDesc>
                        <Limit>
                            <FormatInteger>501</FormatInteger>
                            <LimitAppliesToCd>ACC</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>502</FormatInteger>
                            <LimitAppliesToCd>PER</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>503</FormatInteger>
                            <DeductibleTypeCd>T1</DeductibleTypeCd>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>O1</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd>O2</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt3</OptionTypeCd>
                            <OptionCd>O3</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>Yes</OptionCd>
                        </Option>
                        <TerritoryCd>TE1</TerritoryCd>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CO</CoverageCd>
                        <CoverageDesc>Oth Cov 2 Description</CoverageDesc>
                        <Limit>
                            <FormatInteger>505</FormatInteger>
                            <LimitAppliesToCd>PER</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>506</FormatInteger>
                            <LimitAppliesToCd>ACC</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>507</FormatInteger>
                            <DeductibleTypeCd>T2</DeductibleTypeCd>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>A1</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd>A2</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt3</OptionTypeCd>
                            <OptionCd>A3</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>No</OptionCd>
                        </Option>
                        <TerritoryCd>TE2</TerritoryCd>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COV3</CoverageCd>
                        <CoverageDesc>Oth Cov 3 Description</CoverageDesc>
                        <Limit>
                            <FormatInteger>509</FormatInteger>
                            <LimitAppliesToCd>APPL1</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>510</FormatInteger>
                            <LimitAppliesToCd>APPL2</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>511</FormatInteger>
                            <DeductibleTypeCd>T3</DeductibleTypeCd>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>B1</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd>B2</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt3</OptionTypeCd>
                            <OptionCd>B3</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>Yes</OptionCd>
                        </Option>
                        <TerritoryCd>TE3</TerritoryCd>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COV4</CoverageCd>
                        <CoverageDesc>Oth Cov 4 Description</CoverageDesc>
                        <Limit>
                            <FormatInteger>513</FormatInteger>
                            <LimitAppliesToCd>APPL3</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>514</FormatInteger>
                            <LimitAppliesToCd>APPL4</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>515</FormatInteger>
                            <DeductibleTypeCd>T4</DeductibleTypeCd>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>C1</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd>C2</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt3</OptionTypeCd>
                            <OptionCd>C4</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>No</OptionCd>
                        </Option>
                        <TerritoryCd>TE4</TerritoryCd>
                    </Coverage>
                    <QuestionAnswer>
                        <QuestionCd>GENRL20</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME03</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL54</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                        <Num>99</Num>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL28</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME09</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL31</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME10</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME11</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME12</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME24</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL43</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME25</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL15</QuestionCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME31</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <DwellStateSupplement>
                        <DistanceToOceanOrOtherBodyWater>
                            <NumUnits>96</NumUnits>
                            <UnitMeasurementCd>FOT</UnitMeasurementCd>
                        </DistanceToOceanOrOtherBodyWater>
                        <SeacoastOrOtherBodyWaterProximityCd>TidalWater</SeacoastOrOtherBodyWaterProximityCd>
                    </DwellStateSupplement>
                    <InspectionInfo>
                        <InspectionCd>AG</InspectionCd>
                        <InspectionDt>2016-01-01</InspectionDt>
                    </InspectionInfo>
                    <PlumbingConditionCd>AV</PlumbingConditionCd>
                    <PlumbingLeaksInd>1</PlumbingLeaksInd>
                    <RoofConditionCd>E</RoofConditionCd>
                </Dwell>
                <Dwell LocationRef="001">
                    <PolicyTypeCd>BR</PolicyTypeCd>
                    <PurchaseDt>1990-02-02</PurchaseDt>
                    <PurchasePriceAmt>
                        <Amt>22000</Amt>
                    </PurchasePriceAmt>
                    <OilStorageTankLocationCd>OAG</OilStorageTankLocationCd>
                    <WiringTypeCd>Aluminum</WiringTypeCd>
                    <WiringInspectedDt>2016-03-28</WiringInspectedDt>
                    <HousekeepingConditionCd>BAVE</HousekeepingConditionCd>
                    <Construction>
                        <ConstructionCd>OT</ConstructionCd>
                        <EIFSInstalledYr>2002</EIFSInstalledYr>
                        <SidingCd>F</SidingCd>
                        <YearBuilt>1920</YearBuilt>
                        <BldgCodeEffectivenessGradeCd>GRADE</BldgCodeEffectivenessGradeCd>
                        <BldgArea>
                            <NumUnits>2000</NumUnits>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </BldgArea>
                        <BasementArea>
                            <NumUnits>0</NumUnits>
                            <UnitMeasurementCd>FTK</UnitMeasurementCd>
                        </BasementArea>
                        <RoofingMaterial>
                            <RoofMaterialCd>ROOFMAT2</RoofMaterialCd>
                        </RoofingMaterial>
                        <StormShuttersCd>B</StormShuttersCd>
                        <ElectricalPanelCd>FUSE</ElectricalPanelCd>
                        <ElectricalStrength>
                            <NumUnits>120</NumUnits>
                            <UnitMeasurementCd>AMP</UnitMeasurementCd>
                        </ElectricalStrength>
                        <ConstructionReasonCd>B</ConstructionReasonCd>
                    </Construction>
                    <DwellOccupancy>
                        <NumRooms>22</NumRooms>
                        <NumApartments>4</NumApartments>
                        <ResidenceTypeCd>CO</ResidenceTypeCd>
                        <DwellUseCd>6</DwellUseCd>
                        <OccupancyTypeCd>VACAN</OccupancyTypeCd>
                        <VisibilityInd>1</VisibilityInd>
                        <DaytimeOccupancyInd>1</DaytimeOccupancyInd>
                        <LengthTimeRentedToOthers>
                            <NumUnits>2</NumUnits>
                            <UnitMeasurementCd>WEE</UnitMeasurementCd>
                        </LengthTimeRentedToOthers>
                        <NumResidentsInHousehold>5</NumResidentsInHousehold>
                    </DwellOccupancy>
                    <DwellRating>
                        <TerritoryCd>te2</TerritoryCd>
                        <OLAndTCd>P2</OLAndTCd>
                        <ClassSpecificRatedCd>S</ClassSpecificRatedCd>
                        <HomeRatingCreditCd>LOC2RATCR1</HomeRatingCreditCd>
                        <HomeRatingCreditCd>LOC2RATCR2</HomeRatingCreditCd>
                        <WindClassCd>OT</WindClassCd>
                    </DwellRating>
                    <BldgProtection>
                        <NumFireDivisions>26</NumFireDivisions>
                        <NumUnitsInFireDivisions>27</NumUnitsInFireDivisions>
                        <FireProtectionClassCd>ptc2</FireProtectionClassCd>
                        <DistanceToFireStation>
                            <NumUnits>240</NumUnits>
                            <UnitMeasurementCd>SMI</UnitMeasurementCd>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits>230</NumUnits>
                            <UnitMeasurementCd>FOT</UnitMeasurementCd>
                        </DistanceToHydrant>
                        <FireExtinguisherInd>0</FireExtinguisherInd>
                        <ProtectionDeviceBurglarCd>CN</ProtectionDeviceBurglarCd>
                        <ProtectionDeviceFireCd>DR</ProtectionDeviceFireCd>
                        <ProtectionDeviceSmokeCd>LO</ProtectionDeviceSmokeCd>
                        <DoorLockCd>SPRNG</DoorLockCd>
                    </BldgProtection>
                    <BldgImprovements>
                        <HeatingImprovementCd>C</HeatingImprovementCd>
                        <HeatingImprovementYear>14</HeatingImprovementYear>
                        <PlumbingImprovementCd>P</PlumbingImprovementCd>
                        <PlumbingImprovementYear>13</PlumbingImprovementYear>
                        <RoofingImprovementCd>P</RoofingImprovementCd>
                        <RoofingImprovementYear>15</RoofingImprovementYear>
                        <WiringImprovementCd>C</WiringImprovementCd>
                        <WiringImprovementYear>12</WiringImprovementYear>
                    </BldgImprovements>
                    <DwellInspectionValuation>
                        <EstimatedReplCostAmt>
                            <Amt>23000</Amt>
                        </EstimatedReplCostAmt>
                        <HeatSourcePrimaryCd>CPI</HeatSourcePrimaryCd>
                        <HeatSourceSupplementalCd>HEATPUMP</HeatSourceSupplementalCd>
                        <NumFamilies>3</NumFamilies>
                        <FireplaceInfo>
                            <NumHearths>0</NumHearths>
                            <NumChimneys>0</NumChimneys>
                        </FireplaceInfo>
                        <FireplaceInfo>
                            <FireplaceTypeCd>MetalInsert</FireplaceTypeCd>
                            <NumFireplaces>0</NumFireplaces>
                        </FireplaceInfo>
                        <FireplaceInfo>
                            <FireplaceTypeCd>PreFabMetal</FireplaceTypeCd>
                            <NumFireplaces>0</NumFireplaces>
                        </FireplaceInfo>
                        <GarageInfo>
                            <SurfaceArea>
                                <NumUnits>2100</NumUnits>
                                <UnitMeasurementCd>FTK</UnitMeasurementCd>
                            </SurfaceArea>
                        </GarageInfo>
                        <MarketValueAmt>
                            <Amt>200000</Amt>
                        </MarketValueAmt>
                    </DwellInspectionValuation>
                    <SwimmingPool/>
                    <Coverage>
                        <CoverageCd>LOU</CoverageCd>
                        <Limit>
                            <FormatInteger>2203</FormatInteger>
                            <ValuationCd>ACT</ValuationCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>THFTB</CoverageCd>
                        <Limit>
                            <FormatInteger>2206</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Incl</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CO21</CoverageCd>
                        <CoverageDesc>Loc 2 Other Cov 1 Desc</CoverageDesc>
                        <Limit>
                            <FormatInteger>22010</FormatInteger>
                            <LimitAppliesToCd>per</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>22011</FormatInteger>
                            <LimitAppliesToCd>acc</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>22012</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                        </Deductible>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>G1</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd>G2</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt3</OptionTypeCd>
                            <OptionCd>G3</OptionCd>
                        </Option>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>Yes</OptionCd>
                        </Option>
                        <TerritoryCd>CT2</TerritoryCd>
                    </Coverage>
                    <QuestionAnswer>
                        <QuestionCd>GENRL20</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME03</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL54</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                        <Num>0</Num>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL28</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME09</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL31</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME10</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME11</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME12</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME24</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL43</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME25</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>GENRL15</QuestionCd>
                        <YesNoCd>NO</YesNoCd>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>HOME31</QuestionCd>
                        <YesNoCd>YES</YesNoCd>
                    </QuestionAnswer>
                    <DwellStateSupplement>
                        <DistanceToOceanOrOtherBodyWater>
                            <NumUnits>210</NumUnits>
                            <UnitMeasurementCd>SMI</UnitMeasurementCd>
                        </DistanceToOceanOrOtherBodyWater>
                        <SeacoastOrOtherBodyWaterProximityCd>TidalWater</SeacoastOrOtherBodyWaterProximityCd>
                    </DwellStateSupplement>
                    <InspectionInfo>
                        <InspectionCd>AG</InspectionCd>
                    </InspectionInfo>
                    <PlumbingConditionCd>GD</PlumbingConditionCd>
                    <PlumbingLeaksInd>1</PlumbingLeaksInd>
                    <RoofConditionCd>AV</RoofConditionCd>
                </Dwell>
                <QuestionAnswer>
                    <QuestionCd>GENRL22</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL06</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL23</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL26</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL59</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL63</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
            </DwellFireLineBusiness>
            <RemarkText IdRef="PAYPLANCD1">Pay Plan</RemarkText>
            <RemarkText IdRef="ADINTLOC32">LO</RemarkText>
            <RemarkText IdRef="OCCTYPCDLOC2">occupancy</RemarkText>
        </DwellFirePolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
<ACORD>
    <BookTransferMeta>
        <creation>
            <type>xmlExtraction</type>
            <origin>SLIM</origin>
        </creation>
    </BookTransferMeta>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName>AMSServices.com</SPName>
                <CustPermId/>
                <CustLoginId><EMAIL></CustLoginId>
            </CustId>
            <CustPswd>
                <EncryptionTypeCd>NONE</EncryptionTypeCd>
                <Pswd/>
            </CustPswd>
        </SignonPswd>
        <ClientDt>11/15/2022 8:35 PM</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>AMS Services</Org>
            <Name>Transit</Name>
            <Version>V2.5.5</Version>
        </ClientApp>
        <ProxyClient>
            <Org>BCFTech</Org>
            <Name>TransmitXML</Name>
            <Version>V1.00</Version>
        </ProxyClient>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>1697A9A1-8364-4936-97FF-2B1E8595180C</RqUID>
        <DwellFirePolicyQuoteInqRq>
            <RqUID>400C6243-B6E8-4C98-A371-56C887228676</RqUID>
            <TransactionRequestDt>2022-11-15</TransactionRequestDt>
            <TransactionEffectiveDt>2022-05-26</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <BroadLOBCd>P</BroadLOBCd>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>999999</ContractNumber>
                    <ProducerSubCode>01</ProducerSubCode>
                    <ProducerRoleCd>Agency</ProducerRoleCd>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>LITARO1</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>ROBERT DALE LITAKER &amp; JULIE ANNA LITAKER LITAKER</CommercialName>
                        </CommlName>
                        <LegalEntityCd>IN</LegalEntityCd>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>240 RED OAK LANE</Addr1>
                        <City>SALISBURY</City>
                        <StateProvCd>NC</StateProvCd>
                        <PostalCode>28146</PostalCode>
                    </Addr>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy id="PolicyLevel_M941">
                <PolicyNumber>DF 3020284</PolicyNumber>
                <CompanyProductCd>DF</CompanyProductCd>
                <LOBCd>DFIRE</LOBCd>
                <LOBSubCd>VOL</LOBSubCd>
                <NAICCd>25127</NAICCd>
                <ContractTerm>
                    <EffectiveDt>2022-05-26</EffectiveDt>
                    <ExpirationDt>2023-05-26</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                        <UnitMeasurementCd>MON</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <BillingAccountNumber>DF 3020284</BillingAccountNumber>
                <BillingMethodCd>CPB</BillingMethodCd>
                <CurrentTermAmt>
                    <Amt>1207.00</Amt>
                </CurrentTermAmt>
                <LanguageCd>E</LanguageCd>
                <OriginalInceptionDt>1995-05-26</OriginalInceptionDt>
                <PayorCd>IN</PayorCd>
                <Form AggregateRef="DW1_M941">
                    <FormNumber>DP0003</FormNumber>
                    <FormName>DWELLING PROPERTY 3 SPECIAL FORM</FormName>
                    <EditionDt>2002-12-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form AggregateRef="DW1_M941">
                    <FormNumber>DP3232</FormNumber>
                    <FormName>SPECIAL PROVISIONS - NC</FormName>
                    <EditionDt>2021-01-01</EditionDt>
                    <IterationNumber>002</IterationNumber>
                </Form>
                <Form AggregateRef="DW1_M941">
                    <FormNumber>FI36NC</FormNumber>
                    <FormName>DWELLING FIRE COVER INDEX NC</FormName>
                    <EditionDt>2010-07-01</EditionDt>
                    <IterationNumber>003</IterationNumber>
                </Form>
                <Form AggregateRef="DW2_M941">
                    <FormNumber>DP3250</FormNumber>
                    <FormName>PREMISES ALARM OR FIRE PROT SYS NC</FormName>
                    <EditionDt>2007-09-01</EditionDt>
                    <IterationNumber>004</IterationNumber>
                </Form>
                <Form AggregateRef="DW3_M941">
                    <FormNumber>DP3250</FormNumber>
                    <FormName>PREMISES ALARM OR FIRE PROT SYS NC</FormName>
                    <EditionDt>2007-09-01</EditionDt>
                    <IterationNumber>005</IterationNumber>
                </Form>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber>DF 3020284</PolicyNumber>
                    <LOBCd>DFIRE</LOBCd>
                    <InsurerName>STATE AUTO</InsurerName>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <PaymentPlanCd>AN</PaymentPlanCd>
                    <DayMonthDue>26</DayMonthDue>
                </PaymentOption>
                <PersApplicationInfo>
                    <InsuredOrPrincipal/>
                    <ResidenceTypeCd>DW</ResidenceTypeCd>
                </PersApplicationInfo>
                <ControllingStateProvCd>NC</ControllingStateProvCd>
            </PersPolicy>
            <Location id="L1_M941">
                <ItemIdInfo>
                    <AgencyId>0001</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>133 OAKWOOD AVE</Addr1>
                    <City>SALISBURY</City>
                    <StateProvCd>NC</StateProvCd>
                    <PostalCode>28146</PostalCode>
                </Addr>
                <RiskLocationCd>INN</RiskLocationCd>
                <FireDistrict>SALISBURY CITY</FireDistrict>
            </Location>
            <Location id="L2_M941">
                <ItemIdInfo>
                    <AgencyId>0002</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>620 MORLAN PARK RD</Addr1>
                    <City>SALISBURY</City>
                    <StateProvCd>NC</StateProvCd>
                    <PostalCode>28146</PostalCode>
                </Addr>
                <RiskLocationCd>INN</RiskLocationCd>
                <FireDistrict>SALISBURY CITY</FireDistrict>
            </Location>
            <Location id="L3_M941">
                <ItemIdInfo>
                    <AgencyId>0003</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>432 MORLAN PARK RD</Addr1>
                    <City>SALISBURY</City>
                    <StateProvCd>NC</StateProvCd>
                    <PostalCode>28146</PostalCode>
                </Addr>
                <RiskLocationCd>INN</RiskLocationCd>
                <FireDistrict>SALISBURY</FireDistrict>
            </Location>
            <DwellFireLineBusiness>
                <LOBCd>DFIRE</LOBCd>
                <LOBSubCd>VOL</LOBSubCd>
                <NAICCd>25127</NAICCd>
                <CompanyProductCd>DF</CompanyProductCd>
                <CurrentTermAmt>
                    <Amt>1207.00</Amt>
                </CurrentTermAmt>
                <RateEffectiveDt>2022-05-26</RateEffectiveDt>
                <Dwell LocationRef="L1_M941" id="DW1_M941">
                    <PolicyTypeCd>SP</PolicyTypeCd>
                    <Construction>
                        <ConstructionCd>F</ConstructionCd>
                        <YearBuilt>1940</YearBuilt>
                        <FoundationCd>NONE</FoundationCd>
                    </Construction>
                    <DwellOccupancy>
                        <ResidenceTypeCd>DW</ResidenceTypeCd>
                        <DwellUseCd>1</DwellUseCd>
                        <OccupancyTypeCd>TENAN</OccupancyTypeCd>
                    </DwellOccupancy>
                    <DwellRating>
                        <TerritoryCd>320</TerritoryCd>
                        <ClassSpecificRatedCd>CLR</ClassSpecificRatedCd>
                        <RatingMethodCd>CLR</RatingMethodCd>
                    </DwellRating>
                    <BldgProtection>
                        <FireProtectionClassCd>01</FireProtectionClassCd>
                        <DistanceToFireStation>
                            <NumUnits>3</NumUnits>
                            <UnitMeasurementCd>SMI</UnitMeasurementCd>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits>500</NumUnits>
                            <UnitMeasurementCd>LF</UnitMeasurementCd>
                        </DistanceToHydrant>
                    </BldgProtection>
                    <DwellInspectionValuation>
                        <NumFamilies>1</NumFamilies>
                    </DwellInspectionValuation>
                    <Coverage btId="e6cd6127-91ac-4fcc-be4a-c9961ec28ceb">
                        <CoverageCd>DWELL</CoverageCd>
                        <Limit>
                            <FormatInteger>95000</FormatInteger>
                            <LimitAppliesToCd>Coverage</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="a2267d90-82f3-440d-8e35-057fbf68db5d">
                            <FormatInteger>1000</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>362.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </Dwell>
                <Dwell LocationRef="L2_M941" id="DW2_M941">
                    <PolicyTypeCd>SP</PolicyTypeCd>
                    <Construction>
                        <ConstructionCd>F</ConstructionCd>
                        <YearBuilt>1955</YearBuilt>
                        <FoundationCd>NONE</FoundationCd>
                    </Construction>
                    <DwellOccupancy>
                        <ResidenceTypeCd>DW</ResidenceTypeCd>
                        <DwellUseCd>1</DwellUseCd>
                        <OccupancyTypeCd>TENAN</OccupancyTypeCd>
                    </DwellOccupancy>
                    <DwellRating>
                        <TerritoryCd>320</TerritoryCd>
                        <ClassSpecificRatedCd>CLR</ClassSpecificRatedCd>
                        <RatingMethodCd>CLR</RatingMethodCd>
                    </DwellRating>
                    <BldgProtection>
                        <FireProtectionClassCd>01</FireProtectionClassCd>
                        <DistanceToFireStation>
                            <NumUnits>3</NumUnits>
                            <UnitMeasurementCd>SMI</UnitMeasurementCd>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits>1000</NumUnits>
                            <UnitMeasurementCd>LF</UnitMeasurementCd>
                        </DistanceToHydrant>
                    </BldgProtection>
                    <DwellInspectionValuation>
                        <NumFamilies>1</NumFamilies>
                    </DwellInspectionValuation>
                    <Coverage btId="5be2fa12-3b26-410d-9e06-9826286918bc">
                        <CoverageCd>TEST</CoverageCd>
                        <Limit>
                            <FormatInteger>110400</FormatInteger>
                            <LimitAppliesToCd>Coverage</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="148de20b-bc69-4084-8538-bc1e3b897a20">
                            <FormatInteger>1000</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>415.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="d9580096-956e-42f2-89b2-9dd090055714">
                        <CoverageCd>ALARM</CoverageCd>
                        <Option>
                            <OptionTypeCd>NumV1</OptionTypeCd>
                            <OptionValue>5.0</OptionValue>
                        </Option>
                        <CurrentTermAmt>
                            <Amt>-21.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </Dwell>
                <Dwell LocationRef="L3_M941" id="DW3_M941">
                    <PolicyTypeCd>SP</PolicyTypeCd>
                    <Construction>
                        <ConstructionCd>F</ConstructionCd>
                        <YearBuilt>1960</YearBuilt>
                        <FoundationCd>NONE</FoundationCd>
                    </Construction>
                    <DwellOccupancy>
                        <ResidenceTypeCd>DW</ResidenceTypeCd>
                        <DwellUseCd>1</DwellUseCd>
                        <OccupancyTypeCd>TENAN</OccupancyTypeCd>
                    </DwellOccupancy>
                    <DwellRating>
                        <TerritoryCd>320</TerritoryCd>
                        <ClassSpecificRatedCd>CLR</ClassSpecificRatedCd>
                        <RatingMethodCd>CLR</RatingMethodCd>
                    </DwellRating>
                    <BldgProtection>
                        <FireProtectionClassCd>01</FireProtectionClassCd>
                        <DistanceToFireStation>
                            <NumUnits>3</NumUnits>
                            <UnitMeasurementCd>SMI</UnitMeasurementCd>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits>500</NumUnits>
                            <UnitMeasurementCd>LF</UnitMeasurementCd>
                        </DistanceToHydrant>
                    </BldgProtection>
                    <DwellInspectionValuation>
                        <NumFamilies>1</NumFamilies>
                    </DwellInspectionValuation>
                    <Coverage btId="cb109c96-4036-4f32-82f5-01e3eca3cd03">
                        <CoverageCd>DWELL</CoverageCd>
                        <Limit>
                            <FormatInteger>126700</FormatInteger>
                            <LimitAppliesToCd>Coverage</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="c4e678b6-f4d3-43b3-9c94-9d34989a59b9">
                            <FormatInteger>1000</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>475.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="be3a9568-abaa-4f4e-9d5b-b884f41dde42">
                        <CoverageCd>*ALM2</CoverageCd>
                        <Option>
                            <OptionTypeCd>NumV1</OptionTypeCd>
                            <OptionValue>5.0</OptionValue>
                        </Option>
                        <CurrentTermAmt>
                            <Amt>-24.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                </Dwell>
            </DwellFireLineBusiness>
            <RemarkText IdRef="PolicyLevel_M941">CREDIT SCORE INFORMATION: AUTHORIZATION CODE:               REFERENCE NO:
            </RemarkText>
        </DwellFirePolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
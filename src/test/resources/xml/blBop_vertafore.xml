<?xml version="1.0" encoding="UTF-8"?>
<?ACORD version="1.3.1"?>
<ACORD>
	<SignonRq>
		<SignonPswd>
			<CustId>
				<SPName>BCFTechnology.com</SPName>
				<CustPermId>test</CustPermId>
				<CustLoginId>test</CustLoginId>
			</CustId>
			<CustPswd>
				<EncryptionTypeCd>NONE</EncryptionTypeCd>
				<Pswd>test</Pswd>
			</CustPswd>
		</SignonPswd>
		<ClientDt>2018-11-09T09:53:45</ClientDt>
		<CustLangPref>en-US</CustLangPref>
		<ClientApp>
			<Org>AMS Services</Org>
			<Name>AMS360</Name>
			<Version>V1.3</Version>
		</ClientApp>
		<ProxyClient>
			<Org>BCFTech</Org>
			<Name>TransmitXML</Name>
			<Version>V1.00</Version>
		</ProxyClient>
	</SignonRq>
	<InsuranceSvcRq>
		<RqUID>E6FD34FD-C4B2-465B-827B-C5893D7238A1</RqUID>
		<BOPPolicyQuoteInqRq>
			<RqUID>82DC0C8F-0FAC-4D4D-80D3-5CC3480A9367</RqUID>
			<TransactionRequestDt>2018-11-09T10:52:34</TransactionRequestDt>
			<TransactionEffectiveDt>2018-12-01</TransactionEffectiveDt>
			<CurCd>USD</CurCd>
			<Producer>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>Carrier Database</CommercialName>
						</CommlName>
					</NameInfo>
					<Addr>
						<AddrTypeCd>StreetAddress</AddrTypeCd>
						<Addr1>123 Main Street</Addr1>
						<Addr2>ADDR2</Addr2>
						<City>Bothell</City>
						<StateProvCd>WA</StateProvCd>
						<PostalCode>97202</PostalCode>
					</Addr>
					<Communications>
						<PhoneInfo>
							<PhoneTypeCd>Phone</PhoneTypeCd>
							<CommunicationUseCd>Business</CommunicationUseCd>
							<PhoneNumber>******-2436205</PhoneNumber>
						</PhoneInfo>
					</Communications>
				</GeneralPartyInfo>
				<ProducerInfo>
					<ContractNumber/>
					<ProducerRoleCd>Agency</ProducerRoleCd>
				</ProducerInfo>
			</Producer>
			<InsuredOrPrincipal>
				<ItemIdInfo>
					<AgencyId>00012345</AgencyId>
				</ItemIdInfo>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>New BOP 56-1</CommercialName>
						</CommlName>
						<LegalEntityCd>IN</LegalEntityCd>
					</NameInfo>
					<Addr>
						<AddrTypeCd>MailingAddress</AddrTypeCd>
						<Addr1>123 Main Street</Addr1>
						<City>WICHITA</City>
						<StateProvCd>KS</StateProvCd>
						<PostalCode>672289633</PostalCode>
					</Addr>
				</GeneralPartyInfo>
				<InsuredOrPrincipalInfo>
					<InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
				</InsuredOrPrincipalInfo>
			</InsuredOrPrincipal>
			<InsuredOrPrincipal>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>New BOP 56-1</CommercialName>
						</CommlName>
					</NameInfo>
				</GeneralPartyInfo>
				<InsuredOrPrincipalInfo>
					<InsuredOrPrincipalRoleCd>DEC</InsuredOrPrincipalRoleCd>
				</InsuredOrPrincipalInfo>
			</InsuredOrPrincipal>
			<CommlPolicy id="PolicyLevel">
				<PolicyNumber>ENP 1234567-1</PolicyNumber>
				<LOBCd>BOP</LOBCd>
				<ContractTerm>
					<EffectiveDt>2018-12-01</EffectiveDt>
					<ExpirationDt>2019-12-01</ExpirationDt>
					<DurationPeriod>
						<NumUnits>12</NumUnits>
						<UnitMeasurementCd>MON</UnitMeasurementCd>
					</DurationPeriod>
				</ContractTerm>
				<BillingMethodCd>CPB</BillingMethodCd>
				<Form>
					<FormNumber>PB1203</FormNumber>
					<FormName>LOSS PAYABLE PROVISIONS</FormName>
					<EditionDt>2001-01-01</EditionDt>
					<IterationNumber>001</IterationNumber>
				</Form>
				<Form>
					<FormNumber>PB0006</FormNumber>
					<FormName>PREMIER BUSINESSOWNERS LIABILITY COVERAG</FormName>
					<EditionDt>2001-01-01</EditionDt>
					<IterationNumber>002</IterationNumber>
				</Form>
				<Form>
					<FormNumber>PB9015</FormNumber>
					<FormName>KANSAS AMENDATORY ENDORSEMENT</FormName>
					<EditionDt>2005-01-01</EditionDt>
					<IterationNumber>003</IterationNumber>
				</Form>
				<Form>
					<FormNumber>PB2999</FormNumber>
					<FormName>EXCLUSION - FUNGI OR BACTERIA</FormName>
					<EditionDt>2002-01-01</EditionDt>
					<IterationNumber>004</IterationNumber>
				</Form>
				<Form>
					<FormNumber>LI0021</FormNumber>
					<FormName>NUCLEAR ENERGY LIABILITY EXCLUSION</FormName>
					<EditionDt>2001-01-01</EditionDt>
					<IterationNumber>005</IterationNumber>
				</Form>
				<Form>
					<FormNumber>IN7291</FormNumber>
					<FormName>POTENTIAL RESTRICTIONS OF TERRORISM COVE</FormName>
					<EditionDt>2004-10-01</EditionDt>
					<IterationNumber>006</IterationNumber>
				</Form>
				<Form>
					<FormNumber>PB5440</FormNumber>
					<FormName>DEDUCTIBLE - WINDSTORM OR HAIL CAUSES OF</FormName>
					<EditionDt>2001-01-01</EditionDt>
					<IterationNumber>007</IterationNumber>
				</Form>
				<Form>
					<FormNumber>PB0009</FormNumber>
					<FormName>PREMIER BUSINESSOWNERS COMMON POLICY CON</FormName>
					<EditionDt>2001-01-01</EditionDt>
					<IterationNumber>008</IterationNumber>
				</Form>
				<Form>
					<FormNumber>PB0564</FormNumber>
					<FormName>CONDITIONAL EXCLUSION OF TERRORISM</FormName>
					<EditionDt>2005-01-01</EditionDt>
					<IterationNumber>009</IterationNumber>
				</Form>
				<Form>
					<FormNumber>PB8102</FormNumber>
					<FormName>PREMIER BUSINESSOWNERS POLICY - SCHEDULE</FormName>
					<EditionDt>2001-01-01</EditionDt>
					<IterationNumber>010</IterationNumber>
				</Form>
				<Form>
					<FormNumber>PB0002</FormNumber>
					<FormName>PREMIER BUSINESSOWNERS PROPERTY COVERAGE</FormName>
					<EditionDt>2001-01-01</EditionDt>
					<IterationNumber>011</IterationNumber>
				</Form>
				<Form>
					<FormNumber>PB0534</FormNumber>
					<FormName>EXCLUSION - WAR AND ACTS OF TERRORISM</FormName>
					<EditionDt>2003-06-01</EditionDt>
					<IterationNumber>012</IterationNumber>
				</Form>
				<MiscParty>
					<GeneralPartyInfo>
						<NameInfo>
							<CommlName>
								<CommercialName>AMS Test Company</CommercialName>
							</CommlName>
						</NameInfo>
					</GeneralPartyInfo>
					<MiscPartyInfo>
						<MiscPartyRoleCd>A</MiscPartyRoleCd>
					</MiscPartyInfo>
				</MiscParty>
				<MiscParty>
					<GeneralPartyInfo>
						<NameInfo>
							<CommlName>
								<CommercialName>TNow Test Company</CommercialName>
							</CommlName>
						</NameInfo>
					</GeneralPartyInfo>
					<MiscPartyInfo>
						<MiscPartyRoleCd>C</MiscPartyRoleCd>
					</MiscPartyInfo>
				</MiscParty>
				<CommlPolicySupplement>
					<BusinessStartDt>2003-01-01</BusinessStartDt>
					<OperationsDesc>VETERINARY CLINIC</OperationsDesc>
					<LengthTimeInBusiness>
						<NumUnits>15</NumUnits>
						<UnitMeasurementCd>ANN</UnitMeasurementCd>
					</LengthTimeInBusiness>
				</CommlPolicySupplement>
				<ControllingStateProvCd>KS</ControllingStateProvCd>
				<CurrentTermAmt>
					<Amt>0</Amt>
				</CurrentTermAmt>
			</CommlPolicy>
			<Location id="L1">
				<ItemIdInfo>
					<AgencyId>0001</AgencyId>
				</ItemIdInfo>
				<Addr>
					<Addr1>456 Main Street</Addr1>
					<City>ROSALIA</City>
					<StateProvCd>KS</StateProvCd>
					<PostalCode>*********</PostalCode>
					<County>Butler</County>
				</Addr>
				<SubLocation id="L1S1">
					<ItemIdInfo>
						<AgencyId>001</AgencyId>
					</ItemIdInfo>
					<SubLocationDesc>ONE STORY MASONRY/NON-COMBUSTIBLE BUILDING</SubLocationDesc>
					<Addr>
						<Addr1>678 Main Street</Addr1>
						<City>ROSALIA</City>
						<StateProvCd>KS</StateProvCd>
						<PostalCode>*********</PostalCode>
					</Addr>
				</SubLocation>
			</Location>
			<BOPLineBusiness>
				<LOBCd>BOP</LOBCd>
				<RateEffectiveDt>2018-12-01</RateEffectiveDt>
				<PropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>ACCTS</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>10000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>ACCTS</CoverageCd>
							<Limit>
								<FormatInteger>10000</FormatInteger>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>BPPSP</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>15000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>BPPSP</CoverageCd>
							<Limit>
								<FormatInteger>15000</FormatInteger>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>DFORG</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>2500</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>DFORG</CoverageCd>
							<Limit>
								<FormatInteger>2500</FormatInteger>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>MNSOF</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>10000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>MNSOF</CoverageCd>
							<Limit>
								<FormatInteger>10000</FormatInteger>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>MNSON</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>10000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>MNSON</CoverageCd>
							<Limit>
								<FormatInteger>10000</FormatInteger>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>PP</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>317700</Amt>
						</ItemValueAmt>
						<ClassSpecificRatedCd>CLR</ClassSpecificRatedCd>
						<CommlCoverage>
							<CoverageCd>PP</CoverageCd>
							<Limit>
								<FormatInteger>317700</FormatInteger>
								<ValuationCd>RC</ValuationCd>
							</Limit>
							<Deductible>
								<FormatInteger>1000</FormatInteger>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
							<Limit>
								<FormatPct>4.0</FormatPct>
							</Limit>
							<CurrentTermAmt>
								<Amt>656.00</Amt>
							</CurrentTermAmt>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>PINTR</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>15000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>PINTR</CoverageCd>
							<Limit>
								<FormatInteger>15000</FormatInteger>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>SIGN</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>2500</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>SIGN</CoverageCd>
							<Limit>
								<FormatInteger>2500</FormatInteger>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>SBBAS</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>50000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>SBBAS</CoverageCd>
							<Limit>
								<FormatInteger>50000</FormatInteger>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
							<CurrentTermAmt>
								<Amt>72.00</Amt>
							</CurrentTermAmt>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>TSP</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>10000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>TSP</CoverageCd>
							<Limit>
								<FormatInteger>10000</FormatInteger>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>PAPER</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>10000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>PAPER</CoverageCd>
							<Limit>
								<FormatInteger>10000</FormatInteger>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlCoverage>
						<CoverageCd>PPAOL</CoverageCd>
						<Limit>
							<FormatInteger>15000</FormatInteger>
						</Limit>
						<Deductible>
							<FormatInteger>1000</FormatInteger>
						</Deductible>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>LAWCN</CoverageCd>
						<Limit>
							<FormatInteger>10000</FormatInteger>
						</Limit>
						<Deductible>
							<FormatInteger>1000</FormatInteger>
						</Deductible>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>SDB</CoverageCd>
						<Limit>
							<FormatInteger>5000</FormatInteger>
						</Limit>
						<Limit>
							<FormatInteger>25000</FormatInteger>
						</Limit>
						<Deductible>
							<FormatInteger>1000</FormatInteger>
						</Deductible>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>TGLAS</CoverageCd>
						<Limit>
							<FormatInteger>1000</FormatInteger>
						</Limit>
						<Deductible>
							<FormatInteger>250</FormatInteger>
						</Deductible>
					</CommlCoverage>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>ACCTS</SubjectInsuranceCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>BPPSP</SubjectInsuranceCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>DFORG</SubjectInsuranceCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>MNSOF</SubjectInsuranceCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>MNSON</SubjectInsuranceCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>PP</SubjectInsuranceCd>
							<ValuationCd>RC</ValuationCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>PINTR</SubjectInsuranceCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>SIGN</SubjectInsuranceCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>SBBAS</SubjectInsuranceCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>TSP</SubjectInsuranceCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>PAPER</SubjectInsuranceCd>
						</StatementValuesInfo>
					</StatementValues>
				</PropertyInfo>
				<LiabilityInfo>
					<CommlCoverage>
						<CoverageCd>FIRDM</CoverageCd>
						<Limit>
							<FormatInteger>300000</FormatInteger>
						</Limit>
						<Deductible>
							<FormatInteger>1000</FormatInteger>
						</Deductible>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>EAOCC</CoverageCd>
						<Limit>
							<FormatInteger>1000000</FormatInteger>
							<LimitAppliesToCd>PerOcc</LimitAppliesToCd>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>PIADV</CoverageCd>
						<Limit>
							<FormatInteger>1000000</FormatInteger>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>GENAG</CoverageCd>
						<Limit>
							<FormatInteger>2000000</FormatInteger>
							<LimitAppliesToCd>Aggregate</LimitAppliesToCd>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>PRDCO</CoverageCd>
						<Limit>
							<FormatInteger>2000000</FormatInteger>
							<LimitAppliesToCd>Aggregate</LimitAppliesToCd>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>MEDEX</CoverageCd>
						<Limit>
							<FormatInteger>5000</FormatInteger>
							<LimitAppliesToCd>PerPerson</LimitAppliesToCd>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>BIALS</CoverageCd>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>MPD</CoverageCd>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>IRPM</CoverageCd>
					</CommlCoverage>
					<GeneralLiabilityClassification LocationRef="L1">
						<CommlCoverage>
							<CoverageCd>PREM</CoverageCd>
							<CurrentTermAmt>
								<Amt>30.00</Amt>
							</CurrentTermAmt>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>PRDCO</CoverageCd>
							<CurrentTermAmt>
								<Amt>50.00</Amt>
							</CurrentTermAmt>
						</CommlCoverage>
						<ClassCd>68493</ClassCd>
						<ClassCdDesc>loc 1 class</ClassCdDesc>
						<Exposure>100000</Exposure>
						<TerritoryCd>562</TerritoryCd>
						<PremiumBasisCd>AREA</PremiumBasisCd>
						<PremOpRate>0.3</PremOpRate>
						<ProdRate>0.5</ProdRate>
					</GeneralLiabilityClassification>
				</LiabilityInfo>
				<CurrentTermAmt>
					<Amt>0</Amt>
				</CurrentTermAmt>
			</BOPLineBusiness>
			<CommlSubLocation LocationRef="L1" SubLocationRef="L1S1">
				<InterestCd>OWNER</InterestCd>
				<Construction>
					<YearBuilt>2000</YearBuilt>
					<BldgArea>
						<NumUnits>800</NumUnits>
						<UnitMeasurementCd>FTK</UnitMeasurementCd>
					</BldgArea>
					<NumStories>1</NumStories>
				</Construction>
				<BldgProtection>
					<FireProtectionClassCd>9</FireProtectionClassCd>
				</BldgProtection>
			</CommlSubLocation>
		</BOPPolicyQuoteInqRq>
	</InsuranceSvcRq>
</ACORD>

<?xml version="1.0" encoding="utf-8"?>
<ACORD>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName>com.AMS</SPName>
                <CustPermId/>
                <CustLoginId><EMAIL></CustLoginId>
            </CustId>
            <CustPswd>
                <EncryptionTypeCd/>
                <Pswd/>
            </CustPswd>
        </SignonPswd>
        <ClientDt>6/23/2017 1:28 PM</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>AMS</Org>
            <Name>AMS 360</Name>
            <Version>2.0</Version>
        </ClientApp>
        <ProxyClient>
            <Org>Vertafore</Org>
            <Name>AgencyBookRoll</Name>
            <Version>V1.00</Version>
        </ProxyClient>
    </SignonRq>
    <InsuranceSvcRq BypassScrubbing="SkipNaming">
        <RqUID>D25EDEA4-9AA0-4CBC-AE5F-70149F5217EC</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <RqUID>CB6A99B5-12E7-47CF-8F5E-B1B0B93030D3</RqUID>
            <TransactionRequestDt>2017-05-01T14:17:00</TransactionRequestDt>
            <TransactionEffectiveDt>2017-05-01</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>USI Insurance Svcs LLC, PL</CommercialName>
                        </CommlName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>StreetAddress</AddrTypeCd>
                        <Addr1>312 Elm Street 24th Floor</Addr1>
                        <City>Cincinnati</City>
                        <StateProvCd>OH</StateProvCd>
                        <PostalCode>45202</PostalCode>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Business</CommunicationUseCd>
                            <PhoneNumber>******-8526301</PhoneNumber>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>
                <ProducerInfo>
                    <ContractNumber>318905</ContractNumber>
                    <ProducerRoleCd>Agency</ProducerRoleCd>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>1403952</AgencyId>
                    <OtherIdentifier>
                        <OtherIdTypeCd>Insured</OtherIdTypeCd>
                        <OtherId>MULHEJOA</OtherId>
                    </OtherIdentifier>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>DONNA BAYER</CommercialName>
                        </CommlName>
                        <LegalEntityCd>IN</LegalEntityCd>
                        <SupplementaryNameInfo>
                            <SupplementaryName>DONNA BAYER</SupplementaryName>
                        </SupplementaryNameInfo>
                        <PersonName>
                            <GivenName>DONNA</GivenName>
                            <Surname>BAYER</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>2400 ELMWOOD AVE</Addr1>
                        <City>ROCHESTER</City>
                        <StateProvCd>NY</StateProvCd>
                        <PostalCode>14618</PostalCode>
                        <County></County>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-5555555</PhoneNumber>
                        </PhoneInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt>1983-08-26</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy id="PolicyLevel">
                <PolicyNumber>9661894100</PolicyNumber>
                <CompanyProductCd>06</CompanyProductCd>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>26638</NAICCd>
                <ContractTerm>
                    <EffectiveDt>2019-11-17</EffectiveDt>
                    <ExpirationDt>2020-05-17</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>6</NumUnits>
                        <UnitMeasurementCd>MON</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <BillingMethodCd>CAB</BillingMethodCd>
                <CurrentTermAmt>
                    <Amt>267.00</Amt>
                </CurrentTermAmt>
                <OriginalInceptionDt>1994-02-01</OriginalInceptionDt>
                <Form>
                    <FormNumber>79939</FormNumber>
                    <FormName>VEH 0001 SEC III DMG TO YOUR AUTO AMENDATORY END</FormName>
                    <EditionDt>2012-01-01</EditionDt>
                </Form>
                <MiscParty>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>zz** Denise Adamski</CommercialName>
                            </CommlName>
                        </NameInfo>
                        <Communications>
                            <PhoneInfo>
                                <PhoneTypeCd>Phone</PhoneTypeCd>
                                <PhoneNumber>******-8034422</PhoneNumber>
                            </PhoneInfo>
                            <PhoneInfo>
                                <PhoneTypeCd>Fax</PhoneTypeCd>
                                <PhoneNumber>******-8526454</PhoneNumber>
                            </PhoneInfo>
                            <PhoneInfo>
                                <PhoneTypeCd>Phone</PhoneTypeCd>
                                <PhoneNumber>******-8034422</PhoneNumber>
                            </PhoneInfo>
                            <PhoneInfo>
                                <PhoneTypeCd>Fax</PhoneTypeCd>
                                <PhoneNumber>******-8526454</PhoneNumber>
                            </PhoneInfo>
                            <EmailInfo>
                                <EmailAddr><EMAIL></EmailAddr>
                            </EmailInfo>
                            <EmailInfo>
                                <EmailAddr><EMAIL></EmailAddr>
                            </EmailInfo>
                        </Communications>
                    </GeneralPartyInfo>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>A</MiscPartyRoleCd>
                    </MiscPartyInfo>
                </MiscParty>
                <PersApplicationInfo>
                    <InsuredOrPrincipal/>
                </PersApplicationInfo>
                <DriverVeh DriverRef="D1" VehRef="V1">
                    <UsePct>100</UsePct>
                </DriverVeh>
                <ControllingStateProvCd>OH</ControllingStateProvCd>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>Auto Owners</InsurerName>
                </OtherOrPriorPolicy>
                <QuoteInfo>
                    <CompanysQuoteNumber>def0d249-13a8-4971-bc74-e57aa547173e</CompanysQuoteNumber>
                    <com.Safeco_AQEOpportunityID>222247</com.Safeco_AQEOpportunityID>
                    <com_safeco_CompanyURL>
                        <![CDATA[https://safesite.safeco.com/Client/OpenActivity.aspx?l=o&p=def0d249-13a8-4971-bc74-e57aa547173e]]></com_safeco_CompanyURL>
                </QuoteInfo>
                <SalesforceBookTransferName>56162</SalesforceBookTransferName>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>26638</NAICCd>
                <CompanyProductCd>06</CompanyProductCd>
                <CurrentTermAmt>
                    <Amt>267.00</Amt>
                </CurrentTermAmt>
                <RateEffectiveDt>2017-02-17</RateEffectiveDt>
                <PersDriver id="D1">
                    <ItemIdInfo>
                        <AgencyId>0001</AgencyId>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>DONNA BAYER</CommercialName>
                            </CommlName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1983-08-26</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>*********</DriversLicenseNumber>
                            <StateProvCd>NY</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicenseTypeCd>Driver</LicenseTypeCd>
                            <LicensePermitNumber></LicensePermitNumber>
                            <StateProvCd>NY</StateProvCd>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo id="PD1">
                        <DefensiveDriverCd>N</DefensiveDriverCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                        <GoodStudentCd>N</GoodStudentCd>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh id="V1" LocationRef="L1" RatedDriverRef="D1">
                    <ItemIdInfo>
                        <AgencyId>0001</AgencyId>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>PONT</Manufacturer>
                    <Model>SUNFIRE</Model>
                    <ModelYear>2004</ModelYear>
                    <VehBodyTypeCd>00000</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>OH</StateProvCd>
                    </Registration>
                    <LeasedVehInd>0</LeasedVehInd>
                    <RegistrationStateProvCd>OH</RegistrationStateProvCd>
                    <TerritoryCd>042</TerritoryCd>
                    <VehIdentificationNumber>1G2JB12FX47243564</VehIdentificationNumber>
                    <VehSymbolCd>18</VehSymbolCd>
                    <AlteredInd>0</AlteredInd>
                    <AntiLockBrakeCd>4</AntiLockBrakeCd>
                    <DamageabilityCd>H</DamageabilityCd>
                    <MultiCarDiscountInd>0</MultiCarDiscountInd>
                    <NonOwnedVehInd>0</NonOwnedVehInd>
                    <PricingCd>5</PricingCd>
                    <AntiTheftDeviceCd>Y</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>80.32</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <FormatInteger>250</FormatInteger>
                            <DeductibleTypeCd>FL</DeductibleTypeCd>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>12.75</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>123.05</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>10000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>25.40</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RREIM</CoverageCd>
                        <Limit>
                            <FormatInteger>40</FormatInteger>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>8.19</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMISP</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.36</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDSP</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>10.66</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ABS</CoverageCd>
                        <CoverageDesc>Anti-Lock Brake Discount ANTI-LOCK BRAKES DISCOUNT</CoverageDesc>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PASSR</CoverageCd>
                        <CoverageDesc>Passive Restraint PASSIVE RESTRAINT DISCOUNT</CoverageDesc>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ANTHF</CoverageCd>
                        <CoverageDesc>Anti-Theft Device ANTI-THEFT DEVICE DISCOUNT</CoverageDesc>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>2</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SCAR</CoverageCd>
                        <CoverageDesc>SCAR SINGLE-CAR DISCOUNT</CoverageDesc>
                    </Coverage>
                    <FullTermAmt>
                        <Amt/>
                    </FullTermAmt>
                </PersVeh>
                <PersVeh LocationRef="Gar1" RatedDriverRef="" id="VEH8E6EFC2E-8EA4-4689-9036-60EF68EC9C6E">
                    <ItemIdInfo>
                        <AgencyId/>
                    </ItemIdInfo>
                    <Manufacturer>JAYCO</Manufacturer>
                    <Model>NORTH POINT FIFTH WHEEL TRAILERS</Model>
                    <ModelYear>2017</ModelYear>
                    <VehBodyTypeCd/>
                    <VehBodyTypeDesc/>
                    <VehTypeCd>FWTT</VehTypeCd>
                    <MultiCarDiscountInd/>
                    <Registration>
                        <RegistrationTypeCd>LicensePlate</RegistrationTypeCd>
                        <RegistrationId/>
                        <RegistrationTypeId/>
                        <StateProvCd/>
                    </Registration>
                    <POLKRestraintDeviceCd/>
                    <CostNewAmt>
                        <Amt>75100</Amt>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek/>
                    <NumCylinders/>
                    <PurchaseDt>2017-03-28</PurchaseDt>
                    <VehInspectionStatusCd/>
                    <DaytimeRunningLightInd>N</DaytimeRunningLightInd>
                    <EstimatedAnnualDistance>
                        <NumUnits>9999</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt>
                        <Amt>641</Amt>
                    </FullTermAmt>
                    <Horsepower>
                        <NumUnits/>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <TerritoryCd/>
                    <VehIdentificationNumber>1UJCJ0BU841LC0176</VehIdentificationNumber>
                    <VehSymbolCd/>
                    <GrossVehWeight>
                        <NumUnits/>
                        <UnitMeasurementCd>LBR</UnitMeasurementCd>
                    </GrossVehWeight>
                    <CarpoolInd/>
                    <DistanceOneWay>
                        <NumUnits>5</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NewVehInd>Y</NewVehInd>
                    <LengthTimePerMonth>
                        <NumUnits/>
                        <UnitMeasurementCd>WEE</UnitMeasurementCd>
                    </LengthTimePerMonth>
                    <OdometerReading>
                        <NumUnits/>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd/>
                    <AntiLockBrakeCd>N</AntiLockBrakeCd>
                    <RateClassCd/>
                    <VehPerformanceCd/>
                    <VehSalvageTitleNumber/>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>None</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDUM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OPTBI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc/>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_GPCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PIP</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                        </Deductible>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RREIM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>MaxAmount</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMSLA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMCSL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_UMPDA</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                        <Deductible>
                            <FormatInteger/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDPD</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SORPE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LUSE</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ADB</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Option>
                            <OptionCd>N</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PPI</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc/>
                        <Limit>
                            <FormatInteger/>
                            <LimitBasisCd/>
                            <LimitAppliesToCd/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                        <TerritoryCd/>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDSP</CoverageCd>
                        <CoverageDesc>Underinsured Split Limit (Policy Level Coverage)</CoverageDesc>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ANTHF</CoverageCd>
                        <CoverageDesc>Anti-Theft</CoverageDesc>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RPV</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>Y</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SOPKG</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LOAN</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionCd>N</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_CovLevel</CoverageCd>
                        <CoverageDesc/>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>C</OptionCd>
                        </Option>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ABS</CoverageCd>
                        <CoverageDesc>Anti-lock brake discount</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RVTRL</CoverageCd>
                        <Limit>
                            <FormatInteger>10</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEXCO</CoverageCd>
                        <Option>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RVPE</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>AV</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                    </Coverage>
                    <SettlementType>REP</SettlementType>
                    <CollisionSymbolCd/>
                    <ComprehensiveOTCSymbolCd/>
                    <Coverage>
                        <CoverageCd>ADDA</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Option>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="001" id="V3">
                    <ItemIdInfo>
                        <AgencyId>0003</AgencyId>
                        <InsurerId>0003</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>KAWASAKI</Manufacturer>
                    <Model>KVF750 BRUTEFORCE</Model>
                    <ModelYear>2005</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <FullTermAmt>
                        <Amt>50.00</Amt>
                    </FullTermAmt>
                    <Displacement>
                        <NumUnits>749</NumUnits>
                    </Displacement>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>4</TerritoryCd>
                    <VehIdentificationNumber>JKAVFDA125B514246</VehIdentificationNumber>
                    <VehSymbolCd>54</VehSymbolCd>
                    <VehUseCd>OT</VehUseCd>
                    <Coverage>
                        <CoverageCd>ACCES</CoverageCd>
                        <CoverageDesc>Accessory Coverage</CoverageDesc>
                        <Limit>
                            <FormatInteger>3000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>1.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc>Bodily injury limit(s)</CoverageDesc>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>10.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <CoverageDesc>Collision</CoverageDesc>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>11.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <CoverageDesc>Comprehensive</CoverageDesc>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>9.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc>Property damage-single limit</CoverageDesc>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>5.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PEPIP</CoverageCd>
                        <CoverageDesc>Pedestrian PIP</CoverageDesc>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>2.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SPOUS</CoverageCd>
                        <CoverageDesc>SPOUS</CoverageDesc>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMSUM</CoverageCd>
                        <CoverageDesc>Uninsured Motorist supplementary</CoverageDesc>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>2.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <CoverageDesc>Towing and labor</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt>10.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc>Uninsured Motorist Liab / BI</CoverageDesc>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="001" RatedDriverRef="D0" id="V4">
                    <ItemIdInfo>
                        <InsurerId>0004</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HARLEY-DAVIDSON</Manufacturer>
                    <Model>FLFBS FAT BOY</Model>
                    <ModelYear>2020</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <FullTermAmt>
                        <Amt>357.00</Amt>
                    </FullTermAmt>
                    <TerritoryCd>3</TerritoryCd>
                    <VehIdentificationNumber>1HD1YRK19LBO46747</VehIdentificationNumber>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Eaglemark Savings Bk</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr2>PO Box 21750</Addr2>
                                <City>Carson City</City>
                                <StateProvCd>NV</StateProvCd>
                                <PostalCode>89721</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LIEN</NatureInterestCd>
                            <PayorInd>0</PayorInd>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <AntiLockBrakeCd>Y</AntiLockBrakeCd>
                    <OdometerReading>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="137c4bbf-9bd3-42d2-8466-08df9204aed2">
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>73.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="0a96b08c-811a-4a59-9950-4698de29094b">
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>39.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="a215e036-435a-4dc5-92fc-008f159cc251">
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="3fc82180-d377-4498-828e-64b2af0b2b53">
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible bookTransferAssociatedNodeId="V4" btId="e91d0765-d51f-46a7-af8f-be9069e04648">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>47.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="b50a7288-0ad0-4dca-83e4-5d5fad457834">
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible bookTransferAssociatedNodeId="V4" btId="fd045bce-70dd-4dc6-8f45-206fe1b51141">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>136.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="ee70e96d-76c3-46a2-a5e6-5d8f3e98f8ea">
                        <CoverageCd>ACCES</CoverageCd>
                        <CoverageDesc>Accessory Coverage</CoverageDesc>
                        <Limit>
                            <FormatInteger>3000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>1.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="95a73d8f-81a0-47ec-8ead-c19e635fa28a">
                        <CoverageCd>PEPIP</CoverageCd>
                        <CoverageDesc>Pedestrian PIP</CoverageDesc>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>2.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="20cc852a-0b53-4910-964f-06ccbf7c16c1">
                        <CoverageCd>SPOUS</CoverageCd>
                        <CoverageDesc>Supplemental Spousal Liability</CoverageDesc>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="da33a062-b3b5-4b8c-821d-568d3ab4c43a">
                        <CoverageCd>UMSUM</CoverageCd>
                        <CoverageDesc>Supplementary Uninsured Motorist</CoverageDesc>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>59.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V4" btId="cbd0d8eb-7159-4871-acd4-27e9f3664e2d">
                        <CoverageCd>AT1</CoverageCd>
                        <CoverageDesc>ANTI-THEFT DEVICE 1 DISCOUNT</CoverageDesc>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="001" RatedDriverRef="D1" id="V3">
                    <ItemIdInfo>
                        <InsurerId>0003</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HARLEY-DAVIDSON</Manufacturer>
                    <Model>FLTRXSE CVO ROAD GLI</Model>
                    <ModelYear>2019</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <VehTypeCd>OT</VehTypeCd>
                    <FullTermAmt>
                        <Amt>502.00</Amt>
                    </FullTermAmt>
                    <TerritoryCd>3</TerritoryCd>
                    <VehIdentificationNumber>1HD1TCL13KB951865</VehIdentificationNumber>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Eaglemark Savings Bk</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr2>PO Box 21750</Addr2>
                                <City>Carson City</City>
                                <StateProvCd>NV</StateProvCd>
                                <PostalCode>89721</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LIEN</NatureInterestCd>
                            <PayorInd>0</PayorInd>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <AntiLockBrakeCd>Y</AntiLockBrakeCd>
                    <OdometerReading>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="4277407c-e402-444a-865f-60b1cf886c9c">
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>80.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="9dee5e60-95dd-477f-842b-18a0909bbf83">
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>45.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="72ea2524-1b01-4f2b-97c7-c8155d8e4efc">
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="2d69c152-f1d3-4805-90a9-dd70b2219283">
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible bookTransferAssociatedNodeId="V3" btId="838ad1fc-f6c1-4a0e-b0aa-e11ef2d832c0">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>64.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="67537143-3002-4757-99ac-a64202e144cd">
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible bookTransferAssociatedNodeId="V3" btId="8dd8f8b4-2cf7-4ea8-a5e9-4efa9b235800">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>249.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="fad61128-25c4-442a-ae55-6d896a0e3f22">
                        <CoverageCd>ACCES</CoverageCd>
                        <CoverageDesc>Accessory Coverage</CoverageDesc>
                        <Limit>
                            <FormatInteger>3000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>1.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="c9783498-43a3-4866-9bb3-2c0c93d90c88">
                        <CoverageCd>UMSUM</CoverageCd>
                        <CoverageDesc>Supplementary Uninsured Motorist</CoverageDesc>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>61.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="99c3d3d0-5e82-4946-8904-309ef545b0f8">
                        <CoverageCd>PEPIP</CoverageCd>
                        <CoverageDesc>Pedestrian PIP</CoverageDesc>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>2.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="397886f8-a00a-4ffd-a3c3-dca4afbb1ae9">
                        <CoverageCd>AT1</CoverageCd>
                        <CoverageDesc>ANTI-THEFT DEVICE 1 DISCOUNT</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="c7179b8e-b4f8-4618-81c7-5b2ea6abb30d">
                        <CoverageCd>SPOUS</CoverageCd>
                        <CoverageDesc>Supplemental Spousal Liability</CoverageDesc>
                        <Limit>
                            <FormatInteger>250000</FormatInteger>
                        </Limit>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="e6b81378-7a12-4930-bb85-a793cd815703">
                        <CoverageCd>CFD</CoverageCd>
                        <CoverageDesc>Claims Free</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="f94816e2-f4c0-4dc0-aad2-5ef345daf22e">
                        <CoverageCd>HON</CoverageCd>
                        <CoverageDesc>Homeowner Discount</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="3bd1037f-cf8c-42b7-b3e0-49f8f80197ff">
                        <CoverageCd>SMV</CoverageCd>
                        <CoverageDesc>Multi Vehicle Discount</CoverageDesc>
                    </Coverage>
                    <Coverage bookTransferAssociatedNodeId="V3" btId="c726321b-be4f-420a-9f73-59174f1bb2c3">
                        <CoverageCd>PIF</CoverageCd>
                        <CoverageDesc>Paid In Full</CoverageDesc>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="Gar1" id="Veh1">
                    <Manufacturer>HARLEY-DAVIDSON</Manufacturer>
                    <Model>XL1200</Model>
                    <ModelYear>2021</ModelYear>
                    <Registration>
                        <RegistrationId>MCN</RegistrationId>
                    </Registration>
                    <CostNewAmt>
                        <Amt>0</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>3000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <Displacement>
                        <NumUnits>1200</NumUnits>
                        <UnitMeasurementCd>CC</UnitMeasurementCd>
                    </Displacement>
                    <PurchaseDt>2021-05-11</PurchaseDt>
                    <VehIdentificationNumber>1HD1LP319MB409972</VehIdentificationNumber>
                    <com.safeco_TrikeConversion>
                        <com.safeco_ConversionInd>N</com.safeco_ConversionInd>
                    </com.safeco_TrikeConversion>
                    <AntiLockBrakeCd>N</AntiLockBrakeCd>
                    <GaragingCd>G</GaragingCd>
                    <NonOwnedVehInd>0</NonOwnedVehInd>
                    <VehUseCd/>
                    <SeatBeltTypeCd>0</SeatBeltTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>15000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <FormatInteger>1000</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <FormatInteger>1000</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PIP</CoverageCd>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CUSTE</CoverageCd>
                        <Limit>
                            <FormatInteger>3000</FormatInteger>
                            <LimitAppliesToCd>PerOcc</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger>100</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>PerOccurence</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>com.safeco_GP</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                </PersVeh>
                <PersVeh LocationRef="001" id="V1">
                    <ItemIdInfo>
                        <AgencyId>0001</AgencyId>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>OTHER</Manufacturer>
                    <Model>ATV MODEL</Model>
                    <ModelYear>2013</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <FullTermAmt>
                        <Amt>26.00</Amt>
                    </FullTermAmt>
                    <Displacement>
                        <NumUnits>38</NumUnits>
                    </Displacement>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>4</TerritoryCd>
                    <VehIdentificationNumber>L5NAAFTD6D1005051</VehIdentificationNumber>
                    <VehSymbolCd>LA</VehSymbolCd>
                    <VehUseCd>OT</VehUseCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <CoverageDesc>Bodily injury limit(s)</CoverageDesc>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>8.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <CoverageDesc>Property damage-single limit</CoverageDesc>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>4.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PEPIP</CoverageCd>
                        <CoverageDesc>Pedestrian PIP</CoverageDesc>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>2.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SPOUS</CoverageCd>
                        <CoverageDesc>SPOUS</CoverageDesc>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMSUM</CoverageCd>
                        <CoverageDesc>Uninsured Motorist supplementary</CoverageDesc>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>2.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <CoverageDesc>Towing and labor</CoverageDesc>
                        <CurrentTermAmt>
                            <Amt>10.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <CoverageDesc>Uninsured Motorist Liab / BI</CoverageDesc>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="L1">
                <ItemIdInfo>
                    <AgencyId>0001</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>2400 ELMWOOD AVE</Addr1>
                    <City>ROCHESTER</City>
                    <StateProvCd>NY</StateProvCd>
                    <PostalCode>14618</PostalCode>
                    <County></County>
                    <Addr2/>
                </Addr>
                <CountyTownCd></CountyTownCd>
            </Location>
            <RemarkText IdRef="PolicyLevel">Individual
                Change Description:
            </RemarkText>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
<ACORD>
    <SignonRq>
        <ClientDt>2016-04-27</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>BookSmart</Org>
            <Name>AgencyPort</Name>
        </ClientApp>
        <SignonTransport>
            <SignonRoleCd>Agent</SignonRoleCd>
            <CustId>
                <SPName>com.agencyport</SPName>
                <CustLoginId>default</CustLoginId>
            </CustId>
        </SignonTransport>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>6F6ECD35BA124E68A42C4FF485D8EB1A</RqUID>
        <PersUmbrellaPolicyQuoteInqRq>
            <RqUID>2B440158E3EF4E2FA22A332D6EAD4BA5</RqUID>
            <Producer>
                <ProducerInfo>
                    <ContractNumber>431683</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>GILBDO1C-8</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName></CommercialName>
                            <SupplementaryNameInfo>
                                <SupplementaryNameCd>ATTN</SupplementaryNameCd>
                                <SupplementaryName>Insured Attn</SupplementaryName>
                            </SupplementaryNameInfo>
                        </CommlName>
                        <PersonName>
                            <GivenName>Todd</GivenName>
                            <Surname>Smith</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>100 Main Street</Addr1>
                        <City>Park Forest</City>
                        <StateProvCd>PA</StateProvCd>
                        <PostalCode>60466</PostalCode>
                        <County>Cook</County>
                    </Addr>
                    <Communications>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <PhoneNumber>************</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <PhoneTypeCd>Phone</PhoneTypeCd>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <PhoneNumber>************</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr>primary@emailaddress</EmailAddr>
                        </EmailInfo>
                        <EmailInfo>
                            <CommunicationUseCd>Alternate</CommunicationUseCd>
                            <EmailAddr>secondary@emailaddress</EmailAddr>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <OccupationDesc>Appl Occupation</OccupationDesc>
                        <LengthTimeEmployed>
                            <NumUnits>1</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <MiscParty>
                            <GeneralPartyInfo>
                                <NameInfo>
                                    <CommlName>
                                        <CommercialName>Appl Employer Name</CommercialName>
                                    </CommlName>
                                </NameInfo>
                                <Addr>
                                    <Addr1>Appl Employer Street</Addr1>
                                    <City>Appl Employer City</City>
                                    <StateProvCd>IN</StateProvCd>
                                    <PostalCode>10001-1111</PostalCode>
                                </Addr>
                            </GeneralPartyInfo>
                            <MiscPartyInfo>
                                <MiscPartyRoleCd>Employer</MiscPartyRoleCd>
                            </MiscPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <OccupationDesc>Co--Appl Occupation</OccupationDesc>
                        <LengthTimeEmployed>
                            <NumUnits>2</NumUnits>
                            <UnitMeasurementCd>ANN</UnitMeasurementCd>
                        </LengthTimeEmployed>
                        <MiscParty>
                            <GeneralPartyInfo>
                                <NameInfo>
                                    <CommlName>
                                        <CommercialName>Co-Appl Employer Name</CommercialName>
                                    </CommlName>
                                </NameInfo>
                                <Addr>
                                    <Addr1>Co-Appl Employer Street</Addr1>
                                    <City>Co-Appl Employer City</City>
                                    <StateProvCd>HI</StateProvCd>
                                    <PostalCode>10002-2222</PostalCode>
                                </Addr>
                            </GeneralPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <PolicyNumber>PU-COMPLETE DATA</PolicyNumber>
                <CompanyProductCd>Company Plan</CompanyProductCd>
                <LOBCd>UMBRC</LOBCd>
                <NAICCd>39012</NAICCd>
                <ContractTerm>
                    <EffectiveDt></EffectiveDt>
                    <ExpirationDt>2018-03-21</ExpirationDt>
                </ContractTerm>
                <BillingAccountNumber>BILLING ACCT NUM</BillingAccountNumber>
                <BillingMethodCd>CPB</BillingMethodCd>
                <MailingResponsibiltyCd>CO</MailingResponsibiltyCd>
                <PayorCd>AIF</PayorCd>
                <NumLossesYrs>5</NumLossesYrs>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <PolicyNumber>PRIOR-POLNUMBER</PolicyNumber>
                    <InsurerName>AIG</InsurerName>
                    <ContractTerm>
                        <ExpirationDt>2016-01-06</ExpirationDt>
                    </ContractTerm>
                </OtherOrPriorPolicy>
                <PaymentOption>
                    <PaymentPlanCd>BM</PaymentPlanCd>
                    <MethodPaymentCd id="PAYMETH1">DRAFT</MethodPaymentCd>
                </PaymentOption>
                <MiscParty>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Finance Company Name</CommercialName>
                            </CommlName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <MiscPartyInfo>
                        <MiscPartyRoleCd>FC</MiscPartyRoleCd>
                    </MiscPartyInfo>
                </MiscParty>
                <AssignedRiskFacilityCd>FACILI</AssignedRiskFacilityCd>
                <PersApplicationInfo>
                    <CurrentResidenceDt>2016-01-01</CurrentResidenceDt>
                </PersApplicationInfo>
                <AccidentViolation DriverRef="DRV1">
                    <AccidentViolationDt>2015-12-01</AccidentViolationDt>
                    <DamageTotalAmt>
                        <Amt>800</Amt>
                    </DamageTotalAmt>
                    <AccidentViolationDesc>Accident 1 - Driver 1 Description</AccidentViolationDesc>
                </AccidentViolation>
                <AccidentViolation DriverRef="DRV2">
                    <AccidentViolationDt>2015-11-01</AccidentViolationDt>
                    <DamageTotalAmt>
                        <Amt>801</Amt>
                    </DamageTotalAmt>
                    <AccidentViolationDesc>Accident 2 - Driver 2 Description</AccidentViolationDesc>
                </AccidentViolation>
                <AccidentViolation DriverRef="DRV3">
                    <AccidentViolationDt>2015-10-01</AccidentViolationDt>
                    <DamageTotalAmt>
                        <Amt>802</Amt>
                    </DamageTotalAmt>
                    <AccidentViolationDesc>Accident 3 - Driver 3 Description</AccidentViolationDesc>
                </AccidentViolation>
                <AccidentViolation DriverRef="DRV4">
                    <AccidentViolationDt>2015-09-01</AccidentViolationDt>
                    <DamageTotalAmt>
                        <Amt>803</Amt>
                    </DamageTotalAmt>
                    <AccidentViolationDesc>Accident 4 - Driver 4 Description</AccidentViolationDesc>
                </AccidentViolation>
                <AccidentViolation DriverRef="DRV5">
                    <AccidentViolationDt>2015-08-01</AccidentViolationDt>
                    <AccidentViolationDesc>Violation 1 - Driver 5 Description</AccidentViolationDesc>
                </AccidentViolation>
                <AccidentViolation DriverRef="DRV6">
                    <AccidentViolationDt>2015-07-01</AccidentViolationDt>
                    <AccidentViolationDesc>Violation 2 - Driver 6 Description</AccidentViolationDesc>
                </AccidentViolation>
                <AccidentViolation DriverRef="DRV1">
                    <AccidentViolationDt>2015-06-01</AccidentViolationDt>
                    <AccidentViolationDesc>Violation 3 - Driver 1 Description</AccidentViolationDesc>
                </AccidentViolation>
                <AccidentViolation DriverRef="DRV2">
                    <AccidentViolationDt>2015-05-01</AccidentViolationDt>
                    <AccidentViolationDesc>Violation 4 - Driver 2 Description</AccidentViolationDesc>
                </AccidentViolation>
                <DriverVeh DriverRef="DRV1" VehRef="VEH1">
                    <UsePct>61</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV1" VehRef="BOAT1">
                    <UsePct>71</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV2" VehRef="VEH2">
                    <UsePct>62</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV2" VehRef="BOAT2">
                    <UsePct>72</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV3" VehRef="VEH3">
                    <UsePct>63</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV3" VehRef="BOAT3">
                    <UsePct>73</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV4" VehRef="VEH4">
                    <UsePct>64</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV4" VehRef="BOAT1">
                    <UsePct>74</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV5" VehRef="VEH5">
                    <UsePct>65</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV5" VehRef="BOAT2">
                    <UsePct>75</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV6" VehRef="VEH6">
                    <UsePct>66</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="DRV6" VehRef="BOAT3">
                    <UsePct>76</UsePct>
                </DriverVeh>
            </PersPolicy>
            <Location id="LOC7">
                <ItemIdInfo>
                    <AgencyId>1</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1></Addr1>
                    <City>loc 1 city</City>
                    <StateProvCd>AK</StateProvCd>
                    <PostalCode>11111-1111</PostalCode>
                </Addr>
                <LocationDesc>loc 1 desc 2</LocationDesc>
            </Location>
            <Location>
                <ItemIdInfo>
                    <AgencyId>2</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 2 street</Addr1>
                    <City>loc 2 city</City>
                    <StateProvCd>AL</StateProvCd>
                    <PostalCode>22222-2222</PostalCode>
                </Addr>
                <LocationDesc>loc 2 desc 2</LocationDesc>
            </Location>
            <Location id="LOC3">
                <ItemIdInfo>
                    <AgencyId>3</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 3 street</Addr1>
                    <City>loc 3 city</City>
                    <StateProvCd>AR</StateProvCd>
                    <PostalCode>33333-3333</PostalCode>
                </Addr>
                <LocationDesc>loc 3 desc 2</LocationDesc>
            </Location>
            <Location id="LOC4">
                <ItemIdInfo>
                    <AgencyId>4</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 4 street</Addr1>
                    <City>loc 4 city</City>
                    <StateProvCd>AZ</StateProvCd>
                    <PostalCode>44444-4444</PostalCode>
                </Addr>
                <LocationDesc>loc4desc</LocationDesc>
            </Location>
            <Location id="LOC5">
                <ItemIdInfo>
                    <AgencyId>5</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 5 street</Addr1>
                    <City>loc 5 city</City>
                    <StateProvCd>CA</StateProvCd>
                    <PostalCode>55555-5555</PostalCode>
                </Addr>
                <LocationDesc>loc 5 desc 2</LocationDesc>
            </Location>
            <Location id="LOC6">
                <ItemIdInfo>
                    <AgencyId>6</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 6 street</Addr1>
                    <City>loc 6 city</City>
                    <StateProvCd>CO</StateProvCd>
                    <PostalCode>66666-6666</PostalCode>
                </Addr>
                <LocationDesc>loc 6 desc 2</LocationDesc>
            </Location>
            <Location id="LOC7">
                <ItemIdInfo>
                    <AgencyId>7</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>loc 7 street</Addr1>
                    <City>loc 7 city</City>
                    <StateProvCd>CT</StateProvCd>
                    <PostalCode>77777-7777</PostalCode>
                </Addr>
                <LocationDesc>loc 7 desc 2</LocationDesc>
            </Location>
            <PersUmbrellaLineBusiness id="PERUMBR1">
                <LOBCd>UMBRP</LOBCd>
                <Coverage>
                    <CoverageCd>PCL</CoverageCd>
                    <Limit>
                        <FormatInteger>10000000</FormatInteger>
                    </Limit>
                    <Deductible>
                        <FormatInteger>101</FormatInteger>
                        <DeductibleTypeCd>R</DeductibleTypeCd>
                    </Deductible>
                    <CurrentTermAmt>
                        <Amt>108</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <Coverage>
                    <CoverageCd>RESID</CoverageCd>
                    <CurrentTermAmt>
                        <Amt>109</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <Coverage>
                    <CoverageCd>AUTO</CoverageCd>
                    <CurrentTermAmt>
                        <Amt>110</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <Coverage>
                    <CoverageCd>RECVH</CoverageCd>
                    <CurrentTermAmt>
                        <Amt>111</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <Coverage>
                    <CoverageCd>BOAT</CoverageCd>
                    <CurrentTermAmt>
                        <Amt>114</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <Coverage>
                    <CoverageCd>BUSPR</CoverageCd>
                    <CoverageDesc>other coverage 2</CoverageDesc>
                    <Limit>
                        <FormatInteger>105</FormatInteger>
                    </Limit>
                    <CurrentTermAmt>
                        <Amt>115</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <Coverage>
                    <CoverageCd>UM</CoverageCd>
                    <Limit>
                        <FormatInteger>102</FormatInteger>
                    </Limit>
                    <CurrentTermAmt>
                        <Amt>112</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <Coverage>
                    <CoverageCd>UNDUM</CoverageCd>
                    <Limit>
                        <FormatInteger>103</FormatInteger>
                    </Limit>
                    <CurrentTermAmt>
                        <Amt>113</Amt>
                    </CurrentTermAmt>
                </Coverage>
                <Coverage>
                    <CoverageCd>COV1</CoverageCd>
                    <CoverageDesc>other coverage 1</CoverageDesc>
                    <Limit>
                        <FormatInteger>0</FormatInteger>
                    </Limit>
                </Coverage>
                <PersUmbrella>
                    <CalculationsDesc>calc 2 calc 3 calc 4 calc 5 calc 6 calc 7 calc 8</CalculationsDesc>
                </PersUmbrella>
                <RealEstate LocationRef="001">
                    <InterestCd>L1INT</InterestCd>
                    <OccupancyTypeCd>OWNER</OccupancyTypeCd>
                    <DwellUseCd>1</DwellUseCd>
                    <RealEstateTypeCd id="ESTATETYPE1">Apt</RealEstateTypeCd>
                    <YearBuilt>1901</YearBuilt>
                </RealEstate>
                <RealEstate LocationRef="001">
                    <InterestCd>L2INT</InterestCd>
                    <OccupancyTypeCd>TENAN</OccupancyTypeCd>
                    <DwellUseCd>2</DwellUseCd>
                    <RealEstateTypeCd id="ESTATETYPE2">Resi</RealEstateTypeCd>
                    <YearBuilt>1902</YearBuilt>
                </RealEstate>
                <RealEstate LocationRef="LOC1">
                    <InterestCd>L3INT</InterestCd>
                    <OccupancyTypeCd>UNOCC</OccupancyTypeCd>
                    <DwellUseCd>4</DwellUseCd>
                    <RealEstateTypeCd id="ESTATETYPE3">Resi</RealEstateTypeCd>
                    <YearBuilt>1903</YearBuilt>
                </RealEstate>
                <RealEstate LocationRef="LOC4">
                    <InterestCd>L4IN6</InterestCd>
                    <OccupancyTypeCd>VACAN</OccupancyTypeCd>
                    <DwellUseCd id="USETYPE4">OT</DwellUseCd>
                    <RealEstateTypeCd id="ESTATETYPE4">Condo</RealEstateTypeCd>
                    <YearBuilt>1904</YearBuilt>
                </RealEstate>
                <RealEstate LocationRef="LOC5">
                    <InterestCd>L5INT</InterestCd>
                    <OccupancyTypeCd id="OCCTYPE5">OT</OccupancyTypeCd>
                    <DwellUseCd id="USETYPE5">OT</DwellUseCd>
                    <RealEstateTypeCd id="ESTATETYPE5">Resi</RealEstateTypeCd>
                    <YearBuilt>1905</YearBuilt>
                </RealEstate>
                <RealEstate LocationRef="LOC6">
                    <InterestCd>L6INT</InterestCd>
                    <OccupancyTypeCd>OWNER</OccupancyTypeCd>
                    <DwellUseCd>6</DwellUseCd>
                    <RealEstateTypeCd id="ESTATETYPE6">Resi</RealEstateTypeCd>
                    <YearBuilt>1906</YearBuilt>
                </RealEstate>
                <RealEstate LocationRef="LOC7">
                    <InterestCd>L7INT</InterestCd>
                    <OccupancyTypeCd>OWNER</OccupancyTypeCd>
                    <DwellUseCd>4</DwellUseCd>
                    <RealEstateTypeCd id="ESTATETYPE7">Resi</RealEstateTypeCd>
                    <YearBuilt>1907</YearBuilt>
                </RealEstate>
                <BasicVehInfo id="VEH1">
                    <ItemIdInfo>
                        <AgencyId>1</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>MAKE1</Manufacturer>
                    <Model>MODEL1</Model>
                    <ModelYear>2001</ModelYear>
                    <VehBodyTypeCd>BODY1</VehBodyTypeCd>
                </BasicVehInfo>
                <BasicVehInfo id="VEH2">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>MAKE2</Manufacturer>
                    <Model>MODEL2</Model>
                    <ModelYear>2002</ModelYear>
                    <VehBodyTypeCd>BODY2</VehBodyTypeCd>
                </BasicVehInfo>
                <BasicVehInfo id="VEH3">
                    <ItemIdInfo>
                        <AgencyId>3</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>MAKE3</Manufacturer>
                    <Model>MODEL3</Model>
                    <ModelYear>2003</ModelYear>
                    <VehBodyTypeCd>BODY3</VehBodyTypeCd>
                </BasicVehInfo>
                <BasicVehInfo id="VEH4">
                    <ItemIdInfo>
                        <AgencyId>4</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>MAKE4</Manufacturer>
                    <Model>MODEL4</Model>
                    <ModelYear>2004</ModelYear>
                    <VehBodyTypeCd>BODY4</VehBodyTypeCd>
                </BasicVehInfo>
                <BasicVehInfo id="VEH5">
                    <ItemIdInfo>
                        <AgencyId>5</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>MAKE5</Manufacturer>
                    <Model>MODEL5</Model>
                    <ModelYear>2005</ModelYear>
                    <VehBodyTypeCd>BODY5</VehBodyTypeCd>
                </BasicVehInfo>
                <BasicVehInfo id="VEH6">
                    <ItemIdInfo>
                        <AgencyId>6</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>MAKE6</Manufacturer>
                    <Model>MODEL6</Model>
                    <ModelYear>2006</ModelYear>
                    <VehBodyTypeCd>BODY6</VehBodyTypeCd>
                </BasicVehInfo>
                <BasicVehInfo id="VEH7">
                    <ItemIdInfo>
                        <AgencyId>7</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>MAKE7</Manufacturer>
                    <Model>MODEL7</Model>
                    <ModelYear>2007</ModelYear>
                    <VehBodyTypeCd>BODY7</VehBodyTypeCd>
                </BasicVehInfo>
                <BasicVehInfo id="VEH8">
                    <ItemIdInfo>
                        <AgencyId>8</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>MAKE8</Manufacturer>
                    <Model>MODEL8</Model>
                    <ModelYear>2008</ModelYear>
                    <VehBodyTypeCd>BODY8</VehBodyTypeCd>
                </BasicVehInfo>
                <BasicWatercraftInfo id="BOAT1">
                    <ItemIdInfo>
                        <AgencyId>1</AgencyId>
                    </ItemIdInfo>
                    <ItemDefinition>
                        <Manufacturer>WA MAKE 1</Manufacturer>
                        <Model>WA MODEL 1</Model>
                        <ModelYear>2010</ModelYear>
                    </ItemDefinition>
                    <Length>
                        <NumUnits>50</NumUnits>
                        <UnitMeasurementCd>FOT</UnitMeasurementCd>
                    </Length>
                    <Speed>
                        <NumUnits>52</NumUnits>
                        <UnitMeasurementCd>HM</UnitMeasurementCd>
                    </Speed>
                    <WatersNavigatedCd>A</WatersNavigatedCd>
                    <Horsepower>
                        <NumUnits>51</NumUnits>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <PropulsionTypeCd>INBRD</PropulsionTypeCd>
                </BasicWatercraftInfo>
                <BasicWatercraftInfo id="BOAT2">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <ItemDefinition>
                        <Manufacturer>WA MAKE 2</Manufacturer>
                        <Model>WA MODEL 2</Model>
                        <ModelYear>2011</ModelYear>
                    </ItemDefinition>
                    <Length>
                        <NumUnits>53</NumUnits>
                        <UnitMeasurementCd>FOT</UnitMeasurementCd>
                    </Length>
                    <Speed>
                        <NumUnits>55</NumUnits>
                        <UnitMeasurementCd>HM</UnitMeasurementCd>
                    </Speed>
                    <WatersNavigatedCd>B</WatersNavigatedCd>
                    <Horsepower>
                        <NumUnits>54</NumUnits>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <PropulsionTypeCd>OUT</PropulsionTypeCd>
                </BasicWatercraftInfo>
                <BasicWatercraftInfo id="BOAT3">
                    <ItemIdInfo>
                        <AgencyId>3</AgencyId>
                    </ItemIdInfo>
                    <ItemDefinition>
                        <Manufacturer>WA MAKE 3</Manufacturer>
                        <Model>WA MODEL 3</Model>
                        <ModelYear>2012</ModelYear>
                    </ItemDefinition>
                    <Length>
                        <NumUnits>56</NumUnits>
                        <UnitMeasurementCd>FOT</UnitMeasurementCd>
                    </Length>
                    <Speed>
                        <NumUnits>58</NumUnits>
                        <UnitMeasurementCd>HM</UnitMeasurementCd>
                    </Speed>
                    <WatersNavigatedCd id="WATRNAV3">OT</WatersNavigatedCd>
                    <Horsepower>
                        <NumUnits>57</NumUnits>
                        <UnitMeasurementCd>BHP</UnitMeasurementCd>
                    </Horsepower>
                    <PropulsionTypeCd id="PROPTYPE3">OT</PropulsionTypeCd>
                </BasicWatercraftInfo>
                <BasicDriverInfo id="DRV1">
                    <ItemIdInfo>
                        <AgencyId>1</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Drv 2 Last</Surname>
                                <GivenName>Drv 1 First</GivenName>
                                <OtherGivenName>A</OtherGivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>2001-06-01</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                        </PersonInfo>
                        <License>
                            <LicensedDt>2015-06-12</LicensedDt>
                            <LicensePermitNumber>DRV 1 LIC NUM</LicensePermitNumber>
                            <StateProvCd>DE</StateProvCd>
                        </License>
                    </DriverInfo>
                </BasicDriverInfo>
                <BasicDriverInfo id="DRV2">
                    <ItemIdInfo>
                        <AgencyId>2</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>B</Surname>
                                <GivenName>Drv 2 First</GivenName>
                                <OtherGivenName>Drv 2 Last</OtherGivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>2002-07-02</BirthDt>
                            <MaritalStatusCd>W</MaritalStatusCd>
                        </PersonInfo>
                        <License>
                            <LicensedDt>2015-07-12</LicensedDt>
                            <LicensePermitNumber>DRV 2 LIC NUM</LicensePermitNumber>
                            <StateProvCd>FL</StateProvCd>
                        </License>
                    </DriverInfo>
                </BasicDriverInfo>
                <BasicDriverInfo id="DRV3">
                    <ItemIdInfo>
                        <AgencyId>3</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Drv 3 Last</Surname>
                                <GivenName>Drv 3 First</GivenName>
                                <OtherGivenName>C</OtherGivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>2003-08-03</BirthDt>
                            <MaritalStatusCd>D</MaritalStatusCd>
                        </PersonInfo>
                        <License>
                            <LicensedDt>2015-08-12</LicensedDt>
                            <LicensePermitNumber>DRV 3 LIC NUM</LicensePermitNumber>
                            <StateProvCd>GA</StateProvCd>
                        </License>
                    </DriverInfo>
                </BasicDriverInfo>
                <BasicDriverInfo id="DRV4">
                    <ItemIdInfo>
                        <AgencyId>4</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Drv 4 Last</Surname>
                                <GivenName>Drv 4 First</GivenName>
                                <OtherGivenName>D</OtherGivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>2004-09-04</BirthDt>
                            <MaritalStatusCd>V</MaritalStatusCd>
                        </PersonInfo>
                        <License>
                            <LicensedDt>2015-09-12</LicensedDt>
                            <LicensePermitNumber>DRV 4 LIC NUM</LicensePermitNumber>
                            <StateProvCd>IA</StateProvCd>
                        </License>
                    </DriverInfo>
                </BasicDriverInfo>
                <BasicDriverInfo id="DRV5">
                    <ItemIdInfo>
                        <AgencyId>5</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Drv 5 Last</Surname>
                                <GivenName>Drv 5 First</GivenName>
                                <OtherGivenName>E</OtherGivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <MaritalStatusCd>P</MaritalStatusCd>
                        </PersonInfo>
                        <License>
                            <LicensedDt>2015-10-12</LicensedDt>
                        </License>
                    </DriverInfo>
                </BasicDriverInfo>
                <BasicDriverInfo id="DRV6">
                    <ItemIdInfo>
                        <AgencyId>6</AgencyId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Drv 6 Last</Surname>
                                <GivenName>Drv 6 First</GivenName>
                                <OtherGivenName>F</OtherGivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <MaritalStatusCd>U</MaritalStatusCd>
                        </PersonInfo>
                        <License>
                            <LicensedDt>2015-11-12</LicensedDt>
                        </License>
                    </DriverInfo>
                </BasicDriverInfo>
                <OtherOrPriorPolicy>
                    <PolicyCd>Underly</PolicyCd>
                    <PolicyNumber>AUTO POLICY NUMBER</PolicyNumber>
                    <LOBCd>AUTOP</LOBCd>
                    <InsurerName>auto company</InsurerName>
                    <ContractTerm>
                        <EffectiveDt>2001-01-01</EffectiveDt>
                        <ExpirationDt>2002-01-01</ExpirationDt>
                    </ContractTerm>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>200</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>201</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>202</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <FormatInteger>203</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>204</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMPD</CoverageCd>
                        <Limit>
                            <FormatInteger>205</FormatInteger>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Underly</PolicyCd>
                    <PolicyNumber>HOME POLICY NUMBER</PolicyNumber>
                    <LOBCd>HOME</LOBCd>
                    <InsurerName>home company</InsurerName>
                    <ContractTerm>
                        <EffectiveDt>2001-02-02</EffectiveDt>
                        <ExpirationDt>2002-02-02</ExpirationDt>
                    </ContractTerm>
                    <Coverage>
                        <CoverageCd>PL</CoverageCd>
                        <Limit>
                            <FormatInteger>206</FormatInteger>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Underly</PolicyCd>
                    <PolicyNumber>WA POLICY NUMBER</PolicyNumber>
                    <LOBCd>BOAT</LOBCd>
                    <InsurerName>wa company</InsurerName>
                    <ContractTerm>
                        <EffectiveDt>2001-04-04</EffectiveDt>
                        <ExpirationDt>2002-04-04</ExpirationDt>
                    </ContractTerm>
                    <Coverage>
                        <CoverageCd>WATER</CoverageCd>
                        <Limit>
                            <FormatInteger>209</FormatInteger>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Underly</PolicyCd>
                    <PolicyNumber>RV POLICY NUMBER</PolicyNumber>
                    <LOBCd>RECV</LOBCd>
                    <InsurerName>RV Company</InsurerName>
                    <ContractTerm>
                        <EffectiveDt>2001-05-05</EffectiveDt>
                        <ExpirationDt>2002-05-05</ExpirationDt>
                    </ContractTerm>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>216</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>215</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <FormatInteger>217</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>218</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMPD</CoverageCd>
                        <Limit>
                            <FormatInteger>219</FormatInteger>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Underly</PolicyCd>
                    <PolicyNumber>EL POLICY NUMBER</PolicyNumber>
                    <LOBCd>EL</LOBCd>
                    <InsurerName>EL Company</InsurerName>
                    <ContractTerm>
                        <EffectiveDt>2001-06-06</EffectiveDt>
                        <ExpirationDt>2002-06-06</ExpirationDt>
                    </ContractTerm>
                    <Coverage>
                        <CoverageCd>EL</CoverageCd>
                        <Limit>
                            <FormatInteger>220</FormatInteger>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Underly</PolicyCd>
                    <PolicyNumber>OTHER POLICY NUMBER</PolicyNumber>
                    <LOBCd>OTLOB</LOBCd>
                    <InsurerName>Other Company</InsurerName>
                    <ContractTerm>
                        <EffectiveDt>2001-07-07</EffectiveDt>
                        <ExpirationDt>2002-07-07</ExpirationDt>
                    </ContractTerm>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>222</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>223</FormatInteger>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Underly</PolicyCd>
                    <PolicyNumber>DFIRE POLICY NUMBER</PolicyNumber>
                    <LOBCd>DFIRE</LOBCd>
                    <InsurerName>dfire company</InsurerName>
                    <ContractTerm>
                        <EffectiveDt>2001-03-03</EffectiveDt>
                        <ExpirationDt>2002-03-03</ExpirationDt>
                    </ContractTerm>
                    <Coverage>
                        <CoverageCd>PL</CoverageCd>
                        <Limit>
                            <FormatInteger>207</FormatInteger>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <QuestionAnswer>
                    <QuestionCd>PUMBR10</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>INMRC21</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>AUTOP06</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>HOME56</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>PUMBR11</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL24</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL31</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>PUMBR12</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>PUMBR01</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                    <Explanation>Real Estate used for business Description</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>PUMBR02</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                    <Explanation>Real Estate Not Covered by Primary Description</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>PUMBR03</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                    <Explanation>Engaged in Farming Operations Description</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>PUMBR04</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                    <Explanation>Non-Compensated Positions Description</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>PUMBR06</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                    <Explanation>Non-Owned Property Description</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>PUMBR07</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                    <Explanation>Business Activities Description</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>PUMBR08</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                    <Explanation>Reduced Limits of Liability Description</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>PUMBR09</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL06</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                    <Explanation>5 Driver 5 - Declined Cancelled Description</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL26</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                    <Explanation>Insurance Transferred Description</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>GENRL42</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>CPR92</QuestionCd>
                    <YesNoCd>NO</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>AUTOP13</QuestionCd>
                    <YesNoCd>YES</YesNoCd>
                </QuestionAnswer>
            </PersUmbrellaLineBusiness>
            <RemarkText IdRef="PERUMBR1">General Remarks</RemarkText>
            <RemarkText>Bi-Mthly</RemarkText>
            <RemarkText IdRef="PAYMETH1">PAY METHOD</RemarkText>
            <RemarkText IdRef="ESTATETYPE1">Apartment</RemarkText>
            <RemarkText IdRef="ESTATETYPE2">Dwelling-Insured Residence (non-farm)</RemarkText>
            <RemarkText IdRef="ESTATETYPE3">Co-op</RemarkText>
            <RemarkText IdRef="ESTATETYPE4">Condo</RemarkText>
            <RemarkText IdRef="USETYPE4">Other Usage Desc</RemarkText>
            <RemarkText IdRef="ESTATETYPE5">Other Residence Code</RemarkText>
            <RemarkText IdRef="OCCTYPE5">Other</RemarkText>
            <RemarkText IdRef="USETYPE5">Other Use</RemarkText>
            <RemarkText IdRef="ESTATETYPE6">Townhouse</RemarkText>
            <RemarkText IdRef="ESTATETYPE7">Row House</RemarkText>
            <RemarkText IdRef="DRV1">Drv 1 Ot</RemarkText>
            <RemarkText IdRef="DRV2">Drv 2 Ot</RemarkText>
            <RemarkText IdRef="DRV3">Drv 3 Ot</RemarkText>
            <RemarkText IdRef="DRV4">Drv 4 Ot</RemarkText>
            <RemarkText IdRef="DRV5">Drv 5 Ot</RemarkText>
            <RemarkText IdRef="DRV6">Drv 6 Ot</RemarkText>
            <RemarkText IdRef="PROPTYPE3">Other Power</RemarkText>
            <RemarkText IdRef="WATRNAV3">Other Waters Navigat</RemarkText>
            <FileAttachmentInfo>
                <AttachmentTypeCd>SS</AttachmentTypeCd>
            </FileAttachmentInfo>
            <FileAttachmentInfo>
                <AttachmentDesc>Other Attachment 1</AttachmentDesc>
                <AttachmentTypeCd>OTH</AttachmentTypeCd>
            </FileAttachmentInfo>
            <FileAttachmentInfo>
                <AttachmentDesc>Other Attachment 2</AttachmentDesc>
                <AttachmentTypeCd>OTH</AttachmentTypeCd>
            </FileAttachmentInfo>
        </PersUmbrellaPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
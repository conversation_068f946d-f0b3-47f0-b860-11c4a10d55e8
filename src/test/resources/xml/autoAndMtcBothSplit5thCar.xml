<ACORD>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName>AMSServices.com</SPName>
                <CustPermId/>
                <CustLoginId><EMAIL></CustLoginId>
            </CustId>
            <CustPswd>
                <EncryptionTypeCd>NONE</EncryptionTypeCd>
                <Pswd/>
            </CustPswd>
        </SignonPswd>
        <ClientDt>6/17/2020 5:25 AM</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>AMS Services</Org>
            <Name>Transit</Name>
            <Version>V2.5.5</Version>
        </ClientApp>
        <ProxyClient>
            <Org>BCFTech</Org>
            <Name>TransmitXML</Name>
            <Version>V1.00</Version>
        </ProxyClient>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>7E02FD57-F685-4104-877E-CBC439F740D0</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <RqUID>9E9932BC-C297-4DF9-B8F6-18CFB7E7C831</RqUID>
            <TransactionRequestDt>2020-06-11T12:05:49</TransactionRequestDt>
            <TransactionEffectiveDt>2020-01-30</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ContractNumber/>
                    <ProducerRoleCd>Agency</ProducerRoleCd>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <Surname>Armstrong</Surname>
                            <GivenName>James</GivenName>
                        </PersonName>
                        <LegalEntityCd>IN</LegalEntityCd>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>96a Cove Road</Addr1>
                        <City>Oyster Bay</City>
                        <StateProvCd>NY</StateProvCd>
                        <PostalCode>11771</PostalCode>
                    </Addr>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy id="PolicyLevel">
                <PolicyNumber>0048407913</PolicyNumber>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>19429P</NAICCd>
                <ContractTerm>
                    <EffectiveDt>2020-01-30</EffectiveDt>
                    <ExpirationDt>2021-01-30</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                        <UnitMeasurementCd>MON</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <BillingMethodCd>CPB</BillingMethodCd>
                <CurrentTermAmt>
                    <Amt>4880.00</Amt>
                </CurrentTermAmt>
                <Form>
                    <FormNumber>FS20</FormNumber>
                    <FormName>FS-20-New York Vehicle ID Card</FormName>
                    <EditionDt>2001-10-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAAG</FormNumber>
                    <FormName>PCSA-AG-Accident Report Guide</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAAMDPPN</FormNumber>
                    <FormName>PCSA-AMDPP-NY-Amendment of Policy Provisions - New</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSADECNY</FormNumber>
                    <FormName>PCSA-DEC-NY-Premier Client Solutions Automobile De</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSADWINOT</FormNumber>
                    <FormName>PCSA-DWINOT-NY-DWI Disclosure Notice - New York</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAFCR</FormNumber>
                    <FormName>PCSA-FCR-Important Notice Regarding The Fair Credi</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAFEDGBN</FormNumber>
                    <FormName>PCSA-FEDGB-NY-Federal Employees Using Autos in Gov</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAJKT</FormNumber>
                    <FormName>PCSA-JKT-Premier Client Solutions Auto Policy Jack</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAMILNOT</FormNumber>
                    <FormName>PCSA-MILNOT-NY-Notice To Policyholders Serving In</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAMPIPNY</FormNumber>
                    <FormName>PCSA-MPIP-NY-Personal Injury Protection Coverage -</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAMVLEFE</FormNumber>
                    <FormName>PCSA-MVLEFEE-NY-Motor Vehicle Law Enforcement Fee</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAOFAC</FormNumber>
                    <FormName>PCSA-OFAC-Economic Sanctions Endorsement</FormName>
                    <EditionDt>2013-12-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAPAPII</FormNumber>
                    <FormName>PCSA-PAP II-Personal Auto Policy</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSARTINFO</FormNumber>
                    <FormName>PCSA-RTINFO-NY-Rating Information - New York</FormName>
                    <EditionDt>2017-02-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSARVCNY</FormNumber>
                    <FormName>PCSA-RVC-NY-Rental Vehicle Coverage Endorsement -</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSATIERNO</FormNumber>
                    <FormName>PCSA-TIERNOT-NY-Multi-Tier Disclosure Notice - New</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAUMSUMN</FormNumber>
                    <FormName>PCSA-UMSUMNOT-NY-UM/SUM Explanatory Notice - New Y</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAADIL</FormNumber>
                    <FormName>PCSA-ADIL-Additional Insured - Lessor</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAAPIPNY</FormNumber>
                    <FormName>PCSA-APIP-NY-Additional Personal Injury Protection</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAAUL</FormNumber>
                    <FormName>PCSA-AUL-Autolock Coverage</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAEXNO</FormNumber>
                    <FormName>PCSA-EXNO-Extended Non-Owned Coverage - Vehicles F</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAFULLGL</FormNumber>
                    <FormName>PCSA-FULLGL-Full Coverage Window Glass Endorsement</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAITC</FormNumber>
                    <FormName>PCSA-ITC-Increased Towing Coverage</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSALPC</FormNumber>
                    <FormName>PCSA-LPC-Loss Payable Clause</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAMISCVE</FormNumber>
                    <FormName>PCSA-MISCVEH-NY-Miscellaneous Type Vehicle Endorse</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAMOPIPN</FormNumber>
                    <FormName>PCSA-MOPIP-NY-Personal Injury Protection Coverage</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAOBELNY</FormNumber>
                    <FormName>PCSA-OBEL-NY-Optional Basic Economic Loss Coverage</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAPIPSEL</FormNumber>
                    <FormName>PCSA-PIPSEL-NY-Selection of Personal Injury Protec</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSARDS</FormNumber>
                    <FormName>PCSA-RDS-Roadside Coverage</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSASLLNY</FormNumber>
                    <FormName>PCSA-SLL-NY-Single Liability Limit - New York</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSASSLUMU</FormNumber>
                    <FormName>PCSA-SSL-UMUIM-NY-Single Limit Supplemental Uninsu</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAUMNY</FormNumber>
                    <FormName>PCSA-UM-NY-Uninsured Motorists Endorsement - New Y</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>91222PCS</FormNumber>
                    <FormName>91222 PCS-Policyholder Notice</FormName>
                    <EditionDt>2016-09-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSAAANNOT</FormNumber>
                    <FormName>PCSA-AANNOT-Important Notice - Auto Policy - Consu</FormName>
                    <EditionDt>2012-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSANYNBR</FormNumber>
                    <FormName>PCSA-NY-NB-R-An Update For Our New York Policyhold</FormName>
                    <EditionDt>2013-04-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSDESNOTN</FormNumber>
                    <FormName>PCS-DESNOT-NY-Important Notice For Senior Policyho</FormName>
                    <EditionDt>2016-05-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSFEENOTI</FormNumber>
                    <FormName>PCS-FEENOTICE-Installment Fees</FormName>
                    <EditionDt>2018-08-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSPRIVNOT</FormNumber>
                    <FormName>PCS-PRIVNOT-Privacy and Data Security Notice</FormName>
                    <EditionDt>2018-08-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <Form>
                    <FormNumber>PCSARL</FormNumber>
                    <FormName>PCSA-RL-Auto Renewal Letter for Premier Clients</FormName>
                    <EditionDt>2012-06-01</EditionDt>
                    <IterationNumber>001</IterationNumber>
                </Form>
                <PersApplicationInfo>
                    <InsuredOrPrincipal/>
                </PersApplicationInfo>
                <AccidentViolation DriverRef="D1">
                    <AccidentViolationDt>2013-11-27</AccidentViolationDt>
                    <NumSurchargePoints>1</NumSurchargePoints>
                    <AccidentViolationDesc>001, SPEEDING GENERAL</AccidentViolationDesc>
                    <PlaceIncident>NY</PlaceIncident>
                    <AccidentViolationRecordTypeCd>VIO</AccidentViolationRecordTypeCd>
                </AccidentViolation>
                <AccidentViolation DriverRef="D1">
                    <AccidentViolationDt>2017-03-20</AccidentViolationDt>
                    <AccidentViolationDesc>002, A-GLASS - COMP</AccidentViolationDesc>
                    <PlaceIncident>NY</PlaceIncident>
                    <AccidentViolationRecordTypeCd>VIO</AccidentViolationRecordTypeCd>
                </AccidentViolation>
                <AccidentViolation DriverRef="D1">
                    <AccidentViolationDt>2018-03-22</AccidentViolationDt>
                    <AccidentViolationDesc>003, A-OTHER - COMPREHENSIVE - COMP</AccidentViolationDesc>
                    <PlaceIncident>NY</PlaceIncident>
                    <AccidentViolationRecordTypeCd>VIO</AccidentViolationRecordTypeCd>
                </AccidentViolation>
                <AccidentViolation DriverRef="D1">
                    <AccidentViolationDt>2019-08-09</AccidentViolationDt>
                    <AccidentViolationDesc>004, A-INS HIT UNOCC PARK VEH-AF</AccidentViolationDesc>
                    <PlaceIncident>NY</PlaceIncident>
                    <AccidentViolationRecordTypeCd>VIO</AccidentViolationRecordTypeCd>
                </AccidentViolation>
                <AccidentViolation DriverRef="D2">
                    <AccidentViolationDt>2011-02-21</AccidentViolationDt>
                    <AccidentViolationDesc>001, A-COMPREHENSIVE CLAIMS (GLASS)</AccidentViolationDesc>
                    <AccidentViolationRecordTypeCd>VIO</AccidentViolationRecordTypeCd>
                </AccidentViolation>
                <ControllingStateProvCd>NY</ControllingStateProvCd>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <NAICCd>19429P</NAICCd>
                <CurrentTermAmt>
                    <Amt>4880.00</Amt>
                </CurrentTermAmt>
                <NetChangeAmt>
                    <Amt>4880.00</Amt>
                </NetChangeAmt>
                <RateEffectiveDt>2020-01-30</RateEffectiveDt>
                <Coverage btId="1e9535a1-f3ea-4eac-90ca-21a93d3e5deb">
                    <CoverageCd>MCAR</CoverageCd>
                </Coverage>
                <Coverage btId="76223419-0576-423f-8244-04d0eb149a99">
                    <CoverageCd>LFREE</CoverageCd>
                </Coverage>
                <PersDriver id="D1">
                    <ItemIdInfo>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Armstrong</Surname>
                                <GivenName>James</GivenName>
                                <OtherGivenName>F</OtherGivenName>
                            </PersonName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1952-08-08</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>575518216</DriversLicenseNumber>
                            <StateProvCd>NY</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicenseTypeCd>Driver</LicenseTypeCd>
                            <LicensePermitNumber>575518216</LicensePermitNumber>
                            <StateProvCd>NY</StateProvCd>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo id="PD1">
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="D2">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Armstrong</Surname>
                                <GivenName>Leslie</GivenName>
                                <OtherGivenName>N</OtherGivenName>
                            </PersonName>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1953-04-23</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                        </PersonInfo>
                        <DriversLicense>
                            <DriversLicenseNumber>263949789</DriversLicenseNumber>
                            <StateProvCd>NY</StateProvCd>
                        </DriversLicense>
                        <License>
                            <LicenseTypeCd>Driver</LicenseTypeCd>
                            <LicensePermitNumber>263949789</LicensePermitNumber>
                            <StateProvCd>NY</StateProvCd>
                        </License>
                    </DriverInfo>
                    <PersDriverInfo id="PD2">
                        <DistantStudentInd>0</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>0</DriverTrainingInd>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="L1" id="V1">
                    <ItemIdInfo>
                        <InsurerId>0001</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>CHEVROLET</Manufacturer>
                    <Model>SUBURBAN 1500 LT</Model>
                    <ModelYear>2012</ModelYear>
                    <VehBodyTypeCd>PP</VehBodyTypeCd>
                    <VehTypeCd>PP</VehTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <EstimatedAnnualDistance>
                        <NumUnits>21831</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>1GNSKJE79CR227869</VehIdentificationNumber>
                    <VehSymbolCd>20</VehSymbolCd>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>TD Auto Finance LLC</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>Po Box 9224</Addr1>
                                <City>Farmington Hills</City>
                                <StateProvCd>MI</StateProvCd>
                                <PostalCode>48333</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LOSSP</NatureInterestCd>
                            <InterestIdNumber>001</InterestIdNumber>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <AntiLockBrakeCd>A</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>Y</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="0ba3f3b7-66c7-4ae6-aea1-dde2e931f93e" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>175000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>163.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="227eb93e-0bcf-419f-bb48-940d1db05477" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="867db052-76d0-464d-96d9-0936090ffafd" bookTransferAssociatedNodeId="V1">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>135.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="35bb1f8e-2e1a-4677-9279-5fa9ff79d556" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>APIP</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>IncEconLoss</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>21.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="b559663a-ed12-4b64-898c-24d6dfddbd17" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="b715375d-76ec-419b-84e2-24be4bbc56b1" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>176.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="3b148729-bdb1-464a-bb16-dfb48fa987cb" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>EXNON</CoverageCd>
                        <CurrentTermAmt>
                            <Amt>290.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="ff9b45c0-f698-4989-8413-a7e5c078e81b" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible btId="4f309265-eaad-4a58-9200-226a0e1eba50" bookTransferAssociatedNodeId="V1">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>487.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f30d9344-9b06-4008-899f-5f7c6bab5ae7" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>ROAD</CoverageCd>
                        <CurrentTermAmt>
                            <Amt>8.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="49f2a451-54a0-4714-a37a-1d7fd120cbb7" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>3.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="3fafbc12-5229-4049-8324-8a888341640a" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible btId="e5009ace-7fe2-40c9-aed9-a3698dfb5790" bookTransferAssociatedNodeId="V1">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>99.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="bc71588e-5571-4946-ace9-a4f154fbd5ef" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>LOCK</CoverageCd>
                        <CurrentTermAmt>
                            <Amt>5.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="ee8030cf-008b-4a33-8a43-a974a49e2f92" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>TL</CoverageCd>
                        <Limit>
                            <FormatInteger>50</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>10.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="113edd4f-f0d5-4f9a-b0eb-67b03bd15c89" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>945.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="11eeb1b6-da72-4a7c-b956-dae02adf00d8" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>TRNEX</CoverageCd>
                        <Limit>
                            <FormatInteger>1200</FormatInteger>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage btId="107a47f2-b73b-4297-9df5-e11da4afd0b8" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="f1f0a0c6-f518-40b4-a3b4-841ad33bf29c" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="0e60cda1-8088-468a-90e9-50b4d524d1a3" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                    <Coverage btId="5c69ba82-0831-4230-b1be-edd651263ce3" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>ABS</CoverageCd>
                    </Coverage>
                    <Coverage btId="01e135e4-9b77-406f-805a-5fd12d17d228" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>ANTHF</CoverageCd>
                    </Coverage>
                    <Coverage btId="39ef8c04-796a-4bef-bacb-f5607c84ff0d" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>AUTBR</CoverageCd>
                    </Coverage>
                    <Coverage btId="57bff84b-2efd-4d06-8f4d-6b11163c5052" bookTransferAssociatedNodeId="V1">
                        <CoverageCd>PASSR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L2" id="V2">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L3" id="V3">
                    <ItemIdInfo>
                        <InsurerId>0003</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>AUDI</Manufacturer>
                    <Model>Q7 QUATTRO PREMIUM P</Model>
                    <ModelYear>2018</ModelYear>
                    <VehBodyTypeCd>PP</VehBodyTypeCd>
                    <VehTypeCd>PP</VehTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>70415</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>21624</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>WA1LAAF75JD025484</VehIdentificationNumber>
                    <VehSymbolCd>58</VehSymbolCd>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>USB Leasing LT</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>1850 Osborn Ave</Addr1>
                                <City>Oshkosh</City>
                                <StateProvCd>WI</StateProvCd>
                                <PostalCode>54902</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LOSSPAI</NatureInterestCd>
                            <InterestIdNumber>002</InterestIdNumber>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <AntiLockBrakeCd>A</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>Y</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="38c553ec-ae67-4553-ac0e-dafa4d9f3812" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>175000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>181.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="1fc32649-dca4-4cf1-946f-de1c29be6550" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="2d23ac91-0ea3-48ce-a425-f65c28b00909" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>153.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="aa666c5b-6ea0-44ab-8d4d-fd329d713c16" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>APIP</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>IncEconLoss</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>21.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="636a8b82-538a-4ec0-abe4-2fcf3de0a2a4" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="8c39ea81-d3ef-4667-a12b-76492a59f557" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>176.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="06585e6f-f61a-46b1-9ebd-dfb747a520cb" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible btId="5ea9954c-4c16-41cc-9c32-f9c9ef7c5154" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>879.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="3a36fd1f-37a0-43c1-9f38-8895b5d1bf05" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ROAD</CoverageCd>
                        <CurrentTermAmt>
                            <Amt>8.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="a1cdc334-df94-467c-8c9a-3ca9fc95000d" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>3.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="630d5946-447b-49ea-9330-9fcd962b3d4a" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible btId="ac695c2d-eb75-476f-aa3e-fc4ff5934a2b" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>172.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="1a74a18f-423b-4424-8d08-d58a293a7f07" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>TL</CoverageCd>
                        <Limit>
                            <FormatInteger>50</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage btId="7ab0d472-2bd2-44a9-abe5-e16bc4091948" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>669.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="24ddab97-6a9f-4c9e-b436-f56fe9f89452" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>TRNEX</CoverageCd>
                        <Limit>
                            <FormatInteger>1200</FormatInteger>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage btId="c0e06413-18c7-458a-9fdd-0640eaded081" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="aae5d95d-25b3-40ea-8d72-f1785ea617aa" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="d7772edb-f92a-4ff8-a234-0c969c5633ef" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                    <Coverage btId="aef98967-590c-4467-9779-7e61e3486f61" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ABS</CoverageCd>
                    </Coverage>
                    <Coverage btId="1b5b049d-7223-461d-935b-f61d441c0c59" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ANTHF</CoverageCd>
                    </Coverage>
                    <Coverage btId="d61d9bed-7f8b-494d-8a84-1c6e3c38b741" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PASSR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L3" id="V4">
                    <ItemIdInfo>
                        <InsurerId>0003</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>AUDI</Manufacturer>
                    <Model>Q7 QUATTRO PREMIUM P</Model>
                    <ModelYear>2018</ModelYear>
                    <VehBodyTypeCd>PP</VehBodyTypeCd>
                    <VehTypeCd>PP</VehTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>70415</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>21624</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>WA1LAAF75JD025484</VehIdentificationNumber>
                    <VehSymbolCd>58</VehSymbolCd>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>USB Leasing LT</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>1850 Osborn Ave</Addr1>
                                <City>Oshkosh</City>
                                <StateProvCd>WI</StateProvCd>
                                <PostalCode>54902</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LOSSPAI</NatureInterestCd>
                            <InterestIdNumber>002</InterestIdNumber>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <AntiLockBrakeCd>A</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>Y</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="38c553ec-ae67-4553-ac0e-dafa4d9f3812" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>175000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>181.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="1fc32649-dca4-4cf1-946f-de1c29be6550" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="2d23ac91-0ea3-48ce-a425-f65c28b00909" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>153.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="aa666c5b-6ea0-44ab-8d4d-fd329d713c16" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>APIP</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>IncEconLoss</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>21.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="636a8b82-538a-4ec0-abe4-2fcf3de0a2a4" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="8c39ea81-d3ef-4667-a12b-76492a59f557" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>176.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="06585e6f-f61a-46b1-9ebd-dfb747a520cb" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible btId="5ea9954c-4c16-41cc-9c32-f9c9ef7c5154" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>879.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="3a36fd1f-37a0-43c1-9f38-8895b5d1bf05" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ROAD</CoverageCd>
                        <CurrentTermAmt>
                            <Amt>8.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="a1cdc334-df94-467c-8c9a-3ca9fc95000d" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>3.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="630d5946-447b-49ea-9330-9fcd962b3d4a" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible btId="ac695c2d-eb75-476f-aa3e-fc4ff5934a2b" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>172.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="1a74a18f-423b-4424-8d08-d58a293a7f07" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>TL</CoverageCd>
                        <Limit>
                            <FormatInteger>50</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage btId="7ab0d472-2bd2-44a9-abe5-e16bc4091948" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>669.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="24ddab97-6a9f-4c9e-b436-f56fe9f89452" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>TRNEX</CoverageCd>
                        <Limit>
                            <FormatInteger>1200</FormatInteger>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage btId="c0e06413-18c7-458a-9fdd-0640eaded081" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="aae5d95d-25b3-40ea-8d72-f1785ea617aa" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="d7772edb-f92a-4ff8-a234-0c969c5633ef" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                    <Coverage btId="aef98967-590c-4467-9779-7e61e3486f61" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ABS</CoverageCd>
                    </Coverage>
                    <Coverage btId="1b5b049d-7223-461d-935b-f61d441c0c59" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ANTHF</CoverageCd>
                    </Coverage>
                    <Coverage btId="d61d9bed-7f8b-494d-8a84-1c6e3c38b741" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PASSR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L3" id="V5">
                    <ItemIdInfo>
                        <InsurerId>0003</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>AUDI</Manufacturer>
                    <Model>Q7 QUATTRO PREMIUM P</Model>
                    <ModelYear>2018</ModelYear>
                    <VehBodyTypeCd>PP</VehBodyTypeCd>
                    <VehTypeCd>PP</VehTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>70415</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>21624</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>WA1LAAF75JD025484</VehIdentificationNumber>
                    <VehSymbolCd>58</VehSymbolCd>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>USB Leasing LT</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>1850 Osborn Ave</Addr1>
                                <City>Oshkosh</City>
                                <StateProvCd>WI</StateProvCd>
                                <PostalCode>54902</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LOSSPAI</NatureInterestCd>
                            <InterestIdNumber>002</InterestIdNumber>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <AntiLockBrakeCd>A</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>Y</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="38c553ec-ae67-4553-ac0e-dafa4d9f3812" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>175000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>181.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="1fc32649-dca4-4cf1-946f-de1c29be6550" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="2d23ac91-0ea3-48ce-a425-f65c28b00909" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>153.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="aa666c5b-6ea0-44ab-8d4d-fd329d713c16" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>APIP</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>IncEconLoss</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>21.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="636a8b82-538a-4ec0-abe4-2fcf3de0a2a4" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="8c39ea81-d3ef-4667-a12b-76492a59f557" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>176.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="06585e6f-f61a-46b1-9ebd-dfb747a520cb" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible btId="5ea9954c-4c16-41cc-9c32-f9c9ef7c5154" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>879.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="3a36fd1f-37a0-43c1-9f38-8895b5d1bf05" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ROAD</CoverageCd>
                        <CurrentTermAmt>
                            <Amt>8.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="a1cdc334-df94-467c-8c9a-3ca9fc95000d" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>3.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="630d5946-447b-49ea-9330-9fcd962b3d4a" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible btId="ac695c2d-eb75-476f-aa3e-fc4ff5934a2b" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>172.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="1a74a18f-423b-4424-8d08-d58a293a7f07" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>TL</CoverageCd>
                        <Limit>
                            <FormatInteger>50</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage btId="7ab0d472-2bd2-44a9-abe5-e16bc4091948" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>669.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="24ddab97-6a9f-4c9e-b436-f56fe9f89452" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>TRNEX</CoverageCd>
                        <Limit>
                            <FormatInteger>1200</FormatInteger>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage btId="c0e06413-18c7-458a-9fdd-0640eaded081" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="aae5d95d-25b3-40ea-8d72-f1785ea617aa" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="d7772edb-f92a-4ff8-a234-0c969c5633ef" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                    <Coverage btId="aef98967-590c-4467-9779-7e61e3486f61" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ABS</CoverageCd>
                    </Coverage>
                    <Coverage btId="1b5b049d-7223-461d-935b-f61d441c0c59" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ANTHF</CoverageCd>
                    </Coverage>
                    <Coverage btId="d61d9bed-7f8b-494d-8a84-1c6e3c38b741" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PASSR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L3" id="V6">
                    <ItemIdInfo>
                        <InsurerId>0003</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>AUDI</Manufacturer>
                    <Model>Q7 QUATTRO PREMIUM P</Model>
                    <ModelYear>2018</ModelYear>
                    <VehBodyTypeCd>PP</VehBodyTypeCd>
                    <VehTypeCd>PP</VehTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>70415</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>21624</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>WA1LAAF75JD025484</VehIdentificationNumber>
                    <VehSymbolCd>58</VehSymbolCd>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>USB Leasing LT</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>1850 Osborn Ave</Addr1>
                                <City>Oshkosh</City>
                                <StateProvCd>WI</StateProvCd>
                                <PostalCode>54902</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                        <AdditionalInterestInfo>
                            <NatureInterestCd>LOSSPAI</NatureInterestCd>
                            <InterestIdNumber>002</InterestIdNumber>
                        </AdditionalInterestInfo>
                    </AdditionalInterest>
                    <AntiLockBrakeCd>A</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>Y</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="38c553ec-ae67-4553-ac0e-dafa4d9f3812" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>175000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>181.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="1fc32649-dca4-4cf1-946f-de1c29be6550" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="2d23ac91-0ea3-48ce-a425-f65c28b00909" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>153.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="aa666c5b-6ea0-44ab-8d4d-fd329d713c16" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>APIP</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>IncEconLoss</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>21.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="636a8b82-538a-4ec0-abe4-2fcf3de0a2a4" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="8c39ea81-d3ef-4667-a12b-76492a59f557" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>176.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="06585e6f-f61a-46b1-9ebd-dfb747a520cb" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible btId="5ea9954c-4c16-41cc-9c32-f9c9ef7c5154" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>879.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="3a36fd1f-37a0-43c1-9f38-8895b5d1bf05" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ROAD</CoverageCd>
                        <CurrentTermAmt>
                            <Amt>8.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="a1cdc334-df94-467c-8c9a-3ca9fc95000d" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>3.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="630d5946-447b-49ea-9330-9fcd962b3d4a" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible btId="ac695c2d-eb75-476f-aa3e-fc4ff5934a2b" bookTransferAssociatedNodeId="V3">
                            <FormatInteger>1000</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>172.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="1a74a18f-423b-4424-8d08-d58a293a7f07" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>TL</CoverageCd>
                        <Limit>
                            <FormatInteger>50</FormatInteger>
                        </Limit>
                    </Coverage>
                    <Coverage btId="7ab0d472-2bd2-44a9-abe5-e16bc4091948" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>669.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="24ddab97-6a9f-4c9e-b436-f56fe9f89452" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>TRNEX</CoverageCd>
                        <Limit>
                            <FormatInteger>1200</FormatInteger>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage btId="c0e06413-18c7-458a-9fdd-0640eaded081" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="aae5d95d-25b3-40ea-8d72-f1785ea617aa" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="d7772edb-f92a-4ff8-a234-0c969c5633ef" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                    <Coverage btId="aef98967-590c-4467-9779-7e61e3486f61" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ABS</CoverageCd>
                    </Coverage>
                    <Coverage btId="1b5b049d-7223-461d-935b-f61d441c0c59" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>ANTHF</CoverageCd>
                    </Coverage>
                    <Coverage btId="d61d9bed-7f8b-494d-8a84-1c6e3c38b741" bookTransferAssociatedNodeId="V3">
                        <CoverageCd>PASSR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L2" id="V7">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L2" id="V8">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L2" id="V9">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="L2" id="V10">
                    <ItemIdInfo>
                        <InsurerId>0002</InsurerId>
                    </ItemIdInfo>
                    <Manufacturer>HONDA</Manufacturer>
                    <Model>CH150LH</Model>
                    <ModelYear>1987</ModelYear>
                    <VehBodyTypeCd>MC</VehBodyTypeCd>
                    <Registration>
                        <RegistrationId>Unknown</RegistrationId>
                        <StateProvCd>NY</StateProvCd>
                    </Registration>
                    <CostNewAmt>
                        <Amt>5000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>1000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <PurchaseDt>2020-01-30</PurchaseDt>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>022</TerritoryCd>
                    <VehIdentificationNumber>JH2KF0117HK205563</VehIdentificationNumber>
                    <VehSymbolCd>7</VehSymbolCd>
                    <AntiLockBrakeCd>D</AntiLockBrakeCd>
                    <NewVehInd>0</NewVehInd>
                    <AntiTheftDeviceCd>N</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage btId="222fcbaa-1407-4d8e-a1d8-b7c663656a4b" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>Total</CoverageCd>
                        <Limit>
                            <FormatInteger>75000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>26.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="4ac43a29-234f-4ade-8f8d-6bf464c059df" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PIP</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <Deductible btId="4abcdb7b-2303-46d1-9554-fa0ce7a52bfc" bookTransferAssociatedNodeId="V2">
                            <FormatInteger>200</FormatInteger>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt>19.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="9e825f62-568f-48bf-aefb-7d02fc689635" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>OBEL</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>Statutory</LimitAppliesToCd>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>7.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="f97a7f5f-9528-4a21-8ab5-c72176f42cc2" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>SUMCS</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>352.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="c7d718dc-2170-4d44-95ae-cfe2ab69b002" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>5000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>6.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="cedea411-9534-4686-b78f-c7f25a44b56c" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>500000</FormatInteger>
                        </Limit>
                        <CurrentTermAmt>
                            <Amt>202.00</Amt>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage btId="5f4f8cbb-6c65-4426-812c-825094d3a19a" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>PEXLI</CoverageCd>
                    </Coverage>
                    <Coverage btId="c0559555-e1b0-486c-9776-6ed077ef5c36" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>HODIS</CoverageCd>
                    </Coverage>
                    <Coverage btId="3a01e8bd-3eec-4d2a-9825-cdb9473d4be7" bookTransferAssociatedNodeId="V2">
                        <CoverageCd>VALAR</CoverageCd>
                    </Coverage>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="L1">
                <ItemIdInfo>
                    <AgencyId>0001</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>96a Cove Road</Addr1>
                    <City>Oyster Bay</City>
                    <StateProvCd>NY</StateProvCd>
                    <PostalCode>11771</PostalCode>
                </Addr>
            </Location>
            <Location id="L2">
                <ItemIdInfo>
                    <AgencyId>0002</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>96a Cove Road</Addr1>
                    <City>Oyster Bay</City>
                    <StateProvCd>NY</StateProvCd>
                    <PostalCode>11771</PostalCode>
                </Addr>
            </Location>
            <Location id="L3">
                <ItemIdInfo>
                    <AgencyId>0003</AgencyId>
                </ItemIdInfo>
                <Addr>
                    <Addr1>96a Cove Road</Addr1>
                    <City>Oyster Bay</City>
                    <StateProvCd>NY</StateProvCd>
                    <PostalCode>11771</PostalCode>
                </Addr>
            </Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
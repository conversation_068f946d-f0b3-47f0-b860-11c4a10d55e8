<ACORD>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName>www.quotepro.com</SPName>
                <CustLoginId>FakeCusomterLoginID</CustLoginId>
            </CustId>
            <CustPswd>
                <EncryptionTypeCd>NONE</EncryptionTypeCd>
                <Pswd>test</Pswd>
            </CustPswd>
        </SignonPswd>
        <SignonTransport>
            <SignonRoleCd>QUOTEPRO</SignonRoleCd>
            <CustId>
                <SPName>www.quotepro.com</SPName>
                <CustPermId>523628</CustPermId>
            </CustId>
        </SignonTransport>
        <ClientDt>2021-05-11T11:31:17</ClientDt>
        <CustLangPref>English</CustLangPref>
        <ClientApp>
            <Org>QUOTEPRO INC</Org>
            <Name>REALTIMERATE</Name>
            <Version>0.38</Version>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>8a7731d2-74b8-467e-854c-c1e14568b8f9</RqUID>
        <PersPkgPolicyQuoteInqRq>
            <com.safeco_TransactionType>QuoteRq</com.safeco_TransactionType>
            <RqUID>8a7731d2-74b8-467e-854c-c1e14568b8f9</RqUID>
            <TransactionRequestDt>2021-05-11T11:31:17</TransactionRequestDt>
            <TransactionEffectiveDt>2021-05-11T11:31:17</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <ItemIdInfo>
                    <InsurerId>523628</InsurerId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Test Name</CommercialName>
                        </CommlName>
                    </NameInfo>
                </GeneralPartyInfo>
                <ProducerInfo>
                    <ContractNumber>530818</ContractNumber>
                    <ProducerSubCode>test</ProducerSubCode>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>17721</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <Surname>Duck</Surname>
                            <GivenName>Daisy</GivenName>
                        </PersonName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId/>
                        </TaxIdentity>
                    </NameInfo>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-8888888</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <CommunicationUseCd>Work</CommunicationUseCd>
                            <PhoneNumber>******-4567890</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo>
                            <CommunicationUseCd>Cell</CommunicationUseCd>
                            <PhoneNumber/>
                        </PhoneInfo>
                        <PhoneInfo>
                            <CommunicationUseCd>Fax</CommunicationUseCd>
                            <PhoneNumber/>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <PersonInfo>
                        <GenderCd>F</GenderCd>
                        <BirthDt>1987-01-01</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <LOBCd>AUTOP</LOBCd>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>000</InsurerName>
                    <ContractTerm/>
                </OtherOrPriorPolicy>
                <CreditScoreInfo>
                    <com.safeco_ConsumerCreditScoreCd>IBS</com.safeco_ConsumerCreditScoreCd>
                </CreditScoreInfo>
                <QuestionAnswer>
                    <QuestionCd>Current_Insurance_Value</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                    <Explanation>NV</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Credit_Accept_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Cancelled_Declined?</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <PersApplicationInfo>
                    <InsuredOrPrincipal>
                        <GeneralPartyInfo>
                            <Addr>
                                <AddrTypeCd>StreetAddress</AddrTypeCd>
                                <AddrTypeCd>MailingAddress</AddrTypeCd>
                                <Addr1>1401 Bearwallow Rd</Addr1>
                                <City>ASHLAND CITY</City>
                                <StateProvCd>TN</StateProvCd>
                                <PostalCode>37015</PostalCode>
                                <County>CHEATHAM</County>
                            </Addr>
                        </GeneralPartyInfo>
                    </InsuredOrPrincipal>
                    <CurrentResidenceDt>2021-05-11</CurrentResidenceDt>
                    <ResidenceOwnedRentedCd>RENTD</ResidenceOwnedRentedCd>
                    <NumResidentsInHousehold>2</NumResidentsInHousehold>
                    <NumVehsInHousehold>1</NumVehsInHousehold>
                    <ResidenceTypeCd>APT</ResidenceTypeCd>
                </PersApplicationInfo>
                <DriverVeh DriverRef="Drv1" VehRef="Veh1">
                    <UsePct>60</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="Drv2" VehRef="Veh1">
                    <UsePct>40</UsePct>
                </DriverVeh>
            </PersPolicy>
            <Location id="Gar1">
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>1401 Bearwallow Rd</Addr1>
                    <City>ASHLAND CITY</City>
                    <StateProvCd>TN</StateProvCd>
                    <PostalCode>37015</PostalCode>
                    <County>CHEATHAM</County>
                </Addr>
            </Location>
            <PersDriver id="Drv1">
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <Surname>Duck</Surname>
                            <GivenName>Daisy</GivenName>
                        </PersonName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId/>
                        </TaxIdentity>
                    </NameInfo>
                </GeneralPartyInfo>
                <DriverInfo>
                    <PersonInfo>
                        <com.safeco_IndustryCd>Banking/Finance/RE</com.safeco_IndustryCd>
                        <com.safeco_OccupationCd>Accountant/Auditor</com.safeco_OccupationCd>
                        <GenderCd>F</GenderCd>
                        <BirthDt>1987-01-01</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                        <OccupationClassCd>ACCOUNTANT</OccupationClassCd>
                        <MiscParty>
                            <GeneralPartyInfo>
                                <NameInfo>
                                    <CommlName>
                                        <CommercialName/>
                                    </CommlName>
                                </NameInfo>
                                <Addr>
                                    <AddrTypeCd>StreetAddress</AddrTypeCd>
                                    <Addr1/>
                                    <Addr2/>
                                    <City/>
                                    <StateProvCd>TN</StateProvCd>
                                    <PostalCode/>
                                    <County/>
                                </Addr>
                            </GeneralPartyInfo>
                            <MiscPartyInfo>
                                <MiscPartyRoleCd>Employer</MiscPartyRoleCd>
                            </MiscPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                    <DriversLicense>
                        <LicensedDt>2003-01-01</LicensedDt>
                        <DriversLicenseNumber>11111111</DriversLicenseNumber>
                        <LicenseClassCd>VALID</LicenseClassCd>
                        <StateProvCd>TN</StateProvCd>
                    </DriversLicense>
                    <QuestionAnswer>
                        <QuestionCd>Education</QuestionCd>
                        <YesNoCd>Y</YesNoCd>
                        <Explanation>HSD</Explanation>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>Suspended_Revoked?</QuestionCd>
                        <YesNoCd>N</YesNoCd>
                    </QuestionAnswer>
                    <com.safeco_DriversLicenseStatus>VALID</com.safeco_DriversLicenseStatus>
                </DriverInfo>
                <PersDriverInfo>
                    <DefensiveDriverDt/>
                    <DistantStudentInd>True</DistantStudentInd>
                    <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                    <DriverTrainingInd>N</DriverTrainingInd>
                    <FinancialResponsibilityFiling>
                        <FilingStatusCd>N</FilingStatusCd>
                    </FinancialResponsibilityFiling>
                    <GoodDriverInd>False</GoodDriverInd>
                    <GoodStudentCd>N</GoodStudentCd>
                    <com.safeco_HouseholdResidentInd>1</com.safeco_HouseholdResidentInd>
                </PersDriverInfo>
            </PersDriver>
            <PersDriver id="Drv2">
                <GeneralPartyInfo>
                    <NameInfo>
                        <PersonName>
                            <Surname>Sexton</Surname>
                            <GivenName>Derek</GivenName>
                        </PersonName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId/>
                        </TaxIdentity>
                    </NameInfo>
                </GeneralPartyInfo>
                <DriverInfo>
                    <PersonInfo>
                        <com.safeco_IndustryCd>Banking/Finance/RE</com.safeco_IndustryCd>
                        <com.safeco_OccupationCd>Accountant/Auditor</com.safeco_OccupationCd>
                        <GenderCd>M</GenderCd>
                        <BirthDt>1984-01-01</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                        <OccupationClassCd>ACCOUNTANT</OccupationClassCd>
                        <MiscParty>
                            <GeneralPartyInfo>
                                <NameInfo>
                                    <CommlName>
                                        <CommercialName/>
                                    </CommlName>
                                </NameInfo>
                                <Addr>
                                    <AddrTypeCd>StreetAddress</AddrTypeCd>
                                    <Addr1/>
                                    <Addr2/>
                                    <City/>
                                    <StateProvCd>TN</StateProvCd>
                                    <PostalCode/>
                                    <County/>
                                </Addr>
                            </GeneralPartyInfo>
                            <MiscPartyInfo>
                                <MiscPartyRoleCd>Employer</MiscPartyRoleCd>
                            </MiscPartyInfo>
                        </MiscParty>
                    </PersonInfo>
                    <DriversLicense>
                        <LicensedDt>2000-01-01</LicensedDt>
                        <DriversLicenseNumber>022222222</DriversLicenseNumber>
                        <LicenseClassCd>VALID</LicenseClassCd>
                        <StateProvCd>TN</StateProvCd>
                    </DriversLicense>
                    <QuestionAnswer>
                        <QuestionCd>Education</QuestionCd>
                        <YesNoCd>Y</YesNoCd>
                        <Explanation>HSD</Explanation>
                    </QuestionAnswer>
                    <QuestionAnswer>
                        <QuestionCd>Suspended_Revoked?</QuestionCd>
                        <YesNoCd>N</YesNoCd>
                    </QuestionAnswer>
                    <com.safeco_DriversLicenseStatus>VALID</com.safeco_DriversLicenseStatus>
                    <com.safeco_MotorcycleYears>13</com.safeco_MotorcycleYears>
                </DriverInfo>
                <PersDriverInfo>
                    <DefensiveDriverDt/>
                    <DistantStudentInd>True</DistantStudentInd>
                    <DriverRelationshipToApplicantCd>OT</DriverRelationshipToApplicantCd>
                    <DriverTrainingInd>N</DriverTrainingInd>
                    <FinancialResponsibilityFiling>
                        <FilingStatusCd>N</FilingStatusCd>
                    </FinancialResponsibilityFiling>
                    <GoodDriverInd>False</GoodDriverInd>
                    <GoodStudentCd>N</GoodStudentCd>
                    <com.safeco_HouseholdResidentInd>1</com.safeco_HouseholdResidentInd>
                </PersDriverInfo>
            </PersDriver>
            <PersVeh LocationRef="Gar1" id="Veh1">
                <Manufacturer>HARLEY-DAVIDSON</Manufacturer>
                <Model>XL1200</Model>
                <ModelYear>2021</ModelYear>
                <Registration>
                    <RegistrationId>MCN</RegistrationId>
                </Registration>
                <CostNewAmt>
                    <Amt>0</Amt>
                </CostNewAmt>
                <EstimatedAnnualDistance>
                    <NumUnits>3000</NumUnits>
                    <UnitMeasurementCd>Miles</UnitMeasurementCd>
                </EstimatedAnnualDistance>
                <Displacement>
                    <NumUnits>1200</NumUnits>
                    <UnitMeasurementCd>CC</UnitMeasurementCd>
                </Displacement>
                <PurchaseDt>2021-05-11</PurchaseDt>
                <VehIdentificationNumber>1HD1LP319MB409972</VehIdentificationNumber>
                <com.safeco_TrikeConversion>
                    <com.safeco_ConversionInd>N</com.safeco_ConversionInd>
                </com.safeco_TrikeConversion>
                <AntiLockBrakeCd>N</AntiLockBrakeCd>
                <GaragingCd>G</GaragingCd>
                <NonOwnedVehInd>0</NonOwnedVehInd>
                <VehUseCd/>
                <SeatBeltTypeCd>0</SeatBeltTypeCd>
                <Coverage>
                    <CoverageCd>BI</CoverageCd>
                    <Limit>
                        <FormatInteger>25000</FormatInteger>
                        <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                    </Limit>
                    <Limit>
                        <FormatInteger>50000</FormatInteger>
                        <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                    </Limit>
                </Coverage>
                <Coverage>
                    <CoverageCd>PD</CoverageCd>
                    <Limit>
                        <FormatInteger>15000</FormatInteger>
                        <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                    </Limit>
                </Coverage>
                <Coverage>
                    <CoverageCd>COMP</CoverageCd>
                    <Deductible>
                        <FormatInteger>1000</FormatInteger>
                        <DeductibleTypeCd>USAD</DeductibleTypeCd>
                        <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                    </Deductible>
                </Coverage>
                <Coverage>
                    <CoverageCd>COLL</CoverageCd>
                    <Deductible>
                        <FormatInteger>1000</FormatInteger>
                        <DeductibleTypeCd>USAD</DeductibleTypeCd>
                        <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                    </Deductible>
                </Coverage>
                <Coverage>
                    <CoverageCd>UM</CoverageCd>
                </Coverage>
                <Coverage>
                    <CoverageCd>PIP</CoverageCd>
                    <Option>
                        <OptionTypeCd>Opt1</OptionTypeCd>
                        <OptionCd>REJ</OptionCd>
                    </Option>
                </Coverage>
                <Coverage>
                    <CoverageCd>CUSTE</CoverageCd>
                    <Limit>
                        <FormatInteger>3000</FormatInteger>
                        <LimitAppliesToCd>PerOcc</LimitAppliesToCd>
                    </Limit>
                    <Deductible>
                        <FormatInteger>100</FormatInteger>
                        <DeductibleTypeCd>USAD</DeductibleTypeCd>
                        <DeductibleAppliesToCd>PerOccurence</DeductibleAppliesToCd>
                    </Deductible>
                </Coverage>
                <Coverage>
                    <CoverageCd>com.safeco_GP</CoverageCd>
                    <Limit>
                        <FormatInteger>25000</FormatInteger>
                        <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                    </Limit>
                    <Limit>
                        <FormatInteger>50000</FormatInteger>
                        <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                    </Limit>
                </Coverage>
            </PersVeh>
            <PersPkgAutoLineBusiness>
                <LOBCd>com.safeco_MC</LOBCd>
            </PersPkgAutoLineBusiness>
        </PersPkgPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
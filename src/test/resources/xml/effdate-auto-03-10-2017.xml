<?xml-stylesheet type="text/xsl" href='http://vmrid-apiqia01/BookTransfer/DEVML/WebAPI/scripts/xslt/AutoXMLView.xslt'?>
<ACORD>
    <SignonRq>
        <ClientDt>2016-04-27</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>BookSmart</Org>
            <Name>AgencyPort</Name>
        </ClientApp>
        <SignonTransport>
            <SignonRoleCd>Agent</SignonRoleCd>
            <CustId>
                <SPName>com.agencyport</SPName>
                <CustLoginId>default</CustLoginId>
            </CustId>
        </SignonTransport>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>6F6ECD35BA124E68A42C4FF485D8EB1A</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ProducerSubCode>APP</ProducerSubCode>
                    <ContractNumber>431683</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>8X4ZT33</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Duration GreaterThan6Months</CommercialName>
                        </CommlName>
                        <PersonName>
                            <Surname/>
                            <GivenName>Fred</GivenName>
                        </PersonName>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>************</TaxId>
                        </TaxIdentity>
                    </NameInfo>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-3348904</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <GenderCd>M</GenderCd>
                        <BirthDt>1967-06-15</BirthDt>
                        <MaritalStatusCd>M</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <LOBCd>AUTOP</LOBCd>
                <ContractTerm>
                    <EffectiveDt>2017-03-10</EffectiveDt>
                    <ExpirationDt>2017-09-10</ExpirationDt>
                </ContractTerm>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>AIG</InsurerName>
                    <LOBCd>Auto</LOBCd>
                    <ContractTerm>
                        <ExpirationDt>2016-04-13</ExpirationDt>
                    </ContractTerm>
                    <LengthTimeWithPreviousInsurer>
                        <NumUnits>5</NumUnits>
                        <UnitMeasurementCd>Months</UnitMeasurementCd>
                    </LengthTimeWithPreviousInsurer>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <QuoteInfo>
                    <CompanysQuoteNumber/>
                </QuoteInfo>
                <CreditScoreInfo>
                    <CreditScore>720</CreditScore>
                    <CreditScoreDt>2012-05-14</CreditScoreDt>
                </CreditScoreInfo>
                <QuestionAnswer>
                    <QuestionCd>Current_Insurance_Value</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>CI</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Cancelled_Declined?</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Excluded_Vehicle_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>All_Vehicles_Covered_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Credit_Accept_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Accept_Contract_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>002</Explanation>
                </QuestionAnswer>
                <QuoteInfo>
                    <CompanysQuoteNumber/>
                </QuoteInfo>
                <PersApplicationInfo>
                    <InsuredOrPrincipal>
                        <GeneralPartyInfo>
                            <Addr>
                                <AddrTypeCd>StreetAddress</AddrTypeCd>
                                <Addr1>84 Henry St</Addr1>
                                <City>Manchester</City>
                                <StateProvCd>CT</StateProvCd>
                                <PostalCode>06042</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                    </InsuredOrPrincipal>
                    <ResidenceOwnedRentedCd>OWNED</ResidenceOwnedRentedCd>
                    <NumResidentsInHousehold>2</NumResidentsInHousehold>
                    <NumVehsInHousehold>1</NumVehsInHousehold>
                    <ResidenceTypeCd>DW</ResidenceTypeCd>
                </PersApplicationInfo>
                <DriverVeh DriverRef="Drv1" VehRef="Veh1">
                    <UsePct>70</UsePct>
                </DriverVeh>
                <DriverVeh DriverRef="Drv2" VehRef="Veh1">
                    <UsePct>30</UsePct>
                </DriverVeh>
                <CurrentTermAmt>
                    <Amt>9999.50</Amt>
                </CurrentTermAmt>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <PersDriver id="1">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>BALL</Surname>
                                <GivenName>Adams</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1966-01-15</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>2015-01-15</LicensedDt>
                            <DriversLicenseNumber>*********</DriversLicenseNumber>
                            <StateProvCd>WV</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>HS</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="2">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>BALL</Surname>
                                <GivenName>Bobby</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1967-02-15</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>2015-02-15</LicensedDt>
                            <DriversLicenseNumber>*********</DriversLicenseNumber>
                            <StateProvCd>WV</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>HS</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>SP</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="3">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>BALL</Surname>
                                <GivenName>Cody</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                                <TaxId>***********</TaxId>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>M</GenderCd>
                            <BirthDt>1963-03-15</BirthDt>
                            <MaritalStatusCd>M</MaritalStatusCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>2014-03-15</LicensedDt>
                            <DriversLicenseNumber></DriversLicenseNumber>
                            <StateProvCd>WV</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>HS</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>L</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh LocationRef="1" id="1" RatedDriverRef=" ">
                    <Manufacturer>MERCEDES-BENZ</Manufacturer>
                    <Model>R350 R CLASS</Model>
                    <ModelYear>2006</ModelYear>
                    <PurchaseDt>05-05-2009</PurchaseDt>
                    <CostNewAmt>
                        <Amt>48000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>4999</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <LeasedVehInd>Y</LeasedVehInd>
                    <VehIdentificationNumber>4JGCB65E86A022804</VehIdentificationNumber>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>american eagle</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>84 henery st</Addr1>
                                <City>manchester</City>
                                <StateProvCd>CT</StateProvCd>
                                <PostalCode>06042</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                    </AdditionalInterest>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>A</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>Driver</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDUM</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMSUM</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt><Amt>1598.50</Amt></FullTermAmt>
                </PersVeh>
                <PersVeh LocationRef="1" id="2" RatedDriverRef=" ">
                    <ItemIdInfo>
                        <AgencyId>1</AgencyId>
                    </ItemIdInfo>
                    <Manufacturer>HYUNDAI</Manufacturer>
                    <Model>ELANTRA GLS/GT</Model>
                    <ModelYear>2007</ModelYear>
                    <AntiTheftDeviceInfo>
                        <AntiTheftDeviceCd>Y</AntiTheftDeviceCd>
                    </AntiTheftDeviceInfo>
                    <CostNewAmt>
                        <Amt>0</Amt>
                    </CostNewAmt>
                    <NumDaysDrivenPerWeek>1</NumDaysDrivenPerWeek>
                    <EstimatedAnnualDistance>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <FullTermAmt><Amt>1598.50</Amt>
                    </FullTermAmt>
                    <LeasedVehInd>1</LeasedVehInd>
                    <RegistrationStateProvCd>NY</RegistrationStateProvCd>
                    <TerritoryCd>120</TerritoryCd>
                    <VehIdentificationNumber>JTEEW44A392031173</VehIdentificationNumber>
                    <VehSymbolCd>08</VehSymbolCd>
                    <GrossVehWeight>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>LBR</UnitMeasurementCd>
                    </GrossVehWeight>
                    <CarpoolInd>0</CarpoolInd>
                    <DistanceOneWay>
                        <NumUnits>10</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </DistanceOneWay>
                    <MultiCarDiscountInd>0</MultiCarDiscountInd>
                    <NewVehInd>0</NewVehInd>
                    <LengthTimePerMonth>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>WEE</UnitMeasurementCd>
                    </LengthTimePerMonth>
                    <OdometerReading>
                        <NumUnits>100000</NumUnits>
                        <UnitMeasurementCd>SMI</UnitMeasurementCd>
                    </OdometerReading>
                    <RateClassCd>40NMMP81</RateClassCd>
                    <VehSalvageTitleNumber/>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                        <Deductible>
                            <FormatInteger/>
                        </Deductible>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>10</FormatInteger>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                        <Option>
                            <OptionTypeCd>Opt2</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                        <Option>
                            <OptionTypeCd/>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Limit>
                            <FormatInteger/>
                        </Limit>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>RREIM</CoverageCd>
                        <Limit>
                            <FormatInteger>40</FormatInteger>
                            <LimitAppliesToCd>PerDay</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>1200</FormatInteger>
                            <LimitAppliesToCd>MaxAmount</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionTypeCd>Opt1</OptionTypeCd>
                            <OptionCd/>
                        </Option>
                    </Coverage>
                </PersVeh>
                <PersVeh LocationRef="1" id="3" RatedDriverRef=" ">
                    <Manufacturer>AUDI</Manufacturer>
                    <Model>A4</Model>
                    <ModelYear>2008</ModelYear>
                    <PurchaseDt>02-04-2009</PurchaseDt>
                    <CostNewAmt>
                        <Amt>48000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>8999</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <LeasedVehInd>Y</LeasedVehInd>
                    <VehIdentificationNumber>WAUFFAFL3AN048951</VehIdentificationNumber>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Amber Eagle</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>84 henery st</Addr1>
                                <City>manchester</City>
                                <StateProvCd>CT</StateProvCd>
                                <PostalCode>06042</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                    </AdditionalInterest>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>A</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>Driver</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDUM</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMSUM</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt><Amt>1598.50</Amt>
                    </FullTermAmt>
                </PersVeh>
                <PersVeh LocationRef="1" id="4" RatedDriverRef=" ">
                    <Manufacturer>Volkswagen</Manufacturer>
                    <Model>R32</Model>
                    <ModelYear>2009</ModelYear>
                    <PurchaseDt>04-04-2009</PurchaseDt>
                    <CostNewAmt>
                        <Amt>38000</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>9999</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <LeasedVehInd>Y</LeasedVehInd>
                    <VehIdentificationNumber>WVWKG61J14D129233</VehIdentificationNumber>
                    <AdditionalInterest>
                        <GeneralPartyInfo>
                            <NameInfo>
                                <CommlName>
                                    <CommercialName>Merian Tonagle</CommercialName>
                                </CommlName>
                            </NameInfo>
                            <Addr>
                                <Addr1>84 henery st</Addr1>
                                <City>manchester</City>
                                <StateProvCd>CT</StateProvCd>
                                <PostalCode>06042</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                    </AdditionalInterest>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <AntiTheftDeviceCd>A</AntiTheftDeviceCd>
                    <VehUseCd>PL</VehUseCd>
                    <AirBagTypeCd>Driver</AirBagTypeCd>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UNDUM</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UMSUM</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>300000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <FormatInteger>500</FormatInteger>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt><Amt>1598.50</Amt>
                    </FullTermAmt>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="1">
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>85 Henry St</Addr1>
                    <Addr2>4Drv_4Veh_CorrectID_001</Addr2>
                    <City>Manchester</City>
                    <StateProvCd>CT</StateProvCd>
                    <PostalCode>6042</PostalCode>
                </Addr>
            </Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
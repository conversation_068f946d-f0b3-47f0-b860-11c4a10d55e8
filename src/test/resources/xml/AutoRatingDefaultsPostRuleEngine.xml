<?xml-stylesheet type="text/xsl" href='http://localhost/BookTransferAPI/scripts/xslt/AutoXMLView.xslt'?>
<ACORD>
    <SignonRq>
        <SignonTransport>
            <SignonRoleCd>Agent</SignonRoleCd>
            <CustId>
                <SPName>www.answerfinancial.com</SPName>
                <CustPermId>897404</CustPermId>
            </CustId>
        </SignonTransport>
        <ClientDt>2015-07-10T08:11:37</ClientDt>
        <CustLangPref>English</CustLangPref>
        <ClientApp>
            <Org>Answer Financial, Inc.</Org>
            <Name>AFISafecoAuto</Name>
            <Version>1.0</Version>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>41b513bc-bb09-42dc-9455-528c14d17ad2</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <com.safeco_TransactionType>Ratecall1</com.safeco_TransactionType>
            <RqUID>41b513bc-bb09-42dc-9455-528c14d17ad2</RqUID>
            <TransactionRequestDt>2015-07-10T08:11:37</TransactionRequestDt>
            <TransactionEffectiveDt>2015-07-10</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ProducerSubCode>WW</ProducerSubCode>
                    <ContractNumber>534555</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>FJPNCB3</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>test</CommercialName>
                        </CommlName>
                        <PersonName>
                            <Surname>FIERRO</Surname>
                            <GivenName>GUADALUPE</GivenName>
                        </PersonName>
                    </NameInfo>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-3391325</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <GenderCd>F</GenderCd>
                        <BirthDt>1975-11-11</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <LOBCd>AUTOP</LOBCd>
                <com.safeco_Esign>N</com.safeco_Esign>
                <com.safeco_PropertyCrossSell>
                    <com.safeco_PropertyAddressScrubYN>Y</com.safeco_PropertyAddressScrubYN>
                </com.safeco_PropertyCrossSell>
                <PaymentOption>
                    <ElectronicFundsTransfer>
                        <FromAcct>
                            <AccountNumberId>C0000012345678</AccountNumberId>
                        </FromAcct>
                    </ElectronicFundsTransfer>
                </PaymentOption>
                <ContractTerm>
                    <EffectiveDt>2016-08-09</EffectiveDt>
                    <ExpirationDt>2017-08-09</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                        <UnitMeasurementCd>Months</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>Other</InsurerName>
                    <LOBCd>Auto</LOBCd>
                    <ContractTerm>
                        <ExpirationDt>2016-07-10</ExpirationDt>
                    </ContractTerm>
                    <LengthTimeWithPreviousInsurer>
                        <NumUnits>75</NumUnits>
                        <UnitMeasurementCd>Months</UnitMeasurementCd>
                    </LengthTimeWithPreviousInsurer>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <CreditScoreInfo>
                    <CreditScore>640</CreditScore>
                    <CreditScoreDt>2015-07-10</CreditScoreDt>
                </CreditScoreInfo>
                <QuestionAnswer>
                    <QuestionCd>Current_Insurance_Value</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>CI</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Cancelled_Declined?</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Excluded_Vehicle_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>All_Vehicles_Covered_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Credit_Accept_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Accept_Contract_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>002</Explanation>
                </QuestionAnswer>
                <PersApplicationInfo>
                    <InsuredOrPrincipal>
                        <GeneralPartyInfo>
                            <Addr>
                                <AddrTypeCd>StreetAddress</AddrTypeCd>
                                <Addr1>1025 5TH ST</Addr1>
                                <City>DEL NORTE</City>
                                <StateProvCd>CO</StateProvCd>
                                <PostalCode>81132</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                    </InsuredOrPrincipal>
                    <ResidenceOwnedRentedCd>OWNED</ResidenceOwnedRentedCd>
                    <NumResidentsInHousehold>1</NumResidentsInHousehold>
                    <NumVehsInHousehold>1</NumVehsInHousehold>
                    <ResidenceTypeCd>DW</ResidenceTypeCd>
                </PersApplicationInfo>
                <DriverVeh>
                    <UsePct>100</UsePct>
                </DriverVeh>
                <CurrentTermAmt>
                    <Amt>69875.55</Amt>
                </CurrentTermAmt>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <PersDriver id="DRV1">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>FIERRO</Surname>
                                <GivenName>GUADALUPE</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1975-11-11</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                            <com.safeco_IndustryCd>Construct/EnrgyTrds</com.safeco_IndustryCd>
                            <com.safeco_OccupationCd>Laborer/Worker</com.safeco_OccupationCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>1991-11-11</LicensedDt>
                            <DriversLicenseNumber>041330466</DriversLicenseNumber>
                            <StateProvCd>CO</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>BS</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV2">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>FIERRO</Surname>
                                <GivenName>GUADALUPE</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1975-11-11</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                            <com.safeco_IndustryCd>Construct/EnrgyTrds</com.safeco_IndustryCd>
                            <com.safeco_OccupationCd>Laborer/Worker</com.safeco_OccupationCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>1991-11-11</LicensedDt>
                            <DriversLicenseNumber>041330466</DriversLicenseNumber>
                            <StateProvCd>CO</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>BS</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV3">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>FIERRO</Surname>
                                <GivenName>GUADALUPE</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1975-11-11</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                            <com.safeco_IndustryCd>Construct/EnrgyTrds</com.safeco_IndustryCd>
                            <com.safeco_OccupationCd>Laborer/Worker</com.safeco_OccupationCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>1991-11-11</LicensedDt>
                            <DriversLicenseNumber>041330466</DriversLicenseNumber>
                            <StateProvCd>CO</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>BS</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersDriver id="DRV4">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>FIERRO</Surname>
                                <GivenName>GUADALUPE</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1975-11-11</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                            <com.safeco_IndustryCd>Construct/EnrgyTrds</com.safeco_IndustryCd>
                            <com.safeco_OccupationCd>Laborer/Worker</com.safeco_OccupationCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>1991-11-11</LicensedDt>
                            <DriversLicenseNumber>041330466</DriversLicenseNumber>
                            <StateProvCd>CO</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>BS</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh id="Veh1" RatedDriverRef="DRV1" LocationRef="001">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2002</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <NumUnits>99999</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>
                    	<Amt>5289</Amt></FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh2" RatedDriverRef="DRV2" LocationRef="001">
                    <Manufacturer>Jeep</Manufacturer>
                    <Model>Wrangler</Model>
                    <ModelYear>1993</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>VIN2</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <NumUnits>99999</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>
                    	<Amt>5289</Amt></FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh3" RatedDriverRef="DRV3" LocationRef="001">
                    <Manufacturer>Toyota</Manufacturer>
                    <Model>Camry</Model>
                    <ModelYear>2014</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>VIN3</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <NumUnits>99999</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>200000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>

                    <Coverage>
                        <CoverageCd>CSL</CoverageCd>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>20000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>
                        <Amt>1234</Amt>
                    </FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh4" RatedDriverRef="DRV4" LocationRef="002">
                    <Manufacturer>Dodge</Manufacturer>
                    <Model>Charger</Model>
                    <ModelYear>2017</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>Vin4</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <NumUnits>99999</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>
                        <Amt>4789</Amt>
                    </FullTermAmt>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location id="001">
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>1025 5TH ST</Addr1>
                    <City>DEL NORTE</City>
                    <StateProvCd>CO</StateProvCd>
                    <PostalCode>81132</PostalCode>
                </Addr>
            </Location>
            <Location id="002">
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>123 W. Main St.</Addr1>
                    <City>Carmel</City>
                    <StateProvCd>IN</StateProvCd>
                    <PostalCode>46220</PostalCode>
                </Addr>
            </Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
    <com.BookTransfer_errors>
        <Common>
            <Applicant>Missing</Applicant>
        </Common>
    </com.BookTransfer_errors>
</ACORD>
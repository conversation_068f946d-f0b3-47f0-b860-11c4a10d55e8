<?xml version="1.0" encoding="UTF-8"?>
<?ACORD version="1.6.1"?>
<ACORD>
	<SignonRq>
		<SignonPswd>
			<CustId>
				<SPName>BCFTechnology.com</SPName>
				<CustPermId>test</CustPermId>
				<CustLoginId>test</CustLoginId>
			</CustId>
			<CustPswd>
				<EncryptionTypeCd>NONE</EncryptionTypeCd>
				<Pswd>test</Pswd>
			</CustPswd>
		</SignonPswd>
		<ClientDt>2018-11-09T08:28:12</ClientDt>
		<CustLangPref>en-US</CustLangPref>
		<ClientApp>
			<Org>AMS Services</Org>
			<Name>AMS360</Name>
			<Version>V1.6</Version>
		</ClientApp>
		<ProxyClient>
			<Org>BCFTech</Org>
			<Name>TransmitXML</Name>
			<Version>V1.00</Version>
		</ProxyClient>
	</SignonRq>
	<InsuranceSvcRq>
		<RqUID>33771A1A-ADFD-47F8-B047-0468F42AFDA6</RqUID>
		<CommlUmbrellaPolicyQuoteInqRq>
			<RqUID>93626CE5-DD0B-4B6F-A749-66439051BFD5</RqUID>
			<TransactionRequestDt>2018-11-09T09:18:46</TransactionRequestDt>
			<TransactionEffectiveDt>2013-08-08</TransactionEffectiveDt>
			<CurCd>USD</CurCd>
			<Producer>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>Carrier Database</CommercialName>
						</CommlName>
					</NameInfo>
					<Addr>
						<AddrTypeCd>StreetAddress</AddrTypeCd>
						<Addr1>123 Main Street</Addr1>
						<Addr2>ADDR2</Addr2>
						<City>Bothell</City>
						<StateProvCd>WA</StateProvCd>
						<PostalCode>97202</PostalCode>
					</Addr>
					<Communications>
						<PhoneInfo>
							<PhoneTypeCd>Phone</PhoneTypeCd>
							<CommunicationUseCd>Business</CommunicationUseCd>
							<PhoneNumber>9999999999</PhoneNumber>
						</PhoneInfo>
					</Communications>
				</GeneralPartyInfo>
				<ProducerInfo>
					<ContractNumber/>
					<ProducerRoleCd>Agency</ProducerRoleCd>
				</ProducerInfo>
			</Producer>
			<InsuredOrPrincipal>
				<ItemIdInfo>
					<AgencyId>00012345</AgencyId>
				</ItemIdInfo>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>201307 DEDUCTIBLE TESTING INSURED NAME 2 INSURED NAME 4 INSURED NAME 3 INSURED NAME 5</CommercialName>
						</CommlName>
						<LegalEntityCd>IN</LegalEntityCd>
					</NameInfo>
					<Addr>
						<AddrTypeCd>MailingAddress</AddrTypeCd>
						<Addr1>123 Main Street</Addr1>
						<City>CRIDERSVILLE</City>
						<StateProvCd>OH</StateProvCd>
						<PostalCode>458062202</PostalCode>
					</Addr>
				</GeneralPartyInfo>
				<InsuredOrPrincipalInfo>
					<InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
				</InsuredOrPrincipalInfo>
			</InsuredOrPrincipal>
			<InsuredOrPrincipal>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>DEDUCTIBLE TESTING</CommercialName>
						</CommlName>
					</NameInfo>
				</GeneralPartyInfo>
				<InsuredOrPrincipalInfo>
					<InsuredOrPrincipalRoleCd>DEC</InsuredOrPrincipalRoleCd>
				</InsuredOrPrincipalInfo>
			</InsuredOrPrincipal>
			<CommlPolicy id="PolicyLevel">
				<PolicyNumber>ABC 1234567</PolicyNumber>
				<LOBCd>UMBRC</LOBCd>
				<LOBSubCd>VOL</LOBSubCd>
				<ContractTerm>
					<EffectiveDt>2013-08-08</EffectiveDt>
					<ExpirationDt>2014-08-08</ExpirationDt>
					<DurationPeriod>
						<NumUnits>12</NumUnits>
						<UnitMeasurementCd>MON</UnitMeasurementCd>
					</DurationPeriod>
				</ContractTerm>
				<BillingMethodCd>AB</BillingMethodCd>
				<CurrentTermAmt>
					<Amt>13694.00</Amt>
				</CurrentTermAmt>
				<Form>
					<FormNumber>CU2123</FormNumber>
					<FormName>EXCL-NUCLEAR ENERGY LIAB (BROAD FORM)</FormName>
					<EditionDt>2002-02-01</EditionDt>
					<IterationNumber>001</IterationNumber>
				</Form>
				<Form>
					<FormNumber>CU0001</FormNumber>
					<FormName>COMMERCIAL LIABILITY UMBRELLA COVERAGE FORM</FormName>
					<EditionDt>2013-04-01</EditionDt>
					<IterationNumber>002</IterationNumber>
				</Form>
				<Form>
					<FormNumber>CU2130</FormNumber>
					<FormName>CAP ON LOSSES FROM CERTIFIED ACTS OF TERRORIS</FormName>
					<EditionDt>2008-01-01</EditionDt>
					<IterationNumber>003</IterationNumber>
				</Form>
				<Form>
					<FormNumber>8-2004</FormNumber>
					<FormName>LIMITATION OF POLICY TO FOLLOWING FORM LIABIL</FormName>
					<EditionDt>2010-03-01</EditionDt>
					<IterationNumber>004</IterationNumber>
				</Form>
				<Form>
					<FormNumber>IL0244</FormNumber>
					<FormName>OHIO CHANGES - CANCELLATION &amp; NONRENEWAL</FormName>
					<EditionDt>2007-09-01</EditionDt>
					<IterationNumber>005</IterationNumber>
				</Form>
				<Form>
					<FormNumber>IL0017</FormNumber>
					<FormName>COMMON POL CONDS</FormName>
					<EditionDt>1998-11-01</EditionDt>
					<IterationNumber>006</IterationNumber>
				</Form>
				<Form>
					<FormNumber>CU2700</FormNumber>
					<FormName>UNDERLYING CLAIMS-MADE COVERAGE</FormName>
					<EditionDt>2013-04-01</EditionDt>
					<IterationNumber>007</IterationNumber>
				</Form>
				<Form>
					<FormNumber>20-1900</FormNumber>
					<FormName>CENTRAL MUTUAL POLICY COVER SHEET</FormName>
					<EditionDt>1992-09-01</EditionDt>
					<IterationNumber>008</IterationNumber>
				</Form>
				<Form>
					<FormNumber>20-1769</FormNumber>
					<FormName>IN WITNESS CLAUSE FOR CENTRAL MUTUAL &amp; ALL AM</FormName>
					<EditionDt>1991-08-01</EditionDt>
					<IterationNumber>009</IterationNumber>
				</Form>
				<Form>
					<FormNumber>20-1768</FormNumber>
					<FormName>MUTUAL POL CONDS-APPLICABLE TO CENTRAL MUTUAL</FormName>
					<EditionDt>1991-08-01</EditionDt>
					<IterationNumber>010</IterationNumber>
				</Form>
				<OtherOrPriorPolicy>
					<PolicyCd>Underly</PolicyCd>
					<PolicyNumber>1234567</PolicyNumber>
					<LOBCd>OLIB</LOBCd>
					<LOBSubCd>PROF</LOBSubCd>
					<InsurerName>AFLAK</InsurerName>
					<ContractTerm>
						<EffectiveDt>2013-05-05</EffectiveDt>
						<ExpirationDt>2014-05-05</ExpirationDt>
						<DurationPeriod>
							<NumUnits>12</NumUnits>
							<UnitMeasurementCd>MON</UnitMeasurementCd>
						</DurationPeriod>
					</ContractTerm>
					<Coverage>
						<CoverageCd>PR</CoverageCd>
						<Limit>
							<FormatInteger>500000</FormatInteger>
							<LimitAppliesToCd>PerOcc</LimitAppliesToCd>
						</Limit>
						<Limit>
							<FormatInteger>500000</FormatInteger>
							<LimitAppliesToCd>Aggregate</LimitAppliesToCd>
						</Limit>
					</Coverage>
				</OtherOrPriorPolicy>
				<OtherOrPriorPolicy>
					<PolicyCd>Underly</PolicyCd>
					<PolicyNumber>1234567</PolicyNumber>
					<LOBCd>CGL</LOBCd>
					<InsurerName>AFLAK</InsurerName>
					<ContractTerm>
						<EffectiveDt>2013-05-05</EffectiveDt>
						<ExpirationDt>2014-05-05</ExpirationDt>
						<DurationPeriod>
							<NumUnits>12</NumUnits>
							<UnitMeasurementCd>MON</UnitMeasurementCd>
						</DurationPeriod>
					</ContractTerm>
					<Coverage>
						<CoverageCd>GL</CoverageCd>
						<Limit>
							<FormatInteger>500000</FormatInteger>
							<LimitAppliesToCd>PerOcc</LimitAppliesToCd>
						</Limit>
						<Limit>
							<FormatInteger>500000</FormatInteger>
							<LimitAppliesToCd>Aggregate</LimitAppliesToCd>
						</Limit>
					</Coverage>
				</OtherOrPriorPolicy>
				<OtherOrPriorPolicy>
					<PolicyCd>Underly</PolicyCd>
					<PolicyNumber>1234567</PolicyNumber>
					<LOBCd>OLIB</LOBCd>
					<InsurerName>AFLAK</InsurerName>
					<ContractTerm>
						<EffectiveDt>2013-05-05</EffectiveDt>
						<ExpirationDt>2014-05-05</ExpirationDt>
						<DurationPeriod>
							<NumUnits>12</NumUnits>
							<UnitMeasurementCd>MON</UnitMeasurementCd>
						</DurationPeriod>
					</ContractTerm>
					<Coverage>
						<CoverageCd>LL</CoverageCd>
						<Limit>
							<FormatInteger>500000</FormatInteger>
							<LimitAppliesToCd>PerOcc</LimitAppliesToCd>
						</Limit>
						<Limit>
							<FormatInteger>1000000</FormatInteger>
							<LimitAppliesToCd>Aggregate</LimitAppliesToCd>
						</Limit>
					</Coverage>
				</OtherOrPriorPolicy>
				<PaymentOption>
					<PaymentPlanCd>AN</PaymentPlanCd>
				</PaymentOption>
				<MiscParty>
					<GeneralPartyInfo>
						<NameInfo>
							<CommlName>
								<CommercialName>AMS Test</CommercialName>
							</CommlName>
						</NameInfo>
					</GeneralPartyInfo>
					<MiscPartyInfo>
						<MiscPartyRoleCd>A</MiscPartyRoleCd>
					</MiscPartyInfo>
				</MiscParty>
				<CommlPolicySupplement>
					<AuditInd>0</AuditInd>
					<OperationsDesc>DEDUCTIBLE CHANGES</OperationsDesc>
				</CommlPolicySupplement>
				<ControllingStateProvCd>OH</ControllingStateProvCd>
			</CommlPolicy>
			<Location>
				<ItemIdInfo/>
				<Addr/>
			</Location>
			<CommlUmbrellaLineBusiness>
				<LOBCd>UMBRC</LOBCd>
				<LOBSubCd>VOL</LOBSubCd>
				<CurrentTermAmt>
					<Amt>13694.00</Amt>
				</CurrentTermAmt>
				<RateEffectiveDt>2013-08-08</RateEffectiveDt>
				<CommlCoverage>
					<CoverageCd>CUMBR</CoverageCd>
					<Limit>
						<FormatInteger>3000000</FormatInteger>
						<LimitAppliesToCd>PerOcc</LimitAppliesToCd>
					</Limit>
					<Limit>
						<FormatInteger>3000000</FormatInteger>
						<LimitAppliesToCd>Aggregate</LimitAppliesToCd>
					</Limit>
					<Deductible>
						<FormatInteger>0</FormatInteger>
					</Deductible>
				</CommlCoverage>
				<CommlCoverage>
					<CoverageCd>TRIA</CoverageCd>
					<CurrentTermAmt>
						<Amt>82.00</Amt>
					</CurrentTermAmt>
				</CommlCoverage>
				<CommlCoverage>
					<CoverageCd>XLIB</CoverageCd>
				</CommlCoverage>
				<UnderlyingExposureInfo>
					<CoverageCd>LIQUR</CoverageCd>
					<Description>Both Underlying Coverage and Insurance Exposure</Description>
				</UnderlyingExposureInfo>
				<UnderlyingExposureInfo>
					<Description>PRINTERS E&amp;O:</Description>
				</UnderlyingExposureInfo>
				<QuestionAnswer>
					<QuestionCd>CUMBR25</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
			</CommlUmbrellaLineBusiness>
		</CommlUmbrellaPolicyQuoteInqRq>
	</InsuranceSvcRq>
</ACORD>

﻿<ACORD>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName/>
                <CustPermId>Long &amp; Foster</CustPermId>
                <CustLoginId><EMAIL></CustLoginId>
            </CustId>
        </SignonPswd>
        <ClientDt>2017-02-15</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>Applied Systems</Org>
            <Name>EPIC</Name>
            <Version>2016</Version>
        </ClientApp>
        <ProxyClient>
            <Org>Applied Systems</Org>
            <Name>IVANS</Name>
            <Version>1.0</Version>
        </ProxyClient>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>5226B2B6-D307-4DC8-BBBE-AEF1863A8F7A</RqUID>
        <HomePolicyQuoteInqRq>
            <RqUID>997120CB-9AA2-430D-8B42-E2B684FB2058</RqUID>
            <CurCd>USD</CurCd>
            <BroadLOBCd>P</BroadLOBCd>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <!--<CommercialName>Hulk Tarly</CommercialName>-->
                        </CommlName>
                        <LegalEntityCd/>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>***********</TaxId>
                        </TaxIdentity>
                        <PersonName>
                            <GivenName>RAYMONT</GivenName>
                            <Surname>EDWARDS</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>108 S AUGUSTA AVE</Addr1>
                        <City>BALTIMORE MD</City>
                        <StateProvCd>MD</StateProvCd>
                        <PostalCode>21229</PostalCode>
                        <County/>
                    </Addr>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt>02/1101-01</BirthDt>
                        <MaritalStatusCd/>
                        <OccupationDesc>Skilled</OccupationDesc>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>RAYMONT &amp; HILDA EDWARDS</CommercialName>
                        </CommlName>
                        <LegalEntityCd/>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>***********</TaxId>
                        </TaxIdentity>
                        <PersonName>
                            <GivenName>HILDA</GivenName>
                            <Surname>EDWARDS</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>108 S AUGUSTA AVE</Addr1>
                        <City>BALTIMORE MD</City>
                        <StateProvCd>MD</StateProvCd>
                        <PostalCode>21229</PostalCode>
                        <County/>
                    </Addr>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>DEC</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt/>
                        <MaritalStatusCd/>
                        <OccupationDesc>Skilled</OccupationDesc>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>RAYMONT &amp; HILDA EDWARDS</CommercialName>
                        </CommlName>
                        <LegalEntityCd/>
                        <TaxIdentity>
                            <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            <TaxId>***********</TaxId>
                        </TaxIdentity>
                        <PersonName>
                            <GivenName>HILDA</GivenName>
                            <Surname>EDWARDS</Surname>
                        </PersonName>
                    </NameInfo>
                    <Addr>
                        <AddrTypeCd>MailingAddress</AddrTypeCd>
                        <Addr1>108 S AUGUSTA AVE</Addr1>
                        <City>BALTIMORE MD</City>
                        <StateProvCd>MD</StateProvCd>
                        <PostalCode>21229</PostalCode>
                        <County/>
                    </Addr>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Coinsured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <BirthDt/>
                        <MaritalStatusCd/>
                        <OccupationDesc>Skilled</OccupationDesc>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <PolicyNumber>HO 639529</PolicyNumber>
                <LOBCd>HOME</LOBCd>
                <NAICCd/>
                <ControllingStateProvCd>MI</ControllingStateProvCd>
                <ContractTerm>
                    <EffectiveDt>2017-05-11</EffectiveDt>
                    <ExpirationDt>2018-05-11</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                        <UnitMeasurementCd>MON</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <BillingAccountNumber/>
                <BillingMethodCd/>
                <CurrentTermAmt>
                    <Amt>1312</Amt>
                </CurrentTermAmt>
                <ControllingStateProvCd>MD</ControllingStateProvCd>
                <OtherOrPriorPolicy>
                    <PolicyCd/>
                    <PolicyNumber>HO 639529</PolicyNumber>
                    <LOBCd>HOME</LOBCd>
                    <InsurerName>Harleysville</InsurerName>
                    <ContractTerm>
                        <EffectiveDt>05/11/2016</EffectiveDt>
                        <ExpirationDt>05/11/2017</ExpirationDt>
                        <DurationPeriod>
                            <NumUnits>12</NumUnits>
                            <UnitMeasurementCd>MON</UnitMeasurementCd>
                        </DurationPeriod>
                    </ContractTerm>
                </OtherOrPriorPolicy>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>Allstate</InsurerName>
                </OtherOrPriorPolicy>
            </PersPolicy>
            <Location id="LOC1">
                <ItemIdInfo>
                    <AgencyId/>
                </ItemIdInfo>
                <Addr>
                    <AddrTypeCd/>
                    <Addr1>108 S Augusta Ave</Addr1>
                    <City>Baltimore MD</City>
                    <StateProvCd>MD</StateProvCd>
                    <PostalCode>21229</PostalCode>
                    <County/>
                    <Addr2/>
                </Addr>
                <AdditionalInterest>
                    <GeneralPartyInfo>
                        <NameInfo>
                            <CommlName>
                                <CommercialName>Wells Fargo Bank, NA. #936 Its Successors and/or Assigns/ATIMA
                                </CommercialName>
                            </CommlName>
                        </NameInfo>
                        <Addr>
                            <Addr1>P.O. Box 100515</Addr1>
                            <Addr2/>
                            <City>Florence</City>
                            <StateProvCd>SC</StateProvCd>
                            <PostalCode>29502</PostalCode>
                        </Addr>
                    </GeneralPartyInfo>
                    <AdditionalInterestInfo>
                        <NatureInterestCd>MORTG</NatureInterestCd>
                        <InterestRank/>
                        <AccountNumberId>**********</AccountNumberId>
                        <PayorInd/>
                    </AdditionalInterestInfo>
                </AdditionalInterest>
            </Location>
            <HomeLineBusiness>
                <LOBCd>HOME</LOBCd>
                <CurrentTermAmt>
                    <Amt>1312</Amt>
                </CurrentTermAmt>
                <RateEffectiveDt>05/11/2016</RateEffectiveDt>
                <Dwell id="DWELL" LocationRef="001">
                    <PolicyTypeCd>03</PolicyTypeCd>
                    <Construction>
                        <ConstructionCd>M</ConstructionCd>
                        <YearBuilt>1943</YearBuilt>
                        <BldgCodeEffectivenessGradeCd/>
                    </Construction>
                    <DwellOccupancy>
                        <ResidenceTypeCd/>
                        <DwellUseCd/>
                    </DwellOccupancy>
                    <DwellRating>
                        <TerritoryCd>30</TerritoryCd>
                        <PremiumGroup/>
                    </DwellRating>
                    <BldgProtection>
                        <FireProtectionClassCd/>
                        <DistanceToFireStation>
                            <NumUnits/>
                            <UnitMeasurementCd/>
                        </DistanceToFireStation>
                        <DistanceToHydrant>
                            <NumUnits/>
                            <UnitMeasurementCd/>
                        </DistanceToHydrant>
                        <ProtectionClassImprovedInd/>
                    </BldgProtection>
                    <DwellInspectionValuation>
                        <NumFamilies>1</NumFamilies>
                    </DwellInspectionValuation>
                    <Coverage>
                        <CoverageCd>DWELL</CoverageCd>
                        <CoverageDesc>Dwelling</CoverageDesc>
                        <Limit>
                            <FormatInteger>230000</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible>
                            <FormatInteger>125</FormatInteger>
                            <DeductibleTypeCd/>
                            <DeductibleAppliesToCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>OS</CoverageCd>
                        <CoverageDesc>Other Structures</CoverageDesc>
                        <Limit>
                            <FormatInteger>23000</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible>
                            <FormatInteger>125</FormatInteger>
                            <DeductibleTypeCd/>
                            <DeductibleAppliesToCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PP</CoverageCd>
                        <CoverageDesc>Personal Property</CoverageDesc>
                        <Limit>
                            <FormatInteger>161000</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible>
                            <FormatInteger>125</FormatInteger>
                            <DeductibleTypeCd/>
                            <DeductibleAppliesToCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>LOU</CoverageCd>
                        <CoverageDesc>Loss of Use</CoverageDesc>
                        <Limit>
                            <FormatInteger>46000</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible>
                            <FormatInteger>125</FormatInteger>
                            <DeductibleTypeCd/>
                            <DeductibleAppliesToCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <CoverageDesc>Medical Payments</CoverageDesc>
                        <Limit>
                            <FormatInteger>1000</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible>
                            <FormatInteger>125</FormatInteger>
                            <DeductibleTypeCd/>
                            <DeductibleAppliesToCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>SEWER</CoverageCd>
                        <CoverageDesc>Water Backup</CoverageDesc>
                        <Limit>
                            <FormatInteger>15000</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                            <DeductibleAppliesToCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PPREP</CoverageCd>
                        <CoverageDesc>Extended Dwelling Coverage</CoverageDesc>
                        <Limit>
                            <FormatInteger>125</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                            <DeductibleAppliesToCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>ALARM</CoverageCd>
                        <CoverageDesc>Local Alarm</CoverageDesc>
                        <Limit>
                            <FormatInteger>YES</FormatInteger>
                            <LimitAppliesToCd/>
                        </Limit>
                        <Deductible>
                            <FormatInteger/>
                            <DeductibleTypeCd/>
                            <DeductibleAppliesToCd/>
                        </Deductible>
                        <CurrentTermAmt>
                            <Amt/>
                        </CurrentTermAmt>
                    </Coverage>
                </Dwell>
            </HomeLineBusiness>
            <RemarkText>Harleysville</RemarkText>
        </HomePolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
<?xml version="1.0" encoding="UTF-8"?>
<?ACORD version="1.3.1"?>
<ACORD>
	<SignonRq>
		<SignonPswd>
			<CustId>
				<SPName>BCFTechnology.com</SPName>
				<CustPermId>test</CustPermId>
				<CustLoginId>test</CustLoginId>
			</CustId>
			<CustPswd>
				<EncryptionTypeCd>NONE</EncryptionTypeCd>
				<Pswd>test</Pswd>
			</CustPswd>
		</SignonPswd>
		<ClientDt>2018-11-09T09:53:47</ClientDt>
		<CustLangPref>en-US</CustLangPref>
		<ClientApp>
			<Org>AMS Services</Org>
			<Name>AMS360</Name>
			<Version>V1.3</Version>
		</ClientApp>
		<ProxyClient>
			<Org>BCFTech</Org>
			<Name>TransmitXML</Name>
			<Version>V1.00</Version>
		</ProxyClient>
	</SignonRq>
	<InsuranceSvcRq>
		<RqUID>22E24C4D-9C63-4209-8557-3DE1DA1ED859</RqUID>
		<CommlPkgPolicyQuoteInqRq>
			<RqUID>C9DBE818-07FD-4C2A-A539-7889A2317ECD</RqUID>
			<TransactionRequestDt>2018-11-09T10:52:34</TransactionRequestDt>
			<TransactionEffectiveDt>2018-12-01</TransactionEffectiveDt>
			<CurCd>USD</CurCd>
			<Producer>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>Carrier Database</CommercialName>
						</CommlName>
					</NameInfo>
					<Addr>
						<AddrTypeCd>StreetAddress</AddrTypeCd>
						<Addr1>123 Main Street</Addr1>
						<Addr2>ADDR2</Addr2>
						<City>Bothell</City>
						<StateProvCd>WA</StateProvCd>
						<PostalCode>97202</PostalCode>
					</Addr>
					<Communications>
						<PhoneInfo>
							<PhoneTypeCd>Phone</PhoneTypeCd>
							<CommunicationUseCd>Business</CommunicationUseCd>
							<PhoneNumber>9999999999</PhoneNumber>
						</PhoneInfo>
					</Communications>
				</GeneralPartyInfo>
				<ProducerInfo>
					<ContractNumber/>
					<ProducerRoleCd>Agency</ProducerRoleCd>
				</ProducerInfo>
			</Producer>
			<InsuredOrPrincipal>
				<ItemIdInfo>
					<AgencyId>00012345</AgencyId>
				</ItemIdInfo>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>AMS Test</CommercialName>
						</CommlName>
						<LegalEntityCd>CP</LegalEntityCd>
					</NameInfo>
					<Addr>
						<AddrTypeCd>MailingAddress</AddrTypeCd>
						<Addr1>123 Main Street</Addr1>
						<City>Louisville</City>
						<StateProvCd>KY</StateProvCd>
						<PostalCode>402610273</PostalCode>
					</Addr>
					<Communications>
						<PhoneInfo>
							<PhoneTypeCd>Phone</PhoneTypeCd>
							<CommunicationUseCd>Day</CommunicationUseCd>
							<PhoneNumber>9999999999</PhoneNumber>
						</PhoneInfo>
					</Communications>
				</GeneralPartyInfo>
				<InsuredOrPrincipalInfo>
					<InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
				</InsuredOrPrincipalInfo>
			</InsuredOrPrincipal>
			<InsuredOrPrincipal>
				<GeneralPartyInfo>
					<NameInfo>
						<CommlName>
							<CommercialName>AMS Test</CommercialName>
						</CommlName>
					</NameInfo>
				</GeneralPartyInfo>
				<InsuredOrPrincipalInfo>
					<InsuredOrPrincipalRoleCd>DEC</InsuredOrPrincipalRoleCd>
				</InsuredOrPrincipalInfo>
			</InsuredOrPrincipal>
			<CommlPolicy id="PolicyLevel">
				<PolicyNumber>12345678901B-1</PolicyNumber>
				<LOBCd>CPKGE</LOBCd>
				<ContractTerm>
					<EffectiveDt>2018-12-01</EffectiveDt>
					<ExpirationDt>2019-12-01</ExpirationDt>
					<DurationPeriod>
						<NumUnits>12</NumUnits>
						<UnitMeasurementCd>MON</UnitMeasurementCd>
					</DurationPeriod>
				</ContractTerm>
				<BillingMethodCd>AB</BillingMethodCd>
				<MiscParty>
					<GeneralPartyInfo>
						<NameInfo>
							<CommlName>
								<CommercialName>Tnow Test</CommercialName>
							</CommlName>
						</NameInfo>
					</GeneralPartyInfo>
					<MiscPartyInfo>
						<MiscPartyRoleCd>A</MiscPartyRoleCd>
					</MiscPartyInfo>
				</MiscParty>
				<MiscParty>
					<GeneralPartyInfo>
						<NameInfo>
							<CommlName>
								<CommercialName>Tnow Test</CommercialName>
							</CommlName>
						</NameInfo>
					</GeneralPartyInfo>
					<MiscPartyInfo>
						<MiscPartyRoleCd>C</MiscPartyRoleCd>
					</MiscPartyInfo>
				</MiscParty>
				<QuestionAnswer>
					<QuestionCd>GENRL34</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>GENRL47</QuestionCd>
					<YesNoCd>YES</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>GENRL39</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>GENRL41</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<CommlPolicySupplement>
					<AuditInd>1</AuditInd>
					<CatastropheInd>0</CatastropheInd>
					<OperationsDesc>MANUFACTURER/PLASTIC</OperationsDesc>
					<AuditFrequencyCd>AN</AuditFrequencyCd>
				</CommlPolicySupplement>
				<ControllingStateProvCd>KY</ControllingStateProvCd>
				<CurrentTermAmt>
					<Amt>0</Amt>
				</CurrentTermAmt>
			</CommlPolicy>
			<Location id="L1">
				<ItemIdInfo>
					<AgencyId>0001</AgencyId>
				</ItemIdInfo>
				<Addr>
					<Addr1>123 Main Street</Addr1>
					<City>Louisville</City>
					<StateProvCd>KY</StateProvCd>
					<PostalCode>40218</PostalCode>
					<County>Jefferson</County>
				</Addr>
				<RiskLocationCd>IN</RiskLocationCd>
				<SubLocation id="L1S1">
					<ItemIdInfo>
						<AgencyId>001</AgencyId>
					</ItemIdInfo>
					<SubLocationDesc>Office/Shop</SubLocationDesc>
					<Addr>
						<Addr1>123 Main Street</Addr1>
						<City>Louisville</City>
						<StateProvCd>KY</StateProvCd>
						<PostalCode>40218</PostalCode>
					</Addr>
					<AdditionalInterest>
						<GeneralPartyInfo>
							<NameInfo>
								<CommlName>
									<CommercialName>AMS Test Company</CommercialName>
								</CommlName>
							</NameInfo>
							<Addr>
								<Addr1>123 Main Street</Addr1>
								<City>Louisville</City>
								<StateProvCd>KY</StateProvCd>
								<PostalCode>40206</PostalCode>
							</Addr>
							<Communications>
								<PhoneInfo>
									<PhoneTypeCd>Phone</PhoneTypeCd>
									<CommunicationUseCd>Business</CommunicationUseCd>
									<PhoneNumber>9999999999</PhoneNumber>
								</PhoneInfo>
							</Communications>
						</GeneralPartyInfo>
						<AdditionalInterestInfo>
							<NatureInterestCd>LOSSP</NatureInterestCd>
						</AdditionalInterestInfo>
					</AdditionalInterest>
				</SubLocation>
			</Location>
			<Location id="L2">
				<ItemIdInfo>
					<AgencyId>0002</AgencyId>
				</ItemIdInfo>
				<Addr>
					<Addr1>123 Main Street</Addr1>
					<City>Saint Albans</City>
					<StateProvCd>WV</StateProvCd>
					<PostalCode>25177</PostalCode>
					<County>Kanawha</County>
				</Addr>
				<RiskLocationCd>OUT</RiskLocationCd>
				<SubLocation id="L2S1">
					<ItemIdInfo>
						<AgencyId>001</AgencyId>
					</ItemIdInfo>
					<Addr>
						<Addr1>123 Main Street</Addr1>
						<City>Saint Albans</City>
						<StateProvCd>WV</StateProvCd>
						<PostalCode>25177</PostalCode>
					</Addr>
				</SubLocation>
			</Location>
			<CommlSubLocation LocationRef="L1" SubLocationRef="L1S1">
				<InterestCd>OWNER</InterestCd>
				<Construction>
					<YearBuilt>1997</YearBuilt>
					<BldgArea>
						<NumUnits>15375</NumUnits>
						<UnitMeasurementCd>FTK</UnitMeasurementCd>
					</BldgArea>
					<NumStories>1</NumStories>
					<NumBasements>0</NumBasements>
				</Construction>
				<BldgProtection>
					<FireProtectionClassCd>2</FireProtectionClassCd>
					<ProtectionDeviceSprinklerCd>SP</ProtectionDeviceSprinklerCd>
					<SprinkleredPct>100</SprinkleredPct>
				</BldgProtection>
				<QuestionAnswer>
					<QuestionCd>BOP29</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
			</CommlSubLocation>
			<CommlSubLocation LocationRef="L2" SubLocationRef="L2S1">
				<InterestCd>OWNER</InterestCd>
				<Construction>
					<YearBuilt>1987</YearBuilt>
				</Construction>
			</CommlSubLocation>
			<CommlPropertyLineBusiness>
				<LOBCd>PROPC</LOBCd>
				<RateEffectiveDt>2018-12-01</RateEffectiveDt>
				<PropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>BUSIN</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>800000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>BUSIN</CoverageCd>
							<Limit>
								<FormatInteger>800000</FormatInteger>
								<ValuationCd>ACT</ValuationCd>
							</Limit>
							<Deductible>
								<FormatInteger>500</FormatInteger>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPECL</CoverageCd>
							<Limit>
								<FormatInteger>800000</FormatInteger>
							</Limit>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>BLDG</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>900000</Amt>
						</ItemValueAmt>
						<FloorArea>
							<NumUnits>15375</NumUnits>
							<UnitMeasurementCd>FTK</UnitMeasurementCd>
						</FloorArea>
						<CommlCoverage>
							<CoverageCd>BLDG</CoverageCd>
							<Limit>
								<FormatInteger>900000</FormatInteger>
								<ValuationCd>RC</ValuationCd>
							</Limit>
							<Deductible>
								<FormatInteger>500</FormatInteger>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPECL</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L1" SubLocationRef="L1S1">
						<SubjectInsuranceCd>BPP</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>500000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>BPP</CoverageCd>
							<Limit>
								<FormatInteger>500000</FormatInteger>
								<ValuationCd>RC</ValuationCd>
							</Limit>
							<Deductible>
								<FormatInteger>500</FormatInteger>
							</Deductible>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>SPECL</CoverageCd>
							<Limit>
								<FormatInteger>500000</FormatInteger>
							</Limit>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L2" SubLocationRef="L2S1">
						<SubjectInsuranceCd>BLDG</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>890000</Amt>
						</ItemValueAmt>
						<AgreedValueInd>1</AgreedValueInd>
						<CommlCoverage>
							<CoverageCd>BLDG</CoverageCd>
							<Limit>
								<FormatInteger>890000</FormatInteger>
								<ValuationCd>G</ValuationCd>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>BRD</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<CommlPropertyInfo LocationRef="L2" SubLocationRef="L2S1">
						<SubjectInsuranceCd>PP</SubjectInsuranceCd>
						<ItemValueAmt>
							<Amt>350000</Amt>
						</ItemValueAmt>
						<CommlCoverage>
							<CoverageCd>PP</CoverageCd>
							<Limit>
								<FormatInteger>350000</FormatInteger>
								<ValuationCd>FVR</ValuationCd>
							</Limit>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>BASIC</CoverageCd>
						</CommlCoverage>
					</CommlPropertyInfo>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>BUSIN</SubjectInsuranceCd>
							<ValuationCd>ACT</ValuationCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>BLDG</SubjectInsuranceCd>
							<ValuationCd>RC</ValuationCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L1" SubLocationRef="L1S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>BPP</SubjectInsuranceCd>
							<ValuationCd>RC</ValuationCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L2" SubLocationRef="L2S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>BLDG</SubjectInsuranceCd>
							<ValuationCd>G</ValuationCd>
						</StatementValuesInfo>
					</StatementValues>
					<StatementValues LocationRef="L2" SubLocationRef="L2S1">
						<StatementValuesInfo>
							<SubjectInsuranceCd>PP</SubjectInsuranceCd>
							<ValuationCd>FVR</ValuationCd>
						</StatementValuesInfo>
					</StatementValues>
				</PropertyInfo>
				<CurrentTermAmt>
					<Amt>0</Amt>
				</CurrentTermAmt>
			</CommlPropertyLineBusiness>
			<GeneralLiabilityLineBusiness>
				<LOBCd>CGL</LOBCd>
				<LOBSubCd>NPO</LOBSubCd>
				<RateEffectiveDt>2018-12-01</RateEffectiveDt>
				<AdditionalInterest>
					<GeneralPartyInfo>
						<NameInfo>
							<CommlName>
								<CommercialName>Tnow Test Company</CommercialName>
							</CommlName>
						</NameInfo>
						<Addr>
							<Addr1>123 Main Street</Addr1>
							<City>Louisville</City>
							<StateProvCd>KY</StateProvCd>
							<PostalCode>40206</PostalCode>
						</Addr>
						<Communications>
							<PhoneInfo>
								<PhoneTypeCd>Phone</PhoneTypeCd>
								<CommunicationUseCd>Business</CommunicationUseCd>
								<PhoneNumber>9999999999</PhoneNumber>
							</PhoneInfo>
						</Communications>
					</GeneralPartyInfo>
					<AdditionalInterestInfo>
						<NatureInterestCd>LOSSP</NatureInterestCd>
					</AdditionalInterestInfo>
				</AdditionalInterest>
				<LiabilityInfo>
					<CommlCoverage>
						<CoverageCd>GENAG</CoverageCd>
						<Limit>
							<FormatInteger>2000000</FormatInteger>
							<LimitAppliesToCd>Aggregate</LimitAppliesToCd>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>PRDCO</CoverageCd>
						<Limit>
							<FormatInteger>2000000</FormatInteger>
							<LimitAppliesToCd>Aggregate</LimitAppliesToCd>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>PIADV</CoverageCd>
						<Limit>
							<FormatInteger>1000000</FormatInteger>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>EAOCC</CoverageCd>
						<Limit>
							<FormatInteger>1000000</FormatInteger>
							<LimitAppliesToCd>PerOcc</LimitAppliesToCd>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>FIRDM</CoverageCd>
						<Limit>
							<FormatInteger>500000</FormatInteger>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>MEDEX</CoverageCd>
						<Limit>
							<FormatInteger>10000</FormatInteger>
							<LimitAppliesToCd>PerPerson</LimitAppliesToCd>
						</Limit>
					</CommlCoverage>
					<CommlCoverage>
						<CoverageCd>EBLIA</CoverageCd>
						<Limit>
							<FormatInteger>1000000</FormatInteger>
							<LimitAppliesToCd>EachClaim</LimitAppliesToCd>
						</Limit>
						<Limit>
							<FormatInteger>3000000</FormatInteger>
						</Limit>
						<Deductible>
							<FormatInteger>1000</FormatInteger>
						</Deductible>
						<Option>
							<OptionTypeCd>Num1</OptionTypeCd>
							<OptionValue>25</OptionValue>
						</Option>
					</CommlCoverage>
					<GeneralLiabilityClassification LocationRef="L1">
						<CommlCoverage>
							<CoverageCd>PREM</CoverageCd>
						</CommlCoverage>
						<CommlCoverage>
							<CoverageCd>PRDCO</CoverageCd>
						</CommlCoverage>
						<ClassCd>58058</ClassCd>
						<ClassCdDesc>PLASTIC/RUBBER GOODS-OTHER THAN</ClassCdDesc>
						<Exposure>2100000</Exposure>
						<PremiumBasisCd>GrSales</PremiumBasisCd>
						<PremOpRate>0.191</PremOpRate>
						<ProdRate>0.452</ProdRate>
					</GeneralLiabilityClassification>
					<GeneralLiabilityClassification LocationRef="L1">
						<CommlCoverage>
							<CoverageCd>PREM</CoverageCd>
						</CommlCoverage>
						<ClassCd>20410</ClassCd>
						<ClassCdDesc>Miscellaneous Classes - BI Exceptions Class 20410</ClassCdDesc>
						<PremOpRate>48.0</PremOpRate>
					</GeneralLiabilityClassification>
					<GeneralLiabilityClassification LocationRef="L2">
						<CommlCoverage>
							<CoverageCd>PREM</CoverageCd>
						</CommlCoverage>
						<ClassCd>20296</ClassCd>
						<ClassCdDesc>Miscellaneous Classes - Extended Liability class 20296</ClassCdDesc>
						<PremOpRate>105.0</PremOpRate>
					</GeneralLiabilityClassification>
				</LiabilityInfo>
				<QuestionAnswer>
					<QuestionCd>CGL12</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>CGL13</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>CGL14</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>CGL15</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>CGL16</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>CGL17</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>CGL18</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>CGL19</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>CGL20</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<QuestionAnswer>
					<QuestionCd>CGL21</QuestionCd>
					<YesNoCd>NO</YesNoCd>
				</QuestionAnswer>
				<CurrentTermAmt>
					<Amt>0</Amt>
				</CurrentTermAmt>
			</GeneralLiabilityLineBusiness>
		</CommlPkgPolicyQuoteInqRq>
	</InsuranceSvcRq>
</ACORD>

﻿<ACORD>
    <SignonRq>
        <ClientDt>2016-03-04</ClientDt>
        <ClientApp>
            <Org>Applied Systems</Org>
            <Name>TAM</Name>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>41b513bc-bb09-42dc-9455-528c14d17ad2</RqUID>
        <PersAutoPolicyQuoteInqRq>
            <com.safeco_TransactionType>Ratecall1</com.safeco_TransactionType>
            <RqUID>41b513bc-bb09-42dc-9455-528c14d17ad2</RqUID>
            <TransactionRequestDt>2015-07-10T08:11:37</TransactionRequestDt>
            <TransactionEffectiveDt>2015-07-10</TransactionEffectiveDt>
            <CurCd>USD</CurCd>
            <Producer>
                <ProducerInfo>
                    <ProducerSubCode>WW</ProducerSubCode>
                    <ContractNumber>897404</ContractNumber>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal>
                <ItemIdInfo>
                    <AgencyId>FJPNCB3</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo>
                    <NameInfo>
                        <CommlName>
                            <CommercialName>Percy Baratheon</CommercialName>
                        </CommlName>
                        <PersonName>
                            <Surname>FIERRO</Surname>
                            <GivenName>GUADALUPE</GivenName>
                        </PersonName>
                    </NameInfo>
                    <Communications>
                        <PhoneInfo>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                            <PhoneNumber>******-1111121</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo>
                            <EmailAddr><EMAIL></EmailAddr>
                            <CommunicationUseCd>Home</CommunicationUseCd>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo>
                    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo>
                        <GenderCd>F</GenderCd>
                        <BirthDt>1975-11-11</BirthDt>
                        <MaritalStatusCd>S</MaritalStatusCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <PersPolicy>
                <LOBCd>AUTOP</LOBCd>
                <com.safeco_Esign>N</com.safeco_Esign>
                <com.safeco_PropertyCrossSell>
                    <com.safeco_PropertyAddressScrubYN>Y</com.safeco_PropertyAddressScrubYN>
                </com.safeco_PropertyCrossSell>
                <ContractTerm>
                    <EffectiveDt>2015-05-10</EffectiveDt>
                    <ExpirationDt>2016-05-10</ExpirationDt>
                    <DurationPeriod>
                        <NumUnits>12</NumUnits>
                        <UnitMeasurementCd>Months</UnitMeasurementCd>
                    </DurationPeriod>
                </ContractTerm>
                <OtherOrPriorPolicy>
                    <PolicyCd>Prior</PolicyCd>
                    <InsurerName>Other</InsurerName>
                    <LOBCd>Auto</LOBCd>
                    <ContractTerm>
                        <EffectiveDt>2015-05-10</EffectiveDt>
                        <ExpirationDt>2016-07-10</ExpirationDt>
                    </ContractTerm>
                    <LengthTimeWithPreviousInsurer>
                        <NumUnits>75</NumUnits>
                        <UnitMeasurementCd>Months</UnitMeasurementCd>
                    </LengthTimeWithPreviousInsurer>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                </OtherOrPriorPolicy>
                <CreditScoreInfo>
                    <CreditScore>640</CreditScore>
                    <CreditScoreDt>2015-07-10</CreditScoreDt>
                </CreditScoreInfo>
                <QuestionAnswer>
                    <QuestionCd>Current_Insurance_Value</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>CI</Explanation>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Cancelled_Declined?</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Excluded_Vehicle_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>All_Vehicles_Covered_YN</QuestionCd>
                    <YesNoCd>N</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Credit_Accept_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                </QuestionAnswer>
                <QuestionAnswer>
                    <QuestionCd>Accept_Contract_YN</QuestionCd>
                    <YesNoCd>Y</YesNoCd>
                    <Explanation>002</Explanation>
                </QuestionAnswer>
                <PersApplicationInfo>
                    <InsuredOrPrincipal>
                        <GeneralPartyInfo>
                            <Addr>
                                <AddrTypeCd>StreetAddress</AddrTypeCd>
                                <Addr1>1025 5TH ST</Addr1>
                                <City>DEL NORTE</City>
                                <StateProvCd>CO</StateProvCd>
                                <PostalCode>81132</PostalCode>
                            </Addr>
                        </GeneralPartyInfo>
                    </InsuredOrPrincipal>
                    <ResidenceOwnedRentedCd>OWNED</ResidenceOwnedRentedCd>
                    <NumResidentsInHousehold>1</NumResidentsInHousehold>
                    <NumVehsInHousehold>1</NumVehsInHousehold>
                    <ResidenceTypeCd>DW</ResidenceTypeCd>
                </PersApplicationInfo>
                <DriverVeh>
                    <UsePct>100</UsePct>
                </DriverVeh>
                <CurrentTermAmt>
                    <Amt>69875.55</Amt>
                </CurrentTermAmt>
            </PersPolicy>
            <PersAutoLineBusiness>
                <LOBCd>AUTOP</LOBCd>
                <PersDriver id="1">
                    <GeneralPartyInfo>
                        <NameInfo>
                            <PersonName>
                                <Surname>Sagan</Surname>
                                <GivenName>Gregor</GivenName>
                            </PersonName>
                            <TaxIdentity>
                                <TaxIdTypeCd>SSN</TaxIdTypeCd>
                            </TaxIdentity>
                        </NameInfo>
                    </GeneralPartyInfo>
                    <DriverInfo>
                        <PersonInfo>
                            <GenderCd>F</GenderCd>
                            <BirthDt>1980-01-01</BirthDt>
                            <MaritalStatusCd>S</MaritalStatusCd>
                            <com.safeco_IndustryCd>Construct/EnrgyTrds</com.safeco_IndustryCd>
                            <com.safeco_OccupationCd>Laborer/Worker</com.safeco_OccupationCd>
                            <LengthTimeCurrentOccupation>
                                <NumUnits>03</NumUnits>
                                <UnitMeasurementCd>Years</UnitMeasurementCd>
                            </LengthTimeCurrentOccupation>
                        </PersonInfo>
                        <DriversLicense>
                            <LicenseClassCd>Valid</LicenseClassCd>
                            <LicensedDt>1991-11-11</LicensedDt>
                            <DriversLicenseNumber>1234567</DriversLicenseNumber>
                            <StateProvCd>CO</StateProvCd>
                        </DriversLicense>
                        <QuestionAnswer>
                            <QuestionCd>Suspended_Revoked?</QuestionCd>
                            <YesNoCd>N</YesNoCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Number_Days_Suspended_Revoked</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>AlcoholDrugCourseDate</QuestionCd>
                        </QuestionAnswer>
                        <QuestionAnswer>
                            <QuestionCd>Education</QuestionCd>
                            <YesNoCd>Y</YesNoCd>
                            <Explanation>BS</Explanation>
                        </QuestionAnswer>
                    </DriverInfo>
                    <PersDriverInfo>
                        <DistantStudentInd>N</DistantStudentInd>
                        <DriverRelationshipToApplicantCd>IN</DriverRelationshipToApplicantCd>
                        <DriverTrainingInd>N</DriverTrainingInd>
                        <FinancialResponsibilityFiling>
                            <FilingStatusCd>N</FilingStatusCd>
                        </FinancialResponsibilityFiling>
                        <GoodStudentCd>N</GoodStudentCd>
                        <MatureDriverInd>N</MatureDriverInd>
                        <YoungDriverSupplement>
                            <DriverTrainingInd>N</DriverTrainingInd>
                        </YoungDriverSupplement>
                    </PersDriverInfo>
                </PersDriver>
                <PersVeh id="Veh1">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2001</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh2">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2002</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh3">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2003</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh4">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2004</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh5">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2005</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh6">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2006</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh7">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2007</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh8">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2008</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh9">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2009</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh10">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2010</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh11">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2011</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh12">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2012</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh13">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2013</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh14">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2014</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh15">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2015</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh16">
                    <Manufacturer>VOLKSWAGEN</Manufacturer>
                    <Model>PASSAT GLX</Model>
                    <ModelYear>2016</ModelYear>
                    <CostNewAmt>
                        <Amt>28750</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200624</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
                <PersVeh id="Veh17">
                    <Manufacturer>Toyota</Manufacturer>
                    <Model>Tacoma TRD Off-Road</Model>
                    <ModelYear>2020</ModelYear>
                    <CostNewAmt>
                        <Amt>45768</Amt>
                    </CostNewAmt>
                    <EstimatedAnnualDistance>
                        <NumUnits>6000</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </EstimatedAnnualDistance>
                    <VehIdentificationNumber>1FAFP55S64G200462</VehIdentificationNumber>
                    <DistanceOneWay>
                        <NumUnits>0</NumUnits>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </DistanceOneWay>
                    <NonOwnedVehInd>N</NonOwnedVehInd>
                    <OdometerReading>
                        <UnitMeasurementCd>Miles</UnitMeasurementCd>
                    </OdometerReading>
                    <VehUseCd>PL</VehUseCd>
                    <Coverage>
                        <CoverageCd>TL</CoverageCd>
                        <Option>
                            <OptionTypeCd>YNInd1</OptionTypeCd>
                            <OptionCd>N</OptionCd>
                        </Option>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COMP</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>COLL</CoverageCd>
                        <Deductible>
                            <DeductibleTypeCd>USAD</DeductibleTypeCd>
                            <DeductibleAppliesToCd>AllPeril</DeductibleAppliesToCd>
                        </Deductible>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>BI</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <FormatInteger>100000</FormatInteger>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>PD</CoverageCd>
                        <Limit>
                            <FormatInteger>50000</FormatInteger>
                            <LimitAppliesToCd>PropDam</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>MEDPM</CoverageCd>
                        <Limit>
                            <FormatInteger>25000</FormatInteger>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                    </Coverage>
                    <Coverage>
                        <CoverageCd>UM</CoverageCd>
                        <Limit>
                            <LimitAppliesToCd>PerPerson</LimitAppliesToCd>
                        </Limit>
                        <Limit>
                            <LimitAppliesToCd>PerAccident</LimitAppliesToCd>
                        </Limit>
                        <Option>
                            <OptionCd>REJ</OptionCd>
                        </Option>
                    </Coverage>
                    <FullTermAmt>5289.89</FullTermAmt>
                </PersVeh>
            </PersAutoLineBusiness>
            <Location>
                <Addr>
                    <AddrTypeCd>GaragingAddress</AddrTypeCd>
                    <Addr1>1025 5TH ST</Addr1>
                    <City>DEL NORTE</City>
                    <StateProvCd>CO</StateProvCd>
                    <PostalCode>81132</PostalCode>
                </Addr>
            </Location>
        </PersAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
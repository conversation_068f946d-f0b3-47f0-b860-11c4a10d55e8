bl:
    default:
        uploadevent:
            id: 1
booktransfer:
    defaultid: 6044
eighthcar:
    enabled: true
email:
    address: <EMAIL>
    url: https://emailservice-dev.pdc.np.paas.lmig.com/sendemail
opportunity:
    topic:
        arn: arn:aws:sns:us-east-1:5854564:opportunityTopic
oppservice:
    oauth2:
        auditlog:
            client:
                access-token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
                client-id: fsdfewhgfhf
                client-secret: adsafdgfdhtrd
                grant-type: client_credentials
                scope: auditlog.create
        bt:
            payment:
                service:
                    client:
                        access-token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
                        client-id: fsdfewhgfhf
                        client-secret: adsafdgfdhtrd
                        grant-type: client_credentials
                        scope: btpaymentservice.read
        quoting:
            guideline:
                client:
                    access-token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
                    client-id: fsdfewhgfhf
                    client-secret: adsafdgfdhtrd
                    grant-type: client_credentials
                    scope: quotingguideline.read
        transformation:
            client:
                access-token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
                client-id: dfgdjdfghm
                client-secret: cdytrfghxdiyrdyxc
                grant-type: client_credentials
                scope: transformation.execute
        upload:
            preprocessor:
                client:
                    access-token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
                    client-id: fsdfewhgfhf
                    client-secret: adsafdgfdhtrd
                    grant-type: client_credentials
                    scope: uploadpreprocessor.scrub
        sensitivedata:
            client:
                access-token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
                client-id: sensitive
                client-secret: sds
                grant-type: client_credentials
                scope: tokenize
        quoteadapter:
            client:
                access-token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
                client-id: qqqw
                client-secret: dfdf
                grant-type: client_credentials
                scope: lambda_execute
        addresscleanse:
            client:
                access-token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
                client-id: sadsaa
                client-secret: adsas
                grant-type: client_credentials
                scope: addresscleanse.read
        eft:
            paymentaccounts:
                client:
                    access-token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
                    client-id: sadsaa
                    client-secret: adsas
                    grant-type: client_credentials
                    scope: read
proxy:
    port: 80
    url: app-proxy.lmig.com
qni:
    url: http://ecdev-sam-dev.apps.safeco.com/Client/DevML/Client/OpenActivity.aspx?l=o
salesforce:
    mongodb:
        connectionURL: test
        database: test
        password: SALESFORCE_MONGO_PW
        username: SALESFORCE_MONGO_UN
server:
    port: 8585
service:
    book-transfer-url-provider:
        base-url: https://test.com
        path: /
    quote-report-url-provider:
        base-url: https://test.com
        path: /v1/quote-report
    quoting-adapter-url-provider:
        url: https://test.com
    transformation-url-provider:
        url: https://www.url.com
    upload:
        base-url: https://uploadservices-test.pdc.np.paas.lmig.com
        path: /
    eft-payment:
        base
spring:
    cloud:
        aws:
            sqs:
                endpoint: http://localhost:4566
            credentials:
                access-key: someUser
                secret-key: someKey
            region:
                auto: false
                static: us-east-1
            stack:
                auto: false
    security:
      oauth2:
        client:
            registration:
              transformation:
                  client-id: dfgdjdfghm
                  client-secret: cdytrfghxdiyrdyxc
                  authorization-grant-type: client_credentials
                  scope: transformation.execute
            quote-report:
              client-id: sadsaa
              client-secret: adsas
              authorization-grant-type: client_credentials
              scope: read
            provider:
              transformation:
                  token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
              quote-report:
                    token-uri: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://www.url.com
    datasource:
        driverClassName: org.h2.Driver
        password: ''
        url: jdbc:h2:mem:test;MODE=MSSQLServer;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false;TRACE_LEVEL_FIle=3;CASE_INSENSITIVE_IDENTIFIERS=TRUE;IGNORECASE=TRUE
        username: sa
    h2:
        console:
            enabled: true
            settings:
                trace: true
                web-allow-others: true
    jpa:
        database-platform: org.hibernate.dialect.H2Dialect
        hibernate:
            ddl-auto: none
            naming:
                physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        properties:
            hibernate:
                format_sql: true
        show-sql: false
    main:
        allow-bean-definition-overriding: true
    profiles:
        active: unit,mongodisabled,testing

transformation:
    import:
        packageId: 612d548bc5eef2601e472274
    validation:
        packageId: 6137e2d5c2addc48701bd036
spipolicydata:
    url: https://spi-policy-data-service

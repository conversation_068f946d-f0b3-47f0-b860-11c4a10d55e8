DROP TABLE IF EXISTS OPPORTUNITIES;

CREATE TABLE OPPORTUNITIES (
  OPPORTUNITYID INTEGER IDENTITY PRIMARY KEY,
  status INT,
  data CLOB NOT NULL,
  lastPolicyGuid VARCHAR (50),
  effectiveDate DATE,
  priorPremium DECIMAL,
  lastQuotedPremium DECIMAL,
  businessType VARCHAR(50),
  bookTransferID INT NOT NULL,
  uploadEventID INT,
  nAICCd DECIMAL,
  customerName VARCHAR(150),
  priorCarrierGuid VARCHAR(80),
  originalXML CLOB,
  state VARCHAR(20),
  masterOppID VARCHAR(20),
  timestampCallPartner TIMESTAMP,
  timestampUpload DATE,
  timestampCleanup DATE,
  timestampIssued DATETIME,
  oppPriorCarrier VARCHAR(100),
  agencyId VARCHAR(30),
  timestampFirstCallPartner TIMESTAMP,
  lineType VARCHAR (8),
  quoteType <PERSON> (15),
  nNumber VARCHAR(12),
  billingAccountNumber VARCHAR(50),
  policyType VARCHAR(50)
);

DROP TABLE IF EXISTS OPPORTUNITYSTATUS;

CREATE TABLE OPPORTUNITYSTATUS (
  STATUS VARCHAR(50) PRIMARY KEY NOT NULL,
  ID INT
);

INSERT INTO
  OPPORTUNITYSTATUS (STATUS, ID)
VALUES
  ('Agent Updated', 12),
  ('Issued', 10),
  ('Missing Required Data', 3),
  ('Quote Clean Up', 9),
  ('Quoted - Error', 6),
  ('Quoted - Failed', 5),
  ('Quoted - Imported', 7),
  ('Quoted - Processing', 8),
  ('Quoted - Success', 4),
  ('Unquoted', 2),
  ('Upload Failed', 1),
  ('Withdrawn', 11);

DROP TABLE IF EXISTS PRIORCARRIERLIST;

CREATE TABLE PRIORCARRIERLIST (
  ID INTEGER IDENTITY PRIMARY KEY,
  acordValue VARCHAR(250),
  description VARCHAR(100)
);

DROP TABLE IF EXISTS BOOKTRANSFER;

CREATE TABLE BOOKTRANSFER(
	bookTransferID INTEGER IDENTITY PRIMARY KEY,
	AgentNum varchar(50) NOT NULL,
	Carrier varchar(50) NULL,
	Status varchar(150) NULL,
	SalesforceCode varchar(80) NULL,
	Name varchar(300) NULL,
	Nnumber varchar(50) NULL,
	SubCode varchar(50) NULL,
	LSCIndicator varchar(50) NULL,
	Region varchar(50) NULL,
	SFDCID int NULL,
	PRMNnumber varchar(50) NULL,
	PremiumConversionRateTarget numeric(5) NULL DEFAULT (NULL),
	TotalCompetitorBookWPForecast numeric(9) NULL DEFAULT (NULL),
	AutoPremium numeric(9) NULL DEFAULT (NULL),
	AutoPIF int NULL DEFAULT (NULL),
	PropertyPremium numeric(9) NULL DEFAULT (NULL),
	PropertyPIF int NULL DEFAULT (NULL),
	OtherPremium numeric(9) NULL DEFAULT (NULL),
	OtherPIF int NULL DEFAULT (NULL),
	ExpectedIssuedPremium numeric(9) NULL DEFAULT (NULL),
	BookTransferExpectedPLItems int NULL DEFAULT (NULL),
	PremiumFlatCancellationRateForecast numeric(5) NULL DEFAULT (NULL),
	NBIssuedPremiumForecast numeric(9) NULL DEFAULT (NULL),
	QuotePhase varchar(150) NULL DEFAULT (NULL),
	PeerlessBookTransfer varchar(50) NULL DEFAULT (NULL),
	NBDRelationship varchar(150) NULL DEFAULT (NULL),
	AssignedQuotingUT varchar(150) NULL DEFAULT (NULL),
	FirstEffectiveDate date NULL DEFAULT (NULL),
	EstimatedLastEffectiveDate date NULL DEFAULT (NULL),
	AgencyAddress1 varchar(150) NULL,
	AgencyAddress2 varchar(150) NULL,
	AgencyCity varchar(50) NULL,
	AgencyState varchar(25) NULL,
	AgencyZip varchar(10) NULL,
	AgencyPhoneNumber varchar(15) NULL

);

DROP TABLE IF EXISTS QUOTEREPORTITEM;

CREATE TABLE QUOTEREPORTITEM (
  quoteReportID INTEGER IDENTITY PRIMARY KEY,
  salesforceID INT,
  effectiveDate VARCHAR(150),
  customerName VARCHAR(150),
  ratingState VARCHAR(150),
  lOB VARCHAR(150),
  priorCarrierPolicyTerm VARCHAR(150),
  safecoPolicyTerm VARCHAR(150),
  priorCovA INT,
  safecoCovA INT,
  priorPremium INT,
  safecoPremium INT,
  naturalRate INT,
  status VARCHAR(150),
  policyNumber VARCHAR(150),
  comments VARCHAR(1000),
  extra1 VARCHAR(150),
  extra2 VARCHAR(150),
  extra3 VARCHAR(150),
  phase VARCHAR(150),
  policyGuid VARCHAR(150),
  subCode VARCHAR(150),
  quote_SalesforceID VARCHAR(150),
  customerFirstName VARCHAR(150),
  customerLastName VARCHAR(150),
  nonpay_date VARCHAR(150),
  resolution_date VARCHAR(150),
  unpaidPremiumContactDate VARCHAR(150),
  unpaidPremiumContactStatus VARCHAR(150),
  receiveDate VARCHAR(150),
  priceMatchYN VARCHAR(150),
  safecoLetterMailFileSent VARCHAR(150),
  customerMailZip VARCHAR(150),
  customerMailState VARCHAR(150),
  customerMailCity VARCHAR(150),
  customerMailAddress1 VARCHAR(150),
  customerMailAddress2 VARCHAR(150),
  letterSentIndicator VARCHAR(150),
  customerPhoneNumber VARCHAR(150),
  issuedDate DATE,
  SafecoCompetitiveAtIssue VARCHAR(1),
  expiringPremium INT
);

DROP TABLE IF EXISTS PROCESSRESULTITEM;

CREATE TABLE PROCESSRESULTITEM (
  ProcessResultID INTEGER IDENTITY PRIMARY KEY,
  Data CLOB,
  OpportunityID INT,
  Transactions CLOB,
  TimeStamp VARCHAR,
  ProcessRequestID INT,
  PolicyGUID VARCHAR,
  PartnerRequestXML CLOB
);

DROP TABLE IF EXISTS UploadEvent;

CREATE TABLE UploadEvent(
	UploadEventID INTEGER IDENTITY PRIMARY KEY,
	BookTransferID int,
	AgentNum varchar(50),
	Filename varchar(1024),
	Status varchar(50),
	TimeStamp datetime,
	Nnumber varchar(50),
	OppPriorCarrier varchar(100) 
);
package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.AuditLogWebClient;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.domain.AuditLogException;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.StartTransactionRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.TransactionEventRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.response.TransactionResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Copyright (c) 2022, Liberty Mutual Group
 * <p>
 * Created on 04/08/2022
 */
class AuditLogHelperTest {
    private AuditLogHelper objectUnderTest;
    private AuditLogWebClient auditLogWebClient;

    @BeforeEach
    public void setUp() {
        auditLogWebClient = mock(AuditLogWebClient.class);
        objectUnderTest = new AuditLogHelper(auditLogWebClient);
    }

    @Test
    void testStartTransactionReturnsNonNullResponse() throws AuditLogException {
        //Given I have a transaction to start
        StartTransactionRequest request = StartTransactionRequest.builder()
                .type("test")
                .build();
        //And audit log webclient mock return response back
        when(auditLogWebClient.startTransaction(request)).thenReturn(new TransactionResponse());

        //When I call auditlog helper to start the transaction
        TransactionResponse response = objectUnderTest.startTransaction(request);

        //Then I should get the non-null response
        assertNotNull(response);
    }

    @Test
    void testStartTransactionThrows() throws AuditLogException {
        //Given I have a transaction to start
        StartTransactionRequest request = StartTransactionRequest.builder()
                .type("test")
                .build();
        //And audit log webclient mock throws an exception
        when(auditLogWebClient.startTransaction(request)).thenThrow(new AuditLogException(new Exception()));

        //When I call auditlog helper to start the transaction
        //Then it should throw
        assertThrows(RuntimeException.class, () -> objectUnderTest.startTransaction(request));
    }

    @Test
    void testAddTransactionEventReturnsNonNullResponse() throws AuditLogException {
        //Given I have a transaction event to add to the existing transaction
        String id="123456";
        TransactionEventRequest request = TransactionEventRequest.builder()
                .name("test")
                .build();
        //And audit log webclient mock return response back
        when(auditLogWebClient.addTransactionEvent(id, request)).thenReturn(new TransactionResponse());

        //When I call auditlog helper to add the transaction event
        TransactionResponse response = objectUnderTest.addTransactionEvent(id, request);

        //Then I should get the non-null response
        assertNotNull(response);
    }

    @Test
    void testAddTransactionEventThrows() throws AuditLogException {
        //Given I have a transaction event to add to the existing transaction
        String id="123456";
        TransactionEventRequest request = TransactionEventRequest.builder()
                .name("test")
                .build();
        //And audit log webclient mock throws an exception
        when(auditLogWebClient.addTransactionEvent(id, request)).thenThrow(new AuditLogException(new Exception()));

        //When I call auditlog helper to add the transaction event
        //Then it should throw
        assertThrows(RuntimeException.class, () -> objectUnderTest.addTransactionEvent(id, request));
    }
}

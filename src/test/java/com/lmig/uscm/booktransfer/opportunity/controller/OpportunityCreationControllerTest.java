/*
 * Copyright (C) 2018, Liberty Mutual Group
 *
 * Created on 12/2/18 by n0210477
 */
package com.lmig.uscm.booktransfer.opportunity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityCreationHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class OpportunityCreationControllerTest {
    private MockMvc mvc;
    @Mock
    OpportunityCreationHelper opportunityCreationHelper;

    @InjectMocks
    OpportunityCreationController opportunityCreationController;

    ObjectMapper mapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        mvc = MockMvcBuilders
                .standaloneSetup(opportunityCreationController)
                .setControllerAdvice(new OpportunityAdvice(null))
                .build();
    }

    @Test
    public void testCreateOpportunityForLargeTransferUtility() throws Exception {
        opportunityCreationController.setObjectMapper(mapper);
        MockMultipartFile firstFile = new MockMultipartFile("file", "filename.txt", "text/plain", "some xml".getBytes());

        when(opportunityCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(anyList(), any(), anyString()))
          .thenReturn(new AsyncResult<>(new OpportunityCreationResponse()));
        mvc.perform(MockMvcRequestBuilders.multipart("/opportunity/v3")
            .file(firstFile)
            .param("creationRequest", mapper.writeValueAsString(new OpportunityCreationRequest()))
            .param("tePackageId", "12347fgjl78789"))
            .andExpect(status().isOk())
            .andExpect(content().string("{\"uploadedFileCount\":1}"));
    }

}

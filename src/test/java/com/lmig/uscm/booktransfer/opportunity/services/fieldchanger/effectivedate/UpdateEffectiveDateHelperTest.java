/*
 * Copyright (C) 2017, Liberty Mutual Group
 *
 * Created on Apr 5, 2017
 */

package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.config.BookTransferUrlProvider;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.email.client.domain.EmailException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.ChangeEffectiveDateResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeField;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.services.ExportHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.BusinessAcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.mock;

class UpdateEffectiveDateHelperTest {
	private static final String EXPIRATION_DATE = "//PersPolicy/ContractTerm/ExpirationDt";

	private static final String EFFECTIVE_DATE = "//PersPolicy/ContractTerm/EffectiveDt";

	private final UpdateMatchingCustomerEffectiveDateHelper updateEffectiveDateHelper =
			new UpdateHomeLeadEffectiveDateHelper(null, null, null, null);

	private Opportunity homeOppEffDate20170518;
	private Opportunity homeOppEffDate20170918;

	private Opportunity autoOppEffDate20170510;
	private Opportunity autoOppEffDate20170529;

	private Opportunity umbrpOppEffDate20170321;
	private Opportunity fireOppEffDate20170305;

	// before homeOppEffDate20170518
	private Opportunity workOppEffDate20170101;
	// after homeOppEffDate20170518
	private Opportunity workOppEffDate20171231;

	@BeforeEach
	public void setUp() throws IOException, XPathExpressionException, ParserConfigurationException, SAXException, TransformerException {
		Path path = Paths.get("src/test/resources/xml/effdate-home-05-18-2017.xml");
		byte[] content = Files.readAllBytes(path);
		String acordXml = new String(content);
		homeOppEffDate20170518 = new Opportunity();
		homeOppEffDate20170518.setOpportunityId(1);
		homeOppEffDate20170518.setBusinessType("HOME");
		homeOppEffDate20170518.setData(acordXml);
		homeOppEffDate20170518.setEffectiveDate("2017-05-18");

		path = Paths.get("src/test/resources/xml/effdate-home-09-18-2017.xml");
		content = Files.readAllBytes(path);
		acordXml = new String(content);
		homeOppEffDate20170918 = new Opportunity();
		homeOppEffDate20170918.setOpportunityId(1);
		homeOppEffDate20170918.setBusinessType("HOME");
		homeOppEffDate20170918.setData(acordXml);
		homeOppEffDate20170918.setEffectiveDate("2017-09-18");

		path = Paths.get("src/test/resources/xml/effdate-auto-05-29-2017.xml");
		content = Files.readAllBytes(path);
		acordXml = new String(content);
		autoOppEffDate20170529 = new Opportunity();
		autoOppEffDate20170529.setOpportunityId(2);
		autoOppEffDate20170529.setBusinessType("AUTO");
		autoOppEffDate20170529.setData(acordXml);
		autoOppEffDate20170529.setEffectiveDate("2017-05-29");

		path = Paths.get("src/test/resources/xml/effdate-auto-05-10-2017.xml");
		content = Files.readAllBytes(path);
		acordXml = new String(content);
		autoOppEffDate20170510 = new Opportunity();
		autoOppEffDate20170510.setOpportunityId(2);
		autoOppEffDate20170510.setBusinessType("AUTO");
		autoOppEffDate20170510.setData(acordXml);
		autoOppEffDate20170510.setEffectiveDate("2017-05-10");

		path = Paths.get("src/test/resources/xml/effdate-umbrp-03-21-2017.xml");
		content = Files.readAllBytes(path);
		acordXml = new String(content);
		umbrpOppEffDate20170321 = new Opportunity();
		umbrpOppEffDate20170321.setOpportunityId(3);
		umbrpOppEffDate20170321.setBusinessType("UMBRP");
		umbrpOppEffDate20170321.setData(acordXml);
		umbrpOppEffDate20170321.setEffectiveDate("2017-03-21");

		path = Paths.get("src/test/resources/xml/effdate-dfire-03-05-2017.xml");
		content = Files.readAllBytes(path);
		acordXml = new String(content);
		fireOppEffDate20170305 = new Opportunity();
		fireOppEffDate20170305.setOpportunityId(2);
		fireOppEffDate20170305.setBusinessType("DFIRE");
		fireOppEffDate20170305.setData(acordXml);
		fireOppEffDate20170305.setEffectiveDate("2017-03-05");

		Document workXml = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");
		workOppEffDate20170101 = new Opportunity();
		workOppEffDate20170101.setLineType(LineType.Business);
		workOppEffDate20170101.setOpportunityId(23);
		workOppEffDate20170101.setBusinessType("WORK");
		BusinessAcordHelper.setEffectiveDt(workXml, "2017-01-01");
		workOppEffDate20170101.setEffectiveDate("2017-01-01");
		workOppEffDate20170101.setData(XmlHelper.getDocumentString(workXml));

		workOppEffDate20171231 = new Opportunity();
		workOppEffDate20171231.setLineType(LineType.Business);
		workOppEffDate20171231.setOpportunityId(32);
		workOppEffDate20171231.setBusinessType("WORK");
		BusinessAcordHelper.setEffectiveDt(workXml, "2017-12-31");
		workOppEffDate20171231.setEffectiveDate("2017-12-31");
		workOppEffDate20171231.setData(XmlHelper.getDocumentString(workXml));

		// same month homeOppEffDate20170518
		Opportunity workOppEffDate20170525 = new Opportunity();
		workOppEffDate20170525.setLineType(LineType.Business);
		workOppEffDate20170525.setOpportunityId(32);
		workOppEffDate20170525.setBusinessType("WORK");
		BusinessAcordHelper.setEffectiveDt(workXml, "2017-05-25");
		workOppEffDate20170525.setEffectiveDate("2017-05-25");
		workOppEffDate20170525.setData(XmlHelper.getDocumentString(workXml));
	}

	/**
	 * Scenario: After Start Date = Don't update Given I have selected one opportunity When I click "Change Effective
	 * Dates" And select "True Lead" Or select "Home" And enter a "BT Start Date" that is before all of the effective
	 * dates And I click "Execute" Then all of the opportunities' effective dates remain unchanged
	 */
	@Test
	void testNotUpdatingTestTrueLead() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);

		String startDate = "03/10/2017";
		String leadType = "truelead";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(1, results.size());
		OppChangeFieldResult result = results.get(0);

		assertThatTheDateWasNotUpdated(result);
	}

	/**
	 *
	 * Scenario: After Start Date = Don't update Given I have selected one opportunity When I click "Change Effective
	 * Dates" AND select "Home" And enter a "BT Start Date" that is before all of the effective dates And I click
	 * "Execute" Then all of the opportunities' effective dates remain unchanged
	 */
	@Test
	void testNotUpdatingTestHomeLead() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);

		String startDate = "03/10/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(1, results.size());
		OppChangeFieldResult result = results.get(0);

		assertThatTheDateWasNotUpdated(result);
	}

	@Test
	void testMultipleOppsPLAndBL_homeLead_setDateBeforeEarliestHomeEffDt_updateEffToMatchHome() {
		String expectedEarliestHomeEffDt = homeOppEffDate20170518.getEffectiveDate();
		String setDateValue = "01/01/2016";
		String leadType = "home";

		List<Opportunity> opps = List.of(homeOppEffDate20170518, autoOppEffDate20170510, workOppEffDate20170101, workOppEffDate20171231);
		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(opps, setDateValue, leadType);

		assertEquals(4, results.size());

		assertTrue(results.get(0).didFail()); // HOME
		assertTrue(results.get(1).didFail()); // AUTOP sanity check
		assertEquals(expectedEarliestHomeEffDt, results.get(2).getFieldUpdatedValue()); // WORK
		assertEquals(expectedEarliestHomeEffDt, results.get(3).getFieldUpdatedValue()); // WORK
	}

	@Test
	void testMultipleOppsPLAndBL_trueLead_setDateBeforeEarliestOppEffDt_updateEffToMatchEarliestOpp() {
		String expectedEarliestOppEffDt = workOppEffDate20170101.getEffectiveDate();
		String setDateValue = "01/01/2016";
		String leadType = "truelead";

		List<Opportunity> opps = List.of(homeOppEffDate20170518, autoOppEffDate20170510, workOppEffDate20170101, workOppEffDate20171231);
		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(opps, setDateValue, leadType);

		assertEquals(4, results.size());

		assertEquals(expectedEarliestOppEffDt, results.get(0).getFieldUpdatedValue()); // HOME
		assertEquals(expectedEarliestOppEffDt, results.get(1).getFieldUpdatedValue()); // AUTOP sanity check
		assertTrue(results.get(2).didFail()); // WORK
		assertEquals(expectedEarliestOppEffDt, results.get(3).getFieldUpdatedValue()); // WORK
	}

	@Test
	void testMultipleOppsPLAndBL_trueLead_setDateAfterSomeOppEffDt_updateEffToNextTermAfterSetDateOrEarliestOpp() {
		String expectedEarliestOppNextTermEffDt = "2018-05-10"; // autoOppEffDate20170510
		String setDateValue = "01/05/2018";
		String leadType = "truelead";

		List<Opportunity> opps = List.of(homeOppEffDate20170518, autoOppEffDate20170510, workOppEffDate20170101, workOppEffDate20171231);
		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(opps, setDateValue, leadType);

		assertEquals(4, results.size());

		// updated date has same month as setDate so dont update to earliest opp
		assertEquals("2018-05-18", results.get(0).getFieldUpdatedValue()); // HOME
		assertEquals(expectedEarliestOppNextTermEffDt, results.get(1).getFieldUpdatedValue()); // AUTOP sanity check
		assertEquals(expectedEarliestOppNextTermEffDt, results.get(2).getFieldUpdatedValue()); // WORK
		assertEquals(expectedEarliestOppNextTermEffDt, results.get(3).getFieldUpdatedValue()); // WORK
	}

	@Test
	void testMultipleOppsPLAndBL_nextTermLead_setDateAfterSomeOppEffDt_updateEarlierEffByTermToSetDate() {
		String setDateValue = "05/12/2017";
		String leadType = "next term";

		List<Opportunity> opps = List.of(homeOppEffDate20170518, autoOppEffDate20170510, workOppEffDate20170101, workOppEffDate20171231);
		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(opps, setDateValue, leadType);

		assertEquals(4, results.size());

		assertTrue(results.get(0).didFail()); // HOME
		// term + 2017-05-10
		assertEquals("2018-05-10", results.get(1).getFieldUpdatedValue()); // AUTOP sanity check
		// term + 2017-01-01
		assertEquals("2018-01-01", results.get(2).getFieldUpdatedValue()); // WORK
		assertTrue(results.get(3).didFail()); // WORK
	}

	@Test
	void testMultipleOppsPLAndBL_setDateLead_updateEffToSetDate() {
		String setDateValue = "05/12/2017";
		String expectedValue = "2017-05-12";
		String leadType = "set date";

		List<Opportunity> opps = List.of(homeOppEffDate20170518, autoOppEffDate20170510, workOppEffDate20170101, workOppEffDate20171231);
		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(opps, setDateValue, leadType);

		assertEquals(4, results.size());

		assertEquals(expectedValue, results.get(0).getFieldUpdatedValue()); // HOME
		assertEquals(expectedValue, results.get(1).getFieldUpdatedValue()); // AUTOP sanity check
		assertEquals(expectedValue, results.get(2).getFieldUpdatedValue()); // WORK
		assertEquals(expectedValue, results.get(3).getFieldUpdatedValue()); // WORK
	}

	/**
	 *
	 * Scenario: Before Start Date = Update Given I have selected one opportunity When I click "Change Effective Dates"
	 * And select "True Lead" And enter a "BT Start Date" that is after all of the effective dates And I click "Execute"
	 * Then all of the opportunities' ContractTerm dates are updated to a term starting after the "BT Start Date"
	 */
	@Test
	void testUpdatingTestTrueLead() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);

		String startDate = "10/10/2017";
		String leadType = "truelead";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(1, results.size());
		OppChangeFieldResult result = results.get(0);
		assertEquals("2018-05-18", result.getFieldUpdatedValue());
	}

	/**
	 * Scenario: True Lead / Match / Before Start Date / Diff Months = Update
	 *
	 * <p>Given I have selected multiple opportunities And their AgencyID match And their effective dates are before the
	 * "BT Start Date" When I click "Change Effective Dates" And select "True Lead" And enter a "BT Start Date" that is
	 * after all of the effective dates And I click "Execute" Then all of the opportunities' ContractTerm dates are
	 * updated to a term starting after the "BT Start Date" Then identify the earliest effective date And their new
	 * effective dates are in different months Then all of the opportunities' effective dates are updated to the
	 * earliest effective date
	 */
	@Test
	void testMultiUpdatingTestTrueLead() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(umbrpOppEffDate20170321);

		String startDate = "10/10/2017";
		String leadType = "truelead";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(2, results.size());
		assertEquals("2018-03-21", results.get(0).getFieldUpdatedValue());
		assertEquals("2018-03-21", results.get(1).getFieldUpdatedValue());
	}

	/**
	 * Scenario: True Lead / Match / Before Start Date / Same Month & Year = Update
	 *
	 * <p>Given I have selected multiple opportunities And their AgencyID match And their new effective dates are in the
	 * same month and year as the lead effective date And their effective dates are before the "BT Start Date" When I
	 * click "Change Effective Dates" And select "True Lead" And enter a "BT Start Date" that is after all of the
	 * effective dates And I click "Execute" Then all of the opportunities' ContractTerm dates are updated to a term
	 * starting after the "BT Start Date"
	 */
	@Test
	void testMultiUpdatingTestTrueLeadInSameMonth() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(autoOppEffDate20170529);

		String startDate = "10/10/2017";
		String leadType = "truelead";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(2, results.size());
		assertEquals("2018-05-18", results.get(0).getFieldUpdatedValue());
		assertEquals("2018-05-29", results.get(1).getFieldUpdatedValue());
	}

	/**
	 * Scenario: True Lead / Match / In between Start Date / Same Month & Year = Update
	 *
	 * <p>Given I have selected multiple opportunities And their AgencyID match And their new effective dates are in the
	 * same month and year as the lead effective date And the home effective date is before the start date but the auto
	 * is after When I click "Change Effective Dates" And select "True Home" And enter a "BT Start Date" that is after
	 * all of the effective dates And I click "Execute" Then all of the opportunities' ContractTerm dates are updated to
	 * a term starting after the "BT Start Date" and in the same year as the home needs to be
	 */
	@Test
	void testMultiUpdatingTestTrueHomeInSameMonthWithHomeComingFirst() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(autoOppEffDate20170510);

		String startDate = "05/15/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(2, results.size());
		assertThatTheDateWasNotUpdated(results.get(0));
		assertEquals("2017-05-18", results.get(1).getFieldUpdatedValue());
	}

	/**
	 * Scenario: True Lead / Match / After Start Date AUTO then Home / Same Month & Year = Update
	 *
	 * <p>Given I have selected multiple opportunities And their AgencyID match And their new effective dates are in the
	 * same month and year as the lead effective date And both dates are after the start date, but auto comes first When
	 * I click "Change Effective Dates" And select "True Home" And enter a "BT Start Date" that is after all of the
	 * effective dates And I click "Execute" Then the opportunities should not be updated.
	 */
	@Test
	void testMultiUpdatingTestTrueHomeInSameMonthWithStartDateFirst() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(autoOppEffDate20170510);

		String startDate = "05/01/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(2, results.size());
		assertThatTheDateWasNotUpdated(results.get(0));
		assertThatTheDateWasNotUpdated(results.get(1));
	}

	/**
	 * Scenario: Before Start Date = Update Given I have selected one opportunity When I click "Change Effective Dates"
	 * AND select "Home" And enter a "BT Start Date" that is after all of the effective dates And I click "Execute" Then
	 * all of the opportunities' ContractTerm dates are updated to a term starting after the "BT Start Date"
	 */
	@Test
	void testUpdatingTestHomeLead() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);

		String startDate = "10/10/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(1, results.size());
		OppChangeFieldResult result = results.get(0);
		assertEquals("2018-05-18", result.getFieldUpdatedValue());
	}

	/**
	 * Scenario: Home / Match / Before Start Date / Same Month & Year = Update Given I have selected multiple
	 * opportunities And their AgencyID match And their effective dates are before the "BT Start Date" And their new
	 * effective dates are in the same month and year as the lead Home's effective date When I click "Change Effective
	 * Dates" And select "Home" And enter a "BT Start Date" that is after all of the effective dates And I click
	 * "Execute" Then all of the opportunities' keep the new ContractTerm dates and are not updated to match the Home's
	 * effective date
	 */
	@Test
	void testUpdatingTestHomeLeadBeforeStartDateWithSameMonthAndYear() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(autoOppEffDate20170529);

		String startDate = "10/10/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(2, results.size());
		assertEquals("2018-05-18", results.get(0).getFieldUpdatedValue());
		assertEquals("2018-05-29", results.get(1).getFieldUpdatedValue());
	}

	/**
	 * Scenario: True Lead / Match / After Start Date / Same Month & Year = Don't Update Given I have selected multiple
	 * opportunities And their AgencyID match And the effective dates are in the same month and year as the lead
	 * effective date When I click "Change Effective Dates" And select "True Lead" And enter a "BT Start Date" that is
	 * before all of the effective dates And I click "Execute" Then all of the opportunities' effective dates remain
	 * unchanged
	 */
	@Test
	void testUpdatingOppsInSameMonthTrueLead() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(autoOppEffDate20170529);

		String startDate = "02/10/2017";
		String leadType = "truelead";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(2, results.size());
		assertThatTheDateWasNotUpdated(results.get(0));
		assertThatTheDateWasNotUpdated(results.get(1));
	}

	private void assertThatTheDateWasNotUpdated(OppChangeFieldResult result) {
		assertNull(result.getFieldUpdatedValue());
		assertTrue(result.didFail());
		assertEquals("Effective date same as updated date or is in same month", result.getStatusMessage());
	}

	/**
	 * Scenario: Home / Match / After Start Date / Diff Months = Update Given that i have 3 opportunities that are a
	 * customer And the home update is not the earliest date But I have an auto policy that is set to renew in the stuff
	 * month and year as the home And I have a start date that is before all of the policies start Then everything but
	 * the auto policy will be updated to the home date and the auto policy will stay the same since it is in the month
	 * and year as the home policy.
	 */
	@Test
	void testHomeLead1() throws Exception {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(autoOppEffDate20170529);
		ops.add(umbrpOppEffDate20170321);

		String startDate = "03/10/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(3, results.size(), "results size should be 3");
		OppChangeFieldResult result = results.get(0);
		assertTrue(result.getLob().equalsIgnoreCase("home"), "first opp should be home");
		assertEquals("2017-05-18", result.getOriginalValue(), "original effective date incorrectly updated");
		assertNull(result.getFieldUpdatedValue(), "effective date should not update");
		result = results.get(1);
		assertTrue(result.getLob().equalsIgnoreCase("auto"), "second opp should be auto");
		assertEquals("2017-05-29", result.getOriginalValue(), "original effective date incorrectly updated");
		assertThatTheDateWasNotUpdated(result);

		result = results.get(2);
		assertTrue(result.getLob().equalsIgnoreCase("umbrp"), "third opp should be umb");
		assertEquals("2017-03-21", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2017-05-18", result.getFieldUpdatedValue(), "effective date should not update");

		Document doc = XmlHelper.getDocument(result.getOpportunity().getData());
		XPath xFactory = XPathFactory.newInstance().newXPath();

		Element effectiveDate = (Element) xFactory.evaluate(EFFECTIVE_DATE, doc, XPathConstants.NODE);
		assertEquals("2017-05-18", effectiveDate.getTextContent(), "effectiveDate not set correctly");

		Element expirationDate = (Element) xFactory.evaluate(EXPIRATION_DATE, doc, XPathConstants.NODE);
		assertEquals("2018-05-18", expirationDate.getTextContent(), "expirationDate not set correctly");
	}

	/**
	 * Given that i have 3 opportunities that are a customer And the home update is not the earliest date But I have an
	 * auto policy that is set to renew in the stuff month and year as the home And I have a start date that is after
	 * the home start date Then everything but the auto policy will be updated to the home date added the next policy
	 * term (one year) and the auto policy will move forward one year since it is in the month and year as the home
	 * policy.
	 */
	@Test
	void testHomeLead2() {

		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(autoOppEffDate20170529);
		ops.add(umbrpOppEffDate20170321);

		String startDate = "05/20/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(3, results.size());
		OppChangeFieldResult result = results.get(0);
		assertTrue(result.getLob().equalsIgnoreCase("home"), "first opp should be home");
		assertEquals("2017-05-18", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2018-05-18", result.getFieldUpdatedValue(), "effective date should update");

		result = results.get(1);
		assertEquals("auto", result.getLob().toLowerCase(), "second opp should be auto");
		assertEquals("2017-05-29", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2018-05-29", result.getFieldUpdatedValue());

		result = results.get(2);
		assertTrue(result.getLob().equalsIgnoreCase("umbrp"), "third opp should be umb");
		assertEquals("2017-03-21", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2018-05-18", result.getFieldUpdatedValue(), "effective date should update");
	}

	/**
	 * Given that i have 3 opportunities that are a customer And the home update is not the earliest date And I have a
	 * start date that is after the home start date Then everything will be updated to the home date added the next
	 * policy term (one year)
	 */
	@Test
	void testHomeLead3() throws Exception {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170918);
		ops.add(autoOppEffDate20170529);
		ops.add(umbrpOppEffDate20170321);

		String startDate = "10/10/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(3, results.size());
		OppChangeFieldResult result = results.get(0);
		assertTrue(result.getLob().equalsIgnoreCase("home"), "first opp should be home");
		assertEquals("2017-09-18", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2018-09-18", result.getFieldUpdatedValue(), "effective date should update");

		result = results.get(1);
		assertTrue(result.getLob().equalsIgnoreCase("auto"), "second opp should be auto");
		assertEquals("2017-05-29", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2018-09-18", result.getFieldUpdatedValue(), "effective date should update");

		Document doc = XmlHelper.getDocument(result.getOpportunity().getData());
		XPath xFactory = XPathFactory.newInstance().newXPath();

		Element effectiveDate = (Element) xFactory.evaluate(EFFECTIVE_DATE, doc, XPathConstants.NODE);
		assertEquals("2018-09-18", effectiveDate.getTextContent(), "effectiveDate not set correctly");

		Element expirationDate = (Element) xFactory.evaluate(EXPIRATION_DATE, doc, XPathConstants.NODE);
		assertEquals("2019-09-18", expirationDate.getTextContent(), "expirationDate not set correctly");

		result = results.get(2);
		assertTrue(result.getLob().equalsIgnoreCase("umbrp"), "third opp should be umb");
		assertEquals("2017-03-21", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2018-09-18", result.getFieldUpdatedValue(), "effective date should update");

		doc = XmlHelper.getDocument(result.getOpportunity().getData());
		xFactory = XPathFactory.newInstance().newXPath();

		effectiveDate = (Element) xFactory.evaluate(EFFECTIVE_DATE, doc, XPathConstants.NODE);
		assertEquals("2018-09-18", effectiveDate.getTextContent(), "effectiveDate not set correctly");

		expirationDate = (Element) xFactory.evaluate(EXPIRATION_DATE, doc, XPathConstants.NODE);
		assertEquals("2019-09-18", expirationDate.getTextContent(), "expirationDate not set correctly");
	}

	/**
	 * Given that i have 2 opportunities that are a customer And there is not Home policy And I have a start date that
	 * is before all start dates Then everything will be updated to the earliest policy date
	 */
	@Test
	void testHomeLead4() throws Exception {

		List<Opportunity> ops = new ArrayList<>();
		ops.add(autoOppEffDate20170529);
		ops.add(umbrpOppEffDate20170321);

		String startDate = "02/10/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(2, results.size());
		OppChangeFieldResult result = results.get(0);
		assertTrue(result.getLob().equalsIgnoreCase("auto"), "second opp should be auto");
		assertEquals("2017-05-29", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2017-03-21", result.getFieldUpdatedValue());

		Document doc = XmlHelper.getDocument(result.getOpportunity().getData());
		XPath xFactory = XPathFactory.newInstance().newXPath();

		Element effectiveDate = (Element) xFactory.evaluate(EFFECTIVE_DATE, doc, XPathConstants.NODE);
		assertEquals("2017-03-21", effectiveDate.getTextContent(), "effectiveDate not set correctly");

		Element expirationDate = (Element) xFactory.evaluate(EXPIRATION_DATE, doc, XPathConstants.NODE);
		assertEquals("2018-03-21", expirationDate.getTextContent(), "expirationDate not set correctly");

		result = results.get(1);
		assertTrue(result.getLob().equalsIgnoreCase("umbrp"), "third opp should be umb");
		assertEquals("2017-03-21", result.getOriginalValue(), "original effective date incorrectly updated");
		assertNull(result.getFieldUpdatedValue(), "effective date not should update");

	}

	/**
	 * Given that i have 2 opportunities that are a customer And there is not Home policy And I have a start date that
	 * is in the same month as both policies, however it is in between the two policies Then everything will be updated
	 * to the first effective date after the start date
	 */
	@Test
	void testHomeLead5() {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(fireOppEffDate20170305); // compare 3/5/2018 and 3/21/2017, use 3/21 as lead but also update
		ops.add(umbrpOppEffDate20170321);

		String startDate = "03/10/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(2, results.size(), "size should be 2");
		OppChangeFieldResult result = results.get(0);
		assertTrue(result.getLob().equalsIgnoreCase("dfire"), "first opp should be dfire");
		assertEquals("2017-03-05", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2017-03-21", result.getFieldUpdatedValue());

		result = results.get(1);
		assertTrue(result.getLob().equalsIgnoreCase("umbrp"), "second opp should be umb");
		assertEquals("2017-03-21", result.getOriginalValue(), "original effective date incorrectly updated");
		assertNull(result.getFieldUpdatedValue(), "effective date should not update");
	}

	/**
	 * Given that I have 2 opportunities that are a customer And they are both home And I have a start date that is
	 * after one home start date, but before the other, then everything will be updated to the last home date.
	 */
	@Test
	void testHomeLead6() throws Exception {

		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(homeOppEffDate20170918);
		String startDate = "06/01/2017";
		String leadType = "home";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(2, results.size(), "size should be 2");
		OppChangeFieldResult result = results.get(0);
		assertTrue(result.getLob().equalsIgnoreCase("home"), "first opp should be home");
		assertEquals("2017-05-18", result.getOriginalValue());

		assertEquals("2017-09-18", result.getFieldUpdatedValue(), "effective date should update");

		Document doc = XmlHelper.getDocument(result.getOpportunity().getData());
		XPath xFactory = XPathFactory.newInstance().newXPath();

		Element effectiveDate = (Element) xFactory.evaluate(EFFECTIVE_DATE, doc, XPathConstants.NODE);
		assertEquals("2017-09-18", effectiveDate.getTextContent(), "effectiveDate not set correctly");

		Element expirationDate = (Element) xFactory.evaluate(EXPIRATION_DATE, doc, XPathConstants.NODE);
		assertEquals("2018-09-18", expirationDate.getTextContent(), "expirationDate not set correctly");

		result = results.get(1);
		assertTrue(result.getLob().equalsIgnoreCase("home"), "second opp should be home");
		assertEquals("2017-09-18", result.getOriginalValue(), "original effective date incorrectly updated");
		assertTrue(result.didFail());
		assertEquals("Effective date same as updated date or is in same month", result.getStatusMessage());
		doc = XmlHelper.getDocument(result.getOpportunity().getData());
		xFactory = XPathFactory.newInstance().newXPath();

		effectiveDate = (Element) xFactory.evaluate(EFFECTIVE_DATE, doc, XPathConstants.NODE);
		assertEquals("2017-09-18", effectiveDate.getTextContent(), "effectiveDate not set correctly");

		expirationDate = (Element) xFactory.evaluate(EXPIRATION_DATE, doc, XPathConstants.NODE);
		assertEquals("2018-09-18", expirationDate.getTextContent(), "expirationDate not set correctly");
	}

	/**
	 * Given I have selected multiple opportunities And their AgencyID match And their effective dates are in different
	 * months When I click "Change Effective Dates" And select "True Lead" And enter a "BT Start Date" that is before
	 * all of the effective dates And I click "Execute" Then all of the opportunities' effective dates are updated to
	 * the earliest effective date in their XML
	 */
	@Test
	void testTrueLead1() throws Exception {
		// only auto and umb selected
		// auto after start date
		// umb after start date
		// expected results:
		// auto not changed
		// auto set to umbr

		List<Opportunity> ops = new ArrayList<>();
		ops.add(autoOppEffDate20170529);

		ops.add(umbrpOppEffDate20170321);

		String startDate = "02/10/2017";
		String leadType = "truelead";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(2, results.size(), "size should be 2");
		OppChangeFieldResult result = results.get(0);
		assertTrue(result.getLob().equalsIgnoreCase("auto"), "second opp should be auto");
		assertEquals("2017-05-29", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2017-03-21", result.getFieldUpdatedValue(), "effective date should update");
		Document doc = XmlHelper.getDocument(result.getOpportunity().getData());
		XPath xFactory = XPathFactory.newInstance().newXPath();

		Element effectiveDate = (Element) xFactory.evaluate(EFFECTIVE_DATE, doc, XPathConstants.NODE);
		assertEquals("2017-03-21", effectiveDate.getTextContent(), "effectiveDate not set correctly");

		Element expirationDate = (Element) xFactory.evaluate(EXPIRATION_DATE, doc, XPathConstants.NODE);
		assertEquals("2018-03-21", expirationDate.getTextContent(), "expirationDate not set correctly");

		result = results.get(1);

		assertTrue(result.getLob().equalsIgnoreCase("umbrp"), "third opp should be umb");
		assertEquals("2017-03-21", result.getOriginalValue(), "original effective date incorrectly updated");
		// assertTrue(!result.isChangedYN(), "isChangedYN should return false");
		assertNull(result.getFieldUpdatedValue(), "effective date not should update");
	}

	/**
	 * Given we have 3 policies that match as a customer And the first policy is before the start date And the other two
	 * are after it Then we update to the first policy after the start date
	 */
	@Test
	void testTrueLead2() {
		// home before start date
		// home after start date
		// auto after start date
		// expected results:
		// first home set to auto
		// second home set to auto
		// auto stays the same

		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(homeOppEffDate20170918);
		ops.add(autoOppEffDate20170529);
		String startDate = "05/20/2017";
		String leadType = "truelead";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);
		assertEquals(3, results.size(), "size should be 3");
		OppChangeFieldResult result = results.get(0);
		assertTrue(result.getLob().equalsIgnoreCase("home"), "first opp should be home");
		assertEquals("2017-05-18", result.getOriginalValue());
		assertEquals("2017-05-29", result.getFieldUpdatedValue());
		result = results.get(1);
		assertTrue(result.getLob().equalsIgnoreCase("home"), "second opp should be home");
		assertEquals("2017-09-18", result.getOriginalValue(), "original effective date incorrectly updated");
		assertEquals("2017-05-29", result.getFieldUpdatedValue(), "effective date should update");

		result = results.get(2);
		assertTrue(result.getLob().equalsIgnoreCase("auto"), "second opp should be auto");
		assertEquals("2017-05-29", result.getOriginalValue(), "original effective date incorrectly updated");
		assertNull(result.getFieldUpdatedValue(), "effective date should not update");
	}

	@Test
	void testPartialMatch() {
		ArrayList<String> agencyIds = new ArrayList<>();
		agencyIds.add("123456");
		Opportunity opp = new Opportunity();
		opp.setAgencyId("12345678");
		assertThat(updateEffectiveDateHelper.getPartialMatches(agencyIds, opp), contains("123456"));
		opp.setAgencyId("09876543");
		assertEquals(0, updateEffectiveDateHelper.getPartialMatches(agencyIds, opp).size());
	}

	@Test
	void testPartialMatchWithJrInName() {
		ArrayList<String> agencyIds = new ArrayList<>();
		agencyIds.add("FREEMJOS01");
		Opportunity opp = new Opportunity();
		opp.setAgencyId("FREEMJJROS01");
		assertThat(updateEffectiveDateHelper.getPartialMatches(agencyIds, opp), contains("FREEMJOS01"));
	}

	@Test
	void testIfStartsTheSame() {
		ArrayList<String> agencyIds = new ArrayList<>();
		agencyIds.add("FREEMJJROS01");
		Opportunity opp = new Opportunity();
		opp.setAgencyId("FREEMJOS01");
		assertThat(updateEffectiveDateHelper.getPartialMatches(agencyIds, opp), contains("FREEMJJROS01"));
	}

	@Test
	void testPartialMatchIfAgencyIdIsLongerThanOppId() {
		ArrayList<String> agencyIds = new ArrayList<>();
		agencyIds.add("FREEMJOS01");
		Opportunity opp = new Opportunity();
		opp.setAgencyId("FREEMJJROS01");
		assertThat(updateEffectiveDateHelper.getPartialMatches(agencyIds, opp), contains("FREEMJOS01"));
	}

	@Test
	void testSettingNulls() {
		ArrayList<String> agencyIds = new ArrayList<>();
		agencyIds.add("FREEMJOS01");
		agencyIds.add(null);

		Opportunity opportunity = new Opportunity();
		opportunity.setAgencyId(null);
		assertEquals(0, updateEffectiveDateHelper.getPartialMatches(agencyIds, opportunity).size());
	}

	/**
	 * Test all of the code from getting partial matches to generating. This is more of an intergation testing
	 *
	 * <p>Given I have a 4 customers 2 that match the first customer and one that matches none of them When I generate
	 * partial matches Then I will generate a spread sheet that has the required fields on it for the matching opps.
	 */
	@Test
	void testPartialMatchCSVGeneration() throws BookTransferException {
		List<Opportunity> opps = new ArrayList<>();
		Opportunity opp = new Opportunity();
		opp.setAgencyId("FREEMJOS01");
		opp.setBookTransferID(1);
		opp.setCustomerName("Darrel Jenkins");
		opp.setEffectiveDate("10/04/2019");
		opp.setBusinessType("AUTOP");
		opps.add(opp);

		Opportunity oppThatMatchesOpp = new Opportunity();
		oppThatMatchesOpp.setAgencyId("FREEMJJROS01");
		oppThatMatchesOpp.setBookTransferID(1);
		opps.add(oppThatMatchesOpp);

		Opportunity oppWithDifferentBook = new Opportunity();
		oppWithDifferentBook.setAgencyId("FREEMJJROS02");
		oppWithDifferentBook.setBookTransferID(2);
		opps.add(oppWithDifferentBook);

		Opportunity oppWithNoMatch = new Opportunity();
		oppWithNoMatch.setAgencyId("NEWID");
		oppWithNoMatch.setBookTransferID(1);
		opps.add(oppWithNoMatch);

		RestTemplate rt = mock(RestTemplate.class);
		BookTransferUrlProvider urlProv = new BookTransferUrlProvider("https://booktransfer.com");

		BookTransferService bts = new BookTransferService(rt, urlProv) {
			@Override
			public BookTransferDTO findByBookTransferId(int bookTransferId) {
				BookTransferDTO bt = new BookTransferDTO();
				bt.setSubCode("1234567" + bookTransferId);
				bt.setSalesforceCode("1234" + bookTransferId);
				bt.setBookTransferID(bookTransferId);
				return bt;
			}
		};

		UpdateMatchingCustomerEffectiveDateHelper updateEffectiveDateHelper =
				new UpdateHomeLeadEffectiveDateHelper(null, bts, null, null);

		String csv = updateEffectiveDateHelper.generatePartialMatchOfAgencyIdsCSV(opps);

		assertEquals(
				"OppID,AgencyID,CustomerName,EffectiveDt,BusinessType,Subcode,SalesforceID,PossibleMatches\r\n"
				+ "0,FREEMJOS01,Darrel Jenkins,10/04/2019,AUTOP,12345671,12341,\"FREEMJJROS01,FREEMJJROS02\",\r\n"
				+ "0,FREEMJJROS01,No Value to extract,No Value to extract,No Value to extract,12345671,12341,\"FREEMJOS01,FREEMJJROS02\",\r\n"
				+ "0,FREEMJJROS02,No Value to extract,No Value to extract,No Value to extract,12345672,12342,\"FREEMJOS01,FREEMJJROS01\",\r\n",
				csv);
	}

	private List<OppChangeFieldResult>
	executeUpdateEffectiveDate(List<Opportunity> ops, String startDate, String leadType) {
		List<OppChangeFieldResult> results = new ArrayList<>();
		for (Opportunity opp : ops) {
			results.add(new OppChangeFieldResult(opp, "", opp.getEffectiveDate()));
		}
		UpdateEffectiveDateHelper updateEffectiveDateHelper;
		switch (leadType) {
			case "truelead":
				updateEffectiveDateHelper = new UpdateLeadEffectiveDateHelper(null, null, null, null);
				break;
			case "home":
				updateEffectiveDateHelper = new UpdateHomeLeadEffectiveDateHelper(null, null, null, null);
				break;
			case "next term":
				updateEffectiveDateHelper = new UpdateNextTermDateHelper(null, null, null);
				break;
			case "set date":
				updateEffectiveDateHelper = new UpdateSetEffectiveDateHelper(null, null, null);
				break;
			default:
				throw new RuntimeException("Does not support any other lead type");
		}
		return updateEffectiveDateHelper.updateEffectiveDatesByPackage(results,
				LocalDate.parse(startDate, DateTimeFormatter.ofPattern("MM/dd/yyyy")));
	}

	/**
	 * Given that i have 2 opportunities with the effectivedate before the start date When i try to change the effective
	 * date with next term Then the effectivedate is updated with expiration date and expiration date is updated with
	 * expiration date plus duration
	 */
	@Test
	void testNextTerm() throws Exception {

		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(autoOppEffDate20170510);

		String startDate = "06/20/2019";
		String leadType = "next term";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);

		assertEquals(2, results.size());

		OppChangeFieldResult result = results.get(0);

		assertEquals("home", result.getLob().toLowerCase());
		assertEquals("2017-05-18", result.getOriginalValue());
		assertEquals("2020-05-18", result.getFieldUpdatedValue());

		Document doc = XmlHelper.getDocument(result.getOpportunity().getData());

		assertDatesInOppXml(doc, "2020-05-18", "2021-05-18");

		result = results.get(1);

		assertEquals("auto", result.getLob().toLowerCase());
		assertEquals("2017-05-10", result.getOriginalValue());
		assertEquals("2020-05-10", result.getFieldUpdatedValue());

		doc = XmlHelper.getDocument(result.getOpportunity().getData());

		assertDatesInOppXml(doc, "2020-05-10", "2021-05-10");
	}

	/**
	 * Given that i have 2 opportunities with the effectivedate on or after the start date When i try to change the
	 * effective date with next term Then the effectivedate and expiration date shouldn't be updated
	 */
	@Test
	void testNextTerm2() throws Exception {
		List<Opportunity> ops = new ArrayList<>();
		ops.add(homeOppEffDate20170518);
		ops.add(autoOppEffDate20170529);

		String startDate = "04/20/2017";
		String leadType = "next term";

		List<OppChangeFieldResult> results = executeUpdateEffectiveDate(ops, startDate, leadType);

		assertEquals(2, results.size());

		OppChangeFieldResult result = results.get(0);

		assertEquals("home", result.getLob().toLowerCase());
		assertEquals("2017-05-18", result.getOriginalValue());
		assertNull(result.getFieldUpdatedValue());

		Document doc = XmlHelper.getDocument(result.getOpportunity().getData());

		assertDatesInOppXml(doc, "2017-05-18", "2018-05-18");

		result = results.get(1);

		assertEquals("auto", result.getLob().toLowerCase());
		assertEquals("2017-05-29", result.getOriginalValue());
		assertNull(result.getFieldUpdatedValue());

		doc = XmlHelper.getDocument(result.getOpportunity().getData());

		assertDatesInOppXml(doc, "2017-05-29", "2018-05-29");
	}

	/**
	 * Given that i have finished the process of Changing the efffective date And I have a results spread sheet When I
	 * ask to send out the spread sheet Then I will send out that results spreadsheet But do not send out a partial
	 * match.
	 */
	@Test
	void testSetTermReturnsOnlyChangeCSV()
			throws JsonProcessingException, BookTransferException, EmailException {
		ExportHelper exportHelper = Mockito.mock(ExportHelper.class);
		UpdateEffectiveDateHelper updateEffectiveDateHelper = new UpdateSetEffectiveDateHelper(null, exportHelper, null);
		ChangeEffectiveDateResponse res =
				updateEffectiveDateHelper.sendResultCSVs(null, "Hello world", "<EMAIL>");

		assertNull(res.getCustomersPartialMatchCSV());
		assertEquals("Hello world", res.getResultsCSV());
		then(exportHelper).should().sendUpdateEffectiveDateCSVs("Hello world", null, "<EMAIL>");

	}

	/**
	 * Given that i have two opportunities that match And I have a start date of 10/24/2025 And they have a policy term
	 * of 1 year When I update the effective dates Then they will be updated to 10/24/2025 And an end date of 10/24/2026
	 */
	@Test
	void testUpdatingDatesCorrectly()
			throws IOException, SAXException, ParserConfigurationException, XPathExpressionException {
		List<OppChangeFieldResult> pacakagedOpps = new ArrayList<>();
		OppChangeFieldResult oppChangeFieldResult =
				new OppChangeFieldResult(autoOppEffDate20170510, new OppChangeField(), null);
		pacakagedOpps.add(oppChangeFieldResult);
		oppChangeFieldResult = new OppChangeFieldResult(homeOppEffDate20170518, new OppChangeField(), null);
		pacakagedOpps.add(oppChangeFieldResult);

		UpdateEffectiveDateHelper updateEffectiveDateHelper = new UpdateSetEffectiveDateHelper(null, null, null);
		List<OppChangeFieldResult> results = updateEffectiveDateHelper.updateEffectiveDatesByPackage(pacakagedOpps,
				LocalDate.parse("10/24/2025", DateTimeFormatter.ofPattern("MM/dd/yyyy")));

		for (OppChangeFieldResult result : results) {
			assertEquals("2025-10-24", result.getFieldUpdatedValue());
			assertFalse(result.didFail());

			assertEquals("2025-10-24", result.getOpportunity().getEffectiveDate());
			Document doc = XmlHelper.getDocument(result.getOpportunity().getData());

			assertDatesInOppXml(doc, "2025-10-24", "2026-10-24");
		}

	}

	/**
	 * UBT-3778 Next Term does not care about AgencyID missing, but all other Effective date changes do as it groups
	 * opportunities by LOBs and modifies the date. This verifies that we do not check for a AgencyID at all and
	 * subsequently set it to error.
	 */
	@Test
	void testNextTermAgencyIdDefect() throws Exception {
		UpdateEffectiveDateHelper updateEffectiveDateHelper = new UpdateNextTermDateHelper(null, null, null);

		List<Opportunity> opportunityList = new ArrayList<>();

		Opportunity opportunityWithNoAgencyID = new Opportunity(1, 5);
		opportunityWithNoAgencyID.setBookTransferID(1);
		opportunityWithNoAgencyID.setAgencyId(null);

		opportunityList.add(opportunityWithNoAgencyID);

		OppChangeFieldResult opportunityResult = updateEffectiveDateHelper
				.buildInitialOpporytunityResults(opportunityList).getAllOpportunityResults().get(0);

		assertFalse(opportunityResult.didFail());

	}

	/**
	 * UBT-3778 Next Term does not care about AgencyID missing, but all other Effective date changes do as it groups
	 * opportunities by LOBs and modifies the date. This verifies that we do not check for a AgencyID at all and
	 * subsequently set it to error, but if there is no SubCode present we still set this as a failure.
	 */
	@Test
	void testNextTermAgencyIdDefectNoBookTransferId() throws Exception {
		UpdateEffectiveDateHelper updateEffectiveDateHelper = new UpdateNextTermDateHelper(null, null, null);

		List<Opportunity> opportunityList = new ArrayList<>();

		Opportunity opportunityWithNoAgencyID = new Opportunity(1, 5);
		opportunityWithNoAgencyID.setBookTransferID(0);
		opportunityWithNoAgencyID.setAgencyId(null);

		opportunityList.add(opportunityWithNoAgencyID);

		OppChangeFieldResult opportunityResult = updateEffectiveDateHelper
				.buildInitialOpporytunityResults(opportunityList).getAllOpportunityResults().get(0);

		assertTrue(opportunityResult.didFail());
		assertEquals("SubCode missing", opportunityResult.getStatusMessage());

	}

	void assertDatesInOppXml(Document doc, String expectedEffectiveDate, String expectedExpirationDate)
			throws XPathExpressionException {
		XPath xFactory = XPathFactory.newInstance().newXPath();

		Element actualEffectiveDate = (Element) xFactory.evaluate(EFFECTIVE_DATE, doc, XPathConstants.NODE);
		assertEquals(expectedEffectiveDate, actualEffectiveDate.getTextContent());

		Element actualExpirationDate = (Element) xFactory.evaluate(EXPIRATION_DATE, doc, XPathConstants.NODE);
		assertEquals(expectedExpirationDate, actualExpirationDate.getTextContent());
	}

}

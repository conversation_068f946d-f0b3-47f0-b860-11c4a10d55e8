package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class DownloadZipFileHelperTest {

	@Mock
	BookTransferService bookTransferHelper;

	@InjectMocks
	DownloadZipFileHelper downloadZipFileHelper;

	@Captor
	ArgumentCaptor<Set<Integer>> getSfdcidsCallArgCaptor;

	/**
	 * Given I pass 2 opportunities to getBookSfdcidsForOppList When I call
	 * bookTransferService.getSfdcidsForBookTransferIds Then both bookIds from the
	 * opps should be passed as parameters
	 */
	@Test
	void testGetBookSfdcidsForOppList() throws BookTransferException {

		Opportunity opp = new Opportunity();
		opp.setBookTransferID(123);

		Opportunity opp2 = new Opportunity();
		opp2.setBookTransferID(456);

		List<Opportunity> oppList = new ArrayList<>();
		oppList.add(opp);
		oppList.add(opp2);

		downloadZipFileHelper.getBookSfdcidsForOppList(oppList);

		verify(bookTransferHelper).getSfdcidsForBookTransferIds(getSfdcidsCallArgCaptor.capture());

		Set<Integer> vals = getSfdcidsCallArgCaptor.getValue();

		assertTrue(vals.contains(opp.getBookTransferID()));
		assertTrue(vals.contains(opp2.getBookTransferID()));
		assertEquals(2, vals.size());
	}

	/**
	 * Given I pass 2 opportunities to getBookSfdcidsForOppList And both opps have
	 * the same BookId When I call bookTransferService.getSfdcidsForBookTransferIds
	 * Then no duplicate bookIds should be passed as parameters
	 */
	@Test
	void testGetBookSfdcidsForOppListNoDuplicates() throws BookTransferException {
		Opportunity opp = new Opportunity();
		opp.setBookTransferID(123);

		Opportunity opp2 = new Opportunity();
		opp2.setBookTransferID(123);

		List<Opportunity> oppList = new ArrayList<>();
		oppList.add(opp);
		oppList.add(opp2);

		downloadZipFileHelper.getBookSfdcidsForOppList(oppList);

		verify(bookTransferHelper).getSfdcidsForBookTransferIds(getSfdcidsCallArgCaptor.capture());

		Set<Integer> vals = getSfdcidsCallArgCaptor.getValue();

		assertTrue(vals.contains(opp.getBookTransferID()));
		assertEquals(1, vals.size());
	}
}

package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.booktransfer.transformationservice.client.domain.TransformationResult;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;
import reactor.core.publisher.Mono;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TransformationServiceTest {

	@Mock
	WebClient transformationServiceWebClient;
	@Mock
	private WebClient.RequestBodyUriSpec requestBodyUriSpecMock;

	@Mock
	private WebClient.RequestBodySpec requestBodySpecMock;

	@SuppressWarnings("rawtypes")
	@Mock
	private WebClient.RequestHeadersSpec requestHeadersSpecMock;

	@Mock
	private WebClient.ResponseSpec responseSpecMock;

	@Test
	void testExecutePackage_whenValidXmlDoc_shouldReturnTransformationResultWithChangedXml()
			throws ParserConfigurationException, IOException, SAXException, TransformerException {
		Document inputXmlDoc = XmlHelper.getDocument("<ACORD></ACORD>");
		Document changedXmlDoc = XmlHelper.getDocument("<TransformedXml></TransformedXml>");
		TransformationResult transformationResult = new TransformationResult();
		transformationResult.setResultDocument(changedXmlDoc);
		when(transformationServiceWebClient.post()).thenReturn(requestBodyUriSpecMock);
		when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
		when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
		when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
		when(responseSpecMock.onStatus(any(), any())).thenReturn(responseSpecMock);
		when(responseSpecMock.bodyToMono(
				ArgumentMatchers.<Class<TransformationResult>>notNull())).thenReturn(Mono.just(transformationResult));
		TransformationService transformationService = new TransformationService(transformationServiceWebClient, "");

		TransformationResult actualTransformationResult = transformationService.executePackage(inputXmlDoc, "5", null);
		Document actualXmlDoc = actualTransformationResult.getResultDocument();

		assertEquals(XmlHelper.getDocumentString(changedXmlDoc), XmlHelper.getDocumentString(actualXmlDoc));
		verify(transformationServiceWebClient, times(1)).post();
	}

	@Test
	void testExecutePackage_whenHttp500StatusCode_shouldReturnTransformationResultWithInputXml() throws ParserConfigurationException, IOException, SAXException, TransformerException {
		Document inputXmlDoc = XmlHelper.getDocument("<ACORD></ACORD>");
		TransformationResult transformationResult = new TransformationResult();
		transformationResult.setResultDocument(inputXmlDoc);
		when(transformationServiceWebClient.post()).thenReturn(requestBodyUriSpecMock);
		when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
		when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
		when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
		when(responseSpecMock.onStatus(any(), any())).thenReturn(responseSpecMock);
		when(responseSpecMock.bodyToMono(
				ArgumentMatchers.<Class<TransformationResult>>notNull())).thenReturn(Mono.just(transformationResult));
		TransformationService transformationService = new TransformationService(transformationServiceWebClient, "");

		TransformationResult actualTransformationResult = transformationService.executePackage(inputXmlDoc, "5", null);
		Document actualXmlDoc = actualTransformationResult.getResultDocument();

		assertEquals(XmlHelper.getDocumentString(inputXmlDoc), XmlHelper.getDocumentString(actualXmlDoc));
		verify(transformationServiceWebClient, times(1)).post();
	}

	@Test
	void testExecutePackage_whenRestClientExceptionThrown_shouldReturnTransformationResultWithInputXml() throws ParserConfigurationException, IOException, SAXException, TransformerException {
		Document inputXmlDoc = XmlHelper.getDocument("<ACORD></ACORD>");
		TransformationResult transformationResult = new TransformationResult();
		transformationResult.setResultDocument(inputXmlDoc);
		when(transformationServiceWebClient.post()).thenReturn(requestBodyUriSpecMock);
		when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
		when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
		when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
		when(responseSpecMock.onStatus(any(), any())).thenReturn(responseSpecMock);
		when(responseSpecMock.bodyToMono(
				ArgumentMatchers.<Class<TransformationResult>>notNull())).thenReturn(Mono.just(transformationResult));
		TransformationService transformationService = new TransformationService(transformationServiceWebClient, "");

		TransformationResult actualTransformationResult = transformationService.executePackage(inputXmlDoc, "5", null);
		Document actualXmlDoc = actualTransformationResult.getResultDocument();

		assertEquals(XmlHelper.getDocumentString(inputXmlDoc), XmlHelper.getDocumentString(actualXmlDoc));
		verify(transformationServiceWebClient, times(1)).post();
	}
}

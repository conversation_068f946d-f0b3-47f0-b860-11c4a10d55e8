package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.config.OpportunityUrlProvider;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.OpportunityEvent;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityEventRepo;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.domain.TransactionStatus;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.EndTransactionRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.TransactionEventRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.response.TransactionResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessResourceFailureException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OpportunityEventHelperTest {
	@Mock
	private OpportunityUrlProvider opportunityUrlProvider;
	@Mock
	private AuditLogHelper auditLogHelper;
	@Mock
	private OpportunityEventRepo opportunityEventRepo;

	// TEST SUBJECT
	@InjectMocks
	private OpportunityEventHelper opportunityEventHelper;

	@BeforeEach
	public void setup() {
		opportunityUrlProvider = mock(OpportunityUrlProvider.class);
		auditLogHelper = mock(AuditLogHelper.class);
		opportunityEventRepo = mock(OpportunityEventRepo.class);

		opportunityEventHelper = new OpportunityEventHelper(
				opportunityEventRepo,
				opportunityUrlProvider,
				auditLogHelper
		);
	}

	@Test
	void givenSaveOppEventSuccess_andAuditLogSuccess_thenAddTransactionEvent() {
		// GIVEN
		OpportunityEvent stubbedEvent = new OpportunityEvent();
		stubbedEvent.setId("stubbedEventId");

		TransactionResponse stubbedTrans = new TransactionResponse();
		stubbedTrans.setId("stubbedTrans");

		when(opportunityEventRepo.save(any())).thenReturn(stubbedEvent);
		when(auditLogHelper.startTransaction(any())).thenReturn(stubbedTrans);
		when(auditLogHelper.addTransactionEvent(any(), any())).thenReturn(stubbedTrans);

		//WHEN
		opportunityEventHelper.processOpportunityEvent(new Opportunity(), "CREATED");

		// THEN
		ArgumentCaptor<TransactionEventRequest> argumentCaptor = ArgumentCaptor.forClass(TransactionEventRequest.class);
		verify(auditLogHelper, times(1)).addTransactionEvent(any(), argumentCaptor.capture());
		TransactionEventRequest capturedArgument = argumentCaptor.getValue();
		// Start event and save event
		assertEquals("OPP_EVENT_SAVED", capturedArgument.getName());
	}

	@Test
	void givenSaveOppEventSuccess_andAuditLogFailure_thenNotifyTeam() {
		OpportunityEvent stubbedEvent = new OpportunityEvent();
		stubbedEvent.setId("stubbedEventId");

		TransactionResponse stubbedTrans = new TransactionResponse();
		stubbedTrans.setId("stubbedTrans");

		when(opportunityEventRepo.save(any())).thenReturn(stubbedEvent);
		when(auditLogHelper.startTransaction(any())).thenReturn(null);

		//WHEN
		OpportunityEvent processedEvent = opportunityEventHelper.processOpportunityEvent(new Opportunity(), "CREATED");

		// Start event and save event
		assertNotNull(processedEvent);
	}

	@Test
	void givenSaveOppEventFailure_thenEndTransaction() {
		// GIVEN
		OpportunityEvent stubbedEvent = new OpportunityEvent();
		stubbedEvent.setId("stubbedEventId");

		TransactionResponse stubbedTrans = new TransactionResponse();
		stubbedTrans.setId("stubbedTrans");

		when(opportunityEventRepo.save(any())).thenThrow(new DataAccessResourceFailureException("whoops", null));
		when(auditLogHelper.startTransaction(any())).thenReturn(stubbedTrans);
		when(auditLogHelper.endTransactionEvent(any(), any())).thenReturn(stubbedTrans);

		//WHEN
		DataAccessResourceFailureException thrown = Assertions.assertThrows(DataAccessResourceFailureException.class, () -> {
			opportunityEventHelper.processOpportunityEvent(new Opportunity(), "CREATED");
		});

		Assertions.assertEquals("whoops", thrown.getMessage());

		// THEN
		ArgumentCaptor<EndTransactionRequest> argumentCaptor = ArgumentCaptor.forClass(EndTransactionRequest.class);
		verify(auditLogHelper, times(0)).addTransactionEvent(any(), any());
		verify(auditLogHelper, times(1)).endTransactionEvent(any(), argumentCaptor.capture());
		EndTransactionRequest capturedArgument = argumentCaptor.getValue();
		// Start event and save event
		assertEquals("OPP_EVENT_SAVE_FAILED", capturedArgument.getEvents().get(0).getName());
		assertEquals(TransactionStatus.FAILED, capturedArgument.getStatus());
	}

	@Test
	void givenAuditLogFails_thenRetry() {
		// GIVEN
		OpportunityEvent stubbedEvent = new OpportunityEvent();
		stubbedEvent.setId("stubbedEventId");

		TransactionResponse stubbedTrans = new TransactionResponse();
		stubbedTrans.setId("stubbedTrans");

		when(opportunityEventRepo.save(any())).thenReturn(stubbedEvent);
		when(auditLogHelper.startTransaction(any()))
				.thenThrow(new RuntimeException())
				.thenThrow(new RuntimeException())
				.thenReturn(stubbedTrans);
		when(auditLogHelper.addTransactionEvent(any(), any())).thenReturn(stubbedTrans);

		//WHEN
		opportunityEventHelper.processOpportunityEvent(new Opportunity(), "CREATED");

		// THEN
		verify(auditLogHelper, times(3)).startTransaction(any());
	}

	@Test
	void givenAuditLogFails_andRetriesFail_thenDontThrowException() {
		// GIVEN
		OpportunityEvent stubbedEvent = new OpportunityEvent();
		stubbedEvent.setId("stubbedEventId");

		TransactionResponse stubbedTrans = new TransactionResponse();
		stubbedTrans.setId("stubbedTrans");

		when(opportunityEventRepo.save(any())).thenReturn(stubbedEvent);
		when(auditLogHelper.startTransaction(any())).thenReturn(stubbedTrans);

		when(auditLogHelper.addTransactionEvent(any(), any()))
				.thenThrow(new RuntimeException())
				.thenThrow(new RuntimeException())
				.thenThrow(new RuntimeException());

		//WHEN
		opportunityEventHelper.processOpportunityEvent(new Opportunity(), "CREATED");

		// THEN
		// no Exception
	}

}

package com.lmig.uscm.booktransfer.opportunity.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.booktransfer.btsensitivedataservice.client.SensitiveDataServiceWebClient;
import com.lmig.booktransfer.btsensitivedataservice.client.config.SensitiveDataUrlProvider;
import com.lmig.booktransfer.transformationservice.client.config.TransformationUrlProvider;
import com.lmig.booktransfer.transformationservice.client.domain.TransformationResult;
import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.config.BookTransferUrlProvider;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.email.client.config.EmailUrlProvider;
import com.lmig.uscm.booktransfer.opportunity.client.config.OpportunityUrlProvider;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityEventRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJpaRepository;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.AddressCleanseHelper;
import com.lmig.uscm.booktransfer.opportunity.services.BTPaymentServiceHelper;
import com.lmig.uscm.booktransfer.opportunity.services.CustomerAccountHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityCreationHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.PropertyInfoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuotingAdapterService;
import com.lmig.uscm.booktransfer.opportunity.services.QuotingGuidelineHelper;
import com.lmig.uscm.booktransfer.opportunity.services.ReportGenerator;
import com.lmig.uscm.booktransfer.opportunity.services.SensitiveDataHelper;
import com.lmig.uscm.booktransfer.opportunity.services.TransformationService;
import com.lmig.uscm.booktransfer.opportunity.services.UploadEventService;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportService;
import com.lmig.uscm.booktransfer.opportunity.services.AuditLogHelper;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.CreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.CreationStrategyFactory;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.FifthVehicleCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.MotorcycleCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.SPQEPreviousOppsCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.SplitBulkCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.BoatCreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.FieldChangeHelper;
import com.lmig.uscm.booktransfer.quotereport.client.config.QuoteReportUrlProvider;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import com.lmig.uscm.booktransfer.uploadmanager.client.domain.UploadEvent;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.config.AuditLogUrlProvider;
import com.lmig.usconsumermarkets.booktransfer.btpaymentservice.client.domain.BillingDetails;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.config.QuotingGuidelineUrlProvider;
import com.lmig.usconsumermarkets.booktransfer.btuploadpreprocessor.client.UploadPreprocessorWebClient;
import com.lmig.usconsumermarkets.booktransfer.btuploadpreprocessor.client.config.UploadPreprocessorUrlProvider;
import com.uscm.lmig.booktransfer.processresult.client.ProcessResultService;
import com.uscm.lmig.booktransfer.processresult.client.config.ProcessResultUrlProvider;
import com.uscm.lmig.booktransfer.processresult.client.domain.LegacyQuoteDataResponse;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.lang.Nullable;
import org.springframework.web.reactive.function.client.WebClient;
import org.w3c.dom.Document;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.Mockito.mock;

@Configuration
@Profile("unit")
@Import(JacksonConfig.class)
public class UnitTestConfig {

	@Bean
	public QuotingGuidelineUrlProvider quotingGuidelineUrlProvider() {
		return new QuotingGuidelineUrlProvider(null);
	}

	@Bean
	public BTPaymentServiceHelper bTPaymentServiceHelper() {
		return new BTPaymentServiceHelper(null, null) {
			@Override
			public BillingDetails getPaymentInfoUsingBillingAccountNumber(String billingAccountNumber) {
				return BillingDetails.builder().build();
			}
		};
	}

	@Bean
	public AddressCleanseHelper addressCleanseHelper() {
		return new AddressCleanseHelper(null, null) {

		};
	}

	@Bean
	public PropertyInfoHelper propertyInfoHelper() {
		return new PropertyInfoHelper(null, null) {

		};
	}

	@Bean
	public CustomerAccountHelper customerAccountHelper() {
		return new CustomerAccountHelper(null, null, null) {
		};
	}

	@Bean
	public AuditLogHelper auditLogHelper() {
		return new AuditLogHelper(null) {
		};
	}

	@Bean
	public BookTransferService bookTransferService() {
		return new BookTransferService(null, new BookTransferUrlProvider("https://booktransfer.com")) {

			@Override
			public Map<Integer, Integer> getSfdcidsForBookTransferIds(final Set<Integer> bookTransferIds) {
				Map<Integer, Integer> resp = new HashMap<>();
				resp.put(2, 5);
				resp.put(1, 9090);
				return resp;
			}

			@Override
			public BookTransferDTO findByBookTransferId(final int bookTransferId) {
				BookTransferDTO bookTransfer = new BookTransferDTO();
				bookTransfer.setBookTransferID(bookTransferId);
				bookTransfer.setSalesforceCode("BT-12345");
				return bookTransfer;
			}
		};
	}

	@Bean
	QuotingGuidelineHelper quotingGuidelineHelper() {
		return new QuotingGuidelineHelper(null);
	}

	@Bean
	OpportunityUrlProvider opportunityUrlProvider() {
		return new OpportunityUrlProvider(null);
	}

	@Bean
	public AuditLogUrlProvider auditLogUrlProvider() {
		return new AuditLogUrlProvider(null);
	}

	@Bean
	public UploadPreprocessorUrlProvider uploadPreprocessorUrlProvider() {
		return new UploadPreprocessorUrlProvider(null);
	}

	@Bean
	public OpportunityEventRepo opportunityEventRepo() {
		return Mockito.mock(OpportunityEventRepo.class);
	}

	@Bean
	public OpportunityRepoHelper opportunityRepoHelper(final OpportunityJpaRepository opportunityRepository,
													   final OpportunityJDBCRepo opportunityJDBCRepo) {
		return new OpportunityRepoHelper(opportunityRepository, opportunityJDBCRepo);
	}

	@Bean
	public OpportunityCreationHelper opportunityCreationHelper() {
		SensitiveDataHelper sensitiveDataHelper = mock(SensitiveDataHelper.class);
		return new OpportunityCreationHelper(
				null, null, null,
				quoteReportItemHelper(), null, null,
				null, creationStrategyFactory(sensitiveDataHelper), null,
				null, null, null, null, null) {

			@Override
			public OpportunityCreationResponse createOpportunityFromOpportunityRequest(boolean isForSPQE,
																					   OpportunityCreationRequest opportunityCreationRequest) {

				return new OpportunityCreationResponse();
			}

			@Override
			public void reuploadOpportunities(List<Integer> oppIds) {

			}
		};
	}

	@Bean
	public ProcessResultService processResultService() {
		return new ProcessResultService(null, new ProcessResultUrlProvider("https://process-result-item.com")) {
			@Override
			public LegacyQuoteDataResponse getLatestQuoteResultForOpportunity(int oppId) {
				return new LegacyQuoteDataResponse();
			}
		};
	}

	@Bean
	public QuoteReportUrlProvider quoteReportUrlProvider() {
		return new QuoteReportUrlProvider("https://dummy-qrs.com");
	}

	@Bean
	public TransformationUrlProvider transformationUrlProvider() {
		return new TransformationUrlProvider("https://dummy-transformation-svc.com");
	}

	@Bean
	public SensitiveDataUrlProvider sensitiveDataUrlProvider() {
		return new SensitiveDataUrlProvider("https://dummy-sensitive-data-svc.com");
	}

	@Bean
	public QuoteReportItemHelper quoteReportItemHelper() {
		return new QuoteReportItemHelper(null) {
			@Override
			public List<QuoteReportItemLegacy> updateQuoteReportItem(Opportunity opp) {
				return new ArrayList<>();
			}

			@Override
			public List<QuoteReportItemLegacy> getQuoteReportItemsForStatusChange(List<Integer> oppIds) {
				List<QuoteReportItemLegacy> qriToReturn = new ArrayList<>();
				for (Integer id : oppIds) {
					QuoteReportItemLegacy qri = new QuoteReportItemLegacy();
					// See UpdateHelper
					qri.setQuoteSalesforceID(Integer.toString(id));
					qriToReturn.add(qri);
				}
				return qriToReturn;
			}
		};
	}

	@Bean
	public FieldChangeHelper fieldChangeHelper() {
		return new FieldChangeHelper(null, null, null, null);
	}

	@Bean
	public UploadEventService uploadEventService() {
		return new UploadEventService(null) {

			@Override
			public Integer createUploadEvent(UploadEvent uploadEvent) {
				return 1299;
			}
		};
	}

	@Bean
	public BookTransferUrlProvider bookTransferUrlProvider() {
		return new BookTransferUrlProvider("https://booktransfer.com");
	}

	@Bean
	public ProcessResultUrlProvider processResultItemUrlProvider() {
		return new ProcessResultUrlProvider("https://process-result-item.com");
	}

	@Bean
	public EmailUrlProvider emailUrlProvider() {
		return new EmailUrlProvider("https://emailservice.com");
	}

	@Bean
	public QuotingAdapterService quotingAdapterService() {
		return new QuotingAdapterService(null, null);
	}

	@Bean(name = {"transformationService", "transformationServiceClient"})
	public TransformationService transformationService() {
		return new TransformationService(null, null) {
			@Override
			public TransformationResult executePackage(final Document xml, final String packageId, @Nullable final Integer oppId) {
				return new TransformationResult();
			}
		};
	}

	@Bean(name = {"quoteReportService", "quoteReportWebClient"})
	public QuoteReportService quoteReportService() {
		return new QuoteReportService(null, null) ;
	}

	@Bean
	public CreationStrategyFactory creationStrategyFactory(final SensitiveDataHelper sensitiveDataHelper) {
		List<CreationStrategy> orderedStrategies = new ArrayList<>(Arrays.asList(
				// Split bulk XML by each LOB Rq tag. Must occur before all other strategies
				new SplitBulkCreationStrategy(sensitiveDataHelper),
				// Split motorcycles into separate Opportunities. Needs to happen before FifthVehicleCreationStrategy
				new MotorcycleCreationStrategy(),
				// Split into 4 vehicles per Opportunity
				new FifthVehicleCreationStrategy(),
				// Split boats from home policies
				new BoatCreationStrategy(),
				// Link SPQE Opportunities to previously created Opportunities. Needs to happen after all splitting strategies
				new SPQEPreviousOppsCreationStrategy()
		));
		return new CreationStrategyFactory(orderedStrategies);
	}

	@Bean
	public OpportunityHelper opportunityHelper(
		final QuoteReportItemHelper quoteReportItemHelper,
		final BookTransferService bookTransferService,
		final ReportGenerator reportGenerator,
		final ProcessResultService processResultService,
		final QuotingAdapterService quotingAdapterService,
		final TransformationService transformationService,
		final SensitiveDataHelper sensitiveDataHelper,
		final OpportunityRepoHelper opportunityRepoHelper,
		final QuotingGuidelineHelper quotingGuidelineHelper,
		final BTPaymentServiceHelper paymentServiceHelper,
		final CustomerAccountHelper accountHelper,
			final ObjectMapper objectMapper) {
		return new OpportunityHelper(
				quoteReportItemHelper,
				bookTransferService,
				reportGenerator,
				processResultService,
				quotingAdapterService,
				transformationService,
				sensitiveDataHelper,
				opportunityRepoHelper,
				quotingGuidelineHelper,
				paymentServiceHelper,
				accountHelper,
				objectMapper);
	}

	@Bean
	public ReportGenerator reportGenerator() {
		return new ReportGenerator();
	}

	@MockBean
	public SensitiveDataServiceWebClient sensitiveDataServiceClient;

	@MockBean
	public UploadPreprocessorWebClient uploadPreprocessorWebClient;

	@MockBean(name = "customerAccountWebClient")
	public WebClient webClient;

	@MockBean(name = "propertyInfoClient")
	public WebClient propertyInfoClient;

	@Bean
	public SensitiveDataHelper sensitiveDataHelper() {
		return new SensitiveDataHelper(sensitiveDataServiceClient, uploadPreprocessorWebClient, List.of("unit")){
			@Override
			public Document scrubXml(Document originalDoc) {
				return originalDoc;
			}
			@Override
			public void tokenizeXml(Document originalDoc) {
			}
		};
	}

}

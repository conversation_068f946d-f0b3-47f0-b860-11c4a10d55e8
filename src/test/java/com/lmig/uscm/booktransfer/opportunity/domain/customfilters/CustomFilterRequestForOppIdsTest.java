package com.lmig.uscm.booktransfer.opportunity.domain.customfilters;

import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.jupiter.api.Assertions.assertThrows;

class CustomFilterRequestForOppIdsTest {

	/**
	 * Given I have a status array as a String When I want to convert into array of
	 * ints Then it will do so
	 */
	@Test
	void testPassingStatusAsString() throws Exception {
		CustomFilterRequestForOppIds customFilterRequest = new CustomFilterRequestForOppIds();

		List<String> items = new ArrayList<>();
		items.add("Quote Clean Up");
		items.add("Issued");
		items.add("Missing Required Data");
		items.add("Quoted - Error");
		items.add("Quoted - Failed");
		items.add("Quoted - Imported");
		items.add("Quoted - Processing");
		items.add("Unquoted");
		items.add("Upload Failed");
		items.add("Withdrawn");
		items.add("Agent Updated");

		customFilterRequest.setStatuses(items);

		assertThat(customFilterRequest.getStatuses(),
				containsInAnyOrder(OpportunityStatus.OPPORTUNITY_STATUS_QUOTE_CLEAN_UP.getOppStatusCode(),
						OpportunityStatus.OPPORTUNITY_STATUS_ISSUED.getOppStatusCode(),
						OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode(),
						OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_ERROR.getOppStatusCode(),
						OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_FAILED.getOppStatusCode(),
						OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_IMPORTED.getOppStatusCode(),
						OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_PROCESSING.getOppStatusCode(),
						OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode(),
						OpportunityStatus.OPPORTUNITY_STATUS_UPLOAD_FAILED.getOppStatusCode(),
						OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode(),
						OpportunityStatus.OPPORTUNITY_STATUS_AGENT_UPDATED.getOppStatusCode()));

	}

	/**
	 * Given I have a status array as a String Then I want to convert into array of
	 * ints
	 */
	@Test
	void testPassingStatusAsStringWithInvalidString() {
		CustomFilterRequestForOppIds customFilterRequest = new CustomFilterRequestForOppIds();
		List<String> items = new ArrayList<>();
		items.add("bad status");

		assertThrows(Exception.class, () -> customFilterRequest.setStatuses(items));
	}
}

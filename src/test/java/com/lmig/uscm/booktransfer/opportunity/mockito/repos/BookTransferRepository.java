package com.lmig.uscm.booktransfer.opportunity.mockito.repos;

import com.lmig.uscm.booktransfer.opportunity.mockito.domain.BookTransfer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * This is used to to have the domain and repo located in one place
 * This will need to be removed in the future.
 */
@Repository
public interface BookTransferRepository extends JpaRepository<BookTransfer, Integer> {
	BookTransfer findByBookTransferID(int var1);

	ArrayList<BookTransfer> findBySubCode(String var1);

	ArrayList<BookTransfer> findBySalesforceCode(String var1);

	List<BookTransfer> findAll();

	BookTransfer findBybookTransferID(int var1);

	List<BookTransfer> findByNnumber(String var1);

	@Query("select distinct(bt.carrier) from BookTransfer bt  order by bt.carrier asc")
	List<String> findAllDistinctCarrier();

	@Query("Select new com.lmig.uscm.booktransfer.opportunity.mockito.domain.BookTransfer(bookT.sFDCID) FROM BookTransfer bookT WHERE bookT.bookTransferID= :bookTransferID")
	BookTransfer getSFCforDownloadZip(@Param("bookTransferID") int var1);

	@Query("select distinct(bt.salesforceCode) from BookTransfer bt")
	List<String> findAllSalesforceCodes();

	@Query("select distinct(bt.subCode) from BookTransfer bt")
	List<String> findAllSubCodes();

	@Query("select distinct(bt.agentNum) from BookTransfer bt")
	List<String> findAllAgentNums();

	@Query("select distinct(bt.nnumber) from BookTransfer bt")
	List<String> findAllNNumbers();

	@Query("select distinct(bt.nBDRelationship) from BookTransfer  bt")
	List<String> getAllNBDRelationships();

	List<BookTransfer> findBysFDCID(int var1);

	BookTransfer findTopBysFDCIDOrderByBookTransferID(int var1);

	List<BookTransfer> findBySalesforceCodeIn(List<String> var1);

	@Query("Select new com.lmig.uscm.booktransfer.opportunity.mockito.domain.BookTransfer(bookT.salesforceCode, bookT.bookTransferID)FROM BookTransfer bookT WHERE bookT.salesforceCode LIKE %:sfdcid% AND bookT.status = 'Active'")
	List<BookTransfer> findBookTransfersWithPartOfSfdcid(@Param("sfdcid") int var1);
}

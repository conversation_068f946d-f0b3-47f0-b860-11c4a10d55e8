package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.QuotingGuidelineWebClient;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.domain.QGFunctionalXmlRequest;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.domain.QuotingGuidelineException;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class QuotingGuidelineHelperTest {
    private QuotingGuidelineHelper objectUnderTest;
    private QuotingGuidelineWebClient quotingGuidelineWebClient;

    @BeforeEach
    public void setUp() {
        quotingGuidelineWebClient = mock(QuotingGuidelineWebClient.class);
        objectUnderTest = new QuotingGuidelineHelper(quotingGuidelineWebClient);
    }

    @Test
    void testGetQuotingGuidelineFunctionalXml() throws QuotingGuidelineException {
        QGFunctionalXmlRequest request = QGFunctionalXmlRequest.builder()
                .salesforceCode("BT-12345")
                .naicCd("98765")
                .lob("HOME").build();

        String quotingGuidelineXml = "<QuoteGuideline><Id>12345</Id></QuoteGuideline>";
        when(quotingGuidelineWebClient.getQuotingGuidelineFunctionalXml(request)).thenReturn(quotingGuidelineXml);

        String response = objectUnderTest.getQuotingGuidelineFunctionalXml(request);

        assertTrue(StringUtils.isNotBlank(response));
    }

    @Test
    void testGetQuotingGuidelineFunctionalXmlThrows() throws QuotingGuidelineException {
        QGFunctionalXmlRequest request = QGFunctionalXmlRequest.builder()
                .salesforceCode("BT-12345")
                .naicCd("98765")
                .lob("HOME").build();

        when(quotingGuidelineWebClient.getQuotingGuidelineFunctionalXml(request)).thenThrow(new QuotingGuidelineException());

        assertThrows(QuotingGuidelineException.class, () -> objectUnderTest.getQuotingGuidelineFunctionalXml(request));
    }
}

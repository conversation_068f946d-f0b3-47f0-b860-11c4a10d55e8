package com.lmig.uscm.booktransfer.opportunity.utility;

import com.lmig.uscm.booktransfer.Domain.utility.XmlHelper;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.junit.jupiter.api.Assertions.assertEquals;

class OpportunityUtilTest {
	@Test
	void testGettingHashMapFromListOfOpps() {
		List<Opportunity> opportunities = new ArrayList<>();
		opportunities.add(null);
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(1);
		opportunities.add(opportunity);

		Opportunity opportunity2 = new Opportunity();
		opportunity2.setOpportunityId(2);
		opportunities.add(opportunity2);

		Map<Integer, Opportunity> opportunityMap = OpportunityUtil.getOpportunityIdToOpportunityMap(opportunities);
		assertEquals(2, opportunityMap.size());
		assertThat(opportunityMap.keySet(), contains(opportunity.getOpportunityId(), opportunity2.getOpportunityId()));
		assertThat(opportunityMap.values(), contains(opportunity, opportunity2));
	}

	@Test
	void testGettingListOfQuoteSalesforceIDs() {
		List<Opportunity> opportunities = new ArrayList<>();
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(1);
		opportunity.setLineType(LineType.Personal);
		opportunities.add(opportunity);

		Opportunity opportunity2 = new Opportunity();
		opportunity2.setOpportunityId(2);
		opportunity2.setLineType(LineType.Personal);
		opportunities.add(opportunity2);

		List<String> quoteSalesForceIds = OpportunityUtil.getOpportunityIdsByLineType(opportunities, LineType.Personal);
		assertEquals(2, quoteSalesForceIds.size());
		assertThat(quoteSalesForceIds, contains(
				String.valueOf(opportunity.getOpportunityId()),
				String.valueOf(opportunity2.getOpportunityId())));
	}
	@ParameterizedTest
	@ValueSource(strings = {"Personal", "Business"})
	void testSetAgentNumber(LineType lineType) throws ParserConfigurationException, IOException, SAXException, XPathExpressionException {
		String oppXml = "<ACORD><node>test</node><Producer><ProducerInfo><ItemIdInfo><AgencyId>987654</AgencyId></ItemIdInfo>" +
			"<ContractNumber>123456</ContractNumber></ProducerInfo></Producer></ACORD>";
		Document oppXmlDoc = XmlHelper.getDocument(oppXml);

		// Before
		assertEquals("987654", AcordHelper.getProducerAgencyId(oppXmlDoc));
		assertEquals("123456", AcordHelper.getAgentNumber(oppXmlDoc));

		Document updatedXmlDoc = OpportunityUtil.setAgentNumber(oppXmlDoc, "567890", lineType);

		// After
		assertEquals("987654", AcordHelper.getProducerAgencyId(updatedXmlDoc));
		assertEquals("567890", AcordHelper.getAgentNumber(updatedXmlDoc));
	}
}

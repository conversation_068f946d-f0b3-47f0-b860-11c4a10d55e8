package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;

import static java.nio.file.Files.readString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class VehicleInfoTest {
	private final VehicleInfo standardInfo = new VehicleInfo();

	private final int formatInteger = 0;
	private final int formatCurrency = 1;
	private final int optionCode = 2;

	private Node buildCoverageNodeFormatInteger100_300()
			throws ParserConfigurationException, IOException, SAXException {
		String xml = "<Coverage><CoverageCd>BI</CoverageCd><CoverageDesc/><Limit>" + "<FormatInteger>100000</FormatInteger><LimitBasisCd/><LimitAppliesToCd>" +
				"PerPerson" + "</LimitAppliesToCd></Limit>" +
				"<Limit><FormatInteger>300000</FormatInteger><LimitBasisCd/><LimitAppliesToCd>" +
				"PerOcc" + "</LimitAppliesToCd></Limit>" +
				"<CurrentTermAmt><Amt>245.28</Amt></CurrentTermAmt><TerritoryCd/></Coverage>";
		return XmlHelper.createNode(xml);
	}

	private Node buildCoverageNodeFormatCurrency()
			throws ParserConfigurationException, IOException, SAXException {
		String xml = "<Coverage><CoverageCd>BI</CoverageCd><CoverageDesc/><Limit>" + "<FormatCurrencyAmt><Amt>500000</Amt></FormatCurrencyAmt><LimitBasisCd/><LimitAppliesToCd>" +
				"PerPerson" + "</LimitAppliesToCd></Limit>" +
				"<Limit><FormatCurrencyAmt><Amt>500000</Amt></FormatCurrencyAmt><LimitBasisCd/><LimitAppliesToCd>" +
				"PerOcc" + "</LimitAppliesToCd></Limit>" +
				"<CurrentTermAmt><Amt>245.28</Amt></CurrentTermAmt><TerritoryCd/></Coverage>";
		return XmlHelper.createNode(xml);
	}

	private Node buildCoverageNodeFormatCurrency100_300()
			throws ParserConfigurationException, IOException, SAXException {
		String xml = "<Coverage><CoverageCd>BI</CoverageCd><CoverageDesc/><Limit>" + "<FormatCurrencyAmt><Amt>100000</Amt></FormatCurrencyAmt><LimitBasisCd/><LimitAppliesToCd>" +
				"PerPerson" + "</LimitAppliesToCd></Limit>" +
				"<Limit><FormatCurrencyAmt><Amt>300000</Amt></FormatCurrencyAmt><LimitBasisCd/><LimitAppliesToCd>" +
				"PerOcc" + "</LimitAppliesToCd></Limit>" +
				"<CurrentTermAmt><Amt>245.28</Amt></CurrentTermAmt><TerritoryCd/></Coverage>";
		return XmlHelper.createNode(xml);
	}

	private Node buildRejectionNode(final int choiceSelection, final String rejectValue)
			throws ParserConfigurationException, IOException, SAXException {
		StringBuilder xml = new StringBuilder("<Coverage><CoverageCd>BI</CoverageCd><CoverageDesc/>");
		if (choiceSelection == formatInteger) {
			xml.append("<Limit><FormatInteger>").append(rejectValue).append("</FormatInteger></Limit>");
		} else if (choiceSelection == formatCurrency) {
			xml.append("<Limit><FormatCurrencyAmt><Amt>").append(rejectValue)
					.append("</Amt></FormatCurrencyAmt></Limit>");
		} else if (choiceSelection == optionCode) {
			xml.append("<Option><OptionCd>").append(rejectValue).append("</OptionCd></Option>");
		}
		xml.append("<CurrentTermAmt><Amt>245.28</Amt></CurrentTermAmt><TerritoryCd/></Coverage>");
		return XmlHelper.createNode(xml.toString());
	}

	@Test
	void getLimitAmount_whenLimitNodeIsNull() throws Exception {
		String limitAmount = standardInfo.getLimitAmount(null);
		assertEquals("", limitAmount);
	}


	@Test
	void getLimitAmount_whenFormatInteger() throws Exception {
		Node coverage = buildCoverageNodeFormatInteger100_300();
		Node limit = AcordHelper.getPerPersonOrBiEachPersLimit(coverage);
		String limitAmount = standardInfo.getLimitAmount(limit);
		assertEquals("100000", limitAmount);
	}

	@Test
	void getLimitAmount_whenFormatCurrencyAmt() throws Exception {
		Node coverage = buildCoverageNodeFormatCurrency100_300();
		Node limit = AcordHelper.getPerAccOrOtherOccLimit(coverage);
		String limitAmount = standardInfo.getLimitAmount(limit);
		assertEquals("300000", limitAmount);
	}
	/**
	 * Given a node without rejection of the coverage When a check to see if the
	 * node is rejected occurs Then false is returned
	 */
	@Test
	void isRejectedNoRejection() throws Exception {
		Node rejectionNode = buildCoverageNodeFormatCurrency();
		assertFalse(standardInfo.isRejected(rejectionNode));
	}

	/**
	 * Given a node with rejection coverage When a check to see if the node is
	 * rejected occurs Then true is returned
	 */
	@Test
	void isRejectedFormatIntegerREJ() throws Exception {
		Node rejectionNode = buildRejectionNode(formatInteger, "REJ");
		assertTrue(standardInfo.isRejected(rejectionNode));
	}

	/**
	 * Given a node with rejection coverage When a check to see if the node is
	 * rejected occurs Then true is returned
	 */
	@Test
	void isRejectedFormatIntegerPascalRej() throws Exception {
		Node rejectionNode = buildRejectionNode(formatInteger, "Rej");
		assertTrue(standardInfo.isRejected(rejectionNode));
	}

	/**
	 * Given a node with rejection coverage When a check to see if the node is
	 * rejected occurs Then true is returned
	 */
	@Test
	void isRejectedFormatIntegerLowercaseRej() throws Exception {
		Node rejectionNode = buildRejectionNode(formatInteger, "rej");
		assertTrue(standardInfo.isRejected(rejectionNode));
	}

	/**
	 * Given a node with rejection coverage When a check to see if the node is
	 * rejected occurs Then true is returned
	 */
	@Test
	void isRejectedFormatCurrencyREJ() throws Exception {
		Node rejectionNode = buildRejectionNode(formatCurrency, "REJ");
		assertTrue(standardInfo.isRejected(rejectionNode));
	}

	/**
	 * Given a node with rejection coverage When a check to see if the node is
	 * rejected occurs Then true is returned
	 */
	@Test
	void isRejectedFormatCurrencyPascalRej() throws Exception {
		Node rejectionNode = buildRejectionNode(formatCurrency, "Rej");
		assertTrue(standardInfo.isRejected(rejectionNode));
	}

	/**
	 * Given a node with rejection coverage When a check to see if the node is
	 * rejected occurs Then true is returned
	 */
	@Test
	void isRejectedFormatCurrencyLowercaseRej() throws Exception {
		Node rejectionNode = buildRejectionNode(formatCurrency, "rej");
		assertTrue(standardInfo.isRejected(rejectionNode));
	}

	/**
	 * Given a node with rejection coverage When a check to see if the node is
	 * rejected occurs Then true is returned
	 */
	@Test
	void isRejectedOptionCdREJ() throws Exception {
		Node rejectionNode = buildRejectionNode(optionCode, "REJ");
		assertTrue(standardInfo.isRejected(rejectionNode));
	}

	/**
	 * Given a node with rejection coverage When a check to see if the node is
	 * rejected occurs Then true is returned
	 */
	@Test
	void isRejectedOptionCdPascalRej() throws Exception {
		Node rejectionNode = buildRejectionNode(optionCode, "Rej");
		assertTrue(standardInfo.isRejected(rejectionNode));
	}

	/**
	 * Given a node with rejection coverage When a check to see if the node is
	 * rejected occurs Then true is returned
	 */
	@Test
	void isRejectedOptionCdLowercaseRej() throws Exception {
		Node rejectionNode = buildRejectionNode(optionCode, "rej");
		assertTrue(standardInfo.isRejected(rejectionNode));
	}

	/**
	 * Given a null String to be formatted When the formatting occurs Then an empty
	 * String is returned
	 */
	@Test
	void formatLimitForSplitNullLimit() {
		assertEquals("", standardInfo.formatLimitForSplit(null));
	}

	/**
	 * Given an empty String to be formatted When the formatting occurs Then an
	 * empty String is returned
	 */
	@Test
	void formatLimitForSplitEmptyString() {
		assertEquals("", standardInfo.formatLimitForSplit(""));
	}

	/**
	 * Given a String with no "$" or "," to be formatted When the formatting occurs
	 * Then result is returned without "$" nor ","
	 */
	@Test
	void formatLimitForSplitNoSpecialCharacters() {
		assertEquals("1", standardInfo.formatLimitForSplit("1000"));
	}

	/**
	 * Given a String less than 1000 to be formatted When the formatting occurs Then
	 * result is returned as is
	 */
	@Test
	void formatLimitForSplitLessThan1000() {
		assertEquals("999", standardInfo.formatLimitForSplit("999"));
	}

	/**
	 * Given a String with special characters to be formatted When the formatting
	 * occurs Then result is returned does not contain "$" or ","
	 */
	@Test
	void formatLimitForSplitWithDollars() {
		assertEquals("500", standardInfo.formatLimitForSplit("$500,000"));
	}

	/**
	 * Given a String large value to be formatted When the formatting occurs Then
	 * result is returned divided by 1000
	 */
	@Test
	void formatLimitForSplitLargeValue() {
		assertEquals("6000000", standardInfo.formatLimitForSplit("6000000000"));
	}

	/**
	 * Given a rejected coverage When the split occurs Then result is returned as
	 * "REJ"
	 */
	@Test
	void generateSplitLimitsRejected() throws Exception {
		VehicleInfo instance = new VehicleInfo() {
			@Override
			boolean isRejected(final Node biCoverageNode) {
				return true;
			}
		};
		assertEquals("REJ", instance.generateSplitLimits(null));
	}

	/**
	 * Given a coverage without per person or per accident When the split occurs
	 * Then result is returned as ""
	 */
	@Test
	void generateSplitLimitsPersonAndAccidentNull() throws Exception {
		Path path = Paths.get("src/test/resources/xml/coverageSnippets.xml");
		String xml = readString(path);
		Document doc = XmlHelper.getDocument(xml);
		NodeList coverages = XmlHelper.getNodeList(doc, "Coverage");
		Node coverage = coverages.item(7);

		VehicleInfo instance = new VehicleInfo() {
			@Override
			boolean isRejected(final Node biCoverageNode) {
				return false;
			}

		};
		assertEquals("", instance.generateSplitLimits(coverage));
	}

	/**
	 * Given a coverage with per person but with per accident When the split occurs
	 * Then result is returned as the coverage limit
	 */
	@Test
	void generateSplitLimitsPersonValAndAccidentNull() throws Exception {
		Path path = Paths.get("src/test/resources/xml/coverageSnippets.xml");
		String xml = readString(path);
		Document doc = XmlHelper.getDocument(xml);
		NodeList coverages = XmlHelper.getNodeList(doc, "Coverage");
		Node coverage = coverages.item(6);

		VehicleInfo instance = new VehicleInfo() {
			@Override
			boolean isRejected(final Node biCoverageNode) {
				return false;
			}
		};
		assertEquals("500", instance.generateSplitLimits(coverage));
	}

	/**
	 * Given a coverage with per person and per accident When the split occurs Then
	 * result is returned as the per person "/" per accident
	 */
	@Test
	void generateSplitLimits() throws Exception {
		Node coverage = buildCoverageNodeFormatCurrency100_300();

		VehicleInfo instance = new VehicleInfo() {
			@Override
			boolean isRejected(final Node biCoverageNode) {
				return false;
			}

		};
		assertEquals("100/300", instance.generateSplitLimits(coverage));
	}
}

package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OriginSource;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.opportunity.services.SensitiveDataHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import java.util.LinkedList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

class SplitBulkCreationStrategyTest extends CreationStrategyTest {

	SensitiveDataHelper sensitiveDataHelper = mock(SensitiveDataHelper.class);
	SplitBulkCreationStrategy creationStrategy = new SplitBulkCreationStrategy(sensitiveDataHelper);

	@Test
	void testSplittableLOBsExcludesSomeLOBs() {
		assertFalse(SplitBulkCreationStrategy.getSplittableLobs().contains(LineOfBusiness.MTR));
		assertFalse(SplitBulkCreationStrategy.getSplittableLobs().contains(LineOfBusiness.GENERIC));
	}

	@Test
	void testGetAllQuoteInqRqNodesForSplittableLobs_collectsAllRequests() throws Exception {
		// single QuoteInqRq with single LOB
		Document autoFiveVeh = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto16Vehs.xml");
		// multiple QuoteInqRq with multiple LOBs
		Document multipleLobs = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/bulkFileNeedingSplit.xml");

		List<Node> singleAutoRequest = SplitBulkCreationStrategy.getAllQuoteInqRqNodesForSplittableLobs(autoFiveVeh);
		List<Node> multipleLobRequest = SplitBulkCreationStrategy.getAllQuoteInqRqNodesForSplittableLobs(multipleLobs);

		assertEquals(1, singleAutoRequest.size());
		assertEquals(10, multipleLobRequest.size());
	}

	@Test
	void testSplitBulkStrategy_ShouldResolve() throws Exception {
		OpportunityCreationRequest homeRequest = buildCreationRequest("src/test/resources/xml/accordhome.xml");
		homeRequest.setOriginSource(OriginSource.AQE);
		OpportunityCreationRequest autoFiveVehRequest = buildCreationRequest("src/test/resources/xml/auto16Vehs.xml");
		autoFiveVehRequest.setOriginSource(OriginSource.AQE);
		OpportunityCreationRequest multipleLobRequest = buildCreationRequest("src/test/resources/xml/bulkFileNeedingSplit.xml");
		multipleLobRequest.setOriginSource(OriginSource.LTU);
		OpportunityCreationRequest workCompRequest = buildCreationRequest("src/test/resources/xml/blWorkersComp_bulk.xml");
		workCompRequest.setOriginSource(OriginSource.LTU);

		boolean shouldResolveHome = creationStrategy.shouldResolveCreationRequest(homeRequest);
		boolean shouldResolveFiveVehicleAuto = creationStrategy.shouldResolveCreationRequest(autoFiveVehRequest);
		boolean shouldResolveMultipleLob = creationStrategy.shouldResolveCreationRequest(multipleLobRequest);
		boolean shouldResolveWorkComp = creationStrategy.shouldResolveCreationRequest(workCompRequest);

		assertFalse(shouldResolveHome);
		assertFalse(shouldResolveFiveVehicleAuto);
		assertTrue(shouldResolveMultipleLob);
		assertTrue(shouldResolveWorkComp);
	}

	@Test
	void testSplitBulkStrategy_SingleCreationBundleWithHome_NoResolves() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest homeRequest = buildCreationRequest("src/test/resources/xml/accordhome.xml");
		CreationStrategyBundle simpleHomeBundle = buildCreationBundle(homeRequest);

		List<CreationStrategyBundle> bundles = new LinkedList<>(List.of(simpleHomeBundle));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(1, bundles.get(0).getRequests().size());
		verify(sensitiveDataHelper, times(0)).tokenizeXmlWithEnvCheck(any());
		verify(sensitiveDataHelper, times(0)).scrubXmlWithEnvCheck(any());
	}

	@Test
	void testSplitBulkStrategyCreationBundleWithMultipleLOBsSplitsPerQuoteInqRq() throws Exception {
		OpportunityCreationRequest bulkRequest = buildCreationRequest("src/test/resources/xml/bulkFileNeedingSplit.xml");
		bulkRequest.setOriginSource(OriginSource.LTU);
		doAnswer(invocation -> invocation.getArgument(0)).when(sensitiveDataHelper).scrubXmlWithEnvCheck(any());

		verifyCreationBundles(bulkRequest, 10, 10);
	}

	@Test
	void testSplitBulkStrategyCreationBundleWithMultipleLOBsSplitsPerCrimePolicyQuoteInqRq() throws Exception {
		OpportunityCreationRequest bulkRequest = buildCreationRequest("src/test/resources/xml/crimBulkAccord.xml");
		bulkRequest.setOriginSource(OriginSource.LTU);
		doAnswer(invocation -> invocation.getArgument(0)).when(sensitiveDataHelper).scrubXmlWithEnvCheck(any());

		verifyCreationBundles(bulkRequest, 4, 4);
	}

	private void verifyCreationBundles(OpportunityCreationRequest request, int expectedBundleSize, int scrubXmlCallCount) throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		CreationStrategyBundle bundle = buildCreationBundle(request);

		List<CreationStrategyBundle> bundles = new LinkedList<>(List.of(bundle));
		creationStrategy.resolveCreationBundles(bundles, response);

		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(expectedBundleSize, bundles.size());
		verify(sensitiveDataHelper, times(0)).tokenizeXmlWithEnvCheck(any());
		verify(sensitiveDataHelper, times(scrubXmlCallCount)).scrubXmlWithEnvCheck(any());
	}

	@Test
	void testSplitBulkStrategy_CreationBundleWithSingleQuoteInqRq() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest homeRequest = buildCreationRequest("src/test/resources/xml/accordhome.xml");
		homeRequest.setOriginSource(OriginSource.LTU);
		CreationStrategyBundle simpleHomeBundle = buildCreationBundle(homeRequest);

		List<CreationStrategyBundle> bundles = new LinkedList<>(List.of(simpleHomeBundle));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(1, bundles.get(0).getRequests().size());
		verify(sensitiveDataHelper, times(0)).tokenizeXmlWithEnvCheck(any());
		verify(sensitiveDataHelper, times(1)).scrubXmlWithEnvCheck(any());
	}
}

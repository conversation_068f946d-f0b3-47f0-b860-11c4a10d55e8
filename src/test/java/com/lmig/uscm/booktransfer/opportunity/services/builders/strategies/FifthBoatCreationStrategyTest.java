package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.junit.jupiter.api.Test;

import javax.xml.xpath.XPathExpressionException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class FifthBoatCreationStrategyTest extends CreationStrategyTest{

    FifthBoatCreationStrategy creationStrategy = new FifthBoatCreationStrategy();

    @Test
    void testSplitFifthBoatStrategyShouldResolve() throws Exception {
        OpportunityCreationRequest fiveBoatRequest = buildCreationRequestFromXml("<ACORD><Watercraft></Watercraft>" +
                "<Watercraft></Watercraft><Watercraft></Watercraft><Watercraft></Watercraft><Watercraft></Watercraft></ACORD>");
        OpportunityCreationRequest fourBoatRequest = buildCreationRequestFromXml("<ACORD><Watercraft></Watercraft>" +
                "<Watercraft></Watercraft><Watercraft></Watercraft><Watercraft></Watercraft></ACORD>");
        OpportunityCreationRequest autoTwoVehRequest = buildCreationRequest("src/test/resources/xml/auto.xml");
        OpportunityCreationRequest homeRequest = buildCreationRequest("src/test/resources/xml/home.xml");

        boolean shouldResolveHome = creationStrategy.shouldResolveCreationRequest(homeRequest);
        boolean shouldResolveTwoVehicleAuto = creationStrategy.shouldResolveCreationRequest(autoTwoVehRequest);
        boolean shouldResolveFourBoat = creationStrategy.shouldResolveCreationRequest(fourBoatRequest);
        boolean shouldResolveFiveBoat = creationStrategy.shouldResolveCreationRequest(fiveBoatRequest);

        assertFalse(shouldResolveHome);
        assertFalse(shouldResolveTwoVehicleAuto);
        assertFalse(shouldResolveFourBoat);
        assertTrue(shouldResolveFiveBoat);
    }

    @Test
    void testSplitFifthBoatStrategy_CreationBundleWithFiveBoats_Resolves() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest boatRequest = getBoatRequest();

        CreationStrategyBundle splitFifthBoatBundle = buildCreationBundle(boatRequest);

        List<CreationStrategyBundle> bundles = List.of(splitFifthBoatBundle);

        creationStrategy.resolveCreationBundles(bundles, response);

        // Check that none errored
        assertEquals(0, (int) response.getFailedCreationCount());
        assertEquals(2, bundles.get(0).getRequests().size());

        // There are six boats total, first opportunity has four boats in it, second opportunity has two boats
        assertEquals(4, AcordHelper.getWatercrafts(bundles.get(0).getRequests().get(0).getUploadedACORD()).getLength());
        assertEquals(2, AcordHelper.getWatercrafts(bundles.get(0).getRequests().get(1).getUploadedACORD()).getLength());

        // Check that the split watercraft request links to MasterOpp properly
        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
        assertFalse(bundles.get(0).getRequests().get(1).isMasterOpp());

        assertAllRequestsHaveFourOrLessBoats(bundles.get(0).getRequests());
    }

    @Test
    void testSplitFifthBoatStrategy_CreationBundleWithFourBoats_NoResolves() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest boatRequest = buildCreationRequestFromXml("<ACORD><Watercraft></Watercraft>" +
                "<Watercraft></Watercraft><Watercraft></Watercraft><Watercraft></Watercraft></ACORD>");

        CreationStrategyBundle splitFifthBoatBundle = buildCreationBundle(boatRequest);

        List<CreationStrategyBundle> bundles = List.of(splitFifthBoatBundle);

        creationStrategy.resolveCreationBundles(bundles, response);

        // Check that none errored
        assertEquals(0, (int) response.getFailedCreationCount());
        assertEquals(1, bundles.get(0).getRequests().size());

        // Check that the split watercraft request links to MasterOpp properly
        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
        assertEquals(1, bundles.size());

        assertAllRequestsHaveFourOrLessBoats(bundles.get(0).getRequests());
    }

    @Test
    void testSplitFifthBoatStrategy_CreationBundleWithWatercraftAccessories_Resolves() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest boatRequest = getBoatRequest();

        CreationStrategyBundle splitFifthBoatBundle = buildCreationBundle(boatRequest);

        List<CreationStrategyBundle> bundles = List.of(splitFifthBoatBundle);

        creationStrategy.resolveCreationBundles(bundles, response);

        // Check that none errored
        assertEquals(0, (int) response.getFailedCreationCount());
        assertEquals(2, bundles.get(0).getRequests().size());

        // Check that all water accessories were saved into the first opportunity
        assertEquals(2, AcordHelper.getWatercraftAccessories(bundles.get(0).getRequests().get(0).getUploadedACORD()).getLength());
        assertEquals(0, AcordHelper.getWatercraftAccessories(bundles.get(0).getRequests().get(1).getUploadedACORD()).getLength());

        // Check that the split watercraft request links to MasterOpp properly
        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());

        assertAllRequestsHaveFourOrLessBoats(bundles.get(0).getRequests());
    }

    @Test
    void testSplitFifthBoatStrategy_CreationBundleWithQuotes_CalculatesCorrectPersPolicies() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest boatRequest = getBoatRequest();

        CreationStrategyBundle splitFifthBoatBundle = buildCreationBundle(boatRequest);

        List<CreationStrategyBundle> bundles = List.of(splitFifthBoatBundle);

        creationStrategy.resolveCreationBundles(bundles, response);

        assertEquals(2, bundles.get(0).getRequests().size());

        assertEquals("725.00", AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(0).getUploadedACORD()));
        assertEquals("250.00", AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(1).getUploadedACORD()));

        assertFalse(bundles.get(0).getRequests().get(0).isDerivedFromHome());
        assertFalse(bundles.get(0).getRequests().get(1).isDerivedFromHome());

        assertEquals(0, AcordHelper.getWatercraftAccessories(bundles.get(0).getRequests().get(1).getUploadedACORD()).getLength());
    }

    private void assertAllRequestsHaveFourOrLessBoats(List<OpportunityCreationRequest> creationRequests) throws XPathExpressionException {
        for (OpportunityCreationRequest request: creationRequests) {
            assertTrue(AcordHelper.getWatercrafts(request.getUploadedACORD()).getLength() <= 4);
        }
    }

    @Test
    void testSplitFifthBoatStrategy_CreationBundleWithNoQuotes_CalculateAsZero() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();

        // two of the last watercraft have no <Amt>x.xx</Amt> in their CurrentTermAmt tags
        OpportunityCreationRequest boatRequest = buildCreationRequestFromXml("<ACORD><InsuranceSvcRq><WatercraftPolicyQuoteInqRq>" +
                "<PersPolicy><CurrentTermAmt><Amt>5000</Amt></CurrentTermAmt></PersPolicy><WatercraftLineBusiness>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt></CurrentTermAmt></Coverage></Watercraft>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>25</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "</WatercraftLineBusiness></WatercraftPolicyQuoteInqRq></InsuranceSvcRq></ACORD>");

        CreationStrategyBundle splitFifthBoatBundle = buildCreationBundle(boatRequest);

        List<CreationStrategyBundle> bundles = List.of(splitFifthBoatBundle);

        creationStrategy.resolveCreationBundles(bundles, response);

        assertEquals(2, bundles.get(0).getRequests().size());

        assertEquals("725.00", AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(0).getUploadedACORD()));
        assertEquals("0.00", AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(1).getUploadedACORD()));

        assertFalse(bundles.get(0).getRequests().get(0).isDerivedFromHome());
        assertFalse(bundles.get(0).getRequests().get(1).isDerivedFromHome());

        assertEquals(0, AcordHelper.getWatercraftAccessories(bundles.get(0).getRequests().get(1).getUploadedACORD()).getLength());
    }

    private OpportunityCreationRequest getBoatRequest() throws Exception {
        return buildCreationRequestFromXml("<ACORD><InsuranceSvcRq><WatercraftPolicyQuoteInqRq>" +
                "<PersPolicy><CurrentTermAmt><Amt>5000</Amt></CurrentTermAmt></PersPolicy><WatercraftLineBusiness>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>50</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>25</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "</WatercraftLineBusiness></WatercraftPolicyQuoteInqRq></InsuranceSvcRq></ACORD>");
    }
}

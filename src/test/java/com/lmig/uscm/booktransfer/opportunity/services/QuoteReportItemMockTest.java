/*
 * Copyright (C) 2018, Liberty Mutual Group
 *
 * Created on 11/13/18 by n0210477
 */
package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.QuoteReportBulkUpdate;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.w3c.dom.Document;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

class QuoteReportItemMockTest {

    @Test
    void updateQuoteReportItemFromOpportunities() {
        QuoteReportService mockQuoteReportService = mock(QuoteReportService.class);
        doNothing().when(mockQuoteReportService).patchQuoteReportFields(any(QuoteReportBulkUpdate.class));
        QuoteReportItemHelper instance = new QuoteReportItemHelper(mockQuoteReportService);
        instance.updateQuoteReportItemForMoveBooks(null, 1232, "567567");
    }

    @Test
    void buildAndSaveQuoteReportItemAdd() throws Exception {
        QuoteReportService service = mock(QuoteReportService.class);
        Mockito.when(service.addQuoteReport(any(QuoteReportItemLegacy.class))).thenReturn(new QuoteReportItemLegacy());
        QuoteReportItemHelper instance = new QuoteReportItemHelper(service) {
            @Override
            public QuoteReportItemLegacy build(Opportunity opportunity, Integer SFDCID, String subCode) throws Exception {
                return new QuoteReportItemLegacy();
            }
        };
        QuoteReportItemLegacy response = instance.buildAndSaveQuoteReportItem(new Opportunity(), 98, "test");
        assertThat(response).isNotNull();
        verify(service).addQuoteReport(any(QuoteReportItemLegacy.class));
    }

    @Test
    void buildAndSaveQuoteReportItemUpdate() throws Exception {
        QuoteReportService service = mock(QuoteReportService.class);
        Mockito.when(service.updateQuoteReport(any(QuoteReportItemLegacy.class))).thenReturn(new QuoteReportItemLegacy());
        QuoteReportItemHelper instance = new QuoteReportItemHelper(service) {
            @Override
            public QuoteReportItemLegacy build(Opportunity opportunity, Integer SFDCID, String subCode) throws Exception {
                QuoteReportItemLegacy item = new QuoteReportItemLegacy();
                item.setQuoteReportID(28);
                return item;
            }
        };
        QuoteReportItemLegacy response = instance.buildAndSaveQuoteReportItem(new Opportunity(), 98, "test");
        assertThat(response).isNotNull();
        verify(service).updateQuoteReport(any(QuoteReportItemLegacy.class));
    }

    @Test
    void getLob5thCar() {
        Opportunity opportunity = new Opportunity();
        opportunity.setMasterOppID("Test");
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.AUTOP, null, opportunity))
                .isEqualTo("Auto - 5th Car Policy");
    }

    @Test
    void getLob5thCarMatchesMasterOppId() {
        Opportunity opportunity = new Opportunity();
        opportunity.setOpportunityId(12);
        opportunity.setMasterOppID("12");
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.AUTOP, null, opportunity)).isEqualTo("Auto");
    }

    @Test
    void getLobAuto() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.AUTOP, null, new Opportunity()))
                .isEqualTo("Auto");
    }

    @Test
    void getLobBoat() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.BOAT, null, new Opportunity()))
                .isEqualTo("Watercraft");
    }

    @Test
    void getLobUmbrella() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.UMBRP, null, new Opportunity()))
                .isEqualTo("Umbrella");
    }

    @Test
    void getLobFire() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.DFIRE, null, new Opportunity()))
                .isEqualTo("Fire");
    }

    @Test
    void getLobHomeCode03() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "03", new Opportunity()))
                .isEqualTo("Home");
    }

    @Test
    void getLobHomeCode05() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "05", new Opportunity()))
                .isEqualTo("Home");
    }

    @Test
    void getLobHomeCodeH03() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "H03", new Opportunity()))
                .isEqualTo("Home");
    }

    @Test
    void getLobHomeCodeH05() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "H05", new Opportunity()))
                .isEqualTo("Home");
    }

    @Test
    void getLobHomeCodeT2() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "T2", new Opportunity()))
                .isEqualTo("Home");
    }

    @Test
    void getLobHomeCodeT3() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "T3", new Opportunity()))
                .isEqualTo("Home");
    }

    @Test
    void getLobRentersCode04() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "04", new Opportunity()))
                .isEqualTo("Renters");
    }

    @Test
    void getLobHomeCodeH04() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "H04", new Opportunity()))
                .isEqualTo("Renters");
    }

    @Test
    void getLobHomeCode4A() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "4A", new Opportunity()))
                .isEqualTo("Renters");
    }

    @Test
    void getLobHomeCodeT4() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "T4", new Opportunity()))
                .isEqualTo("Renters");
    }

    @Test
    void getLobCondoCode06() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "06", new Opportunity()))
                .isEqualTo("Condo");
    }

    @Test
    void getLobCondoCode6A() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "6A", new Opportunity()))
                .isEqualTo("Condo");
    }

    @Test
    void getLobCondoCodeH06() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "H06", new Opportunity()))
                .isEqualTo("Condo");
    }

    @Test
    void getLobCondoCode08() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "08", new Opportunity()))
                .isEqualTo("Condo");
    }

    @Test
    void getLobCondoCodeT6() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "T6", new Opportunity()))
                .isEqualTo("Condo");
    }

    @Test
    void getLobCondoCodeT7() {
        assertThat(new QuoteReportItemHelper(null).getLob(LineOfBusiness.HOME, "T7", new Opportunity()))
                .isEqualTo("Condo");
    }

    private QuoteReportItemHelper buildHelperForUpdate(final String lob) {
        QuoteReportService service = new QuoteReportService(null, null) {
            @Override
            public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityId(final int opportunityId) {
                List<QuoteReportItemLegacy> itemList = new ArrayList<>();
                QuoteReportItemLegacy item = new QuoteReportItemLegacy();
                itemList.add(item);
                return itemList;
            }

            @Override
            public List<QuoteReportItemLegacy> updateBulkQuoteReports(final List<QuoteReportItemLegacy> reportItems) {
                return reportItems;
            }

        };

        return new QuoteReportItemHelper(service) {
            @Override
            protected String getPolicyTerm(Document xmlDoc) {
                return "6 month";
            }

            @Override
            protected String getLob(Document xmlDoc, Opportunity opportunity) {
                return lob;
            }
        };
    }

    @Test
    void updateQuoteReportItemNotAutoAnnualPolicyTerm() throws Exception {
        QuoteReportItemHelper instance = buildHelperForUpdate("home");

        Opportunity opportunity = build6MonthTermPolicy();
        opportunity.setBusinessType(LineOfBusiness.HOME.getFirstPossibleLOBString());
        opportunity.setStatus(1);
        QuoteReportItemLegacy quoteReportItem = instance.updateQuoteReportItem(opportunity).get(0);
        assertThat(quoteReportItem.getSafecoPolicyTerm()).isEqualTo("Annual");
    }

    @Test
    void updateQuoteReportItemAuto6Month() throws Exception {
        QuoteReportItemHelper instance = buildHelperForUpdate("auto");
        Opportunity opportunity = build6MonthTermPolicy();
        opportunity.setStatus(1);
        QuoteReportItemLegacy quoteReportItem = instance.updateQuoteReportItem(opportunity).get(0);
        assertThat(quoteReportItem.getSafecoPolicyTerm()).isEqualTo("6 month");
    }

    @Test
    void updateQuoteReportItemDwellCovA() throws Exception {
        Opportunity opportunity = new Opportunity();
        opportunity.setStatus(1);
        String acord = "<Acord><Dwell><Coverage>" +
                "<CoverageCd>DWELL</CoverageCd><Limit>" +
                "<FormatInteger>5000</FormatInteger></Limit>" +
                "</Coverage></Dwell></Acord>";
        opportunity.setData(acord);
        QuoteReportService service = new QuoteReportService(null, null) {
            @Override
            public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityId(final int opportunityId) {
                List<QuoteReportItemLegacy> itemList = new ArrayList<>();
                QuoteReportItemLegacy item = new QuoteReportItemLegacy();
                item.setPriorCovA(100.00);
                itemList.add(item);
                QuoteReportItemLegacy item2 = new QuoteReportItemLegacy();
                item2.setPriorCovA(200.00);
                itemList.add(item2);
                return itemList;
            }

            @Override
            public List<QuoteReportItemLegacy> updateBulkQuoteReports(final List<QuoteReportItemLegacy> reportItems) {
                return reportItems;
            }
        };
        List<QuoteReportItemLegacy> quoteReportItem = new QuoteReportItemHelper(service).updateQuoteReportItem(opportunity);
        quoteReportItem.forEach(item -> assertThat(item.getPriorCovA()).isEqualTo(5000.0));
    }

    @Test
    void updateQuoteReportItemAutoCovANoUpdate() throws Exception {
        Opportunity opportunity = new Opportunity();
        opportunity.setStatus(1);
        String acord = "<Acord><Auto><Coverage>" +
                "<CoverageCd>DWELL</CoverageCd><Limit>" +
                "<FormatInteger>5000</FormatInteger></Limit>" +
                "</Coverage></Auto></Acord>";
        opportunity.setData(acord);
        QuoteReportService service = new QuoteReportService(null, null) {
            @Override
            public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityId(final int opportunityId) {
                List<QuoteReportItemLegacy> itemList = new ArrayList<>();
                QuoteReportItemLegacy item = new QuoteReportItemLegacy();
                item.setPriorCovA(100.00);
                itemList.add(item);
                QuoteReportItemLegacy item2 = new QuoteReportItemLegacy();
                item2.setPriorCovA(100.00);
                itemList.add(item2);
                return itemList;
            }

            @Override
            public List<QuoteReportItemLegacy> updateBulkQuoteReports(final List<QuoteReportItemLegacy> reportItems) {
                return reportItems;
            }
        };
        List<QuoteReportItemLegacy> quoteReportItems = new QuoteReportItemHelper(service).updateQuoteReportItem(opportunity);
        quoteReportItems.forEach(item -> assertThat(item.getPriorCovA()).isEqualTo(100.0));
        assertThat(quoteReportItems).hasSize(2);
    }

    @Test
    void updateQuoteReportItemNameChange() throws Exception {
        Opportunity opportunity = new Opportunity();
        opportunity.setStatus(1);

        String acordWithLastName = "<Acord><InsuredOrPrincipal>" +
                "<GeneralPartyInfo><NameInfo><PersonName><Surname>" +
                "Rogers</Surname><GivenName>Tina</GivenName>" +
                "</PersonName></NameInfo></GeneralPartyInfo>" +
                "</InsuredOrPrincipal></Acord>";
        opportunity.setData(acordWithLastName);

        QuoteReportService service = new QuoteReportService(null, null) {
            @Override
            public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityId(final int opportunityId) {
                List<QuoteReportItemLegacy> itemList = new ArrayList<>();
                QuoteReportItemLegacy item = new QuoteReportItemLegacy();
                item.setCustomerFirstName("James");
                item.setCustomerFirstName("Taylor");
                itemList.add(item);
                return itemList;
            }

            @Override
            public List<QuoteReportItemLegacy> updateBulkQuoteReports(final List<QuoteReportItemLegacy> reportItems) {
                return reportItems;
            }
        };
        QuoteReportItemLegacy quoteReportItem = new QuoteReportItemHelper(service).updateQuoteReportItem(opportunity).get(0);

        assertThat(quoteReportItem.getCustomerLastName()).isEqualTo("Rogers");
        assertThat(quoteReportItem.getCustomerFirstName()).isEqualTo("Tina");
    }

    @Test
    void updateQuoteReportItemAddressChange() throws Exception {
        Opportunity opportunity = new Opportunity();
        opportunity.setStatus(1);
        opportunity.setData(getAddressAcordXmlString());

        QuoteReportService service = new QuoteReportService(null, null) {
            @Override
            public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityId(final int opportunityId) {
                List<QuoteReportItemLegacy> itemList = new ArrayList<>();
                QuoteReportItemLegacy item = new QuoteReportItemLegacy();
                itemList.add(item);
                itemList.add(item);
                return itemList;
            }

            @Override
            public List<QuoteReportItemLegacy> updateBulkQuoteReports(final List<QuoteReportItemLegacy> reportItems) {
                return reportItems;
            }
        };

        List<QuoteReportItemLegacy> quoteReportItems = new QuoteReportItemHelper(service).updateQuoteReportItem(opportunity);
        quoteReportItems.forEach(item -> {
            assertThat(item.getCustomerMailAddress1()).isEqualTo("Address1 St.");
            assertThat(item.getCustomerMailAddress2()).isEqualTo("APt. Address2");
            assertThat(item.getCustomerMailCity()).isEqualTo("Indianapolis");
            assertThat(item.getCustomerMailState()).isEqualTo("IN");
            assertThat(item.getCustomerMailZip()).isEqualTo("46032");
        });
        assertThat(quoteReportItems).hasSize(2);
    }

    @Test
    void testGetPolicyTermAnnual() throws Exception {
        // test case for Annual Policies
        Document xmlDoc = XmlHelper.getDocument(
                "<PersPolicy><ContractTerm><EffectiveDt>2019-09-01</EffectiveDt><ExpirationDt>2020-09-01</ExpirationDt></ContractTerm></PersPolicy>");

        String annualResult = new QuoteReportItemHelper(null).getPolicyTerm(xmlDoc);
        assertThat(annualResult).isEqualTo("Annual");
    }

    @Test
    void testGetPolicyTerm6Month() throws Exception {
        // test case for Annual Policies
        Document xmlDoc = XmlHelper.getDocument(
                "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><ContractTerm><EffectiveDt>2019-09-01</EffectiveDt><ExpirationDt>2019-09-29</ExpirationDt></ContractTerm></PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>");

        String annualResult = new QuoteReportItemHelper(null).getPolicyTerm(xmlDoc);
        assertThat(annualResult).isEqualTo("6 month");
    }

    @Test
    void testGetPolicyTermMissingDate() throws Exception {
        // test case for Annual Policies
        Document xmlDoc = XmlHelper.getDocument(
                "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><ContractTerm><ExpirationDt>2019-09-29</ExpirationDt></ContractTerm></PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>");

        String annualResult = new QuoteReportItemHelper(null).getPolicyTerm(xmlDoc);
        assertThat(annualResult).isEqualTo("Annual");
    }

    @Test
    void getQuoteReportItemsForStatusChange() throws OpportunityException {
        QuoteReportService service = new QuoteReportService(null, null) {
            @Override
            public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityIds(final List<Integer> opportunityIds) {
                List<QuoteReportItemLegacy> itemList = new ArrayList<>();
                QuoteReportItemLegacy item = new QuoteReportItemLegacy();
                itemList.add(item);
                itemList.add(item);
                return itemList;
            }
        };

        List<QuoteReportItemLegacy> allItems = new QuoteReportItemHelper(service)
                .getQuoteReportItemsForStatusChange(Arrays.asList(1, 2));
        assertThat(allItems).hasSize(2);
    }

    @Test
    void getQuoteReportItemsForBookChange() throws OpportunityException {
        QuoteReportService service = new QuoteReportService(null, null) {
            @Override
            public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityIds(final List<Integer> opportunityIds) {
                List<QuoteReportItemLegacy> itemList = new ArrayList<>();
                QuoteReportItemLegacy item = new QuoteReportItemLegacy();
                itemList.add(item);
                itemList.add(item);
                return itemList;
            }
        };

        List<QuoteReportItemLegacy> allItems = new QuoteReportItemHelper(service)
                .getQuoteReportItemsForBookChange(Arrays.asList(1, 2));
        assertThat(allItems).hasSize(2);
    }

    @Test
    void updateQuoteReportItems() {
        QuoteReportService service = mock(QuoteReportService.class);
        doNothing().when(service).patchQuoteReportFields(any(QuoteReportBulkUpdate.class));
        new QuoteReportItemHelper(service).updateQuoteReportItems(List.of("test"), null);
        verify(service).patchQuoteReportFields(any(QuoteReportBulkUpdate.class));
    }

    @Test
    void updateQuoteReportItemsNoItems() {
        QuoteReportService service = mock(QuoteReportService.class);
        new QuoteReportItemHelper(service).updateQuoteReportItems(null, null);
        verify(service, never()).patchQuoteReportFields(any(QuoteReportBulkUpdate.class));
    }

    @Test
    void updateQuoteReportForLeadEffectiveDate() {
        List<QuoteReportItemLegacy> quoteReports = new ArrayList<>();
        QuoteReportItemLegacy quoteReportLegacy = new QuoteReportItemLegacy();
        quoteReports.add(quoteReportLegacy);
        QuoteReportService service = mock(QuoteReportService.class);
        doNothing().when(service).patchQuoteReportFields(quoteReports);
        new QuoteReportItemHelper(service).updateQuoteReportForLeadEffectiveDate(quoteReports);
        verify(service).patchQuoteReportFields(quoteReports);
    }

    @Test
    void updateQuoteReportForLeadEffectiveDateNoItems() {
        List<QuoteReportItemLegacy> quoteReports = new ArrayList<>();
        QuoteReportItemLegacy quoteReportLegacy = new QuoteReportItemLegacy();
        quoteReports.add(quoteReportLegacy);
        QuoteReportService service = mock(QuoteReportService.class);
        Mockito.when(service.getAllQuoteReportsForOpportunityId(anyInt())).thenReturn(null);
        new QuoteReportItemHelper(service).updateQuoteReportForLeadEffectiveDate(quoteReports);
        verify(service, never()).patchQuoteReportFields(any(QuoteReportBulkUpdate.class));
    }

    @Test
    void updateQuoteReportItemFirstNameLastName() {
        Opportunity opportunity = new Opportunity();
        opportunity.setStatus(1);

        String accordWithInsuredAndInNames = "<Acord>" +
                "  <InsuredOrPrincipal>" +
                "    <GeneralPartyInfo>" +
                "      <NameInfo>" +
                "        <PersonName>" +
                "          <GivenName>ALICE</GivenName>" +
                "          <Surname>BERGQUIST</Surname>" +
                "        </PersonName>" +
                "      </NameInfo>" +
                "    </GeneralPartyInfo>" +
                "  </InsuredOrPrincipal>" +
                "  <InsuredOrPrincipal>" +
                "    <InsuredOrPrincipalInfo>" +
                "      <InsuredOrPrincipalRoleCd>IN</InsuredOrPrincipalRoleCd>" +
                "    </InsuredOrPrincipalInfo>" +
                "    <GeneralPartyInfo>" +
                "      <NameInfo>" +
                "        <PersonName>" +
                "          <GivenName>Mike</GivenName>" +
                "          <Surname>Johnson</Surname>" +
                "        </PersonName>" +
                "        <LegalEntityCd>IN</LegalEntityCd>" +
                "        <CommlName>" +
                "          <CommercialName>ALICE BERGQUIST</CommercialName>" +
                "        </CommlName>" +
                "      </NameInfo>" +
                "    </GeneralPartyInfo>" +
                "  </InsuredOrPrincipal>" +
                "</Acord>";
        opportunity.setData(accordWithInsuredAndInNames);

        QuoteReportService service = new QuoteReportService(null, null) {
            @Override
            public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityId(final int opportunityId) {
                List<QuoteReportItemLegacy> itemList = new ArrayList<>();
                QuoteReportItemLegacy item = new QuoteReportItemLegacy();
                item.setCustomerFirstName("James");
                item.setCustomerLastName("Taylor");
                itemList.add(item);
                return itemList;
            }

            @Override
            public List<QuoteReportItemLegacy> updateBulkQuoteReports(final List<QuoteReportItemLegacy> reportItems) {
                return reportItems;
            }
        };

        // Test first XML
        QuoteReportItemLegacy quoteReportItem = new QuoteReportItemHelper(service).updateQuoteReportItem(opportunity).get(0);
        assertThat(quoteReportItem.getCustomerLastName()).isEqualTo("Johnson");
        assertThat(quoteReportItem.getCustomerFirstName()).isEqualTo("Mike");

        String acordWithPrincipalRoleInsuredName = "<Acord>" +
                "  <InsuredOrPrincipal>" +
                "    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>" +
                "    <GeneralPartyInfo>" +
                "      <NameInfo>" +
                "        <PersonName>" +
                "          <GivenName>Doe</GivenName>" +
                "          <Surname>Dawn</Surname>" +
                "        </PersonName>" +
                "      </NameInfo>" +
                "    </GeneralPartyInfo>" +
                "  </InsuredOrPrincipal>" +
                "  <InsuredOrPrincipal>" +
                "    <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>" +
                "    <GeneralPartyInfo>" +
                "      <NameInfo>" +
                "        <PersonName>" +
                "          <GivenName>Tina</GivenName>" +
                "          <Surname>Rogers</Surname>" +
                "        </PersonName>" +
                "      </NameInfo>" +
                "    </GeneralPartyInfo>" +
                "  </InsuredOrPrincipal>" +
                "</Acord>";
        opportunity.setData(acordWithPrincipalRoleInsuredName);

        // Test second XML
        quoteReportItem = new QuoteReportItemHelper(service).updateQuoteReportItem(opportunity).get(0);
        assertThat(quoteReportItem.getCustomerLastName()).isEqualTo("Dawn");
        assertThat(quoteReportItem.getCustomerFirstName()).isEqualTo("Doe");
    }

    private Opportunity build6MonthTermPolicy() {
        Opportunity opportunity = new Opportunity();
        opportunity.setData(
                "<Acord><PersPolicy><ContractTerm><EffectiveDt>2019-06-06</EffectiveDt><ExpirationDt>2019-12-06</ExpirationDt></ContractTerm></PersPolicy></Acord>");
        opportunity.setBusinessType(LineOfBusiness.AUTOP.getFirstPossibleLOBString());
        return opportunity;
    }

    private String getAddressAcordXmlString() {
        return "<Acord><InsuredOrPrincipal><InsuredOrPrincipalInfo>" +
                "<InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>" +
                "</InsuredOrPrincipalInfo><GeneralPartyInfo><Addr><AddrTypeCd>MailingAddress</AddrTypeCd>" +
                "<Addr1>Address1 St.</Addr1><Addr2>APt. Address2</Addr2><City>Indianapolis</City>" +
                "<StateProvCd>IN</StateProvCd><PostalCode>46032</PostalCode></Addr>" +
                "</GeneralPartyInfo></InsuredOrPrincipal></Acord>";
    }
}

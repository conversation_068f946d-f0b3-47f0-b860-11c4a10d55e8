package com.lmig.uscm.booktransfer.opportunity.domain;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.assertEquals;

class SafecoLobsTest {

    @ParameterizedTest
    @CsvSource({
            "RENT,2,Renters",
            "CNDR,2,Condo",
            "COND,2,Condo",
            "QPC,2,Condo",
            "QCC,2,Condo",
            "HOM,2,Home",
            "QPH,2,Home",
            "QCH,2,Home",
            "V49,2,Home",
            "Other,2,Home",
            "PRF,1,Auto",
            "3,8,Dwelling Fire",
            "BOA,7,Boat",
            "UMB,5,Umbre<PERSON>",
            "MTR,10,Motorcycle",
            "asd,45,Undetermined"
    })
    void testDetermineSafecoLob(String product, String lobCd, String expected){
        assertEquals(expected, SafecoLobs.determineSafecoLob(product, lobCd));
    }
}

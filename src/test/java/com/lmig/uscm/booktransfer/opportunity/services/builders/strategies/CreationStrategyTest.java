package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.util.Arrays;

public abstract class CreationStrategyTest {

	protected OpportunityCreationRequest buildCreationRequest(String pathToData) throws ParserConfigurationException, SAXException, IOException {
		OpportunityCreationRequest request = new OpportunityCreationRequest();
		Document inputAcord = XmlHelper.getXmlDocumentFromPath(pathToData);
		request.setUploadedACORD(inputAcord);
		request.setAsMasterOpp();
		return request;
	}

	protected static OpportunityCreationRequest buildCreationRequestFromXml(String xml) throws ParserConfigurationException, SAXException, IOException {
		OpportunityCreationRequest request = new OpportunityCreationRequest();
		Document inputAcord = XmlHelper.getDocument(xml);
		request.setUploadedACORD(inputAcord);
		request.setAsMasterOpp();
		return request;
	}

	protected CreationStrategyBundle buildCreationBundle(OpportunityCreationRequest... requests) {
		CreationStrategyBundle bundle = new CreationStrategyBundle(null);
		Arrays.stream(requests).forEach(bundle::addCreationRequest);
		return bundle;
	}

}

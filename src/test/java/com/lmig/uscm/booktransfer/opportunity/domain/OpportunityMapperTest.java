package com.lmig.uscm.booktransfer.opportunity.domain;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.utilityservice.DateUtils;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

class OpportunityMapperTest {

	/**
	 * Requires `mvn install` to generate OpportunityMapperImpl by mapstruct factory or you'll get an error like:
	 * java.lang.ClassNotFoundException: Cannot find implementation for com.lmig.uscm.booktransfer.opportunity.domain.OpportunityMapper
 	 */
	OpportunityMapper mapper = Mappers.getMapper(OpportunityMapper.class);

	Integer expectedId = 123456;
	String expectedBusinessType = "HOME";
	Integer expectedOppStatus = 0;
	String expectedData = "<ACORD></ACORD>";
	String expectedAgencyId = "123456";
	String expectedCustomerName = "Name";
	String expectedEffectiveDt = "12/25/1997";
	String expectedPolicyGuid = "00000000-0000-0000-0000-000000000000";
	Double expectedPremium = 4.20;
	LineType expectedLineType = LineType.Personal;
	String expectedMasterOppId = "123456";
	String expectedNaiccd = "123456";
	String expectedPriorCarrier = "priorCarrier";
	String expectedState = "IN";
	String expectedTimestampAsLocalDateTimeString = "2021-06-10T15:05:04.564595";
	LocalDateTime expectedLocalDateTime = DateUtils.getDateTimeFromString(expectedTimestampAsLocalDateTimeString);
	Integer expectedUploadId = 654321;

	private com.lmig.uscm.booktransfer.opportunity.client.domain.Opportunity buildOpportunityDTO() {
		com.lmig.uscm.booktransfer.opportunity.client.domain.Opportunity opportunity = new com.lmig.uscm.booktransfer.opportunity.client.domain.Opportunity();
		opportunity.setOpportunityId(expectedId);
		opportunity.setBusinessType(expectedBusinessType);
		opportunity.setStatus(expectedOppStatus);
		opportunity.setData(expectedData);
		opportunity.setOriginalXML(expectedData);
		opportunity.setAgencyId(expectedAgencyId);
		opportunity.setCustomerName(expectedCustomerName);
		opportunity.setEffectiveDate(expectedEffectiveDt);
		opportunity.setLastPolicyGuid(expectedPolicyGuid);
		opportunity.setLastQuotedPremium(expectedPremium);
		opportunity.setLineType(expectedLineType);
		opportunity.setMasterOppID(expectedMasterOppId);
		opportunity.setNAICCd(expectedNaiccd);
		opportunity.setOppPriorCarrier(expectedPriorCarrier);
		opportunity.setState(expectedState);
		opportunity.setTimestampCallPartner(expectedTimestampAsLocalDateTimeString);
		opportunity.setTimestampCleanup(expectedTimestampAsLocalDateTimeString);
		opportunity.setTimestampFirstCallPartner(expectedTimestampAsLocalDateTimeString);
		opportunity.setTimestampIssued(expectedTimestampAsLocalDateTimeString);
		opportunity.setTimestampUpload(expectedTimestampAsLocalDateTimeString);
		opportunity.setUploadEventID(expectedUploadId);
		
		return opportunity;
	}

	private Opportunity buildOpportunity() {
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(expectedId);
		opportunity.setBusinessType(expectedBusinessType);
		opportunity.setStatus(expectedOppStatus);
		opportunity.setData(expectedData);
		opportunity.setOriginalXML(expectedData);
		opportunity.setAgencyId(expectedAgencyId);
		opportunity.setCustomerName(expectedCustomerName);
		opportunity.setEffectiveDate(expectedEffectiveDt);
		opportunity.setLastPolicyGuid(expectedPolicyGuid);
		opportunity.setLastQuotedPremium(expectedPremium);
		opportunity.setLineType(expectedLineType);
		opportunity.setMasterOppID(expectedMasterOppId);
		opportunity.setNAICCd(expectedNaiccd);
		opportunity.setOppPriorCarrier(expectedPriorCarrier);
		opportunity.setState(expectedState);
		opportunity.setTimestampCallPartner(expectedLocalDateTime);
		opportunity.setTimestampCleanup(expectedLocalDateTime);
		opportunity.setTimestampFirstCallPartner(expectedLocalDateTime);
		opportunity.setTimestampIssued(expectedLocalDateTime);
		opportunity.setTimestampUpload(expectedLocalDateTime);
		opportunity.setUploadEventID(expectedUploadId);

		return opportunity;
	}

	@Test
	void testOpportunityMapperOpportunityDTOtoOpportunity() {
		com.lmig.uscm.booktransfer.opportunity.client.domain.Opportunity dto = buildOpportunityDTO();
		
		Opportunity mapped = mapper.opportunityDTOtoOpportunity(dto);
		
		assertEquals(dto.getOpportunityId(), mapped.getOpportunityId());
		assertEquals(dto.getBookTransferID(), mapped.getBookTransferID());
		assertEquals(dto.getStatus(), mapped.getStatus());
		assertEquals(dto.getData(), mapped.getData());
		assertEquals(dto.getOriginalXML(), mapped.getOriginalXML());
		assertEquals(dto.getAgencyId(), mapped.getAgencyId());
		assertEquals(dto.getCustomerName(), mapped.getCustomerName());
		assertEquals(dto.getEffectiveDate(), mapped.getEffectiveDate());
		assertEquals(dto.getLastPolicyGuid(), mapped.getLastPolicyGuid());
		assertEquals(dto.getLastQuotedPremium(), mapped.getLastQuotedPremium());
		assertEquals(dto.getLineType(), mapped.getLineType());
		assertEquals(dto.getMasterOppID(), mapped.getMasterOppID());
		assertEquals(dto.getNAICCd(), mapped.getNAICCd());
		assertEquals(dto.getOppPriorCarrier(), mapped.getOppPriorCarrier());
		assertEquals(dto.getState(), mapped.getState());
		assertEquals(expectedLocalDateTime, mapped.getTimestampCallPartner());
		assertEquals(expectedLocalDateTime, mapped.getTimestampCleanup());
		assertEquals(expectedLocalDateTime, mapped.getTimestampFirstCallPartner());
		assertEquals(expectedLocalDateTime, mapped.getTimestampIssued());
		assertEquals(dto.getUploadEventID(), mapped.getUploadEventID());
	}

	@Test
	void testOpportunityMapperOpportunityToOpportunityDTO() {
		Opportunity opportunity = buildOpportunity();

		com.lmig.uscm.booktransfer.opportunity.client.domain.Opportunity mapped = mapper.opportunityToOpportunityDTO(opportunity);

		assertEquals(opportunity.getOpportunityId(), mapped.getOpportunityId());
		assertEquals(opportunity.getBookTransferID(), mapped.getBookTransferID());
		assertEquals(opportunity.getStatus(), mapped.getStatus());
		assertEquals(opportunity.getData(), mapped.getData());
		assertEquals(opportunity.getOriginalXML(), mapped.getOriginalXML());
		assertEquals(opportunity.getAgencyId(), mapped.getAgencyId());
		assertEquals(opportunity.getCustomerName(), mapped.getCustomerName());
		assertEquals(opportunity.getEffectiveDate(), mapped.getEffectiveDate());
		assertEquals(opportunity.getLastPolicyGuid(), mapped.getLastPolicyGuid());
		assertEquals(opportunity.getLastQuotedPremium(), mapped.getLastQuotedPremium());
		assertEquals(opportunity.getLineType(), mapped.getLineType());
		assertEquals(opportunity.getMasterOppID(), mapped.getMasterOppID());
		assertEquals(opportunity.getNAICCd(), mapped.getNAICCd());
		assertEquals(opportunity.getOppPriorCarrier(), mapped.getOppPriorCarrier());
		assertEquals(opportunity.getState(), mapped.getState());
		assertEquals(expectedTimestampAsLocalDateTimeString, mapped.getTimestampCallPartner());
		assertEquals(expectedTimestampAsLocalDateTimeString, mapped.getTimestampCleanup());
		assertEquals(expectedTimestampAsLocalDateTimeString, mapped.getTimestampFirstCallPartner());
		assertEquals(expectedTimestampAsLocalDateTimeString, mapped.getTimestampIssued());
		assertEquals(opportunity.getUploadEventID(), mapped.getUploadEventID());
	}

}

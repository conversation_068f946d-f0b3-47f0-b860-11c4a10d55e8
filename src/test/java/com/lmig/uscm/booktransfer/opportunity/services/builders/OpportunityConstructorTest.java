/*
 * Copyright (C) 2018, Liberty Mutual Group
 *
 * Created on 11/29/18 by n0210477
 */
package com.lmig.uscm.booktransfer.opportunity.services.builders;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;

import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.TransformationService;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;

@ExtendWith(MockitoExtension.class)
class OpportunityConstructorTest {

	@InjectMocks
	OpportunityConstructor opportunityConstructor;
	@Mock
	OpportunityHelper opportunityHelper;
	@Mock
	TransformationService transformationService;

	@BeforeEach
	public void mockitoSetup() throws Exception {
		lenient().doAnswer(invocationOnMock -> invocationOnMock.getArgument(1)).when(opportunityHelper)
				.updateNewOpportunity(any(OpportunityCreationRequest.class), any(Opportunity.class), any(Document.class));
	}

	/**
	 * Tests that the correct Opportunity information is populated when the initial
	 * opportunity is created
	 */
	@Test
	void testBuildInitialOpportunity() throws Exception {
		Opportunity opportunity = buildOpportunityForTest();
		assertEquals(3454, opportunity.getUploadEventID());
		assertEquals(454654, opportunity.getBookTransferID());
		assertEquals("67867", opportunity.getMasterOppID());
		assertEquals(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode(), opportunity.getStatus());
		assertEquals("Forrest's Insurer", opportunity.getOppPriorCarrier());

		assertNotNull(opportunity.getTimestampUpload());
		assertNotNull(opportunity.getOriginalXML());
		assertNull(opportunity.getData());
		assertEquals(LineType.Personal, opportunity.getLineType());
		assertEquals("nNumber", opportunity.getNNumber());
	}

	/**
	 * Tests that the correct information is updated when an Opportunity is updated
	 * from a new xl doc
	 */
	@Test
	void testUpdateOpportunity() throws Exception {
		Opportunity opportunity = buildOpportunityForTest();
		opportunity.setData(XmlHelper.getDocumentString(getTestDocument()));
		Mockito.doAnswer(invocationOnMock -> invocationOnMock.getArgument(1)).when(opportunityHelper)
				.updateNewOpportunity(any(OpportunityCreationRequest.class), any(Opportunity.class), any(Document.class));
		opportunity = opportunityConstructor.updateOpportunity(new OpportunityCreationRequest(), opportunity, getTestDocument());

		assertEquals(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode(), opportunity.getStatus());
		assertNotNull(opportunity.getData());

		Mockito.verify(opportunityHelper, Mockito.atLeastOnce()).updateNewOpportunity(any(OpportunityCreationRequest.class),
				any(Opportunity.class),
				any(Document.class));
	}

	@Test
	void testUpdateOpportunity_shouldRemoveXmlPrologue() throws Exception {
		Opportunity opportunity = buildOpportunityForTest();
		String xmlString = getStringFromFile("src/test/resources/xml/Jenny_Auto2.xml");
		opportunity.setData(xmlString);

		opportunity = opportunityConstructor.updateOpportunity(new OpportunityCreationRequest(), opportunity, getTestDocument());

		assertEquals(-1, opportunity.getData().indexOf("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>"));
	}

	@Test
	void testUpdateOpportunityWithNullMasterOppId() throws Exception {
		OpportunityCreationRequest opportunityCreationRequest = buildMockedOpportunityCreationRequest();
		opportunityCreationRequest.setMasterOppID(null);

		Opportunity opportunity = opportunityConstructor.buildInitialOpportunity(opportunityCreationRequest);

		assertNull(opportunity.getMasterOppID());
	}

	@Test
	void testUpdateOpportunityWithNonNullMasterOppId() throws Exception {
		OpportunityCreationRequest opportunityCreationRequest = buildMockedOpportunityCreationRequest();
		opportunityCreationRequest.setMasterOppID(12345);

		Opportunity opportunity = opportunityConstructor.buildInitialOpportunity(opportunityCreationRequest);

		assertEquals("12345", opportunity.getMasterOppID());
	}

	/**
	 * Tests that the xml is formatted and agent number is updated correctly
	 */
	@Test
	void testFormatXmlForOpportunity() throws Exception {
		Document xml = opportunityConstructor.formatXmlForOpportunity(getTestDocument(),
				buildMockedOpportunityCreationRequest());

		assertEquals("574789", XmlHelper.nullSafeExtractFromXml(xml, "//Producer/ProducerInfo/ContractNumber"));
	}


	@Test
	void testFormatXmlForOpportunity_NO_PRODUCERINFO() throws Exception {
		String producerInfoNode = "//ProducerInfo";
		Document xml = opportunityConstructor.formatXmlForOpportunity(getTestDocumentModified(producerInfoNode),
				buildMockedOpportunityCreationRequest());

		assertEquals("574789", XmlHelper.nullSafeExtractFromXml(xml, "//Producer/ProducerInfo/ContractNumber"));
	}

	@Test
	void testFormatXmlForOpportunity_NO_CONTRACT_NUMBER() throws Exception {
		String producerNode = "//ContractNumber";

		Document xml = opportunityConstructor.formatXmlForOpportunity(getTestDocumentModified(producerNode),
				buildMockedOpportunityCreationRequest());

		assertEquals("574789", XmlHelper.nullSafeExtractFromXml(xml, "//Producer/ProducerInfo/ContractNumber"));
	}

	/**
	 * Tests if a xml sting can be converted to a doc.
	 */
	@Test
	void testFormatAndConvertXMLFromString() throws ParserConfigurationException, SAXException, IOException {
		String acordToCleanUp = "<?xml-stylesheet type=\"text/xsl\" href='http://ecdev-tac.safeco.com/BookTransfer/TACML/WebAPI/scripts/xslt/BoatXMLView.xslt'?><ACORD>DATA</ACORD>";

		Document xml = opportunityConstructor.formatAndConvertXMLFromString(acordToCleanUp);

		assertNotNull(xml);
	}

	/**
	 * Tests that the insured is places above the coinsureds information in the xml
	 * order when there is one insured
	 */
	@Test
	void testInsuredAndCoInsuredStructureOneInsured() throws Exception {
		Document acordXml = XmlHelper
				.getXmlDocumentFromPath("src/test/resources/xml/insuredOrderDefect_One_Insured.xml");
		acordXml = opportunityConstructor.orderInsureds(acordXml);
		NodeList insuredNodes = XmlHelper.getNodeList(acordXml, "//InsuredOrPrincipal");
		assertEquals(1, insuredNodes.getLength());
		assertEquals(
				"Insured",
				XmlHelper.nullSafeExtractFromNode(insuredNodes.item(0), "InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd"));

	}

	/**
	 * Tests that the insured is places above the coinsureds information in the xml
	 * order
	 */
	@Test
	void testInsuredAndCoInsuredStructure() throws Exception {
		Document acordXml = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/insuredOrderDefect.xml");
		acordXml = opportunityConstructor.orderInsureds(acordXml);
		NodeList insuredNodes = XmlHelper.getNodeList(acordXml, "//InsuredOrPrincipal");

		assertEquals(2, insuredNodes.getLength());
		assertEquals(
				"Insured",
				XmlHelper.nullSafeExtractFromNode(insuredNodes.item(0), "InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd"));
		assertEquals(
				"Coinsured",
				XmlHelper.nullSafeExtractFromNode(insuredNodes.item(1), "InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd"));

	}

	/**
	 * Tests that the insured is places above the coinsureds information in the xml
	 * order when it is already formatted correctly
	 */
	@Test
	void testInsuredAndCoInsuredStructure_Normal() throws Exception {
		Document acordXml = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/commercialName.xml");
		acordXml = opportunityConstructor.orderInsureds(acordXml);
		NodeList insuredNodes = XmlHelper.getNodeList(acordXml, "//InsuredOrPrincipal");

		assertEquals(2, insuredNodes.getLength());
		assertEquals(
				"Insured",
				XmlHelper.nullSafeExtractFromNode(insuredNodes.item(0), "InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd"));
		assertEquals(
				"Coinsured",
				XmlHelper.nullSafeExtractFromNode(insuredNodes.item(1), "InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd"));

	}

	/**
	 * Tests that the insured is places above the coinsureds information when more
	 * than 2 insured nodes are involved
	 */
	@Test
	void testInsuredAndCoInsuredStructure_SPQE_3_Insureds() throws Exception {
		Document acordXml = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/SPQE_InsuredName.xml");
		acordXml = opportunityConstructor.orderInsureds(acordXml);
		NodeList insuredNodes = XmlHelper.getNodeList(acordXml, "//InsuredOrPrincipal");

		assertEquals(3, insuredNodes.getLength());
		assertEquals("Insured", XmlHelper.nullSafeExtractFromNode(insuredNodes.item(0), "InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd"));
		assertEquals("Coinsured", XmlHelper.nullSafeExtractFromNode(insuredNodes.item(1), "InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd"));
		assertEquals("", XmlHelper.nullSafeExtractFromNode(insuredNodes.item(2), "InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd"));

	}

	@Test
	void testRunRules_shouldCallOpportunityHelper() throws ParserConfigurationException, IOException, SAXException, TransformerException {
		Document inputXml = XmlHelper.getDocument("<ACORD></ACORD>");
		Document expectedXml = XmlHelper.getDocument("<Expected></Expected>");
		doReturn(expectedXml).when(transformationService).runRules(any(Document.class), any(String.class), any());

		Document actualXml = opportunityConstructor.runRules(inputXml, "10", null);

		assertEquals(XmlHelper.getDocumentString(expectedXml), XmlHelper.getDocumentString(actualXml));
		verify(transformationService, times(1)).runRules(any(Document.class), any(String.class), any());
	}

	private Document getTestDocument() throws Exception {
		String acordXml = getStringFromFile("src/test/resources/xml/commercialNameNamesMissing.xml");
		return XmlHelper.getDocument(acordXml);
	}

	private Document getTestDocumentModified(String nodeToRemove) throws Exception {
		String acordXml = getStringFromFile("src/test/resources/xml/commercialNameNamesMissing.xml");
		Document xmlDoc = XmlHelper.getDocument(acordXml);
		XmlHelper.deleteNode(xmlDoc, nodeToRemove);
		return xmlDoc;
	}

	private String getStringFromFile(String filePath) throws IOException {
		Path path = Paths.get(filePath);
		byte[] content = Files.readAllBytes(path);
		return new String(content);
	}

	private Opportunity buildOpportunityForTest() throws Exception {
		return opportunityConstructor.buildInitialOpportunity(buildMockedOpportunityCreationRequest());
	}

	private OpportunityCreationRequest buildMockedOpportunityCreationRequest() throws Exception {
		Path path = Paths.get("src/test/resources/xml/commercialNameNamesMissing.xml");
		byte[] content = Files.readAllBytes(path);

		Document acordXml = XmlHelper.getDocument(new String(content));

		return new OpportunityCreationRequest(acordXml, 3454, 454654, 456546, 67867, "Forrest's Insurer", "344574789",
				0, null, LineType.Personal, "nNumber","testnbd");
	}
}

package com.lmig.uscm.booktransfer.opportunity.mockito;

import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJpaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BuildMockObjects {

	@Autowired
	private OpportunityJpaRepository opportunityRepository;

	public Opportunity buildOpportunity(String data) {
		Opportunity opp = new Opportunity();
		opp.setData(data);
		return opportunityRepository.save(opp);
	}
}

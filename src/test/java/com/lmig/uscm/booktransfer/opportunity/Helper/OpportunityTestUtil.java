package com.lmig.uscm.booktransfer.opportunity.Helper;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.QuoteReportStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.mockito.domain.BookTransfer;
import com.lmig.uscm.booktransfer.opportunity.mockito.repos.BookTransferRepository;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJpaRepository;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.w3c.dom.Document;

import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public final class OpportunityTestUtil {

	private OpportunityTestUtil() {
	}

	public static Document generateXmlDocument(String path) {
		return generateXmlDocument(Paths.get(path));
	}

	public static Document generateXmlDocument(Path path) {
		try {
			byte[] content = Files.readAllBytes(path);
			String acordXml = new String(content).replaceAll("\n", "");
			return XmlHelper.getDocument(acordXml);
		} catch (Exception e) {
			throw new RuntimeException("Error getting xml", e);
		}
	}

	public static String generateStringData(Path path) throws IOException {
		return new String(Files.readAllBytes(path));
	}

	public static Opportunity saveCreatedDummyOpp(
		Integer status,
		String data,
		Integer bookTransferId,
		String orgXML,
		OpportunityJpaRepository opportunityRepository
	) {
		Opportunity createdOpp = createDummyOpportunity(status, data, bookTransferId, orgXML);
		createdOpp = opportunityRepository.save(createdOpp);
		return createdOpp;
	}

	public static Opportunity saveCreatedDummyBLOpp(
		Integer status,
		String data,
		Integer bookTransferId,
		String orgXML,
		OpportunityJpaRepository opportunityRepository
	) {
		Opportunity createdOpp = createDummyOpportunity(status, data, bookTransferId, orgXML);
		createdOpp.setLineType(LineType.Business);
		createdOpp.setBusinessType("WORK");
		createdOpp = opportunityRepository.save(createdOpp);
		return createdOpp;
	}

	public static Opportunity saveCreatedDummyOpp(
		Integer status,
		String data,
		Integer bookTransferId,
		String orgXML,
		LineType lineType,
		OpportunityJpaRepository opportunityRepository
	) {
		Opportunity createdOpp = createDummyOpportunity(status, data, bookTransferId, orgXML, lineType);
		createdOpp = opportunityRepository.save(createdOpp);
		return createdOpp;
	}

	public static Opportunity saveCreatedDummyOpp(
		LineOfBusiness lob,
		OpportunityStatus opportunityStatus,
		Integer bookTransferId,
		String priorCarrier,
		String effectiveDate,
		OpportunityJpaRepository opportunityRepository,
		String state,
		String quoteType
	) {
		Opportunity createdOpp =
				createDummyOpportunity(opportunityStatus.getOppStatusCode(), "", bookTransferId, "");
		createdOpp.setBusinessType(lob.getFirstPossibleLOBString());
		createdOpp.setEffectiveDate(effectiveDate);
		createdOpp.setOppPriorCarrier(priorCarrier);
		createdOpp.setState(state);
		createdOpp.setQuoteType(quoteType);
		createdOpp = opportunityRepository.save(createdOpp);
		return createdOpp;
	}


	public static Opportunity saveCreatedDummyOpp(
		LineOfBusiness lob,
		OpportunityStatus opportunityStatus,
		Integer bookTransferId,
		String priorCarrier,
		String effectiveDate,
		OpportunityJpaRepository opportunityRepository,
		String state
	) {
		Opportunity createdOpp =
				createDummyOpportunity(opportunityStatus.getOppStatusCode(), "", bookTransferId, "");
		createdOpp.setBusinessType(lob.getFirstPossibleLOBString());
		createdOpp.setEffectiveDate(effectiveDate);
		createdOpp.setOppPriorCarrier(priorCarrier);
		createdOpp.setState(state);
		createdOpp = opportunityRepository.save(createdOpp);
		return createdOpp;
	}

	public static Opportunity saveCreatedDummyOpp(
		LineOfBusiness lob,
		OpportunityStatus opportunityStatus,
		Integer bookTransferId,
		String priorCarrier,
		String effectiveDate,
		OpportunityJpaRepository opportunityRepository,
		String state,
		String agencyId,
		String xmlPath
	) throws IOException {
		String xml = OpportunityTestUtil.generateStringData(Paths.get(xmlPath));
		Opportunity createdOpp = createDummyOpportunity(opportunityStatus.getOppStatusCode(), xml, bookTransferId, xml);
		createdOpp.setBusinessType(lob.getFirstPossibleLOBString());
		createdOpp.setEffectiveDate(effectiveDate);
		createdOpp.setOppPriorCarrier(priorCarrier);
		createdOpp.setState(state);
		createdOpp.setAgencyId(OpportunityUtil.trimAgencyId(agencyId));
		createdOpp = opportunityRepository.save(createdOpp);
		return createdOpp;
	}

	public static Opportunity createDummyOpportunity(
			Integer status, @NotNull String data, Integer bookTransferId, String orgXML) {
		Opportunity createdOpp = new Opportunity();
		createdOpp.setStatus(status);
		createdOpp.setData(data);
		createdOpp.setBookTransferID(bookTransferId);
		createdOpp.setOriginalXML(orgXML);
		createdOpp.setLineType(LineType.Personal);
		return createdOpp;
	}

	public static Opportunity createDummyOpportunity(
			Integer status, @NotNull String data, Integer bookTransferId, String orgXML, LineType lineType) {
		Opportunity createdOpp = new Opportunity();
		createdOpp.setStatus(status);
		createdOpp.setData(data);
		createdOpp.setBookTransferID(bookTransferId);
		createdOpp.setOriginalXML(orgXML);
		createdOpp.setLineType(lineType);
		return createdOpp;
	}

	public static BookTransfer saveCreatedBookTransfer(
		int bookTransferID,
		int sFDCID,
		String agentNum,
		BookTransferRepository bookTransferRepository,
		String statCode,
		String status,
		String nbdRelationship
	) {
		BookTransfer bookT = new BookTransfer();
		bookT.setBookTransferID(bookTransferID);
		bookT.setsFDCID(sFDCID);
		bookT.setSalesforceCode("BT - " + sFDCID);
		bookT.setAgentNum(agentNum);
		bookT.setSubCode(statCode);
		bookT.setStatus(status);
		bookT.setnBDRelationship(nbdRelationship);
		bookT.setNnumber("n0312022");
		bookT = bookTransferRepository.save(bookT);
		return bookT;
	}

	public static BookTransfer saveCreatedBookTransfer(int bookTransferID,
													   int sFDCID,
													   String agentNum,
													   BookTransferRepository bookTransferRepository,
													   String status,
													   String nbdRelationship) {
		return saveCreatedBookTransfer(bookTransferID, sFDCID, agentNum, bookTransferRepository, null, status,
				nbdRelationship);
	}

	public static QuoteReportItemLegacy createDummyQRI(QuoteReportStatus status, String salesforceId) {
		QuoteReportItemLegacy qri = new QuoteReportItemLegacy();
		if (status != null) {
			qri.setStatus(status.getQuoteReportMessage());
		}
		qri.setQuoteSalesforceID(salesforceId);
		return qri;
	}

	public static Opportunity buildBusinessOpportunityWithId(int oppId) {
		Opportunity opp = buildOpportunityWithId(oppId, LineOfBusiness.AUTOP);
		opp.setLineType(LineType.Business);
		opp.setBusinessType("WORK");
		return opp;
	}

	public static Opportunity buildOpportunityWithId(int oppId) {
		return buildOpportunityWithId(oppId, LineOfBusiness.AUTOP);
	}

	public static Opportunity buildOpportunityWithId(int oppId, LineOfBusiness lob) {
		Opportunity opportunity2 = createDummyOpportunity(-1, "<Acord></Acord>", 54321, "<Acord></Acord>");
		opportunity2.setOpportunityId(oppId);
		opportunity2.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		opportunity2.setBusinessType(lob.getFirstPossibleLOBString());
		opportunity2.setLineType(LineType.Personal);
		return opportunity2;
	}

	public static Document getXmlDoc(Opportunity opp, OpportunityJDBCRepo opportunityRepository) throws Exception {
		String xmlString = opportunityRepository.findByOpportunityId(opp.getOpportunityId()).getData();
		return XmlHelper.getDocument(xmlString);
	}

	public static String getQueryForH2(String nolockQuery) {
		String newString = "";
		newString = nolockQuery.replaceAll(" with \\(nolock\\)", "");
		return newString;
	}
}

package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.stream.XMLStreamException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class BookTransferAttributeServiceTest {
	private BookTransferAttributeService staxBookTransferAttributeService;

	@BeforeEach
	public void setUp() {
		staxBookTransferAttributeService = new BookTransferAttributeService();
	}

	@Test
	void testAddCoverageAttributesForHome() throws XMLStreamException, IOException, SAXException,
			ParserConfigurationException, XPathExpressionException {
		//Given I have a coverage node not nested in vehicle node
		String xml = "<ACORD><PersPolicy><LOBCd>Home</LOBCd></PersPolicy><Coverage /></ACORD>";

		//When I try to add an attribute with unique id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		AssertPolicyLevelNodeAttributes("//Coverage", xmlDoc);
	}

	@Test
	void testAddCoverageAttributesAndRemoveExistingAttributes() throws ParserConfigurationException,
			SAXException, IOException, XPathExpressionException, XMLStreamException {
		//Given I have a coverage node not nested in vehicle node
		String xml = "<ACORD><PersPolicy><LOBCd>Home</LOBCd></PersPolicy><Coverage id=\"1\" /></ACORD>";

		//When I try to add an attribute with unique id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		AssertPolicyLevelNodeAttributes("//Coverage", xmlDoc);
	}

	@Test
	void testAddCoverageAttributesForAuto() throws XPathExpressionException, XMLStreamException, IOException,
			SAXException, ParserConfigurationException {
		//Given I have a coverage node nested in vehicle node
		String xml =
				"<ACORD><PersPolicy><LOBCd>Auto</LOBCd></PersPolicy><PersVeh id=\"veh1\"><Coverage /></PersVeh></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		Element covNode = (Element) XmlHelper.getNodeFromDoc("//Coverage", xmlDoc);
		//Then The coverage node Should have those two attributes
		assertVehicleChildNodeAttribute(covNode, "veh1");
	}

	@Test
	void testAddCoverageAttributesForAutoWithMultipleVehicles() throws ParserConfigurationException,
			SAXException, XPathExpressionException, IOException, XMLStreamException {
		String vehId = "veh";
		//Given I have a coverage node nested in vehicle node
		String xml = "<ACORD><PersPolicy><LOBCd>Auto</LOBCd></PersPolicy><PersVeh id=\"" + vehId
				+ "1\"><Coverage /></PersVeh><PersVeh id=\"" + vehId + "2\"><Coverage /></PersVeh></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		assertNotNull(xmlDoc);

		//Then The coverage node Should have those two attributes
		assertVehicleChildNode(vehId, xmlDoc, "//PersVeh", "Coverage");
	}

	@Test
	void testAddCoverageAttributesForAutoWithVehicleAndPolicyLevelCoverages()
			throws ParserConfigurationException, SAXException, XPathExpressionException, IOException,
			XMLStreamException {
		String vehId = "veh";
		//Given I have a coverage node nested in vehicle node and also in policy level
		String xml = "<ACORD><PersPolicy><LOBCd>Auto</LOBCd></PersPolicy><Coverage /><PersVeh id=\"" + vehId
				+ "1\"><Coverage /><Coverage /></PersVeh><PersVeh id=\"" + vehId
				+ "2\"><Coverage /><Coverage /></PersVeh></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		assertNotNull(xmlDoc);
		assertVehicleChildNode(vehId, xmlDoc, "//PersVeh", "Coverage");
		AssertPolicyLevelNodeAttributes("//Coverage", xmlDoc);
	}

	@Test
	void testAddCoverageAttributesForBoat() throws XMLStreamException, IOException, SAXException,
			ParserConfigurationException, XPathExpressionException {
		String vehId = "boat";
		//Given I have a coverage node nested in vehicle node
		String xml = "<ACORD><PersPolicy><LOBCd>Watercraft</LOBCd></PersPolicy><Watercraft id=\"" + vehId
				+ "1\"><Coverage /></Watercraft></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		assertNotNull(xmlDoc);

		//Then The coverage node Should have those two attributes
		assertVehicleChildNode(vehId, xmlDoc, "//Watercraft", "Coverage");
	}

	@Test
	void testAddDeductibleAttributesForHome() throws XMLStreamException, IOException, SAXException,
			ParserConfigurationException, XPathExpressionException {
		//Given I have a deductible node not nested in vehicle node
		String xml = "<ACORD><PersPolicy><LOBCd>Home</LOBCd></PersPolicy><Deductible /></ACORD>";

		//When I try to add an attribute with unique id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		AssertPolicyLevelNodeAttributes("//Deductible", xmlDoc);
	}

	@Test
	void testAddDeductibleAttributesAndRemoveExistingAttributes() throws ParserConfigurationException,
			SAXException, IOException, XPathExpressionException, XMLStreamException {
		//Given I have a deductible node not nested in vehicle node
		String xml = "<ACORD><PersPolicy><LOBCd>Home</LOBCd></PersPolicy><Deductible id=\"1\" /></ACORD>";

		//When I try to add an attribute with unique id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		AssertPolicyLevelNodeAttributes("//Deductible", xmlDoc);
	}

	@Test
	void testAddDeductibleAttributesForAuto() throws XPathExpressionException, XMLStreamException, IOException,
			SAXException, ParserConfigurationException {
		//Given I have a coverage node nested in vehicle node
		String xml =
				"<ACORD><PersPolicy><LOBCd>Auto</LOBCd></PersPolicy><PersVeh id=\"veh1\"><Deductible /></PersVeh></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		Element dedNode = (Element) XmlHelper.getNodeFromDoc("//Deductible", xmlDoc);
		//Then The deductivle node Should have those two attributes
		assertVehicleChildNodeAttribute(dedNode, "veh1");
	}

	@Test
	void testAddDeductibleAttributesForAutoWithMultipleVehicles() throws ParserConfigurationException,
			SAXException, XPathExpressionException, IOException, XMLStreamException {
		String vehId = "veh";
		//Given I have a deductible node nested in vehicle node
		String xml = "<ACORD><PersPolicy><LOBCd>Auto</LOBCd></PersPolicy><PersVeh id=\"" + vehId
				+ "1\"><Deductible /></PersVeh><PersVeh id=\"" + vehId + "2\"><Deductible /></PersVeh></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		assertNotNull(xmlDoc);

		//Then The coverage node Should have those two attributes
		assertVehicleChildNode(vehId, xmlDoc, "//PersVeh", "Deductible");
	}

	@Test
	void testAddDeductibleAttributesForAutoWithVehicleAndPolicyLevelDeductibles()
			throws ParserConfigurationException, SAXException, XPathExpressionException, IOException,
			XMLStreamException {
		String vehId = "veh";
		//Given I have a deductible node nested in vehicle node and also in policy level
		String xml = "<ACORD><PersPolicy><LOBCd>Auto</LOBCd></PersPolicy><Deductible /><PersVeh id=\"" + vehId
				+ "1\"><Deductible /><Deductible /></PersVeh><PersVeh id=\"" + vehId
				+ "2\"><Deductible /><Deductible /></PersVeh></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		assertNotNull(xmlDoc);
		assertVehicleChildNode(vehId, xmlDoc, "//PersVeh", "Deductible");
		AssertPolicyLevelNodeAttributes("//Deductible", xmlDoc);
	}

	@Test
	void testAddDeductibleAttributesForBoat() throws XMLStreamException, IOException, SAXException,
			ParserConfigurationException, XPathExpressionException {
		String vehId = "boat";
		//Given I have a deductible node nested in vehicle node
		String xml = "<ACORD><PersPolicy><LOBCd>Watercraft</LOBCd></PersPolicy><Watercraft id=\"" + vehId
				+ "1\"><Deductible /></Watercraft></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		assertNotNull(xmlDoc);

		//Then The deductible node Should have those two attributes
		assertVehicleChildNode(vehId, xmlDoc, "//Watercraft", "Deductible");
	}

	@Test
	void testAddCoverageDeductibleAttributesForHome() throws XMLStreamException, IOException, SAXException,
			ParserConfigurationException, XPathExpressionException {
		//Given I have a coverage and deductible node not nested in vehicle node
		String xml = "<ACORD><PersPolicy><LOBCd>Home</LOBCd></PersPolicy><Coverage><Deductible /></Coverage></ACORD>";

		//When I try to add an attribute with unique id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		AssertPolicyLevelNodeAttributes("//Coverage", xmlDoc);
		AssertPolicyLevelNodeAttributes("//Deductible", xmlDoc);
	}

	@Test
	void testAddCoverageDeductibleAttributesForAutoWithVehicleAndPolicyLevelCoverageDeductibles()
			throws ParserConfigurationException, SAXException, XPathExpressionException, IOException,
			XMLStreamException {
		String vehId = "veh";
		//Given I have a coverage and deductible node nested in vehicle node and also in policy level
		String xml =
				"<ACORD><PersPolicy><LOBCd>Auto</LOBCd></PersPolicy><Coverage><Deductible /></Coverage><PersVeh id=\""
						+ vehId
						+ "1\"><Coverage><Deductible /></Coverage><Coverage><Deductible /></Coverage></PersVeh><PersVeh id=\""
						+ vehId
						+ "2\"><Coverage><Deductible /></Coverage><Coverage><Deductible /></Coverage></PersVeh></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		assertNotNull(xmlDoc);
		assertVehicleChildNode(vehId, xmlDoc, "//PersVeh", "Coverage");
		AssertPolicyLevelNodeAttributes("//Coverage", xmlDoc);
		assertVehicleChildNode(vehId, xmlDoc, "//PersVeh", "Deductible");
		AssertPolicyLevelNodeAttributes("//Deductible", xmlDoc);
	}

	@Test
	void testAddCoverageAttributesWithMissingVehicleId_Auto() throws ParserConfigurationException, SAXException,
			IOException, XMLStreamException, XPathExpressionException {
		String vehId = "";
		//Given I have a coverage and deductible node nested in vehicle node and also in policy level
		String xml =
				"<ACORD><PersPolicy><LOBCd>Auto</LOBCd></PersPolicy><Coverage><Deductible /></Coverage><PersVeh><Coverage><Deductible /></Coverage><Coverage><Deductible /></Coverage></PersVeh><PersVeh><Coverage><Deductible /></Coverage><Coverage><Deductible /></Coverage></PersVeh></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		assertNotNull(xmlDoc);
		assertVehicleChildNode(vehId, xmlDoc, "//PersVeh", "Coverage");
		AssertPolicyLevelNodeAttributes("//Coverage", xmlDoc);
		assertVehicleChildNode(vehId, xmlDoc, "//PersVeh", "Deductible");
		AssertPolicyLevelNodeAttributes("//Deductible", xmlDoc);
	}

	@Test
	void testAddCoverageAttributesWithMissingVehicleId_Boat() throws ParserConfigurationException, SAXException,
			IOException, XMLStreamException, XPathExpressionException {
		String vehId = "";
		//Given I have a deductible node nested in vehicle node
		String xml =
				"<ACORD><PersPolicy><LOBCd>Watercraft</LOBCd></PersPolicy><Watercraft><Deductible /></Watercraft></ACORD>";
		//When I try to add an attribute with unique id and an attribute with vehicle id
		Document xmlDoc =
				XmlHelper.getDocument(staxBookTransferAttributeService.addBookTransferCoverageAttributes(xml));
		assertNotNull(xmlDoc);

		//Then The deductible node Should have those two attributes
		assertVehicleChildNode(vehId, xmlDoc, "//Watercraft", "Deductible");
	}

	private void assertVehicleChildNode(String vehId, Document xmlDoc, String path, String childNodeName)
			throws XPathExpressionException {
		NodeList vehNodes = XmlHelper.getNodeList(xmlDoc, path);
		//Then The coverage node Should have those two attributes
		for (int i = 0; i < vehNodes.getLength(); i++) {
			NodeList covNodes = ((Element) vehNodes.item(i)).getElementsByTagName(childNodeName);
			for (int j = 0; j < covNodes.getLength(); j++) {
				assertVehicleChildNodeAttribute((Element) covNodes.item(j), vehId + (i + 1));
			}
		}
	}

	private void assertVehicleChildNodeAttribute(Element node, String vehId) {
		//For vehicle level coverages/deductible there should be two attributes added.
		assertEquals(2, node.getAttributes().getLength());
		assertNotNull(node.getAttributeNode("btId"));
		assertEquals(vehId, node.getAttributeNode("bookTransferAssociatedNodeId").getTextContent());
	}

	private void AssertPolicyLevelNodeAttributes(String path, Document xmlDoc) throws XPathExpressionException {
		//For policy level or coverages/deductibles not nested in vehicle should have only one attribute
		Element node = (Element) XmlHelper.getNodeFromDoc(path, xmlDoc);
		assertEquals(1, node.getAttributes().getLength());
		assertNotNull(node.getAttributeNode("btId"));
	}
}

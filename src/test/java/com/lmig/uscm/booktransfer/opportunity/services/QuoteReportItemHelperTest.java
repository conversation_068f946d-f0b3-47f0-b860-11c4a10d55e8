package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.Helper.OpportunityTestUtil;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.assertj.core.api.Assertions.assertThat;

class QuoteReportItemHelperTest {

    private final QuoteReportService quoteReportService = new QuoteReportService(null, null) {
        @Override
        public QuoteReportItemLegacy getQuoteReportByOpportunityId(final int opportunityId) {
            QuoteReportItemLegacy returnItem = new QuoteReportItemLegacy();
            returnItem.setCustomerFirstName("Harvey");
            returnItem.setSubCode("12345678");
            return returnItem;
        }
    };

    private Opportunity createDummyOpportunityHelper(String resourcePath) throws IOException {
        return OpportunityTestUtil.createDummyOpportunity(
                3, new String(Files.readAllBytes(Paths.get(resourcePath))), 1, "x");
    }

    @Test
    void build() throws Exception {
        Opportunity opp = createDummyOpportunityHelper("src/test/resources/xml/lobCondo.xml");

        QuoteReportItemHelper instance = new QuoteReportItemHelper(quoteReportService);
        QuoteReportItemLegacy testItem = instance.build(opp, 90909, "12345678");

        assertThat(testItem.getCustomerFirstName()).isEqualTo("Harvey");
        assertThat(testItem.getSubCode()).isEqualTo("12345678");
        assertThat(testItem.getSafecoPolicyTerm()).isEqualTo("Annual");
        assertThat(testItem.getSalesforceID()).isEqualTo(90909);
    }

    @Test
    void buildForMissingData() throws Exception {
        Opportunity opp = createDummyOpportunityHelper("src/test/resources/xml/validateHome.xml");

        QuoteReportItemHelper instance = new QuoteReportItemHelper(quoteReportService);
        QuoteReportItemLegacy testItem = instance.build(opp, 90909, "12345678");

        assertThat(testItem.getStatus()).isEqualTo("Missing Required Data");
        assertThat(testItem.getExtra3()).isEqualTo("false");
    }

    @Test
    void buildIncludesStateAutoExpiredPremium() throws Exception {
        Opportunity opp = createDummyOpportunityHelper("src/test/resources/xml/autoWithExpPrem.xml");

        QuoteReportItemHelper instance = new QuoteReportItemHelper(quoteReportService);
        QuoteReportItemLegacy testItem = instance.build(opp, 90909, "12345678");

        assertThat(testItem.getExpiringPremium()).isEqualTo(300.50);
    }

    @Test
    void buildZerosEmptyStateAutoExpiredPremium() throws Exception {
        Opportunity opp = createDummyOpportunityHelper("src/test/resources/xml/autoWithEmptyExpPrem.xml");

        QuoteReportItemHelper instance = new QuoteReportItemHelper(quoteReportService);
        QuoteReportItemLegacy testItem = instance.build(opp, 90909, "12345678");

        assertThat(testItem.getExpiringPremium()).isEqualTo(0.0);
    }

    @Test
    void buildZerosMissingStateAutoExpiredPremium() throws Exception {
        Opportunity opp = createDummyOpportunityHelper("src/test/resources/xml/auto.xml");

        QuoteReportItemHelper instance = new QuoteReportItemHelper(quoteReportService);
        QuoteReportItemLegacy testItem = instance.build(opp, 90909, "12345678");

        assertThat(testItem.getExpiringPremium()).isEqualTo(0.0);
    }

    @Test
    void buildSetsCustomerFirstAndLastName() throws Exception {
        // Mock the XML document
        Opportunity opp = createDummyOpportunityHelper("src/test/resources/xml/autoInsuredOrPrincipalRoleCdInsured.xml");

        // Create instance of QuoteReportItemHelper
        QuoteReportItemHelper instance = new QuoteReportItemHelper(quoteReportService);

        // Execute the build method
        QuoteReportItemLegacy testItem = instance.build(opp, 90909, "12345678");

        // Assertions
        assertThat(testItem.getCustomerFirstName()).isEqualTo("Mike");
        assertThat(testItem.getCustomerLastName()).isEqualTo("Johnson");

    }

    @Test
    void buildSetsCustomerFirstAndLastNameForInsuredRoleCdIn() throws Exception {
        // Mock the XML document
        Opportunity opp = createDummyOpportunityHelper("src/test/resources/xml/autoInsuredOrPrincipalRoleCdIN.xml");

        // Create instance of QuoteReportItemHelper
        QuoteReportItemHelper instance = new QuoteReportItemHelper(quoteReportService);

        // Execute the build method
        QuoteReportItemLegacy testItem = instance.build(opp, 90909, "12345678");

        // Assertions
        assertThat(testItem.getCustomerFirstName()).isEqualTo("Phil");
        assertThat(testItem.getCustomerLastName()).isEqualTo("Lawrence");

    }

    @Test
    void buildSetsCustomerFirstAndLastNameForNoPrincipalROleValue() throws Exception {
        // Mock the XML document
        Opportunity opp = createDummyOpportunityHelper("src/test/resources/xml/autoInsuredOrPrincipal.xml");

        // Create instance of QuoteReportItemHelper
        QuoteReportItemHelper instance = new QuoteReportItemHelper(quoteReportService);

        // Execute the build method
        QuoteReportItemLegacy testItem = instance.build(opp, 90909, "12345678");

        // Assertions
        assertThat(testItem.getCustomerFirstName()).isEqualTo("ALICE");
        assertThat(testItem.getCustomerLastName()).isEqualTo("BERGQUIST");

    }


}

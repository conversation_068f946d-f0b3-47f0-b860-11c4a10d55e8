package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.Address;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.AddressCleanseResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.AddressCleanseResult;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.MultipleUnparsedAddressRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.addresscleanse.PropertyInfoResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import static org.mockito.Mockito.when;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

class AddressCleanseServiceTest {

    AddressCleanseHelper mockAddressCleanseHelper = mock(AddressCleanseHelper.class);

    PropertyInfoHelper propertyInfoHelper = mock(PropertyInfoHelper.class);

    AddressCleanseService addressCleanseService;

    AddressCleanseService spyDataHelper;

    @BeforeEach
    public void setupEachTest() {
        addressCleanseService = new AddressCleanseService(mockAddressCleanseHelper, propertyInfoHelper);
        spyDataHelper = Mockito.spy(addressCleanseService);
    }
    @Test
    void testAddressCleanseService_addCountiesToLocations() throws IOException, ParserConfigurationException, SAXException, XPathExpressionException {
        Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/home.xml");
        spyDataHelper.addCountiesToLocations(document);
        verify(spyDataHelper, times(1)).addCountiesToLocations(any());
    }

    @Test
    void testAddressCleanseService_addCountiesToLocations_LocationWithoutCounties() throws IOException, ParserConfigurationException, SAXException, XPathExpressionException {
        Document document = XmlHelper.getDocument("<Location><Addr><Addr1>1800 Wheelwright place</Addr1><City>Cary</City><State>NC</State>" +
                "<PostalCode>27519</PostalCode></Addr></Location>");
        AddressCleanseResponse mockResponse = createMockResponseWithCounty();
        ArgumentCaptor<MultipleUnparsedAddressRequest> requestCaptor = ArgumentCaptor.forClass(MultipleUnparsedAddressRequest.class);
        Mockito.when(mockAddressCleanseHelper.sendRequest(requestCaptor.capture())).thenReturn(mockResponse);
        PropertyInfoResponse propertyInfo = new PropertyInfoResponse();
        propertyInfo.setTerritory("233");
        propertyInfo.setDistanceToCoastalLine("7135");
        Mockito.when(propertyInfoHelper.getTerritoryAndCoastalLine(any(), any())).thenReturn(propertyInfo);
        spyDataHelper.addCountiesToLocations(document);

        assertEquals(1, requestCaptor.getAllValues().size(), "sendRequest should be called exactly once");

        NodeList locationNodes = XmlHelper.getNodeList(document, "//Location");

        for (int i = 0; i < locationNodes.getLength(); i++) {
            Node location = locationNodes.item(i);
            // Check if the county node exists in each location
            Node addrNode = XmlHelper.getNodeFromParentNode("//Addr", location);
            Node countyNode = XmlHelper.getNodeFromParentNode("//County", addrNode);
            assertNotNull(countyNode, "County node should exist in location: " + i);
           assertEquals("Wake", countyNode.getTextContent().trim(), "County value should match expected value");
        }
    }

    @Test
    void testAddressCleanseService_addCountiesToLocations_LocationsWithExistingCounties() throws IOException, ParserConfigurationException, SAXException, XPathExpressionException {
        Document document = XmlHelper.getDocument("<Location><Addr><Addr1>789 Wheelwright place</Addr1><City>Cary</City><State>NC</State>" +
                "<PostalCode>27519</PostalCode><County>Wake</County></Addr></Location>");
        spyDataHelper.addCountiesToLocations(document);
        NodeList locationNodes = XmlHelper.getNodeList(document, "//Location");

        for (int i = 0; i < locationNodes.getLength(); i++) {
            Node location = locationNodes.item(i);
            // Check if the county node exists in each location
            Node addrNode = XmlHelper.getNodeFromParentNode("//Addr", location);
            Node countyNode = XmlHelper.getNodeFromParentNode("//County", addrNode);
            assertNotNull(countyNode, "County node should exist in location: " + i);
            assertEquals("Wake", countyNode.getTextContent().trim(), "County value should match expected value");
        }
    }

    @Test
    void testAddressCleanseService_addCountiesToLocations_EmptyDocument() throws ParserConfigurationException, IOException, SAXException, XPathExpressionException {
        Document document = XmlHelper.getDocument("<Root></Root>");
        spyDataHelper.addCountiesToLocations(document);

        NodeList locationNodes = XmlHelper.getNodeList(document, "//Location");
        assertEquals(0, locationNodes.getLength(), "No locations should be present in the document");
        verify(mockAddressCleanseHelper, never()).sendRequest(any());
        verifyNoMoreInteractions(mockAddressCleanseHelper);
    }

    @Test
    void testAddressCleanseService_addCountiesToLocations_NullDocument() {
        assertThrows(NullPointerException.class, () -> spyDataHelper.addCountiesToLocations(null));
    }

    @Test
    void testAddressCleanseService_addCountiesToLocations_NullRequest() throws IOException, ParserConfigurationException, SAXException, XPathExpressionException {
        Document document = XmlHelper.getDocument("<Location><Addr><Addr1>1800 Wheelwright place</Addr1><City>Cary</City><State>NC</State>" +
                "<PostalCode>27519</PostalCode></Addr></Location>");
        // Mocking the sendRequest method to return null response
        when(mockAddressCleanseHelper.sendRequest(any())).thenReturn(null);
        spyDataHelper.addCountiesToLocations(document);
        // Verify that no county node is added to the document
        NodeList locationNodes = XmlHelper.getNodeList(document, "//Location");
        for (int i = 0; i < locationNodes.getLength(); i++) {
            Node location = locationNodes.item(i);
            Node addrNode = XmlHelper.getNodeFromParentNode("//Addr", location);
            Node countyNode = XmlHelper.getNodeFromParentNode("//County", addrNode);
            assertNull(countyNode, "County node should not exist in location: " + i);
        }
    }

    @Test
    void testAddressCleanseService_addCountiesToLocations_EmptyResponse() throws IOException, ParserConfigurationException, SAXException, XPathExpressionException {
        Document document = XmlHelper.getDocument("<Location><Addr><Addr1>1800 Wheelwright place</Addr1><City>Cary</City><State>NC</State>" +
                "<PostalCode>27519</PostalCode></Addr></Location>");
        // Mocking the sendRequest method to return an empty response
        AddressCleanseResponse mockResponse = new AddressCleanseResponse();
        when(mockAddressCleanseHelper.sendRequest(any())).thenReturn(mockResponse);
        spyDataHelper.addCountiesToLocations(document);
        // Verify that no county node is added to the document
        NodeList locationNodes = XmlHelper.getNodeList(document, "//Location");
        for (int i = 0; i < locationNodes.getLength(); i++) {
            Node location = locationNodes.item(i);
            Node addrNode = XmlHelper.getNodeFromParentNode("//Addr", location);
            Node countyNode = XmlHelper.getNodeFromParentNode("//County", addrNode);
            assertNull(countyNode, "County node should not exist in location: " + i);
        }
    }

    @Test
    void testTerritoryNodeForAuto() throws ParserConfigurationException, IOException, SAXException {
        Document document = XmlHelper.getDocument("<Location><Addr><Addr1>1800 Wheelwright place</Addr1><City>Cary</City><State>NC</State>" +
          "<PostalCode>27519</PostalCode></Addr></Location>");
        Node territoryNode = spyDataHelper.getTerritoryNode("PRF: , STD: , NSTD: ", document, "AUTOP");
        assertNotNull(territoryNode, "Territory node should not be null");
        assertEquals(3, territoryNode.getChildNodes().getLength());
    }

    @Test
    void testTerritoryNodeForNonAuto() throws ParserConfigurationException, IOException, SAXException {
        Document document = XmlHelper.getDocument("<Location><Addr><Addr1>1800 Wheelwright place</Addr1><City>Cary</City><State>NC</State>" +
          "<PostalCode>27519</PostalCode></Addr></Location>");
        String territory = "299";
        Node territoryNode = spyDataHelper.getTerritoryNode(territory, document, "HOME");
        assertNotNull(territoryNode, "Territory node should not be null");
        assertEquals(territory, territoryNode.getTextContent(), "Territory value should match expected value");
    }

    @Test
    void testTerritoryNodeWithNull() throws ParserConfigurationException, IOException, SAXException {
        Document document = XmlHelper.getDocument("<Location><Addr><Addr1>1800 Wheelwright place</Addr1><City>Cary</City><State>NC</State>" +
          "<PostalCode>27519</PostalCode></Addr></Location>");
        Node territoryNode = spyDataHelper.getTerritoryNode(null, document, "HOME");
        assertNotNull(territoryNode, "Territory node should not be null");
        assertEquals("", territoryNode.getTextContent(), "Territory value should match expected value");
    }

    @Test
    void testRatingTerritoryNode() throws ParserConfigurationException, IOException, SAXException {
        Document document = XmlHelper.getDocument("<Location><Addr><Addr1>1800 Wheelwright place</Addr1><City>Cary</City><State>NC</State>" +
          "<PostalCode>27519</PostalCode></Addr></Location>");
        String ratingTerritory = "233567";
        Node ratingTerritoryNode = spyDataHelper.getRatingTerritoryNode(ratingTerritory, document);
        assertNotNull(ratingTerritoryNode, "RatingTerritory node should not be null");
        assertEquals(ratingTerritory, ratingTerritoryNode.getTextContent(), "RatingTerritory value should match expected value");
    }

    @Test
    void testRatingTerritoryNodeWithNull() throws ParserConfigurationException, IOException, SAXException {
        Document document = XmlHelper.getDocument("<Location><Addr><Addr1>1800 Wheelwright place</Addr1><City>Cary</City><State>NC</State>" +
          "<PostalCode>27519</PostalCode></Addr></Location>");
        Node ratingTerritoryNode = spyDataHelper.getRatingTerritoryNode(null, document);
        assertNotNull(ratingTerritoryNode, "RatingTerritory node should not be null");
        assertEquals("", ratingTerritoryNode.getTextContent(), "RatingTerritory value should match expected value");
    }

    private AddressCleanseResponse createMockResponseWithCounty() {
        AddressCleanseResponse response = new AddressCleanseResponse();
        AddressCleanseResult result = new AddressCleanseResult();
        Address address = new Address();
        address.setCountyName("Wake County");
        result.setAddress(address);
        response.getAddresses().add(result);
        return response;
    }
}

package com.lmig.uscm.booktransfer.opportunity.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.uscm.booktransfer.opportunity.OpportunityApplication;
import com.lmig.uscm.booktransfer.opportunity.config.UnitTestConfig;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.LobGateway;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoAspect;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.*;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.FieldChangeHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate.UpdateEffectiveDateHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithAnonymousUser;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith({SpringExtension.class})
@SpringBootTest(classes = {OpportunityApplication.class, UnitTestConfig.class, OpportunityRepoAspect.class})
@TestPropertySource(properties = {"spring.profiles.active=unit,transformationClient,mongoTestContainer"})
public class PublicControllerSecurityTest{

    @Autowired
    private WebApplicationContext context;

    private MockMvc mvc;

    @MockBean
    OpportunityRepoHelper opportunityRepoHelper;

    @MockBean
    @Qualifier("opportunityHelper")
    OpportunityHelper opportunityHelper;

    @MockBean
    LobGateway lobGateway;

    @MockBean
    FieldChangeHelper fieldChangeHelper;

    @MockBean
    @Qualifier("trueLeadEffectiveDateHelper")
    UpdateEffectiveDateHelper updateEffectiveDateHelper;

    @MockBean
    OpportunityCreationHelper opportunityCreationHelper;

    @MockBean
    DownloadZipFileHelper downloadZipFileHelper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        mvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();
    }

    @WithAnonymousUser
    @Test
    void test_GET_safecoPolicyGUID_authorized() throws Exception {
        Opportunity ret = new Opportunity();
        ret.setLastPolicyGuid("guid");
        doReturn(ret).when(opportunityRepoHelper).findOpportunityById(anyInt());

        MvcResult mvcResult = mvc.perform(get("/public/opportunity/safecoPolicyGUID?oppId=123"))
                .andExpect(status().isOk()).andReturn();

        String response = mvcResult.getResponse().getContentAsString();

        assertEquals("guid", response);
    }

    @WithAnonymousUser
    @Test
    void test_GET_oppId_authorized() throws Exception {
        doReturn(List.of(123)).when(opportunityRepoHelper).getCustomFilterOppIds(any());

        MvcResult mvcResult = mvc.perform(get("/public/opportunity/oppId?safecoPolicyGUID=123"))
                .andExpect(status().isOk()).andReturn();

        String response = mvcResult.getResponse().getContentAsString();
        List<Integer> resultIds = objectMapper.readValue(response, new TypeReference<>() {
        });

        assertEquals(1, resultIds.size());
        assertEquals(123, resultIds.get(0));
    }
}

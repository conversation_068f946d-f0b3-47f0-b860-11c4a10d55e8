package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferServiceWebClient;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.EFTPaymentAccountsResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.MasterOppSPQEMeta;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OriginSource;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.SourceType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.SPQEOpportunity;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.config.MockReposTestConfig;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.FileContent;
import com.lmig.uscm.booktransfer.opportunity.messaging.Publisher;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.builders.BLUploadOpportunityBuilder;
import com.lmig.uscm.booktransfer.opportunity.services.builders.OpportunityBuilder;
import com.lmig.uscm.booktransfer.opportunity.services.builders.OpportunityConstructor;
import com.lmig.uscm.booktransfer.opportunity.services.builders.UploadOpportunityBuilder;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.CreationStrategy;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.CreationStrategyFactory;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.CreationStrategyName;
import com.lmig.uscm.booktransfer.opportunity.services.builders.strategies.MotorcycleCreationStrategy;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;
import org.w3c.dom.DOMException;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.Stack;
import java.util.concurrent.atomic.AtomicInteger;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {MockReposTestConfig.class})
@ExtendWith(MockitoExtension.class)
class OpportunityCreationHelperTest {

	@Mock
	private OpportunityHelper opportunityHelper;
	@Mock
	private BusinessOpportunityHelper businessOpportunityHelper;
	@Mock
	private OpportunityRepoHelper opportunityRepoHelper;
	private OpportunityConstructor opportunityConstructor;
	private OpportunityConstructor businessOpportunityConstructor;
	@Mock
	private Publisher publisher;
	@Mock
	private QuoteReportItemHelper quoteReportItemHelper;
	@Mock
	private BookTransferServiceWebClient bookTransferService;
	@Autowired
	private CreationStrategyFactory creationStrategyFactory;
	@Mock
	private OpportunityEventHelper opportunityEventHelper;
	@Mock
	private SensitiveDataHelper sensitiveDataHelper;
	@Mock
	private TransformationService transformationService;
	@Mock
	private AddressCleanseService addressCleanseService;
	@Mock
	private EFTPaymentAccountsService eftPaymentAccountsService;
	@Mock
	private CustomerAccountHelper customerAccountHelper;

	private OpportunityCreationHelper oppCreationHelper;

	private OpportunityBuilder oppBuilder;

	private final String VALIDATION_PACKAGE = "612d548bc5eef2601e472274";

	@Mock
	private static UploadEventService uploadEventService;

	@BeforeEach
	public void setup() throws Exception {
		ReflectionTestUtils.setField(opportunityHelper, "VALIDATION_PACKAGE", VALIDATION_PACKAGE);
		ReflectionTestUtils.setField(businessOpportunityHelper, "VALIDATION_PACKAGE", VALIDATION_PACKAGE);

		OpportunityConstructor businessOpportunityConstructorMock = new OpportunityConstructor(businessOpportunityHelper, transformationService);
		businessOpportunityConstructor = Mockito.spy(businessOpportunityConstructorMock);

		OpportunityConstructor opportunityConstructorMock = new OpportunityConstructor(opportunityHelper, transformationService);
		opportunityConstructor = Mockito.spy(opportunityConstructorMock);



		OpportunityCreationHelper oppCreationHelperMock = new OpportunityCreationHelper(
				opportunityRepoHelper, opportunityConstructor, businessOpportunityConstructor,
				quoteReportItemHelper, publisher, bookTransferService, opportunityEventHelper,
				creationStrategyFactory, uploadEventService, transformationService, sensitiveDataHelper,
				addressCleanseService, eftPaymentAccountsService, customerAccountHelper);
		oppCreationHelper = Mockito.spy(oppCreationHelperMock);
	}

	@Test
	void testCreationStrategyOrder() {
		List<CreationStrategy> allStrategies = new ArrayList<>(creationStrategyFactory.getAllStrategies());

		int splitBulkIndex = allStrategies.indexOf(creationStrategyFactory.findStrategy(CreationStrategyName.SplitBulkXmlByLobRq));
		int motorcycleIndex = allStrategies.indexOf(creationStrategyFactory.findStrategy(CreationStrategyName.SplitUpMotorcycles));
		int fifthCarIndex = allStrategies.indexOf(creationStrategyFactory.findStrategy(CreationStrategyName.SplitUpEveryFiveVehicles));
		int spqeLinkIndex = allStrategies.indexOf(creationStrategyFactory.findStrategy(CreationStrategyName.LinkSPQEOppsToPrevious));

		assertEquals(0, splitBulkIndex);
		assertTrue(motorcycleIndex < fifthCarIndex);
		assertTrue(spqeLinkIndex > fifthCarIndex);
	}

	@Test
	void testCreateOpportunityFromOpportunityRequest() throws Exception {
		oppBuilder = mock(UploadOpportunityBuilder.class);
		doReturn(oppBuilder).when(oppCreationHelper).opportunityBuilderFactory(anyBoolean(),
				any(OpportunityCreationRequest.class));
		doReturn(new Opportunity()).when(oppBuilder).uploadOpportunity(any());
		oppCreationHelper.createOpportunityFromOpportunityRequest(false, new OpportunityCreationRequest());
		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(anyInt());
		verify(oppBuilder, times(1)).uploadOpportunity(any(OpportunityCreationRequest.class));
	}

	@Test
	void testCreateOpportunityFromOpportunityRequestPL() throws Exception {
		oppBuilder = mock(UploadOpportunityBuilder.class);

		setupMocksForOppConstructorAndOppHelper();

		LineType lineType = LineType.Personal;
		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/accordhome.xml");

		OpportunityCreationRequest request = getOpportunityCreationRequest(lineType, document, false);

		final Opportunity[] pointerToSavedOpp = getOppArrayForCreateOpportunityTest();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(false, request);
		Opportunity modifiedMockOpp = pointerToSavedOpp[0];

		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(eq(request.getBookTransferID()));
		assertEquals(LineType.Personal, modifiedMockOpp.getLineType());
		assertEquals(1, response.getSuccessfulCreationCount());
	}

	@Test
	void testCreateOpportunityFromOpportunityRequestPLDefaultForNullLineType() throws Exception {
		oppBuilder = mock(UploadOpportunityBuilder.class);

		setupMocksForOppConstructorAndOppHelper();

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/accordhome.xml");

		OpportunityCreationRequest request = getOpportunityCreationRequest(null, document, false);

		final Opportunity[] pointerToSavedOpp = getOppArrayForCreateOpportunityTest();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(false, request);
		Opportunity modifiedMockOpp = pointerToSavedOpp[0];

		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(eq(request.getBookTransferID()));
		assertEquals(LineType.Personal, modifiedMockOpp.getLineType());
		assertEquals(1, response.getSuccessfulCreationCount());
	}

	@Test
	void testCreateOpportunityEnrichesPLOpportunity() throws Exception {
		oppBuilder = mock(UploadOpportunityBuilder.class);


		doCallRealMethod().when(opportunityConstructor).buildInitialOpportunity(any());
		doCallRealMethod().when(opportunityConstructor).updateOpportunity(any(), any(), any());
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(transformationService).runRules(any(Document.class), any(String.class), eq(null));
		//Need to set this to null due to spring config issues
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(businessOpportunityConstructor).runRules(any(Document.class), eq(null), any());
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(addressCleanseService).addCountiesToLocations(any(Document.class));

		Opportunity mockedOpp = new Opportunity();
		mockedOpp.setBusinessType("HOME");
		mockedOpp.setCustomerName("HAROLDRUTH WOOD");
		mockedOpp.setLineType(LineType.Personal);
		mockedOpp.setData("<ACORD></ACORD>");

		doReturn(mockedOpp).when(opportunityHelper).updateNewOpportunity(any(), any(), any());

		LineType lineType = LineType.Personal;

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/accordhome.xml");

		OpportunityCreationRequest request = getOpportunityCreationRequest(lineType, document, false);

		final Opportunity[] pointerToSavedOpp = getOppArrayForCreateOpportunityTest();
		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(false, request);
		Opportunity modifiedMockOpp = pointerToSavedOpp[0];

		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(eq(request.getBookTransferID()));
		assertEquals(LineType.Personal, modifiedMockOpp.getLineType());
		assertEquals("HOME", modifiedMockOpp.getBusinessType());
		assertEquals("HAROLDRUTH WOOD", modifiedMockOpp.getCustomerName());
		assertEquals(1, response.getSuccessfulCreationCount());

	}

	@Test
	void testCreateOpportunityEnrichesBLOpportunity() throws Exception {
		oppBuilder = mock(BLUploadOpportunityBuilder.class);

		setupMocksForBLOppConstructorAndBLOppHelper();

		LineType lineType = LineType.Business;
		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");

		OpportunityCreationRequest request = getOpportunityCreationRequest(lineType, document, false);

		final Opportunity[] pointerToSavedOpp = getOppArrayForCreateOpportunityTest();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(false, request);
		assertEquals(1, response.getSuccessfulCreationCount());

		Opportunity modifiedMockOpp = pointerToSavedOpp[0];

		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(eq(request.getBookTransferID()));
		assertEquals(LineType.Business, modifiedMockOpp.getLineType());
		assertEquals("WORK", modifiedMockOpp.getBusinessType());
		assertEquals("NY", modifiedMockOpp.getState());
		assertEquals(1000, modifiedMockOpp.getCustomerName().length());
		assertEquals("2019-12-29", modifiedMockOpp.getEffectiveDate());
		assertEquals("EASTHAM46", modifiedMockOpp.getAgencyId());
		assertNull(modifiedMockOpp.getNAICCd());
		assertEquals(1000.45, modifiedMockOpp.getPriorPremium());
		assertEquals("19958794", modifiedMockOpp.getPriorCarrierGuid());
		assertNull(modifiedMockOpp.getQuoteType());
		assertNotNull(modifiedMockOpp.getTimestampUpload());
		assertNotNull(modifiedMockOpp.getOriginalXML());
		assertNotNull(modifiedMockOpp.getData());
	}

	@Test
	void testReUploadOpportunities() throws Exception {
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(1);
		oppIds.add(2);
		oppCreationHelper.reuploadOpportunities(oppIds);
		verify(publisher, times(oppIds.size())).sendRequestToReUploadOpportunity(any(String.class));
	}

	@Test
	void testReuploadForSpqeSplitsAndLinks() throws Exception {
		boolean isForSpqe = true;
		int masterOppId = 100100;
		SPQEOpportunity masterSpqeOpp = SPQEOpportunity.builder()
				.opportunityId(100100)
				.lineOfBusiness("AUTOP")
				.build();
		String masterOppIdAsString = Integer.toString(masterOppId);
		int splitChildOppId = 909090;
		SPQEOpportunity splitChildOpp = SPQEOpportunity.builder()
				.opportunityId(909090)
				.lineOfBusiness("AUTOP")
				.build();
		int pseudoRandomOppId = 1;
		Document autoXmlNeedingSplit = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto12Vehs.xml");
		String autoXmlNeedingSplitAsString = XmlHelper.getDocumentString(autoXmlNeedingSplit);
		MasterOppSPQEMeta spqeExistingIdsMeta = new MasterOppSPQEMeta(Arrays.asList(masterOppId, splitChildOppId), Arrays.asList(masterSpqeOpp, splitChildOpp));

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(LineType.Personal, autoXmlNeedingSplit, false);
		opportunityCreationRequest.setMasterOppSPQEMeta(spqeExistingIdsMeta);

		Opportunity masterOpp = new Opportunity();
		masterOpp.setOpportunityId(masterOppId);
		masterOpp.setOriginalXML(autoXmlNeedingSplitAsString);
		masterOpp.setStatus(2);
		// ReUpload already has Id set
		masterOpp.setMasterOppID(masterOppIdAsString);

		Opportunity splitAutoChildOpp = new Opportunity();
		splitAutoChildOpp.setOpportunityId(splitChildOppId);
		splitAutoChildOpp.setOriginalXML(autoXmlNeedingSplitAsString);
		splitAutoChildOpp.setStatus(2);
		// ReUpload already has Id set
		splitAutoChildOpp.setMasterOppID(masterOppIdAsString);

		setupMocksForOppConstructorAndOppHelper();
		doCallRealMethod().when(opportunityHelper).checkForUpdatedSubCode(any(), any(), any());

		when(opportunityRepoHelper.findOpportunityById(masterOppId)).thenReturn(masterOpp);
		when(opportunityRepoHelper.findOpportunityById(splitChildOppId)).thenReturn(splitAutoChildOpp);

		final Map<Integer, Opportunity> pointerToSavedOpp = new LinkedHashMap<>();
		doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			if (opp.getOpportunityId() == 0) {
				opp.setOpportunityId(pseudoRandomOppId);
			}
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).save(any());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(3, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId, splitChildOppId, pseudoRandomOppId), response.getAssociatedOppIds());

		// Check masterOppIds don't get changed
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(splitChildOppId).getMasterOppID());
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(pseudoRandomOppId).getMasterOppID());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(3, savedOpps.size());

		// AUTOP vehicles
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(0).getData())).getLength());
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(1).getData())).getLength());
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(2).getData())).getLength());
	}

	@Test
	void testUploadForSpqeSplitsAndLinks() throws Exception {
		boolean isForSpqe = true;

		AtomicInteger pseudoRandomOppId = new AtomicInteger(1);
		Document autoXmlNeedingSplit = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto12Vehs.xml");
		MasterOppSPQEMeta spqeExistingIdsMeta = new MasterOppSPQEMeta();

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(LineType.Personal, autoXmlNeedingSplit, false);
		opportunityCreationRequest.setMasterOppSPQEMeta(spqeExistingIdsMeta);

		setupMocksForOppConstructorAndOppHelper();
		doCallRealMethod().when(opportunityHelper).checkForUpdatedSubCode(any(), any(), any());

		final Map<Integer, Opportunity> pointerToSavedOpp = new LinkedHashMap<>();
		doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			if (opp.getOpportunityId() == 0) {
				opp.setOpportunityId(pseudoRandomOppId.get());
				pseudoRandomOppId.getAndIncrement();
			}
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).save(any());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(3, response.getSuccessfulCreationCount());

		assertEquals(List.of(1,2,3), response.getAssociatedOppIds());

		// Check masterOppIds don't get changed
		assertEquals("1", pointerToSavedOpp.get(1).getMasterOppID());
		assertEquals("1", pointerToSavedOpp.get(2).getMasterOppID());
		assertEquals("1", pointerToSavedOpp.get(3).getMasterOppID());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(3, savedOpps.size());

		// AUTOP vehicles
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(0).getData())).getLength());
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(1).getData())).getLength());
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(2).getData())).getLength());
	}

	@Test
	void testReuploadAuto() throws Exception {
		boolean isForSpqe = false;
		int masterOppId = 100100;
		String masterOppIdAsString = Integer.toString(masterOppId);

		Document autoXmlOriginal = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto2.xml");
		String autoXmlOriginalString = XmlHelper.getDocumentString(autoXmlOriginal);

		// Create fake OpportunityCreationRequest to simulate re upload
		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, false);
		opportunityCreationRequest.setExistingOpportunityID(masterOppId);

		// Add fake node to request xml
		Document autoXmlData = XmlHelper.cloneDocument(autoXmlOriginal);
		XmlHelper.setNodesTextContent(autoXmlData, "//ACORD/Emma", "This is super fake.");
		String autoXmlDataString = XmlHelper.getDocumentString(autoXmlData);

		// Create opportunity that would have been created on normal upload
		Opportunity masterOpp = getMasterOpportunityForReUpload(masterOppId, masterOppIdAsString, autoXmlOriginalString, autoXmlDataString);
		masterOpp.setLineType(LineType.Personal);

		final Map<Integer, Opportunity> pointerToSavedOpp = setupMocksAndGetOppMap(masterOppId, masterOpp);

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId), response.getAssociatedOppIds());

		// Check that status is Unquoted or Missing Required Data
		assertThat(pointerToSavedOpp.get(masterOppId).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));

		// Check opp ids aren't changed
		assertEquals(masterOppId, pointerToSavedOpp.get(masterOppId).getOpportunityId());

		// Check masterOppIds dont get changed
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());

		// Check that fake node added earlier is no longer there
		Document newDataXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterOppId).getData());
		assertEquals("", XmlHelper.nullSafeExtractFromXml(newDataXml, "//ACORD/Emma"));
	}

	@Test
	void testReuploadMotorcycle() throws Exception {
		boolean isForSpqe = false;
		int masterOppId = 100100;
		String masterOppIdAsString = Integer.toString(masterOppId);

		Document mtrXmlOriginal = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/mtrAfterStrategy.xml");
		String mtrXmlOriginalString = XmlHelper.getDocumentString(mtrXmlOriginal);

		// Create fake OpportunityCreationRequest to simulate re upload
		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, false);
		opportunityCreationRequest.setExistingOpportunityID(masterOppId);

		// Add fake node to request xml
		Document mtrXmlData = XmlHelper.cloneDocument(mtrXmlOriginal);
		XmlHelper.setNodesTextContent(mtrXmlData, "//ACORD/Emma", "This is super fake.");
		String mtrXmlDataString = XmlHelper.getDocumentString(mtrXmlData);

		// Create opportunity that would have been created on normal upload
		Opportunity masterOpp = getMasterOpportunityForReUpload(masterOppId, masterOppIdAsString, mtrXmlOriginalString, mtrXmlDataString);
		masterOpp.setLineType(LineType.Personal);
		final Map<Integer, Opportunity> pointerToSavedOpp = setupMocksAndGetOppMap(masterOppId, masterOpp);

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId), response.getAssociatedOppIds());

		// Check that status is Unquoted or Missing Required Data
		assertThat(pointerToSavedOpp.get(masterOppId).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));

		// Check opp ids aren't changed
		assertEquals(masterOppId, pointerToSavedOpp.get(masterOppId).getOpportunityId());

		// Check masterOppIds dont get changed
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());

		// Check that fake node added earlier is no longer there
		Document newDataXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterOppId).getData());
		assertEquals("", XmlHelper.nullSafeExtractFromXml(newDataXml, "//ACORD/Emma"));
	}

	@Test
	void testReuploadHome() throws Exception {
		boolean isForSpqe = false;
		int masterOppId = 100100;
		String masterOppIdAsString = Integer.toString(masterOppId);

		Document homeXmlOriginal = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/home.xml");
		String homeXmlOriginalString = XmlHelper.getDocumentString(homeXmlOriginal);

		// Create fake OpportunityCreationRequest to simulate re upload
		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, false);
		opportunityCreationRequest.setExistingOpportunityID(masterOppId);

		// Add fake node to request xml
		Document homeXmlData = XmlHelper.cloneDocument(homeXmlOriginal);
		XmlHelper.setNodesTextContent(homeXmlData, "//ACORD/Emma", "This is super fake.");
		String homeXmlDataString = XmlHelper.getDocumentString(homeXmlData);

		// Create opportunity that would have been created on normal upload
		Opportunity masterOpp = getMasterOpportunityForReUpload(masterOppId, masterOppIdAsString, homeXmlOriginalString, homeXmlDataString);
		masterOpp.setLineType(LineType.Personal);

		final Map<Integer, Opportunity> pointerToSavedOpp = setupMocksAndGetOppMap(masterOppId, masterOpp);

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId), response.getAssociatedOppIds());

		// Check that status is Unquoted or Missing Required Data
		assertThat(pointerToSavedOpp.get(masterOppId).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));

		// Check opp ids aren't changed
		assertEquals(masterOppId, pointerToSavedOpp.get(masterOppId).getOpportunityId());

		// Check masterOppIds don't get changed
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());

		// Check that fake node added earlier is no longer there
		Document newDataXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterOppId).getData());
		assertEquals("", XmlHelper.nullSafeExtractFromXml(newDataXml, "//ACORD/Emma"));
	}

	@Test
	void testReuploadWork_converterV2() throws Exception {
		boolean isForSpqe = false;
		int masterOppId = 100100;
		String masterOppIdAsString = Integer.toString(masterOppId);

		Document workXmlOriginal = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");
		String workXmlOriginalString = XmlHelper.getDocumentString(workXmlOriginal);

		// Create fake OpportunityCreationRequest to simulate re upload
		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, false);
		opportunityCreationRequest.setExistingOpportunityID(masterOppId);
		opportunityCreationRequest.setData(workXmlOriginal);
		opportunityCreationRequest.setUploadedACORD(workXmlOriginal);

		// Add fake node to request xml
		Document workXmlData = XmlHelper.cloneDocument(workXmlOriginal);
		XmlHelper.setNodesTextContent(workXmlData, "//ACORD/Shawn", "This is super fake.");
		String workXmlDataString = XmlHelper.getDocumentString(workXmlData);

		// Create opportunity that would have been created on normal upload
		Opportunity masterOpp = getMasterOpportunityForReUpload(masterOppId, masterOppIdAsString, workXmlOriginalString, workXmlDataString);
		masterOpp.setLineType(LineType.Business);
		masterOpp.setBusinessType(LineOfBusiness.WORK.name());

		final Map<Integer, Opportunity> pointerToSavedOpp = setupMocksAndGetOppMap(masterOppId, masterOpp);

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId), response.getAssociatedOppIds());

		// Check that status is Unquoted or Missing Required Data
		assertThat(pointerToSavedOpp.get(masterOppId).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));

		// Check opp ids aren't changed
		assertEquals(masterOppId, pointerToSavedOpp.get(masterOppId).getOpportunityId());

		// Check masterOppIds don't get changed
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());

		// Check that fake node added earlier is no longer there
		Document newDataXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterOppId).getData());
		assertEquals("", XmlHelper.nullSafeExtractFromXml(newDataXml, "//ACORD/Shawn"));

		assertEquals("WORK", pointerToSavedOpp.get(masterOppId).getBusinessType());
	}

	@Test
	void testReuploadAutoSplitsAndCreatesMotorcycle() throws Exception {
		boolean isForSpqe = false;
		int masterOppId = 100100;
		int pseudoRandomOppId = 1;
		String masterOppIdAsString = Integer.toString(masterOppId);

		// contains 2 cars and 1 motorcycle
		Document autoMtrXmlOriginal = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/autoAndMtc.xml");
		String autoMtrXmlOriginalString = XmlHelper.getDocumentString(autoMtrXmlOriginal);

		// Create fake OpportunityCreationRequest to simulate re upload
		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, autoMtrXmlOriginal, false);
		opportunityCreationRequest.setExistingOpportunityID(masterOppId);

		// Add fake node to request xml
		Document mtrXmlData = XmlHelper.cloneDocument(autoMtrXmlOriginal);
		XmlHelper.setNodesTextContent(mtrXmlData, "//ACORD/Emma", "This is super fake.");
		String mtrXmlDataString = XmlHelper.getDocumentString(mtrXmlData);

		// Create opportunity that would have been created on normal upload
		Opportunity masterOpp = getMasterOpportunityForReUpload(masterOppId, masterOppIdAsString, autoMtrXmlOriginalString, mtrXmlDataString);
		masterOpp.setLineType(LineType.Personal);

		// pointer for Reupload
		final Map<Integer, Opportunity> pointerToSavedOpp = setupMocksAndGetOppMap(masterOppId, masterOpp);

		// point for Upload
		doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			if (opp.getOpportunityId() == 0) {
				opp.setOpportunityId(pseudoRandomOppId);
			}
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).save(any(Opportunity.class));

		assertEquals(3, AcordHelper.getPersVehs(XmlHelper.getDocument(masterOpp.getOriginalXML())).getLength());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(2, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId, pseudoRandomOppId), response.getAssociatedOppIds());

		// Check that status is Unquoted or Missing Required Data
		assertThat(pointerToSavedOpp.get(masterOppId).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));
		assertThat(pointerToSavedOpp.get(pseudoRandomOppId).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));

		// Check opp ids aren't changed
		assertEquals(masterOppId, pointerToSavedOpp.get(masterOppId).getOpportunityId());

		// Check masterOppIds change appropriately
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());
		assertNull(pointerToSavedOpp.get(pseudoRandomOppId).getMasterOppID());

		// Check that fake node added earlier is no longer there
		Document newDataXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterOppId).getData());
		assertEquals("", XmlHelper.nullSafeExtractFromXml(newDataXml, "//ACORD/Emma"));

		// Check vehicles on each opportunity
		Document autoXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterOppId).getOriginalXML());
		Document mtrXml = XmlHelper.getDocument(pointerToSavedOpp.get(pseudoRandomOppId).getOriginalXML());
		assertEquals(2, AcordHelper.getPersVehs(autoXml).getLength());
		assertEquals(1, AcordHelper.getPersVehs(mtrXml).getLength());
	}

	@Test
	void testReuploadAutoSplitsAndCreatesMultipleMotorcycles() throws Exception {
		boolean isForSpqe = false;
		int masterOppId = 100100;
		int pseudoRandomOppId1 = 1;
		int pseudoRandomOppId2 = 2;
		String masterOppIdAsString = Integer.toString(masterOppId);

		// contains 2 cars and 1 motorcycle
		Document autoMtrXmlOriginal = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/autoWithTwoCarsFiveMtc.xml");
		String autoMtrXmlOriginalString = XmlHelper.getDocumentString(autoMtrXmlOriginal);

		// Create fake OpportunityCreationRequest to simulate re upload
		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, autoMtrXmlOriginal, false);
		opportunityCreationRequest.setExistingOpportunityID(masterOppId);

		// Add fake node to request xml
		Document mtrXmlData = XmlHelper.cloneDocument(autoMtrXmlOriginal);
		XmlHelper.setNodesTextContent(mtrXmlData, "//ACORD/Emma", "This is super fake.");
		String mtrXmlDataString = XmlHelper.getDocumentString(mtrXmlData);

		// Create opportunity that would have been created on normal upload
		Opportunity masterOpp = getMasterOpportunityForReUpload(masterOppId, masterOppIdAsString, autoMtrXmlOriginalString, mtrXmlDataString);
		masterOpp.setLineType(LineType.Personal);

		// pointer for Reupload
		final Map<Integer, Opportunity> pointerToSavedOpp = setupMocksAndGetOppMap(masterOppId, masterOpp);

		Stack<Integer> idsToUse = new Stack<>();
		idsToUse.push(pseudoRandomOppId2);
		idsToUse.push(pseudoRandomOppId1);
		// pointer for Upload
		doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			if (opp.getOpportunityId() == 0) {
				opp.setOpportunityId(idsToUse.pop());
			}
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).save(any());

		assertEquals(7, AcordHelper.getPersVehs(XmlHelper.getDocument(masterOpp.getOriginalXML())).getLength());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(3, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId, pseudoRandomOppId1, pseudoRandomOppId2), response.getAssociatedOppIds());

		// Check that status is Unquoted or Missing Required Data
		assertThat(pointerToSavedOpp.get(masterOppId).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));
		assertThat(pointerToSavedOpp.get(pseudoRandomOppId1).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));
		assertThat(pointerToSavedOpp.get(pseudoRandomOppId2).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));

		// Check opp ids aren't changed
		assertEquals(masterOppId, pointerToSavedOpp.get(masterOppId).getOpportunityId());

		// Check masterOppIds change appropriately
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());
		assertEquals(String.valueOf(pseudoRandomOppId1), pointerToSavedOpp.get(pseudoRandomOppId1).getMasterOppID());
		assertEquals(String.valueOf(pseudoRandomOppId1), pointerToSavedOpp.get(pseudoRandomOppId2).getMasterOppID());

		// Check that fake node added earlier is no longer there
		Document newDataXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterOppId).getData());
		assertEquals("", XmlHelper.nullSafeExtractFromXml(newDataXml, "//ACORD/Emma"));

		// Check vehicles on each opportunity
		Document autoXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterOppId).getOriginalXML());
		Document mtr1Xml = XmlHelper.getDocument(pointerToSavedOpp.get(pseudoRandomOppId1).getOriginalXML());
		Document mtr2Xml = XmlHelper.getDocument(pointerToSavedOpp.get(pseudoRandomOppId2).getOriginalXML());
		assertEquals(2, AcordHelper.getPersVehs(autoXml).getLength());
		assertEquals(4, AcordHelper.getPersVehs(mtr1Xml).getLength());
		assertEquals(1, AcordHelper.getPersVehs(mtr2Xml).getLength());
	}

	private Map<Integer, Opportunity> setupMocksAndGetOppMap(int masterOppId, Opportunity masterOpp) throws Exception {
		lenient().doCallRealMethod().when(opportunityConstructor).updateOpportunity(any(), any(), any());
		lenient().doCallRealMethod().when(opportunityHelper).updateNewOpportunity(any(), any(), any());
		lenient().doCallRealMethod().when(opportunityHelper).validateMissingData(any());
		lenient().doCallRealMethod().when(opportunityHelper).checkForUpdatedSubCode(any(), any(), any());
		lenient().doCallRealMethod().when(opportunityHelper).updateOpportunityWithValidationResults(any(), any());
		lenient().doCallRealMethod().when(opportunityHelper).hasMissingData(any());
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(opportunityHelper).runRules(any(Document.class), any(String.class), any());
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(opportunityConstructor).runRules(any(Document.class), eq(null), any());
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(addressCleanseService).addCountiesToLocations(any(Document.class));
		lenient().when(opportunityRepoHelper.findOpportunityById(masterOppId)).thenReturn(masterOpp);

		final Map<Integer, Opportunity> pointerToSavedOpp = new LinkedHashMap<>();
		lenient().doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).updateOpportunity(any(Opportunity.class));
		lenient().doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).save(any(Opportunity.class));

		setupMocksForSensitiveDataHelper();

		return pointerToSavedOpp;
	}

	@Test
	void testUploadSplitsAutoAndLinks() throws Exception {
		boolean isForSpqe = false;
		int masterOppId = 100100;
		String masterOppIdAsString = Integer.toString(masterOppId);
		int splitChildOppId = 909090;
		int pseudoRandomOppId = 1;
		Document autoXmlNeedingSplit = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto12Vehs.xml");

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(LineType.Personal, autoXmlNeedingSplit, false);

		setupMocksForOppConstructorAndOppHelper();

		Stack<Integer> idsToUse = new Stack<>();
		idsToUse.push(pseudoRandomOppId);
		idsToUse.push(splitChildOppId);
		idsToUse.push(masterOppId);
		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromStack(idsToUse);

		doAnswer(invocation ->
				invocation.getArgument(0, Opportunity.class)).when(opportunityRepoHelper).updateOpportunity(any());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(3, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId, splitChildOppId, pseudoRandomOppId), response.getAssociatedOppIds());

		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(splitChildOppId).getMasterOppID());
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(pseudoRandomOppId).getMasterOppID());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(3, savedOpps.size());

		// AUTOP vehicles
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(0).getData())).getLength());
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(1).getData())).getLength());
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(2).getData())).getLength());
	}

	private Opportunity[] getOppArrayForCreateOpportunityTest() {
		final Opportunity[] pointerToSavedOpp = {null};
		doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			opp.setOpportunityId(12345);
			pointerToSavedOpp[0] = opp;
			return opp;
		}).when(opportunityRepoHelper).save(any());

		return pointerToSavedOpp;
	}

	private Map<Integer, Opportunity> getOppMapFromStack(Stack<Integer> idsToUse) {
		final Map<Integer, Opportunity> pointerToSavedOpp = new LinkedHashMap<>();

		doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			if (opp.getOpportunityId() == 0) {
				opp.setOpportunityId(idsToUse.pop());
			}
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).save(any());

		return pointerToSavedOpp;
	}

	@Test
	void testUploadSplitsMotorcycleAnd5thCarAndLinks() throws Exception {
		boolean isForSpqe = false;
		int masterOppId = 100100;
		String masterOppIdAsString = Integer.toString(masterOppId);
		int splitChildOppId = 909090;
		Document autoXmlNeedingSplit = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/autoMtcOnly6thCar.xml");

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(LineType.Personal, autoXmlNeedingSplit, false);

		setupMocksForOppConstructorAndOppHelper();

		final Map<Integer, Opportunity> pointerToSavedOpp = new LinkedHashMap<>();

		Stack<Integer> idsToUse = new Stack<>();
		idsToUse.push(splitChildOppId);
		idsToUse.push(masterOppId);
		doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			if (opp.getOpportunityId() == 0) {
				opp.setOpportunityId(idsToUse.pop());
			}
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			assertEquals(LineOfBusiness.MTR.name(), opp.getBusinessType());
			return opp;
		}).when(opportunityRepoHelper).save(any());

		doAnswer(invocation ->
				invocation.getArgument(0, Opportunity.class)).when(opportunityRepoHelper).updateOpportunity(any());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(2, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId, splitChildOppId), response.getAssociatedOppIds());

		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(splitChildOppId).getMasterOppID());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(2, savedOpps.size());

		// MTR vehicles
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(0).getData())).getLength());
		assertEquals(2, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(1).getData())).getLength());
	}

	@Test
	void testUploadSplitsMotorcycleAndLinks() throws Exception {
		boolean isForSpqe = false;
		int autoOppId = 100100;
		int mtrOppId = 909090;
		Document autoXmlNeedingSplit = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/autoAndMtc.xml");

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(LineType.Personal, autoXmlNeedingSplit, false);

		setupMocksForOppConstructorAndOppHelper();

		Stack<Integer> idsToUse = new Stack<>();
		idsToUse.push(mtrOppId);
		idsToUse.push(autoOppId);
		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromStack(idsToUse);

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(2, response.getSuccessfulCreationCount());

		assertEquals(List.of(autoOppId, mtrOppId), response.getAssociatedOppIds());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(2, savedOpps.size());

		assertEquals(2, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(0).getData())).getLength());
		assertEquals(1, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(1).getData())).getLength());

		assertNull(savedOpps.get(0).getMasterOppID());
		assertNull(savedOpps.get(1).getMasterOppID());
	}

	@Test
	void testUploadSplitsMotorcycleAndCorrectlySetsXmls() throws Exception {
		boolean isForSpqe = false;
		Document autoXmlNeedingSplit = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/autoWithTwoMtc.xml");

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, autoXmlNeedingSplit, false);

		setupMocksForOppConstructorAndOppHelper();

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(1, savedOpps.size());

		assertEquals(List.of(savedOpps.get(0).getOpportunityId()), response.getAssociatedOppIds());


	}

	@Test
	void testUploadSplitsAutoMixedAndLinks() throws Exception {
		boolean isForSpqe = false;
		int autoMasterOppId = 100100;
		int mtrMasterOppId = 100101;
		String autoMasterOppIdAsString = Integer.toString(autoMasterOppId);
		String mtrMasterOppIdAsString = Integer.toString(mtrMasterOppId);
		int autoSplitChildOppId = 909090;
		int mtrSplitChildOppId = 909091;
		Document autoXmlNeedingSplit = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/autoAndMtcBothSplit5thCar.xml");

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(LineType.Personal, autoXmlNeedingSplit, false);

		setupMocksForOppConstructorAndOppHelper();

		Stack<Integer> idsToUse = new Stack<>();
		idsToUse.push(mtrSplitChildOppId);
		idsToUse.push(mtrMasterOppId);
		idsToUse.push(autoSplitChildOppId);
		idsToUse.push(autoMasterOppId);
		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromStack(idsToUse);

		doAnswer(invocation ->
				invocation.getArgument(0, Opportunity.class)).when(opportunityRepoHelper).updateOpportunity(any());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(4, response.getSuccessfulCreationCount());

		assertEquals(List.of(autoMasterOppId, autoSplitChildOppId, mtrMasterOppId, mtrSplitChildOppId), response.getAssociatedOppIds());

		assertEquals(autoMasterOppIdAsString, pointerToSavedOpp.get(autoMasterOppId).getMasterOppID());
		assertEquals(mtrMasterOppIdAsString, pointerToSavedOpp.get(mtrMasterOppId).getMasterOppID());
		assertEquals(autoMasterOppIdAsString, pointerToSavedOpp.get(autoSplitChildOppId).getMasterOppID());
		assertEquals(mtrMasterOppIdAsString, pointerToSavedOpp.get(mtrSplitChildOppId).getMasterOppID());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(4, savedOpps.size());

		// AUTOP vehicles
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(0).getData())).getLength());
		assertEquals(1, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(1).getData())).getLength());
		// MTR vehicles
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(2).getData())).getLength());
		assertEquals(1, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(3).getData())).getLength());
	}

	@Test
	void testUploadSplitsAutoMixedAndLinks_checksForVehCount() throws Exception {
		boolean isForSpqe = false;
		Document autoXmlNeedingSplit = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/autoWithFiveCarsFiveMtc.xml");

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(LineType.Personal, autoXmlNeedingSplit, false);

		setupMocksForOppConstructorAndOppHelper();

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		doAnswer(invocation ->
				invocation.getArgument(0, Opportunity.class)).when(opportunityRepoHelper).updateOpportunity(any());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(4, response.getSuccessfulCreationCount());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(4, savedOpps.size());

		// AUTOP vehicles
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(0).getData())).getLength());
		assertEquals(1, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(1).getData())).getLength());
		// MTR vehicles
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(2).getData())).getLength());
		assertEquals(1, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(3).getData())).getLength());
	}

	@Test
	void testCreateOpportunityFromLargeTransferOpportunityRequest_noFile_throwsException() throws Exception {
		Document singleTransfer = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, singleTransfer, true);

		setupMocksForOppConstructorAndOppHelper();

		assertThrows(OpportunityException.class,
				() -> oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
						null, opportunityCreationRequest, null));
	}

	@Test
	void testLargeTransferSplitsAutoMixedAndLinks_checksFailedUpload() throws Exception {
		File singleTransfer = new File("src/test/resources/xml/autoAndMtcMasters.xml");
		List<FileContent> fileContents = ZipFileHelper.readFileContents(singleTransfer);

		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setSFDCID(12345);
		opportunityCreationRequest.setNNumber("nNumber");

		// Mock Setup
		doCallRealMethod().when(opportunityConstructor).buildInitialOpportunity(any(OpportunityCreationRequest.class));
		doCallRealMethod().when(opportunityConstructor).updateOpportunity(any(), any(), any());
		doCallRealMethod().when(opportunityHelper).updateNewOpportunity(any(), any(), any());
		doCallRealMethod().when(opportunityHelper).validateMissingData(any());
		//Need to set this to null due to spring config issues
		doAnswer(invocation -> invocation.getArgument(0))
				.when(opportunityConstructor).runRules(any(Document.class), eq(null), any());
		doAnswer(invocation -> invocation.getArgument(0))
				.when(addressCleanseService).addCountiesToLocations(any(Document.class));
		doAnswer(invocation -> invocation.getArgument(0))
				.when(opportunityRepoHelper).updateOpportunity(any(Opportunity.class));
		setupMocksForSensitiveDataHelper();

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				fileContents, opportunityCreationRequest, null).get();

		// Verify UploadEvent updated
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Split"));
		assertEquals(0, response.getFailedCreationCount());
		assertEquals(4, response.getSuccessfulCreationCount());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(4, savedOpps.size());

		// AUTOP vehicles
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(0).getData())).getLength());
		assertEquals(1, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(1).getData())).getLength());
		// MTR vehicles
		assertEquals(4, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(2).getData())).getLength());
		assertEquals(1, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(3).getData())).getLength());
		// Verify Master Ids linked properly
		assertEquals(String.valueOf(savedOpps.get(0).getOpportunityId()), savedOpps.get(0).getMasterOppID());
		assertEquals(String.valueOf(savedOpps.get(0).getOpportunityId()), savedOpps.get(1).getMasterOppID());
		assertEquals(String.valueOf(savedOpps.get(2).getOpportunityId()), savedOpps.get(2).getMasterOppID());
		assertEquals(String.valueOf(savedOpps.get(2).getOpportunityId()), savedOpps.get(3).getMasterOppID());
	}

	@Test
	void testCreateOpportunityFromLargeTransferOpportunityRequest_xmlFile() throws Exception {
		File singleTransfer = new File("src/test/resources/xml/auto.xml");
		List<FileContent> fileContents = ZipFileHelper.readFileContents(singleTransfer);

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, true);

		setupMocksForOppConstructorAndOppHelper();

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				fileContents, opportunityCreationRequest, null).get();


		// Verify UploadEvent updated
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Split"));
		assertEquals(0, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(1, savedOpps.size());
	}

	@Test
	void testCreateOpportunityFromLargeTransferOpportunityRequest_xmlFile_minimal_opp_creation_request() throws Exception {
		File singleTransfer = new File("src/test/resources/xml/auto.xml");
		List<FileContent> fileContents = ZipFileHelper.readFileContents(singleTransfer);

		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setBookTransferID(12345);
		opportunityCreationRequest.setSFDCID(12345);
		opportunityCreationRequest.setUploadEventID(12345);

		setupMocksForOppConstructorAndOppHelper();

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				fileContents, opportunityCreationRequest, null).get();

		// Verify UploadEvent updated
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Split"));
		// Verify SensitiveHelper called for each file
		verify(sensitiveDataHelper, times(0)).scrubXmlWithEnvCheck(any());
		verify(sensitiveDataHelper, times(1)).tokenizeXmlWithEnvCheck(any());

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(1, savedOpps.size());
	}

	@Test
	void testCreateOpportunityFromLargeTransferOpportunityRequest_archiveFile() throws Exception {
		// contains home.xml and auto12Vehs.xml
		File largeTransfer = new File("src/test/resources/xml/testArchiveDeep.zip");
		List<FileContent> fileContents = ZipFileHelper.readFileContents(largeTransfer);

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, true);

		setupMocksForOppConstructorAndOppHelper();

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				fileContents, opportunityCreationRequest, null).get();

		// Verify UploadEvent updated
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Split"));
		// Verify SensitiveHelper called for each file
		verify(sensitiveDataHelper, times(0)).scrubXmlWithEnvCheck(any());
		verify(sensitiveDataHelper, times(2)).tokenizeXmlWithEnvCheck(any());

		assertEquals(1, response.getFailedCreationCount());
		assertEquals(4, response.getSuccessfulCreationCount());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(4, savedOpps.size());
	}

	@Test
	void testCreateOpportunityFromLargeTransferOpportunityRequest_singleFile_unhandledFailedStrategy() throws Exception {
		File singleTransfer = new File("src/test/resources/xml/auto.xml");
		List<FileContent> fileContents = ZipFileHelper.readFileContents(singleTransfer);

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, true);

		setupMocksForOppConstructorAndOppHelper();

		doThrow(new OpportunityException(new DOMException((short) 5, "test")))
				.when(oppCreationHelper).createOpportunityFromOpportunityRequest(anyBoolean(), any(OpportunityCreationRequest.class));

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				fileContents, opportunityCreationRequest, null).get();

		// Verify UploadEvent updated
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Failed"));
		// Verify SensitiveHelper called for each file
		verify(sensitiveDataHelper, times(0)).scrubXmlWithEnvCheck(any());
		verify(sensitiveDataHelper, times(1)).tokenizeXmlWithEnvCheck(any());

		assertEquals(1, response.getFailedCreationCount());
		assertEquals(0, response.getSuccessfulCreationCount());
	}

	@Test
	void testCreateOpportunityFromLargeTransferOpportunityRequest_singleFile_handledFailedStrategy() throws Exception {
		File singleTransfer = new File("src/test/resources/xml/auto.xml");
		List<FileContent> fileContents = ZipFileHelper.readFileContents(singleTransfer);

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, true);

		setupMocksForOppConstructorAndOppHelper();

		OpportunityCreationResponse mockedResponse = new OpportunityCreationResponse();
		mockedResponse.addExceptions(new OpportunityException(new DOMException((short) 5, "test")));
		mockedResponse.incrementFailedCreationCount();

		doReturn(mockedResponse)
				.when(oppCreationHelper).runRequestThroughStrategies(anyBoolean(), any(OpportunityCreationRequest.class));

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				fileContents, opportunityCreationRequest, null).get();

		// Verify UploadEvent updated
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Failed"));
		// Verify SensitiveHelper called for each file
		verify(sensitiveDataHelper, times(0)).scrubXmlWithEnvCheck(any());
		verify(sensitiveDataHelper, times(1)).tokenizeXmlWithEnvCheck(any());

		assertEquals(1, response.getFailedCreationCount());
		assertEquals(0, response.getSuccessfulCreationCount());
	}

	@Test
	void testCreateOpportunityFromLargeTransferOpportunityRequest_singleFile_failedStrategy() throws Exception {
		File singleTransfer = new File("src/test/resources/xml/auto.xml");
		List<FileContent> fileContents = ZipFileHelper.readFileContents(singleTransfer);

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, true);

		setupMocksForOppConstructorAndOppHelper();

		MotorcycleCreationStrategy mockedStrategy = mock(MotorcycleCreationStrategy.class);
		doThrow(new DOMException((short) 5, "test"))
				.when(mockedStrategy).resolveCreationBundles(anyList(), any(OpportunityCreationResponse.class));

		CreationStrategyFactory mockedFactory = mock(CreationStrategyFactory.class);
		doReturn(List.of(mockedStrategy)).when(mockedFactory).getAllStrategies();

		ReflectionTestUtils.setField(oppCreationHelper, "creationStrategyFactory", mockedFactory);

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				fileContents, opportunityCreationRequest, null).get();

		// Verify UploadEvent updated
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Failed"));
		// Verify SensitiveHelper called for each file
		verify(sensitiveDataHelper, times(1)).tokenizeXmlWithEnvCheck(any());

		assertEquals(1, response.getFailedCreationCount());
		assertEquals(0, response.getSuccessfulCreationCount());
	}

	@Test
	@Disabled
	void testCreateOpportunityFromLargeTransferOpportunityRequest_multipleFiles_failedXmlSplitOnLob() throws Exception {
		String fileToThrowErrorOn = XmlHelper.getFileContentFromPath("src/test/resources/xml/auto12Vehs.xml");
		String fileToProcess = XmlHelper.getFileContentFromPath("src/test/resources/xml/auto.xml");
		List<FileContent> fileContents = Arrays.asList(
				new FileContent("error.xml", fileToThrowErrorOn),
				new FileContent("process.xml", fileToProcess));

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, true);
		opportunityCreationRequest.setSubCode(null);

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		setupMocksForOppConstructorAndOppHelper();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				fileContents, opportunityCreationRequest, null).get();

		// Verify UploadEvent updated
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Failed"));
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Split"));
		// Verify SensitiveHelper called for each file (first file fails before sensitization)
		verify(sensitiveDataHelper, times(0)).scrubXmlWithEnvCheck(any());
		verify(sensitiveDataHelper, times(1)).tokenizeXmlWithEnvCheck(any());

		assertEquals(1, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(1, savedOpps.size());

//		mockedXmlHelper.close();
	}


	private void setupMocksForSensitiveDataHelper() throws Exception {
		BookTransferDTO book = new BookTransferDTO(12345, "BT -12345", null, "n00000000", "status");
		book.setNBDRelationship("testnbd");
		lenient().doReturn(book).when(bookTransferService).getTopBySFDCIDOrderByBookTransferID(anyInt());
		lenient().doNothing().when(sensitiveDataHelper).tokenizeXmlWithEnvCheck(any());
	}

	private void setUpMocksForEFTPaymentAccounts(){
		EFTPaymentAccountsResponse mockData = EFTPaymentAccountsResponse.builder()
				.accountPaymentType("EFT")
				.accountStatus("ACT")
				.accHldrFirstName("DAVE")
				.accHldrMiddleName("X")
				.accHldrLastName("CALIFORNIA")
				.acctHldrFullName("DAVE X CALIFORNIA")
				.accountEditEligibility("N")
				.accountDeleteEligibility("N")
				.accountOwnershipType("Personal")
				.bankAccountNumber("**********")
				.bankAccountType("Checking")
				.paymentAccountToken("7552751708534509635")
				.paymentAccountUsageType("O")
				.bankInstitutionName("TEST Bank")
				.bankRoutingNumber("*********")
				.financialDetailActId("***********")
				.lastActivityCode("EFT")
				.lastActivityDate("2024-02-21 11:55:10.710172")
				.merchantIdCd("067681")
				.build();

		lenient().doReturn(mockData).when(eftPaymentAccountsService).getEFTPaymentAccountsDetails(any());
	}

	@Test
	void testCreateOpportunityFromLargeTransferOpportunityRequest_bulkFile_tePackageId() throws Exception {
		File singleTransfer = new File("src/test/resources/xml/bulkFileNeedingSplit.xml");
		List<FileContent> fileContents = ZipFileHelper.readFileContents(singleTransfer);
		String tePackageId = "12345";

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, true);

		setupMocksForOppConstructorAndOppHelper();

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				fileContents, opportunityCreationRequest, tePackageId).get();

		// Verify runRules was called with tePackageId
		verify(transformationService, times(1)).runRules(any(), eq(tePackageId), eq(null));

		// Verify UploadEvent updated
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Split"));
		assertEquals(0, response.getFailedCreationCount());
		assertEquals(16, response.getSuccessfulCreationCount());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(16, savedOpps.size());
	}

	@Test
	void testCreateOpportunityFromLargeTransferOpportunityRequest_archiveFile_tePackageId() throws Exception {
		File largeTransfer = new File("src/test/resources/xml/testArchiveDeep.zip");
		List<FileContent> fileContents = ZipFileHelper.readFileContents(largeTransfer);
		String tePackageId = "12345";

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, true);

		setupMocksForOppConstructorAndOppHelper();

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromLargeTransferOpportunityRequest(
				fileContents, opportunityCreationRequest, tePackageId).get();

		// Verify runRules was called with tePackageId
		verify(transformationService, times(2)).runRules(any(), eq(tePackageId), eq(null));

		// Verify UploadEvent updated
		verify(uploadEventService, times(1)).updateUploadEventStatus(any(), eq("Split"));
		assertEquals(1, response.getFailedCreationCount());
		assertEquals(4, response.getSuccessfulCreationCount());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(4, savedOpps.size());
	}

	@Test
	void testUploadOpportunity_btMeta_originUnknown() throws Exception {
		oppBuilder = mock(UploadOpportunityBuilder.class);

		setupMocksForOppConstructorAndOppHelper();

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");

		OpportunityCreationRequest request = getOpportunityCreationRequest(LineType.Personal, document, false);
		request.setOriginSource(null);
		request.setSourceType(null);

		final Opportunity[] pointerToSavedOpp = getOppArrayForCreateOpportunityTest();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(false, request);
		assertEquals(1, response.getSuccessfulCreationCount());

		Opportunity modifiedMockOpp = pointerToSavedOpp[0];

		Document originalXml = XmlHelper.getDocument(modifiedMockOpp.getOriginalXML());

		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(eq(request.getBookTransferID()));
		assertEquals(LineType.Personal, modifiedMockOpp.getLineType());
		assertEquals("AUTOP", modifiedMockOpp.getBusinessType());
		assertNull(modifiedMockOpp.getQuoteType());
		assertNotNull(modifiedMockOpp.getTimestampUpload());
		assertEquals("UNKNOWN", AcordHelper.getBTMetaCreationOrigin(originalXml));
		assertEquals("Unknown", AcordHelper.getBTMetaCreationType(originalXml));
		assertNotNull(modifiedMockOpp.getData());
	}

	@Test
	void testUploadOpportunity_btMeta_originKnown() throws Exception {
		oppBuilder = mock(UploadOpportunityBuilder.class);

		setupMocksForOppConstructorAndOppHelper();

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");

		OpportunityCreationRequest request = getOpportunityCreationRequest(LineType.Personal, document, false);
		request.setOriginSource(OriginSource.AQE);
		request.setSourceType(SourceType.XML_EXTRACTION);

		final Opportunity[] pointerToSavedOpp = getOppArrayForCreateOpportunityTest();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(false, request);
		assertEquals(1, response.getSuccessfulCreationCount());

		Opportunity modifiedMockOpp = pointerToSavedOpp[0];

		Document originalXml = XmlHelper.getDocument(modifiedMockOpp.getOriginalXML());

		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(eq(request.getBookTransferID()));
		assertEquals(LineType.Personal, modifiedMockOpp.getLineType());
		assertEquals("AUTOP", modifiedMockOpp.getBusinessType());
		assertNull(modifiedMockOpp.getQuoteType());
		assertNotNull(modifiedMockOpp.getTimestampUpload());
		assertEquals("AQE", AcordHelper.getBTMetaCreationOrigin(originalXml));
		assertEquals("xmlExtraction", AcordHelper.getBTMetaCreationType(originalXml));
		assertNotNull(modifiedMockOpp.getData());
	}

	@Test
	void testUploadBLOpportunity_btMeta_originKnown_aqeFlow() throws Exception {
		oppBuilder = mock(BLUploadOpportunityBuilder.class);

		setupMocksForBLOppConstructorAndBLOppHelper();

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");

		OpportunityCreationRequest request = getOpportunityCreationRequest(LineType.Business, document, false);
		request.setOriginSource(OriginSource.AQE);
		request.setSourceType(SourceType.XML_EXTRACTION);

		final Opportunity[] pointerToSavedOpp = getOppArrayForCreateOpportunityTest();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(false, request);
		assertEquals(1, response.getSuccessfulCreationCount());

		Opportunity modifiedMockOpp = pointerToSavedOpp[0];

		Document originalXml = XmlHelper.getDocument(modifiedMockOpp.getOriginalXML());

		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(eq(request.getBookTransferID()));
		assertEquals(LineType.Business, modifiedMockOpp.getLineType());
		assertEquals("WORK", modifiedMockOpp.getBusinessType());
		assertNull(modifiedMockOpp.getQuoteType());
		assertNotNull(modifiedMockOpp.getTimestampUpload());
		assertEquals("AQE", AcordHelper.getBTMetaCreationOrigin(originalXml));
		assertEquals("xmlExtraction", AcordHelper.getBTMetaCreationType(originalXml));
		assertNotNull(modifiedMockOpp.getData());
	}

	@Test
	void testUploadOpportunity_btMeta_originKnown_fifthCarSplitsInheritMeta() throws Exception {
		oppBuilder = mock(UploadOpportunityBuilder.class);

		setupMocksForOppConstructorAndOppHelper();

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto12Vehs.xml");

		OpportunityCreationRequest request = getOpportunityCreationRequest(LineType.Personal, document, false);
		request.setOriginSource(OriginSource.AQE);
		request.setSourceType(SourceType.XML_EXTRACTION);

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(false, request);
		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(eq(request.getBookTransferID()));
		assertEquals(3, response.getSuccessfulCreationCount());

		List<Opportunity> foundOpps = new ArrayList<>(pointerToSavedOpp.values());

		assertEquals(3, foundOpps.size());

		for (Opportunity opp : foundOpps) {
			Document originalXml = XmlHelper.getDocument(opp.getOriginalXML());
			assertEquals(LineType.Personal, opp.getLineType());
			assertEquals("AUTOP", opp.getBusinessType());
			assertNull(opp.getQuoteType());
			assertNotNull(opp.getTimestampUpload());
			assertEquals("AQE", AcordHelper.getBTMetaCreationOrigin(originalXml));
			assertEquals("xmlExtraction", AcordHelper.getBTMetaCreationType(originalXml));
			assertNotNull(opp.getData());
		}
	}

	@Test
	void testUploadOpportunity_btMeta_originKnown_mtrSplitsInheritMeta() throws Exception {
		oppBuilder = mock(UploadOpportunityBuilder.class);

		setupMocksForOppConstructorAndOppHelper();

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/autoAndMtc.xml");

		OpportunityCreationRequest request = getOpportunityCreationRequest(LineType.Personal, document, false);
		request.setOriginSource(OriginSource.AQE);
		request.setSourceType(SourceType.XML_EXTRACTION);

		final Map<Integer, Opportunity> pointerToSavedOpp = getOppMapFromRandom();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(false, request);
		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(eq(request.getBookTransferID()));
		assertEquals(2, response.getSuccessfulCreationCount());

		List<Opportunity> foundOpps = new ArrayList<>(pointerToSavedOpp.values());

		assertEquals(2, foundOpps.size());

		for (Opportunity opp : foundOpps) {
			Document originalXml = XmlHelper.getDocument(opp.getOriginalXML());
			assertEquals(LineType.Personal, opp.getLineType());
			assertNull(opp.getQuoteType());
			assertNotNull(opp.getTimestampUpload());
			assertEquals("AQE", AcordHelper.getBTMetaCreationOrigin(originalXml));
			assertEquals("xmlExtraction", AcordHelper.getBTMetaCreationType(originalXml));
			assertNotNull(opp.getData());
		}
	}

	/*
		GIVEN the btMeta origin source and type is known
		WHEN I reupload the opportunity
		THEN the originalXML should have the previously known source and type
	 */
	@Test
	void testReuploadOpportunity_btMeta_originKnown() throws Exception {
		boolean isForSpqe = false;
		int masterOppId = 100100;
		String masterOppIdAsString = Integer.toString(masterOppId);

		Document homeXmlOriginal = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/home.xml");
		AcordHelper.setBTMetaCreationOrigin(homeXmlOriginal, OriginSource.AQE.getValue());
		AcordHelper.setBTMetaCreationType(homeXmlOriginal, SourceType.XML_EXTRACTION.getValue());
		String homeXmlOriginalString = XmlHelper.getDocumentString(homeXmlOriginal);

		// Create fake OpportunityCreationRequest to simulate re upload
		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(LineType.Personal, null, false);
		opportunityCreationRequest.setExistingOpportunityID(masterOppId);
		opportunityCreationRequest.setOriginSource(null);
		opportunityCreationRequest.setSourceType(null);

		// Create opportunity that would have been created on normal upload
		Opportunity masterOpp = getMasterOpportunityForReUpload(masterOppId, masterOppIdAsString, homeXmlOriginalString, homeXmlOriginalString);
		masterOpp.setLineType(LineType.Personal);

		final Map<Integer, Opportunity> pointerToSavedOpp = setupMocksAndGetOppMap(masterOppId, masterOpp);

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId), response.getAssociatedOppIds());

		// Check that status is Unquoted or Missing Required Data
		assertThat(pointerToSavedOpp.get(masterOppId).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));

		// Check opp ids aren't changed
		assertEquals(masterOppId, pointerToSavedOpp.get(masterOppId).getOpportunityId());

		// Check masterOppIds don't get changed
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());

		// Check that BT metadata is kept
		Document newDataXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterOppId).getData());
		assertEquals("AQE", AcordHelper.getBTMetaCreationOrigin(newDataXml));
		assertEquals("xmlExtraction", AcordHelper.getBTMetaCreationType(newDataXml));
	}

	/*
		GIVEN the btMeta origin source and type is SPQE
		WHEN I reupload the opportunity
		THEN the originalXML should have the source and type for SPQE
	 */
	@Test
	void testReuploadOpportunity_btMeta_originKnown_SPQE() throws Exception {
		boolean isForSpqe = true;
		int masterSpqeOppId = 100100;
		MasterOppSPQEMeta spqeExistingIdsMeta = new MasterOppSPQEMeta(List.of(masterSpqeOppId));

		Document homeXmlOriginal = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/home.xml");
		Document homeXmlForReplacement = (Document) homeXmlOriginal.cloneNode(true);
		AcordHelper.setBTMetaCreationOrigin(homeXmlOriginal, OriginSource.SPQE.getValue());
		AcordHelper.setBTMetaCreationType(homeXmlOriginal, SourceType.CSV_MAPPING.getValue());
		String homeXmlOriginalString = XmlHelper.getDocumentString(homeXmlOriginal);

		// Create fake OpportunityCreationRequest to simulate re upload
		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, homeXmlForReplacement, false);
		opportunityCreationRequest.setExistingOpportunityID(masterSpqeOppId);
		opportunityCreationRequest.setMasterOppSPQEMeta(spqeExistingIdsMeta);
		opportunityCreationRequest.setOriginSource(null);
		opportunityCreationRequest.setSourceType(null);

		// Create opportunity that would have been created on normal upload
		Opportunity masterOpp = getMasterOpportunityForReUpload(masterSpqeOppId, null, homeXmlOriginalString, homeXmlOriginalString);
		masterOpp.setLineType(LineType.Personal);

		final Map<Integer, Opportunity> pointerToSavedOpp = setupMocksAndGetOppMap(masterOpp.getOpportunityId(), masterOpp);

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(1, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterSpqeOppId), response.getAssociatedOppIds());

		// Check that status is Unquoted or Missing Required Data
		assertThat(pointerToSavedOpp.get(masterSpqeOppId).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));

		// Check opp ids aren't changed
		assertEquals(masterSpqeOppId, pointerToSavedOpp.get(masterSpqeOppId).getOpportunityId());

		// Check masterOppIds don't get changed
		assertNull(pointerToSavedOpp.get(masterSpqeOppId).getMasterOppID());

		// Check that BT Meta data is kept
		Document newDataXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterSpqeOppId).getData());
		assertEquals("SPQE", AcordHelper.getBTMetaCreationOrigin(newDataXml));
		assertEquals("csvMapping", AcordHelper.getBTMetaCreationType(newDataXml));
	}

	@Test
	void testUploadOpportunity_btMeta_originKnown_SPQE() throws Exception {
		boolean isForSpqe = true;

		Document homeXmlOriginal = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/home.xml");
		int pseudoRandomOppId = 1;

		MasterOppSPQEMeta spqeExistingIdsMeta = new MasterOppSPQEMeta();

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(LineType.Personal, homeXmlOriginal, false);
		opportunityCreationRequest.setMasterOppSPQEMeta(spqeExistingIdsMeta);

		setupMocksForOppConstructorAndOppHelper();
		doCallRealMethod().when(opportunityHelper).checkForUpdatedSubCode(any(), any(), any());

		final Map<Integer, Opportunity> pointerToSavedOpp = new LinkedHashMap<>();
		doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			if (opp.getOpportunityId() == 0) {
				opp.setOpportunityId(pseudoRandomOppId);
			}
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).save(any());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(1, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		assertEquals(List.of(pseudoRandomOppId), response.getAssociatedOppIds());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(1, savedOpps.size());
	}

	/*
		GIVEN the btMeta origin source and type is unknown
		WHEN I reupload the opportunity
		THEN the originalXML should have source and type set to unknown
	 */
	@Test
	void testReuploadOpportunity_btMeta_originUnknown() throws Exception {
		boolean isForSpqe = false;
		int masterOppId = 100100;
		String masterOppIdAsString = Integer.toString(masterOppId);

		Document homeXmlOriginal = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/home.xml");
		String homeXmlOriginalString = XmlHelper.getDocumentString(homeXmlOriginal);

		// Create fake OpportunityCreationRequest to simulate re upload
		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(null, null, false);
		opportunityCreationRequest.setExistingOpportunityID(masterOppId);
		opportunityCreationRequest.setOriginSource(null);
		opportunityCreationRequest.setSourceType(null);

		// Create opportunity that would have been created on normal upload
		Opportunity masterOpp = getMasterOpportunityForReUpload(masterOppId, masterOppIdAsString, homeXmlOriginalString, homeXmlOriginalString);
		masterOpp.setLineType(LineType.Personal);

		final Map<Integer, Opportunity> pointerToSavedOpp = setupMocksAndGetOppMap(masterOppId, masterOpp);

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(1, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId), response.getAssociatedOppIds());

		// Check that status is Unquoted or Missing Required Data
		assertThat(pointerToSavedOpp.get(masterOppId).getStatus(),
				anyOf(equalTo(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
						equalTo(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode())));

		// Check opp ids aren't changed
		assertEquals(masterOppId, pointerToSavedOpp.get(masterOppId).getOpportunityId());

		// Check masterOppIds dont get changed
		assertEquals(masterOppIdAsString, pointerToSavedOpp.get(masterOppId).getMasterOppID());

		// Check that BT Meta data is kept
		Document newDataXml = XmlHelper.getDocument(pointerToSavedOpp.get(masterOppId).getData());
		assertEquals("UNKNOWN", AcordHelper.getBTMetaCreationOrigin(newDataXml));
		assertEquals("Unknown", AcordHelper.getBTMetaCreationType(newDataXml));
	}

	private void setupMocksForOppConstructorAndOppHelper() throws Exception {
		lenient().doCallRealMethod().when(opportunityConstructor).buildInitialOpportunity(any(OpportunityCreationRequest.class));
		lenient().doCallRealMethod().when(opportunityConstructor).updateOpportunity(any(), any(), any());
		lenient().doCallRealMethod().when(opportunityHelper).updateNewOpportunity(any(), any(), any());
		lenient().doCallRealMethod().when(opportunityHelper).validateMissingData(any());
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(transformationService).runRules(any(Document.class), any(String.class), eq(null));
		//Need to set this to null due to spring config issues
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(opportunityConstructor).runRules(any(Document.class), eq(null), any());
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(opportunityRepoHelper).updateOpportunity(any(Opportunity.class));
		lenient().doAnswer(invocation -> invocation.getArgument(0)).when(addressCleanseService).addCountiesToLocations(any(Document.class));
		setupMocksForSensitiveDataHelper();
		setUpMocksForEFTPaymentAccounts();
	}

	private void setupMocksForBLOppConstructorAndBLOppHelper() throws Exception {
		lenient().doCallRealMethod().when(businessOpportunityConstructor).buildInitialOpportunity(any(OpportunityCreationRequest.class));
		lenient().doCallRealMethod().when(businessOpportunityConstructor).updateOpportunity(any(), any(), any());
		lenient().doCallRealMethod().when(businessOpportunityHelper).updateNewOpportunity(any(), any(), any());
		lenient().doCallRealMethod().when(businessOpportunityHelper).validateMissingData(any());
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(transformationService).runRules(any(Document.class), any(String.class), eq(null));
		//Need to set this to null due to spring config issues
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(businessOpportunityConstructor).runRules(any(Document.class), eq(null), any());
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(opportunityRepoHelper).updateOpportunity(any(Opportunity.class));
		lenient().doAnswer(invocation -> invocation.getArgument(0))
						.when(addressCleanseService).addCountiesToLocations(any(Document.class));
		setupMocksForSensitiveDataHelper();
	}

	private Map<Integer, Opportunity> getOppMapFromRandom() {
		final Map<Integer, Opportunity> pointerToSavedOpp = new LinkedHashMap<>();

		lenient().doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			if (opp.getOpportunityId() == 0) {
				opp.setOpportunityId(new Random().nextInt());
			}
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).save(any());

		return pointerToSavedOpp;
	}

	private OpportunityCreationRequest getOpportunityCreationRequest(LineType lineType, Document document, boolean setMaster) {
		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setBookTransferID(12345);
		opportunityCreationRequest.setSFDCID(12345);
		opportunityCreationRequest.setUploadEventID(12345);
		opportunityCreationRequest.setPriorCarrier("priorCarrier");
		opportunityCreationRequest.setSubCode("n1529417");
		opportunityCreationRequest.setUploadedACORD(document);
		opportunityCreationRequest.setLineType(lineType);
		opportunityCreationRequest.setMasterOpp(setMaster);
		opportunityCreationRequest.setOriginatingId("someId");
		opportunityCreationRequest.setOriginSource(OriginSource.AQE);
		return opportunityCreationRequest;
	}

	private Opportunity getMasterOpportunityForReUpload(int masterOppId, String masterOppIdAsString, String XmlOriginalString, String XmlDataString) {
		Opportunity masterOpp = new Opportunity();
		masterOpp.setOpportunityId(masterOppId);
		masterOpp.setOriginalXML(XmlOriginalString);
		masterOpp.setData(XmlDataString);
		masterOpp.setStatus(5);    // Quoted - Failed
		// ReUpload already has Id set
		masterOpp.setMasterOppID(masterOppIdAsString);
		return masterOpp;
	}

	@Test
	void testCreateOpportunityForSingleCA() throws Exception{
		oppBuilder = mock(UploadOpportunityBuilder.class);

		setupMocksForOppConstructorAndOppHelper();

		LineType lineType = LineType.Personal;
		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/SingleCAEFTPaymentAuto_OneVehicle.xml");

		OpportunityCreationRequest request = getOpportunityCreationRequest(lineType, document, false);

		final Opportunity[] pointerToSavedOpp = getOppArrayForCreateOpportunityTest();

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(false, request);
		Opportunity modifiedMockOpp = pointerToSavedOpp[0];

		//check booktransferservice is not called for non-spqe
		verify(bookTransferService, times(0)).findByBookTransferId(eq(request.getBookTransferID()));
		assertEquals(LineType.Personal, modifiedMockOpp.getLineType());
		assertEquals(1, response.getSuccessfulCreationCount());
	}

	@Test
	void testUpdateFileContents_NoBypassScrubbingInFilename() throws ParserConfigurationException, SAXException, IOException, TransformerException {
		// Arrange
		String originalContent = "<InsuranceSvcRq></InsuranceSvcRq>";
		FileContent fileContent = new FileContent("no-scrubbing.xml", originalContent);
		List<FileContent> allFilesContents = Collections.singletonList(fileContent);

		// Act
		oppCreationHelper.updateFileContents(allFilesContents);

		// Assert
		String updatedContent = allFilesContents.get(0).getContent();
		String expectedContent = "<InsuranceSvcRq></InsuranceSvcRq>";
		assertEquals(expectedContent, updatedContent, "The content should not be updated");
	}




	@Test
	void testUpdateFileContents_BypassScrubbingAttributeAlreadyExists() throws ParserConfigurationException, SAXException, IOException, TransformerException {
		// Arrange
		String originalContent = "<InsuranceSvcRq BypassScrubbing=\"SkipNaming\"></InsuranceSvcRq>";
		FileContent fileContent = new FileContent("bypass-scrubbing.xml", originalContent);
		List<FileContent> allFilesContents = Collections.singletonList(fileContent);

		// Act
		oppCreationHelper.updateFileContents(allFilesContents);

		// Assert
		String updatedContent = allFilesContents.get(0).getContent();
		String expectedContent = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><InsuranceSvcRq BypassScrubbing=\"SkipNaming\"/>";
		assertEquals(expectedContent, updatedContent, "The content should not be updated");
	}

	@Test
	void testUpdateFileContents_NoInsuranceSvcRqElement() throws ParserConfigurationException, SAXException, IOException, TransformerException {
		// Arrange
		String originalContent = "<NoInsuranceSvcRq></NoInsuranceSvcRq>";
		FileContent fileContent = new FileContent("bypass-scrubbing.xml", originalContent);
		List<FileContent> allFilesContents = Collections.singletonList(fileContent);

		// Act
		oppCreationHelper.updateFileContents(allFilesContents);

		// Assert
		String updatedContent = allFilesContents.get(0).getContent();
		String expectedContent = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><NoInsuranceSvcRq/>";
		assertEquals(expectedContent, updatedContent, "The content should not be updated");
	}

	@Test
	void testUpdateFileContents() throws ParserConfigurationException, SAXException, IOException, TransformerException {
		// Arrange
		String originalContent = "<InsuranceSvcRq></InsuranceSvcRq>";
		FileContent fileContent = new FileContent("bypass-scrubbing.xml", originalContent);
		List<FileContent> allFilesContents = Collections.singletonList(fileContent);

		// Act
		oppCreationHelper.updateFileContents(allFilesContents);

		// Assert
		String updatedContent = allFilesContents.get(0).getContent();
		assertTrue(updatedContent.contains("BypassScrubbing=\"SkipNaming\""), "The content should be updated with BypassScrubbing attribute");
	}

	@Test
	void testUpdateFileContents_MultipleInsuranceSvcRqElements() throws ParserConfigurationException, SAXException, IOException, TransformerException {
		// Arrange
		String originalContent = "<root><InsuranceSvcRq></InsuranceSvcRq><InsuranceSvcRq></InsuranceSvcRq></root>";
		FileContent fileContent = new FileContent("bypass-scrubbing.xml", originalContent);
		List<FileContent> allFilesContents = Collections.singletonList(fileContent);

		// Act
		oppCreationHelper.updateFileContents(allFilesContents);

		// Assert
		String updatedContent = allFilesContents.get(0).getContent();
		String expectedContent = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><root><InsuranceSvcRq BypassScrubbing=\"SkipNaming\"/><InsuranceSvcRq BypassScrubbing=\"SkipNaming\"/></root>";
		assertEquals(expectedContent, updatedContent, "The content should be updated with BypassScrubbing attribute for all InsuranceSvcRq elements");
	}

	@Test
	void testUpdateFileContents_NoInsuranceSvcRqElements() throws ParserConfigurationException, SAXException, IOException, TransformerException {
		// Arrange
		String originalContent = "<NoInsuranceSvcRq></NoInsuranceSvcRq>";
		FileContent fileContent = new FileContent("bypass-scrubbing.xml", originalContent);
		List<FileContent> allFilesContents = Collections.singletonList(fileContent);

		// Act
		oppCreationHelper.updateFileContents(allFilesContents);

		// Assert
		String updatedContent = allFilesContents.get(0).getContent();
		String expectedContent = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><NoInsuranceSvcRq/>";
		assertEquals(expectedContent, updatedContent, "The content should not be updated as there are no InsuranceSvcRq elements");
	}

	@Test
	void testRepushSpqeSplitsAutoAndMtc() throws Exception {
		boolean isForSpqe = true;
		SPQEOpportunity autoSpqeOpp = SPQEOpportunity.builder()
				.opportunityId(100100)
				.lineOfBusiness("AUTOP")
				.build();
		int masterOppId = 100100;

		SPQEOpportunity mtrSpqeOpp = SPQEOpportunity.builder()
				.opportunityId(909090)
				.lineOfBusiness("MTR")
				.build();
		Document autoXmlNeedingSplit = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/autoAndMtc.xml");
		String autoXmlNeedingSplitAsString = XmlHelper.getDocumentString(autoXmlNeedingSplit);
		MasterOppSPQEMeta spqeExistingIdsMeta = new MasterOppSPQEMeta(null, Arrays.asList(autoSpqeOpp, mtrSpqeOpp));

		OpportunityCreationRequest opportunityCreationRequest = getOpportunityCreationRequest(LineType.Personal, autoXmlNeedingSplit, false);
		opportunityCreationRequest.setMasterOppSPQEMeta(spqeExistingIdsMeta);

		Opportunity masterOpp = new Opportunity();
		masterOpp.setOpportunityId(masterOppId);
		masterOpp.setOriginalXML(autoXmlNeedingSplitAsString);
		masterOpp.setStatus(2);
		masterOpp.setLineType(LineType.Personal);
		//

		Opportunity mtrOpp = new Opportunity();
		mtrOpp.setOpportunityId(mtrSpqeOpp.getOpportunityId());
		mtrOpp.setOriginalXML(autoXmlNeedingSplitAsString);
		mtrOpp.setStatus(2);
		mtrOpp.setLineType(LineType.Personal);

		setupMocksForOppConstructorAndOppHelper();
		doCallRealMethod().when(opportunityHelper).checkForUpdatedSubCode(any(), any(), any());

		when(opportunityRepoHelper.findOpportunityById(masterOppId)).thenReturn(masterOpp);
		when(opportunityRepoHelper.findOpportunityById(mtrSpqeOpp.getOpportunityId())).thenReturn(mtrOpp);

		final Map<Integer, Opportunity> pointerToSavedOpp = new LinkedHashMap<>();
		doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).save(any());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, opportunityCreationRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(2, response.getSuccessfulCreationCount());

		assertEquals(List.of(masterOppId, mtrSpqeOpp.getOpportunityId()), response.getAssociatedOppIds());

		// Check masterOppIds don't get changed
		assertEquals(masterOppId, pointerToSavedOpp.get(masterOppId).getOpportunityId());
		assertEquals(mtrSpqeOpp.getOpportunityId(), pointerToSavedOpp.get(mtrSpqeOpp.getOpportunityId()).getOpportunityId());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(2, savedOpps.size());

		// AUTOP vehicles
		assertEquals(2, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(0).getData())).getLength());
		assertEquals(1, AcordHelper.getPersVehs(XmlHelper.getDocument(savedOpps.get(1).getData())).getLength());
	}

	@Test
	void testRepushSpqeSplitsHomeAndBoat() throws Exception {
		Document homeAndBoatXml = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/HomeAndBoat.xml");
		String homeAndBoatXmlString = XmlHelper.getDocumentString(homeAndBoatXml);
		OpportunityCreationRequest homeRequest = buildCreationRequestFromXml(homeAndBoatXml);

		boolean isForSpqe = true;
		SPQEOpportunity homeSpqeOpp = SPQEOpportunity.builder()
				.opportunityId(100100)
				.lineOfBusiness("HOME")
				.build();

		SPQEOpportunity boatSpqeOpp = SPQEOpportunity.builder()
				.opportunityId(909090)
				.lineOfBusiness("BOAT")
				.build();
		int masterOppId = 100100;
		int boatOppId = 909090;

		MasterOppSPQEMeta spqeExistingIdsMeta = new MasterOppSPQEMeta(Arrays.asList(masterOppId, boatOppId), Arrays.asList(homeSpqeOpp, boatSpqeOpp));
		homeRequest.setMasterOppSPQEMeta(spqeExistingIdsMeta);

		Opportunity masterOpp = new Opportunity();
		masterOpp.setOpportunityId(masterOppId);
		masterOpp.setOriginalXML(homeAndBoatXmlString);
		masterOpp.setStatus(2);
		masterOpp.setLineType(LineType.Personal);
		masterOpp.setBusinessType("HOME");

		Opportunity boatOpp = new Opportunity();
		boatOpp.setOpportunityId(boatOppId);
		boatOpp.setOriginalXML(homeAndBoatXmlString);
		boatOpp.setStatus(2);
		boatOpp.setLineType(LineType.Personal);
		boatOpp.setBusinessType("BOAT");

		setupMocksForOppConstructorAndOppHelper();
		doCallRealMethod().when(opportunityHelper).checkForUpdatedSubCode(any(), any(), any());

		when(opportunityRepoHelper.findOpportunityById(masterOppId)).thenReturn(masterOpp);
		when(opportunityRepoHelper.findOpportunityById(boatOppId)).thenReturn(boatOpp);

		final Map<Integer, Opportunity> pointerToSavedOpp = new LinkedHashMap<>();
		doAnswer(invocation -> {
			Opportunity opp = invocation.getArgument(0, Opportunity.class);
			pointerToSavedOpp.put(opp.getOpportunityId(), opp);
			return opp;
		}).when(opportunityRepoHelper).save(any());

		OpportunityCreationResponse response = oppCreationHelper.createOpportunityFromOpportunityRequest(isForSpqe, homeRequest);

		assertEquals(0, response.getFailedCreationCount());
		assertEquals(2, response.getSuccessfulCreationCount());
		assertEquals(List.of(masterOppId, boatOppId), response.getAssociatedOppIds());

		// Check masterOppIds don't get changed
		assertEquals(masterOppId, pointerToSavedOpp.get(masterOppId).getOpportunityId());
		assertEquals(boatOpp.getOpportunityId(), pointerToSavedOpp.get(boatOpp.getOpportunityId()).getOpportunityId());

		List<Opportunity> savedOpps = new ArrayList<>(pointerToSavedOpp.values());
		assertEquals(2, savedOpps.size());
	}

	@Test
	void testGetBooksForAgentIdsWithFileNameContainsMSA() throws BookTransferException {
		//Arrange
		String agentNumber = "123456";
		String oppXml = "<ACORD><node>test</node><Producer><ProducerInfo><ItemIdInfo><AgencyId>987654</AgencyId></ItemIdInfo>" +
			"<ProducerSubCode>"+ agentNumber + "</ProducerSubCode></ProducerInfo></Producer></ACORD>";
		List<FileContent> fileContents = List.of(new FileContent("test1.xml", oppXml));

		when(bookTransferService.getTopBySFDCIDOrderByBookTransferID(anyInt())).thenReturn(new BookTransferDTO("BT - 01103", 1));
		when(bookTransferService.getBookTransfersForMultipleAgencyIdsAndCarrier(anySet(), eq("Main Street America"))).thenReturn(Optional.of(Map.of(
			agentNumber, new BookTransferDTO("BT - 123456", 1)
		)));

		//Act
		OpportunityCreationHelper.BookRecord bookRecord = oppCreationHelper.getBooksForAgentIds(fileContents, "test-Main Street America.zip");

		//Assert
		assertNotNull(bookRecord);
		assertEquals("BT - 01103", bookRecord.defaultBookTransfer().getSalesforceCode());
		assertEquals("BT - 123456", bookRecord.bookTransferMap().get(agentNumber).getSalesforceCode());
	}

	@Test
	void testGetBooksForAgentIdsWithFileNameNotContainsMSA() throws BookTransferException {
		//Arrange
		String agentNumber = "123456";
		String oppXml = "<ACORD><node>test</node><Producer><ProducerInfo><ItemIdInfo><AgencyId>987654</AgencyId></ItemIdInfo>" +
			"<ContractNumber>"+ agentNumber + "</ContractNumber></ProducerInfo></Producer></ACORD>";
		List<FileContent> fileContents = List.of(new FileContent("test1.xml", oppXml));

		//Act
		OpportunityCreationHelper.BookRecord bookRecord = oppCreationHelper.getBooksForAgentIds(fileContents, "test-SAM.zip");

		//Assert
		assertNotNull(bookRecord);
		assertNull(bookRecord.defaultBookTransfer());
		assertTrue(bookRecord.bookTransferMap().isEmpty());
		verify(bookTransferService, times(0)).getBookTransfersForMultipleAgencyIdsAndCarrier(anySet(), anyString());
		verify(bookTransferService, times(0)).getTopBySFDCIDOrderByBookTransferID(anyInt());
	}

	@Test
	void testGetBooksForAgentIdsWithInvalidFileContent() throws BookTransferException {
		//Arrange
		String agentNumber = "123456";
		String oppXml = "<ACORD><node>test</node><Producer><ProducerInfo><ItemIdInfo><AgencyId>987654</AgencyId></ItemIdInfo>" +
			"<ContractNumber>"+ agentNumber + "</ProducerInfo></Producer></ACORD>";
		List<FileContent> fileContents = List.of(new FileContent("test1.xml", oppXml));
		when(bookTransferService.getTopBySFDCIDOrderByBookTransferID(anyInt())).thenReturn(new BookTransferDTO("BT - 01103", 1));

		//Act
		OpportunityCreationHelper.BookRecord bookRecord = oppCreationHelper.getBooksForAgentIds(fileContents, "test-Main Street America.zip");

		//Assert
		assertNotNull(bookRecord);
		assertEquals("BT - 01103", bookRecord.defaultBookTransfer().getSalesforceCode());
		assertTrue(bookRecord.bookTransferMap().isEmpty());
		verify(bookTransferService, times(1)).getTopBySFDCIDOrderByBookTransferID(anyInt());
	}

	@ParameterizedTest
	@ValueSource(strings = {"Main Street America - valid.xml", "OtherCarrier - valid.xml"})
	void getBookTransfer_returnsCorrectBookTransferBasedOnFileName(String fileName) {
		String agentNumber = "123456";
		String oppXml = "<ACORD><node>test</node><Producer><ProducerInfo><ItemIdInfo><AgencyId>987654</AgencyId></ItemIdInfo>" +
			"<ProducerSubCode>"+ agentNumber + "</ProducerSubCode></ProducerInfo></Producer></ACORD>";
		FileContent fileContent = new FileContent(fileName, oppXml);
		Map<String, BookTransferDTO> bookTransferMap = Map.of(agentNumber, new BookTransferDTO("BT - 123456", 1));
		BookTransferDTO defaultBookTransfer = new BookTransferDTO("BT - 01103", 2);
		BookTransferDTO bookTransfer = new BookTransferDTO("BT - 999999", 3);

		BookTransferDTO result = oppCreationHelper.getBookTransfer(bookTransfer, fileContent, bookTransferMap, defaultBookTransfer);

		assertEquals(fileName.contains("Main Street America") ? "BT - 123456" : "BT - 999999", result.getSalesforceCode());
	}

	@ParameterizedTest
	@ValueSource(strings = {"Main Street America - invalid.xml", "OtherCarrier - invalid.xml"})
	void getBookTransfer_returnsDefaultBookTransferWhenAgentNumberNotFound(String fileName) {
		String agentNumber = "123456";
		String oppXml = "<ACORD><node>test</node><Producer><ProducerInfo><ItemIdInfo><AgencyId>987654</AgencyId></ItemIdInfo>" +
			"<ContractNumber>"+ agentNumber + "</ContractNumber></ProducerInfo></Producer></ACORD>";
		FileContent fileContent = new FileContent(fileName, oppXml);
		Map<String, BookTransferDTO> bookTransferMap = Map.of();
		BookTransferDTO defaultBookTransfer = new BookTransferDTO("BT - 01103", 2);
		BookTransferDTO bookTransfer = new BookTransferDTO("BT - 999999", 3);

		BookTransferDTO result = oppCreationHelper.getBookTransfer(bookTransfer, fileContent, bookTransferMap, defaultBookTransfer);

		assertEquals(fileName.contains("Main Street America") ? "BT - 01103" : "BT - 999999", result.getSalesforceCode());
	}

	@ParameterizedTest
	@ValueSource(strings = {"Main Street America - malformed.xml", "OtherCarrier - malformed.xml"})
	void getBookTransfer_logsErrorAndReturnsDefaultWhenXmlIsMalformed(String fileName) {
		String agentNumber = "123456";
		String oppXml = "<ACORD><node>test</node><Producer><ProducerInfo><ItemIdInfo><AgencyId>987654</AgencyId></ItemIdInfo>" +
			"<ContractNumber>"+ agentNumber + "</ContractNumber></ProducerInfo></Producer></ACORD>";
		FileContent fileContent = new FileContent(fileName, oppXml);
		Map<String, BookTransferDTO> bookTransferMap = Map.of();
		BookTransferDTO defaultBookTransfer = new BookTransferDTO("BT - 01103", 2);
		BookTransferDTO bookTransfer = new BookTransferDTO("BT - 999999", 3);

		BookTransferDTO result = oppCreationHelper.getBookTransfer(bookTransfer, fileContent, bookTransferMap, defaultBookTransfer);

		assertEquals(fileName.contains("Main Street America") ? "BT - 01103" : "BT - 999999", result.getSalesforceCode());
	}

	void getBookTransfer_returnsInputBookTransferWhenFileNameDoesNotContainMainStreetAmerica() {
		FileContent fileContent = new FileContent("OtherCarrier - valid.xml", "<ACORD><Producer><ProducerInfo><ItemIdInfo><AgencyId>123456</AgencyId></ItemIdInfo></ProducerInfo></Producer>");
		Map<String, BookTransferDTO> bookTransferMap = Map.of("123456", new BookTransferDTO("BT - 123456", 1));
		BookTransferDTO defaultBookTransfer = new BookTransferDTO("BT - 1103", 2);
		BookTransferDTO bookTransfer = new BookTransferDTO("BT - 999999", 3);

		BookTransferDTO result = oppCreationHelper.getBookTransfer(bookTransfer, fileContent, bookTransferMap, defaultBookTransfer);

		assertEquals("BT - 999999", result.getSalesforceCode());
	}

	protected static OpportunityCreationRequest buildCreationRequestFromXml(Document xml){
		OpportunityCreationRequest request = new OpportunityCreationRequest();
		request.setUploadedACORD(xml);
		request.setAsMasterOpp();
		request.setBookTransferID(12345);
		request.setSFDCID(12345);
		request.setUploadEventID(12345);
		request.setPriorCarrier("priorCarrier");
		request.setSubCode("n1529417");
		request.setLineType(LineType.Personal);
		request.setMasterOpp(false);
		request.setOriginatingId("someId");
		request.setOriginSource(OriginSource.AQE);
		return request;
	}

}

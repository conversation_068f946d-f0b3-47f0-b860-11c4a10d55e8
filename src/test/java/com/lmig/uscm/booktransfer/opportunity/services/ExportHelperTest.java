package com.lmig.uscm.booktransfer.opportunity.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lmig.uscm.booktransfer.email.client.EmailService;
import com.lmig.uscm.booktransfer.email.client.domain.Email;
import com.lmig.uscm.booktransfer.email.client.domain.EmailException;
import com.lmig.uscm.booktransfer.opportunity.config.properties.EmailProperties;
import com.lmig.uscm.booktransfer.opportunity.config.properties.EnvironmentProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.BDDMockito.then;

class ExportHelperTest {
	private ExportHelper exportHelper;
	private EmailService emailSender;

	@BeforeEach
	public void setUp() {
		emailSender = Mockito.mock(EmailService.class);
		EnvironmentProperties environmentProperties = new EnvironmentProperties();
		environmentProperties.setActive("unit");

		EmailProperties emailProperties = new EmailProperties();
		emailProperties.setAddress("<EMAIL>");
		exportHelper = new ExportHelper(emailSender, emailProperties, environmentProperties);
	}

	@Test
	void testSendingEmailWithGivenEmailAddress() throws JsonProcessingException, EmailException {
		exportHelper.sendUpdateEffectiveDateCSVs("Update records", null, "<EMAIL>");
		ArgumentCaptor<Email> varArgs = ArgumentCaptor.forClass(Email.class);

		then(emailSender).should().send(varArgs.capture());

		assertEquals("<EMAIL>", varArgs.getValue().getToList()[0]);

	}

	@Test
	void testSendingEmailWitNoGivenEmailAddress() throws JsonProcessingException, EmailException {
		exportHelper.sendUpdateEffectiveDateCSVs("Update records", null, null);
		ArgumentCaptor<Email> varArgs = ArgumentCaptor.forClass(Email.class);

		then(emailSender).should().send(varArgs.capture());

		assertEquals("<EMAIL>", varArgs.getValue().getToList()[0]);

	}
}

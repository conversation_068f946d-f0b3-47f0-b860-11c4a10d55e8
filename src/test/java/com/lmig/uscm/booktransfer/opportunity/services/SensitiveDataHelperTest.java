package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.booktransfer.btsensitivedataservice.client.SensitiveDataServiceWebClient;
import com.lmig.booktransfer.btsensitivedataservice.client.domain.exception.SensitiveDataException;
import com.lmig.booktransfer.btsensitivedataservice.client.domain.tokenization.TokenizationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunitySensitiveDataException;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import com.lmig.usconsumermarkets.booktransfer.btuploadpreprocessor.client.UploadPreprocessorWebClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class SensitiveDataHelperTest {

	SensitiveDataServiceWebClient mockServiceClient = mock(SensitiveDataServiceWebClient.class);
	UploadPreprocessorWebClient uploadPreprocessorWebClient = mock(UploadPreprocessorWebClient.class);
	List<String> defaultProfiles = Arrays.asList("unit", "testing");

	SensitiveDataHelper sensitiveDataHelper;
	SensitiveDataHelper spyDataHelper;

	@BeforeEach
	public void setupEachTest() {
		sensitiveDataHelper = new SensitiveDataHelper(mockServiceClient, uploadPreprocessorWebClient, defaultProfiles);
		spyDataHelper = Mockito.spy(sensitiveDataHelper);
	}

	@Test
	void testScrubXmlWithEnvCheck_doesNotCallScrubbingForSomeEnvs() throws Exception {
		List<String> envWithoutScrubbingEnabled = List.of("prod");
		sensitiveDataHelper = new SensitiveDataHelper(mockServiceClient, uploadPreprocessorWebClient, envWithoutScrubbingEnabled);
		spyDataHelper = Mockito.spy(sensitiveDataHelper);

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");

		spyDataHelper.scrubXmlWithEnvCheck(document);

		verify(spyDataHelper, times(0)).scrubXml(any());
	}

	@Test
	void testScrubXmlWithEnvCheck_doesCallScrubbingForSomeEnvs() throws Exception {
		List<String> envWithScrubbingEnabled = List.of("test");
		sensitiveDataHelper = new SensitiveDataHelper(mockServiceClient, uploadPreprocessorWebClient, envWithScrubbingEnabled);
		spyDataHelper = Mockito.spy(sensitiveDataHelper);

		doAnswer(invocation -> invocation.getArgument(0)).when(spyDataHelper).scrubXml(any());

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");

		spyDataHelper.scrubXmlWithEnvCheck(document);

		verify(spyDataHelper, times(1)).scrubXml(any());
	}

	@Test
	void testTokenizeXmlWithEnvCheck_doesNotCallTokenizingForSomeEnvs() throws Exception {
		List<String> envWithoutTokenizingEnabled = List.of("sandbox");
		sensitiveDataHelper = new SensitiveDataHelper(mockServiceClient, uploadPreprocessorWebClient, envWithoutTokenizingEnabled);
		spyDataHelper = Mockito.spy(sensitiveDataHelper);

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");

		spyDataHelper.tokenizeXmlWithEnvCheck(document);

		verify(spyDataHelper, times(0)).tokenizeXml(any());
	}

	@Test
	void testTokenizeXmlWithEnvCheck_doesCallTokenizingForSomeEnvs() throws Exception {
		List<String> envWithTokenizingEnabled = List.of("test");
		sensitiveDataHelper = new SensitiveDataHelper(mockServiceClient, uploadPreprocessorWebClient, envWithTokenizingEnabled);
		spyDataHelper = Mockito.spy(sensitiveDataHelper);

		doAnswer(invocation -> invocation.getArgument(0)).when(spyDataHelper).tokenizeXml(any());

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");

		spyDataHelper.tokenizeXmlWithEnvCheck(document);

		verify(spyDataHelper, times(1)).tokenizeXml(any());
	}

	@Test
	void testTokenizeXml_callsTokenizationService() throws Exception {
		TokenizationResponse stubbedResponse = new TokenizationResponse();
		doReturn(Optional.of(stubbedResponse))
				.when(mockServiceClient).tokenize(any());

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");

		sensitiveDataHelper.tokenizeXml(document);

		verify(mockServiceClient, times(1)).tokenize(any());
	}

	@Test
	void testTokenizeXml_throwsException_failedCallTokenizationService() throws Exception {
		when(mockServiceClient.tokenize(any())).thenThrow(new SensitiveDataException("Some bad stuff", 500));

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");

		assertThrows(OpportunitySensitiveDataException.class, () -> sensitiveDataHelper.tokenizeXml(document));
	}

	@Test
	void testTokenizeXml_replacesValues() throws Exception {
		TokenizationResponse stubbedResponse = new TokenizationResponse();
		String expectedToken = "replacementToken";
		stubbedResponse.setTokens(Arrays.asList(
				expectedToken, expectedToken, expectedToken,
				expectedToken, expectedToken, expectedToken));

		doReturn(Optional.of(stubbedResponse))
				.when(mockServiceClient).tokenize(any());

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		List<String> originalSSNs = XmlHelper.nodeListToList(AcordHelper.getSSNNodes(document))
				.stream().map(Node::getTextContent).collect(Collectors.toList());
		assertEquals(6, originalSSNs.size());

		sensitiveDataHelper.tokenizeXml(document);

		List<String> updatedSSNs = XmlHelper.nodeListToList(AcordHelper.getSSNNodes(document))
				.stream().map(Node::getTextContent).collect(Collectors.toList());

		for (int i = 0; i < originalSSNs.size(); i++) {
			assertNotEquals(originalSSNs.get(i), updatedSSNs.get(i));
			assertEquals(expectedToken, updatedSSNs.get(i));
		}
	}

	@Test
	void testTokenizeXml_replacesValues_orDefaultsInvalidsWithEmpties() throws Exception {
		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		List<Node> originalSSNNodes = XmlHelper.nodeListToList(AcordHelper.getSSNNodes(document));
		List<String> originalSSNs = originalSSNNodes.stream().map(Node::getTextContent).collect(Collectors.toList());
		assertEquals(6, originalSSNs.size());

		// empty values on 1 and 5
		originalSSNNodes.get(1).setTextContent("");
		originalSSNNodes.get(5).setTextContent("");
		// invalid value on 3 (8 digits)
		originalSSNNodes.get(3).setTextContent("123-45-678");

		TokenizationResponse stubbedResponse = new TokenizationResponse();
		String expectedToken = "replacementToken";
		List<String> expectedTokens = Arrays.asList(
				expectedToken, "", expectedToken,
				"", expectedToken, "");
		stubbedResponse.setTokens(expectedTokens);

		doReturn(Optional.of(stubbedResponse))
				.when(mockServiceClient).tokenize(any());

		sensitiveDataHelper.tokenizeXml(document);

		List<String> updatedSSNs = XmlHelper.nodeListToList(AcordHelper.getSSNNodes(document))
				.stream().map(Node::getTextContent).collect(Collectors.toList());

		for (int i = 0; i < originalSSNs.size(); i++) {
			assertEquals(expectedTokens.get(i), updatedSSNs.get(i));
		}
	}

	@Test
	void testTokenizeXml_error_defaultsValues() throws Exception {
		when(mockServiceClient.tokenize(any())).thenThrow(new SensitiveDataException("Some bad stuff", 500));

		Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		List<String> originalSSNs = XmlHelper.nodeListToList(AcordHelper.getSSNNodes(document))
				.stream().map(Node::getTextContent).collect(Collectors.toList());
		assertEquals(6, originalSSNs.size());

		try {
			sensitiveDataHelper.tokenizeXml(document);
			fail();
		} catch (OpportunitySensitiveDataException e) {
			// expected exception
		}

		List<String> updatedSSNs = XmlHelper.nodeListToList(AcordHelper.getSSNNodes(document))
				.stream().map(Node::getTextContent).collect(Collectors.toList());

		for (int i = 0; i < originalSSNs.size(); i++) {
			assertNotEquals(originalSSNs.get(i), updatedSSNs.get(i));
			assertEquals("", updatedSSNs.get(i));
		}
	}
}

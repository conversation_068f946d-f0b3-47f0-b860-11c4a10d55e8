package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.MasterOppSPQEMeta;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.SPQEOpportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Document;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class SPQEPreviousOppsCreationStrategyTest extends CreationStrategyTest {

	SPQEPreviousOppsCreationStrategy creationStrategy = new SPQEPreviousOppsCreationStrategy();

	@Test
	void testSPQEPreviousOppsCreationStrategyShouldResolve() throws Exception {
		OpportunityCreationRequest masterAndMetaRequest = buildCreationRequest("src/test/resources/xml/accordhome.xml");
		masterAndMetaRequest.setAsMasterOpp();
		SPQEOpportunity spqeOpportunity = SPQEOpportunity.builder()
				.opportunityId(123456)
				.lineOfBusiness("HOME")
				.build();
		masterAndMetaRequest.setMasterOppSPQEMeta(new MasterOppSPQEMeta(null, Collections.singletonList(spqeOpportunity)));

		OpportunityCreationRequest badMasterAndMetaRequest = buildCreationRequest("src/test/resources/xml/accordhome.xml");
		badMasterAndMetaRequest.setAsMasterOpp();
		badMasterAndMetaRequest.setMasterOppSPQEMeta(new MasterOppSPQEMeta());

		OpportunityCreationRequest masterRequest = buildCreationRequest("src/test/resources/xml/accordhome.xml");
		masterRequest.setAsMasterOpp();

		OpportunityCreationRequest metaRequest = buildCreationRequest("src/test/resources/xml/accordhome.xml");
		metaRequest.setMasterOppSPQEMeta(new MasterOppSPQEMeta());

		OpportunityCreationRequest newRequest = buildCreationRequest("src/test/resources/xml/accordhome.xml");

		boolean shouldResolveMasterAndMetaRequest = creationStrategy.shouldResolveCreationBundle(buildCreationBundle(masterAndMetaRequest));
		boolean shouldResolveBadMasterAndMetaRequest = creationStrategy.shouldResolveCreationBundle(buildCreationBundle(badMasterAndMetaRequest));
		boolean shouldResolveMasterRequest = creationStrategy.shouldResolveCreationBundle(buildCreationBundle(masterRequest));
		boolean shouldResolveMetaRequest = creationStrategy.shouldResolveCreationBundle(buildCreationBundle(metaRequest));
		boolean shouldResolveNewRequest = creationStrategy.shouldResolveCreationBundle(buildCreationBundle(newRequest));

		assertTrue(shouldResolveMasterAndMetaRequest);
		assertFalse(shouldResolveBadMasterAndMetaRequest);
		assertFalse(shouldResolveMasterRequest);
		assertFalse(shouldResolveMetaRequest);
		assertFalse(shouldResolveNewRequest);
	}

	@Test
	void testSPQEPreviousOppsStrategy_SingleCreationBundleWithHome_NoResolves() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest homeRequest = buildCreationRequest("src/test/resources/xml/accordhome.xml");
		homeRequest.setAsMasterOpp();
		CreationStrategyBundle simpleHomeBundle = buildCreationBundle(homeRequest);
		simpleHomeBundle.getMasterCreationRequest().setMasterOppSPQEMeta(new MasterOppSPQEMeta(new ArrayList<Integer>()));

		List<CreationStrategyBundle> bundles = List.of(simpleHomeBundle);

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(1, bundles.get(0).getRequests().size());
	}

	@Test
	void testSPQEPreviousOppsStrategy_CreationBundleAuto_OneResolves() throws Exception {
		SPQEOpportunity spqeOpportunity = SPQEOpportunity.builder()
				.opportunityId(123456)
				.lineOfBusiness("AUTOP")
				.build();
		int oppId = 123456;

		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest auto16VehRequest = buildCreationRequest("src/test/resources/xml/auto16Vehs.xml");
		auto16VehRequest.setAsMasterOpp();
		auto16VehRequest.setMasterOppSPQEMeta(new MasterOppSPQEMeta(null, List.of(spqeOpportunity)));

		CreationStrategyBundle creationBundle = buildCreationBundle(auto16VehRequest);
		List<CreationStrategyBundle> bundles = List.of(creationBundle);

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());

		// Check that the request links to previously saved Opportunity properly
		assertEquals(spqeOpportunity.getOpportunityId(), bundles.get(0).getRequests().get(0).getExistingOpportunityID());
	}

	@Test
	void testSPQEPreviousOppsStrategy_CreationBundleWithTwoAuto_AllResolves() throws Exception {
		SPQEOpportunity spqeOpportunity1 = SPQEOpportunity.builder()
				.opportunityId(123456)
				.lineOfBusiness("AUTOP")
				.build();

		SPQEOpportunity spqeOpportunity2 = SPQEOpportunity.builder()
				.opportunityId(654321)
				.lineOfBusiness("AUTOP")
				.build();
		List<SPQEOpportunity> expectedOppIds = new ArrayList<>(Arrays.asList(spqeOpportunity1, spqeOpportunity2));

		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest auto16VehRequest = buildCreationRequest("src/test/resources/xml/auto16Vehs.xml");
		auto16VehRequest.setAsMasterOpp();
		auto16VehRequest.setMasterOppSPQEMeta(new MasterOppSPQEMeta(null, expectedOppIds));
		OpportunityCreationRequest mockedSplitVehRequest = buildCreationRequest("src/test/resources/xml/auto16Vehs.xml");

		CreationStrategyBundle creationBundle = buildCreationBundle(auto16VehRequest);
		creationBundle.addCreationRequest(mockedSplitVehRequest);
		List<CreationStrategyBundle> bundles = List.of(creationBundle);

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());

		// Check that the split auto request links to previously saved Opportunity properly
		assertEquals(expectedOppIds.get(0).getOpportunityId(), bundles.get(0).getRequests().get(0).getExistingOpportunityID());
		assertEquals(expectedOppIds.get(1).getOpportunityId(), bundles.get(0).getRequests().get(1).getExistingOpportunityID());
	}

	@Test
	void testSplitFifthCarStrategy_CreationBundleWithTwoAuto_TooManyPreviousIds_NoResolves() throws Exception {
		SPQEOpportunity spqeOpportunity1 = SPQEOpportunity.builder()
				.opportunityId(123456)
				.lineOfBusiness("AUTOP")
				.build();

		SPQEOpportunity spqeOpportunity2 = SPQEOpportunity.builder()
				.opportunityId(654321)
				.lineOfBusiness("AUTOP")
				.build();

		SPQEOpportunity spqeOpportunity3 = SPQEOpportunity.builder()
				.opportunityId(987654)
				.lineOfBusiness("AUTOP")
				.build();

		List<SPQEOpportunity> expectedOppIds = new ArrayList<>(Arrays.asList(spqeOpportunity1, spqeOpportunity2, spqeOpportunity3));

		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest auto16VehRequest = buildCreationRequest("src/test/resources/xml/auto16Vehs.xml");
		auto16VehRequest.setAsMasterOpp();
		auto16VehRequest.setMasterOppSPQEMeta(new MasterOppSPQEMeta(null, expectedOppIds));
		OpportunityCreationRequest mockedSplitVehRequest = buildCreationRequest("src/test/resources/xml/auto16Vehs.xml");

		CreationStrategyBundle creationBundle = buildCreationBundle(auto16VehRequest);
		creationBundle.addCreationRequest(mockedSplitVehRequest);
		List<CreationStrategyBundle> bundles = List.of(creationBundle);

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that all errored
		assertEquals(2, (int) response.getFailedCreationCount());

		// Check that requests were removed so new Opportunities arent created and old ones are updated
		assertEquals(0, bundles.get(0).getRequests().size());
	}

	@Test
	void testSPQERepushExistingOpportunitiesForSplitLobOpportunities() throws Exception{
		SPQEOpportunity spqeOpportunity1 = SPQEOpportunity.builder()
				.opportunityId(123456)
				.lineOfBusiness("AUTOP")
				.build();
		SPQEOpportunity spqeOpportunity2 = SPQEOpportunity.builder()
				.opportunityId(654321)
				.lineOfBusiness("MTR")
				.build();
		List<SPQEOpportunity> expectedOppIds = new ArrayList<>(Arrays.asList(spqeOpportunity1, spqeOpportunity2));

		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest masterRequest = new OpportunityCreationRequest();
		Document inputAcord = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		masterRequest.setUploadedACORD(inputAcord);
		masterRequest.setAsMasterOpp();
		masterRequest.setMasterOppSPQEMeta(new MasterOppSPQEMeta(null, expectedOppIds));

		OpportunityCreationRequest mtrRequest = new OpportunityCreationRequest();
		Document mrtAcord = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/mtrAfterStrategy.xml");
		mtrRequest.setUploadedACORD(mrtAcord);

		CreationStrategyBundle autoBundle = buildCreationBundle(masterRequest);
		CreationStrategyBundle mtrBundle = buildCreationBundle(mtrRequest);

		List<CreationStrategyBundle> bundles = List.of(autoBundle, mtrBundle);

		creationStrategy.resolveCreationBundles(bundles, response);

		assertEquals(123456, bundles.get(0).getRequests().get(0).getExistingOpportunityID());
		assertEquals(654321, bundles.get(1).getRequests().get(0).getExistingOpportunityID());
	}

	@Test
	void testSPQERepushExistingOpportunitiesWithNewAutoOpportunity() throws Exception{
		SPQEOpportunity spqeOpportunity1 = SPQEOpportunity.builder()
				.opportunityId(123456)
				.lineOfBusiness("AUTOP")
				.build();
		SPQEOpportunity spqeOpportunity2 = SPQEOpportunity.builder()
				.opportunityId(123457)
				.lineOfBusiness("AUTOP")
				.build();
		SPQEOpportunity spqeOpportunity3 = SPQEOpportunity.builder()
				.opportunityId(654321)
				.lineOfBusiness("MTR")
				.build();
		List<SPQEOpportunity> expectedOppIds = new ArrayList<>(Arrays.asList(spqeOpportunity1, spqeOpportunity2, spqeOpportunity3));

		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest masterRequest = new OpportunityCreationRequest();
		Document inputAcord = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		masterRequest.setUploadedACORD(inputAcord);
		masterRequest.setAsMasterOpp();
		masterRequest.setMasterOppSPQEMeta(new MasterOppSPQEMeta(null, expectedOppIds));

		OpportunityCreationRequest auto2 = new OpportunityCreationRequest();
		Document autoAccord = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto2.xml");
		auto2.setUploadedACORD(autoAccord);

		OpportunityCreationRequest mtrRequest = new OpportunityCreationRequest();
		Document mrtAcord = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/mtrAfterStrategy.xml");
		mtrRequest.setUploadedACORD(mrtAcord);

		CreationStrategyBundle autoBundle = buildCreationBundle(masterRequest);
		autoBundle.addCreationRequest(auto2);
		CreationStrategyBundle mtrBundle = buildCreationBundle(mtrRequest);

		List<CreationStrategyBundle> bundles = List.of(autoBundle, mtrBundle);

		creationStrategy.resolveCreationBundles(bundles, response);

		assertEquals(123456, bundles.get(0).getRequests().get(0).getExistingOpportunityID());
		assertEquals(123457, bundles.get(0).getRequests().get(1).getExistingOpportunityID());
		assertEquals(654321, bundles.get(1).getRequests().get(0).getExistingOpportunityID());
	}
}

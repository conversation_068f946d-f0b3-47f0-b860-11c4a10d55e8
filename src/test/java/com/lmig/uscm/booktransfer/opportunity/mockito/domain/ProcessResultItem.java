package com.lmig.uscm.booktransfer.opportunity.mockito.domain;

import org.springframework.stereotype.Component;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;


/**
 * This class is bc introducing spring boot 2.0 we need to change the generated value. With process result no longer being availbe to us in domain service I created a fake on here
 * This is used to put data in the db for joins.
 * Probably in the future just use sql scripts for this
 * This will need to be removed in the future.
 */
@Component
@Entity
@Table(
		name = "ProcessResultItem"
)
public class ProcessResultItem {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int processResultId;
	@Column
	private int opportunityId;
	@Column
	private String data;
	@Column
	private String transactions;
	@Column
	private String timeStamp;
	@Column
	private int processRequestId;
	@Column
	private String policyGuid;
	@Column
	private String partnerRequestXml;

	public ProcessResultItem() {
	}

	public ProcessResultItem(String data) {
		this.data = data;
	}

	public ProcessResultItem(int ProcessResultID, int OpportunityID, String Data, String Transactions, String TimeStamp, int ProcessRequestID, String PolicyGUID, String PartnerRequestXML) {
		this.processResultId = ProcessResultID;
		this.opportunityId = OpportunityID;
		this.data = Data;
		this.transactions = Transactions;
		this.timeStamp = TimeStamp;
		this.processRequestId = ProcessRequestID;
		this.policyGuid = PolicyGUID;
		this.partnerRequestXml = PartnerRequestXML;
	}

	public int getProcessResultID() {
		return this.processResultId;
	}

	public void setProcessResultID(int processResultID) {
		this.processResultId = processResultID;
	}

	public int getOpportunityID() {
		return this.opportunityId;
	}

	public void setOpportunityID(int OpportunityID) {
		this.opportunityId = OpportunityID;
	}

	public String getData() {
		return this.data;
	}

	public void setData(String data) {
		this.data = data;
	}

	public String getTransactions() {
		return this.transactions;
	}

	public void setTransactions(String transactions) {
		this.transactions = transactions;
	}

	public String getTimeStamp() {
		return this.timeStamp;
	}

	public void setTimeStamp(String timeStamp) {
		this.timeStamp = timeStamp;
	}

	public int getProcessRequestID() {
		return this.processRequestId;
	}

	public void setProcessRequestID(int processRequestID) {
		this.processRequestId = processRequestID;
	}

	public String getPolicyGUID() {
		return this.policyGuid;
	}

	public void setPolicyGUID(String policyGUID) {
		this.policyGuid = policyGUID;
	}

	public String getPartnerRequestXML() {
		return this.partnerRequestXml;
	}

	public void setPartnerRequestXML(String partnerRequestXML) {
		this.partnerRequestXml = partnerRequestXML;
	}
}


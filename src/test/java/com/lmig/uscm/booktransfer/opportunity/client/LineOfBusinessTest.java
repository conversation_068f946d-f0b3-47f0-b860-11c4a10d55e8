package com.lmig.uscm.booktransfer.opportunity.client;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;

class LineOfBusinessTest {

	@ParameterizedTest
	@CsvSource({"src/test/resources/xml/converterV2_BL_WORK.xml, WORK"})
	void testDetermineBusinessLinesLobFromXml(String path, LineOfBusiness expectedLob) throws Exception {
		Document document = XmlHelper.getXmlDocumentFromPath(path);
		Assertions.assertEquals(expectedLob, LineOfBusiness.determineLobForBusinessLineType(document));
	}

	@ParameterizedTest
	@CsvSource({"src/test/resources/xml/lobAuto.xml, AUTOP",
			"src/test/resources/xml/mtrAfterStrategy.xml, MTR"})
	void testDeterminePersonalLinesLobFromXml(String path, LineOfBusiness expectedLob) throws Exception {
		Document document = XmlHelper.getXmlDocumentFromPath(path);
		Assertions.assertEquals(expectedLob, LineOfBusiness.determineLobForPersonalLineType(document));
	}

	//First column: String raw value
	//Second column: Enum LineOfBusiness
	@ParameterizedTest
	@CsvSource({"INMRP, INMRP",
				"Inmrp, INMRP",
				"INLANDMARINE, INMRP",
				"InlandMarine, INMRP",
				"Workers Compensation, WORK",
				"Workers Comp, WORK",
				"Worker's Compensation, WORK",
				"WORK, WORK",
				"Work, WORK",
				"AUTO, AUTOP",
				"AUTOP, AUTOP",
				"AUTO - 5TH CAR POLICY, AUTOP",
				"RV, AUTOP",
				"HOME, HOME",
				"HOME - SECONDARY RESIDENCE, HOME",
				"RENT, HOME",
				"RENTERS, HOME",
				"CONDO, HOME",
				"BOAT, BOAT",
				"WATERCRAFT, BOAT",
				"fIrE, DFIRE",
				"DfIrE, DFIRE",
				"crim, CRIM",
				"test, GENERIC"})
	void testDetermineLOBFromValue(String lob, LineOfBusiness expectedLob){
		Assertions.assertEquals(expectedLob, LineOfBusiness.determineLobFromValue(lob));
	}

	/**
	 * Given a valid BL xml file
	 * When determineLobForBusinessLineType is called with the xml document
	 * Then the expected LOB is returned based on the BL xml node
	 */
	@ParameterizedTest
	@CsvSource({"src/test/resources/xml/blWorkersComp_vertafore.xml, WORK",
			"src/test/resources/xml/blCommlAuto_vertafore.xml, AUTOB",
			"src/test/resources/xml/blBop_vertafore.xml, BOP",
			"src/test/resources/xml/blCommlGl_vertafore.xml, CGL",
			"src/test/resources/xml/blCommlPackage_vertafore.xml, CPKGE",
			"src/test/resources/xml/blProp_vertafore.xml, PROP",
			"src/test/resources/xml/blUmbrella_vertafore.xml, UMBRC"})
	void testDetermineLobForBusinessLineType(String xmlPath, LineOfBusiness expectedLOB)
			throws IOException, ParserConfigurationException, SAXException, XPathExpressionException {
		final Document xml = XmlHelper.getXmlDocumentFromPath(xmlPath);
		assertEquals(expectedLOB, LineOfBusiness.determineLobForBusinessLineType(xml));
	}

	@ParameterizedTest
	@CsvSource({"CommlPkgPolicyQuoteInqRq, crim, CRIM",
				"CommlPkgPolicyQuoteInqRq, cPkGe, CPKGE"})
	void testDetermineLobForBusinessLineType(String requestElement, String lobCd, LineOfBusiness expectedLob)
			throws ParserConfigurationException, IOException, SAXException, XPathExpressionException {
		String xmlString = "<ACORD><InsuranceSvcRq><" + requestElement + "><CommlPolicy><LOBCd>" + lobCd +
				"</LOBCd></CommlPolicy></" + requestElement + "></InsuranceSvcRq></ACORD>";

		Document xmlDoc = XmlHelper.getDocument(xmlString);
		assertEquals(expectedLob, LineOfBusiness.determineLobForBusinessLineType(xmlDoc));
	}

	@Test
	void shouldReturnLOBHomeWhenLobCdWithHomeComesBeforeOtherOrPriorPolicyLobCd() throws Exception {
		String xmlString = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><LOBCd>HOME</LOBCd>" +
				"<OtherOrPriorPolicy><LOBCd>AUTO</LOBCd></OtherOrPriorPolicy></PersPolicy>" +
				"</PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";

		Document xmlDoc = XmlHelper.getDocument(xmlString);
		assertEquals(LineOfBusiness.HOME, LineOfBusiness.determineLobForPersonalLineType(xmlDoc));
	}

	@Test
	void shouldReturnLOBHomeWhenLobCdWithHomeComesAfterOtherOrPriorPolicyLobCd() throws Exception {
		String xmlString = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><OtherOrPriorPolicy>" +
				"<LOBCd>AUTO</LOBCd></OtherOrPriorPolicy><LOBCd>HOME</LOBCd></PersPolicy>" +
				"</PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";

		Document xmlDoc = XmlHelper.getDocument(xmlString);
		assertEquals(LineOfBusiness.HOME, LineOfBusiness.determineLobForPersonalLineType(xmlDoc));
	}

	@Test
	void shouldReturnLOBGenericWhenPersPolicyLobCdIsEmpty() throws Exception {
		String xmlString = "<ACORD><PersPolicy><LOBCd /><OtherOrPriorPolicy></OtherOrPriorPolicy><LOBCd>HOME</LOBCd></PersPolicy></ACORD>";

		Document xmlDoc = XmlHelper.getDocument(xmlString);
		assertEquals(LineOfBusiness.GENERIC, LineOfBusiness.determineLobForPersonalLineType(xmlDoc));
	}

	@Test
	void shouldReturnLOBGenericWhenPersPolicyLobCdIsNotPresent() throws Exception {
		String xmlString = "<ACORD><PersPolicy><OtherOrPriorPolicy><LOBCd>AUTO</LOBCd></OtherOrPriorPolicy></PersPolicy></ACORD>";

		Document xmlDoc = XmlHelper.getDocument(xmlString);
		assertEquals(LineOfBusiness.GENERIC, LineOfBusiness.determineLobForPersonalLineType(xmlDoc));
	}
}

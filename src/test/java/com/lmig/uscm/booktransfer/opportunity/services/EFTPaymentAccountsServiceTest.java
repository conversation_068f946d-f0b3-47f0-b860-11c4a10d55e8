package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.EFTPaymentAccountsResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
public class EFTPaymentAccountsServiceTest {

    @Mock
    WebClient eftPaymentAccountsWebClient;
    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpecMock;

    @SuppressWarnings("rawtypes")
    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpecMock;

    @Mock
    private WebClient.ResponseSpec responseSpecMock;

    @Test
    void testEFTPaymentAccountsSuccessfulResponse(){
        String paymentAccountID = "***********";
        EFTPaymentAccountsResponse mockData = EFTPaymentAccountsResponse.builder()
                .accountPaymentType("EFT")
                .accountStatus("ACT")
                .accHldrFirstName("DAVE")
                .accHldrMiddleName("X")
                .accHldrLastName("CALIFORNIA")
                .acctHldrFullName("DAVE X CALIFORNIA")
                .accountEditEligibility("N")
                .accountDeleteEligibility("N")
                .accountOwnershipType("Personal")
                .bankAccountNumber("**********")
                .bankAccountType("Checking")
                .paymentAccountToken("7552751708534509635")
                .paymentAccountUsageType("O")
                .bankInstitutionName("TEST Bank")
                .bankRoutingNumber("*********")
                .financialDetailActId("***********")
                .lastActivityCode("EFT")
                .lastActivityDate("2024-02-21 11:55:10.710172")
                .merchantIdCd("067681")
                .build();

        when(eftPaymentAccountsWebClient.get()).thenReturn(requestHeadersUriSpecMock);
        when(requestHeadersUriSpecMock.uri(anyString())).thenReturn(requestHeadersSpecMock);
        when(requestHeadersSpecMock.headers(any())).thenReturn(requestHeadersSpecMock);
        when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
        when(responseSpecMock.onStatus(any(), any())).thenReturn(responseSpecMock);
        when(responseSpecMock.bodyToMono(ArgumentMatchers.<Class<EFTPaymentAccountsResponse>>notNull())).thenReturn(Mono.just(mockData));

        EFTPaymentAccountsService eftPaymentAccountsService = new EFTPaymentAccountsService(eftPaymentAccountsWebClient, "");
        EFTPaymentAccountsResponse actualData = eftPaymentAccountsService.getEFTPaymentAccountsDetails(paymentAccountID);

        assertEquals(mockData, actualData);
    }
}

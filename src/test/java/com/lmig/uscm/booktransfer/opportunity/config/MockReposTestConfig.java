package com.lmig.uscm.booktransfer.opportunity.config;

import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJpaRepository;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import org.springframework.context.annotation.Bean;

import static org.mockito.Mockito.mock;


public class MockReposTestConfig extends UnitTestConfig {
	
	@Bean
	OpportunityJpaRepository opportunityRepository() {
		return mock(OpportunityJpaRepository.class);
	}
	
	@Bean
	OpportunityJDBCRepo opportunityJDBCRepo() {
		return mock(OpportunityJDBCRepo.class);
	}

	
	@Bean
	public OpportunityRepoHelper opportunityRepoHelper(final OpportunityJpaRepository opportunityRepository,
			   final OpportunityJDBCRepo opportunityJDBCRepo) {
		return new OpportunityRepoHelper(opportunityRepository, opportunityJDBCRepo);
	}


}

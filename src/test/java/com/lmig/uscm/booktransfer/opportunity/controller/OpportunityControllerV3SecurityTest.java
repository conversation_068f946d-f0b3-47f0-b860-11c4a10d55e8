package com.lmig.uscm.booktransfer.opportunity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.uscm.booktransfer.opportunity.OpportunityApplication;
import com.lmig.uscm.booktransfer.opportunity.client.domain.*;
import com.lmig.uscm.booktransfer.opportunity.config.UnitTestConfig;
import com.lmig.uscm.booktransfer.opportunity.domain.ActionableOpportunitiesResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.MoveOpportunitiesRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.PriorCarrierData;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilter;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequestForOppIds;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.ChangeEffectiveDateResponse;
import com.lmig.uscm.booktransfer.opportunity.repo.LobGateway;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoAspect;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.DownloadZipFileHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityCreationHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.FieldChangeHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate.UpdateEffectiveDateHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.util.Pair;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithAnonymousUser;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doReturn;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith({SpringExtension.class})
@SpringBootTest(classes = {OpportunityApplication.class, UnitTestConfig.class, OpportunityRepoAspect.class})
@TestPropertySource(properties = {"spring.profiles.active=unit,transformationClient,mongoTestContainer"})
public class OpportunityControllerV3SecurityTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mvc;

    @MockBean
    OpportunityRepoHelper opportunityRepoHelper;

    @MockBean
    @Qualifier("opportunityHelper")
    OpportunityHelper opportunityHelper;

    @MockBean
    LobGateway lobGateway;

    @MockBean
    FieldChangeHelper fieldChangeHelper;

    @MockBean
    @Qualifier("trueLeadEffectiveDateHelper")
    UpdateEffectiveDateHelper updateEffectiveDateHelper;

    @MockBean
    OpportunityCreationHelper opportunityCreationHelper;

    @MockBean
    DownloadZipFileHelper downloadZipFileHelper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        mvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ"})
    @Test
    public void test_GET() throws Exception {
        doReturn(new Opportunity()).when(opportunityRepoHelper).findOpportunityById(anyInt());
        mvc.perform(get("/v3/opportunity/1"))
                .andExpect(status().is2xxSuccessful());
    }

    @WithAnonymousUser
    @Test
    void test_GET_UnAuthorized() throws Exception {
        doReturn(new Opportunity()).when(opportunityRepoHelper).findOpportunityById(anyInt());

        mvc.perform(get("/v3/opportunity/1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ"})
    @Test
    void test_getOpportunityForEdit() throws Exception {
        doReturn(new Opportunity()).when(opportunityHelper).getOpportunityForEdit(anyInt());
        mvc.perform(get("/v3/opportunity/getOpportunityForEdit?oppID=1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getOpportunityForEdit_Unauthorized() throws Exception {
        doReturn(new Opportunity()).when(opportunityHelper).getOpportunityForEdit(anyInt());
        mvc.perform(get("/v3/opportunity/getOpportunityForEdit?oppID=1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ", "OPPORTUNITY.DETOKENIZE"})
    @Test
    void test_findByOpportunityId() throws Exception {
        DetokenizeRequest mockRequest = new DetokenizeRequest();
        mockRequest.setDetokenize(false);
        mockRequest.setRequester("n9999");
        List<String> errors = new ArrayList<>();
        errors.add("error");
        int oppId = 1;
        doReturn(Pair.of(new Opportunity(), errors)).when(opportunityHelper).getQuotableOpportunity(oppId, false, "n9999");
        mvc.perform(post("/v3/opportunity/findByOpportunityId?oppId=1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mockRequest)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_findByOpportunityId_Unauthorized() throws Exception {
        DetokenizeRequest mockRequest = new DetokenizeRequest();
        mockRequest.setDetokenize(false);
        mockRequest.setRequester("n9999");
        List<String> errors = new ArrayList<>();
        errors.add("error");
        int oppId = 1;
        doReturn(Pair.of(new Opportunity(), errors)).when(opportunityHelper).getQuotableOpportunity(oppId, false, "n9999");
        mvc.perform(post("/v3/opportunity/findByOpportunityId?oppId=1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mockRequest)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ", "OPPORTUNITY.DETOKENIZE"})
    @Test
    void test_getOpportunityXmlById() throws Exception {
        String oppXml = "<ACORD><node>test</node><Producer><ProducerInfo><ContractNumber/></ProducerInfo></Producer></ACORD>";
        doReturn(oppXml).when(opportunityHelper).getOpportunityXml(anyInt(), anyBoolean());
        mvc.perform(get("/v3/opportunity/1/xml")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getOpportunityXmlById_Unauthorized() throws Exception {
        String oppXml = "<ACORD><node>test</node><Producer><ProducerInfo><ContractNumber/></ProducerInfo></Producer></ACORD>";
        doReturn(oppXml).when(opportunityHelper).getOpportunityXml(anyInt(), anyBoolean());
        mvc.perform(get("/v3/opportunity/1/xml")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_getQuotableOpportunities() throws Exception {

        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        doReturn(new ActionableOpportunitiesResponse()).when(opportunityHelper).getQuotableOpportunities(oppIds);

        this.mvc.perform(post("/v3/opportunity/getQuotableOpportunities")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getQuotableOpportunities_Unauthorized() throws Exception {

        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        doReturn(new ActionableOpportunitiesResponse()).when(opportunityHelper).getQuotableOpportunities(oppIds);

        this.mvc.perform(post("/v3/opportunity/getQuotableOpportunities")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_getOpportunitiesByIdsPost() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        Opportunity mockOpportunity = new Opportunity();
        List<Opportunity> mockOpportunities = new ArrayList<>();
        mockOpportunities.add(mockOpportunity);

        doReturn(mockOpportunities).when(opportunityHelper).getAllOppsByOppIds(oppIds);

        this.mvc.perform(post("/v3/opportunity/byIds")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getOpportunitiesByIdsPost_Unauthorized() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        Opportunity mockOpportunity = new Opportunity();
        List<Opportunity> mockOpportunities = new ArrayList<>();
        mockOpportunities.add(mockOpportunity);

        doReturn(mockOpportunities).when(opportunityHelper).getAllOppsByOppIds(oppIds);

        this.mvc.perform(post("/v3/opportunity/byIds")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ"})
    @Test
    void test_getPreviewData() throws Exception {

        PreviewDataResponse response = new PreviewDataResponse();
        doReturn(response).when(opportunityHelper).getPreviewData(1);
        mvc.perform(get("/v3/opportunity/getPreviewData?oppId=1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getPreviewData_Unauthorized() throws Exception {

        PreviewDataResponse response = new PreviewDataResponse();
        doReturn(response).when(opportunityHelper).getPreviewData(1);
        mvc.perform(get("/v3/opportunity/getPreviewData?oppId=1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_getOpportunitiesErrorInfo() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        OpportunityErrorInfo errorInfo = new OpportunityErrorInfo();
        List<OpportunityErrorInfo> errorInfoList = new ArrayList<>();
        errorInfoList.add(errorInfo);

        doReturn(errorInfoList).when(opportunityHelper).getOpportunitiesErrorInfo(oppIds);
        this.mvc.perform(post("/v3/opportunity/errorInfo")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getOpportunitiesErrorInfo_Unauthorized() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        OpportunityErrorInfo errorInfo = new OpportunityErrorInfo();
        List<OpportunityErrorInfo> errorInfoList = new ArrayList<>();
        errorInfoList.add(errorInfo);

        doReturn(errorInfoList).when(opportunityHelper).getOpportunitiesErrorInfo(oppIds);
        this.mvc.perform(post("/v3/opportunity/errorInfo")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ"})
    @Test
    void test_getOpportunityPriorCarriers() throws Exception {

        List<String> oppPriorCarriers = new ArrayList<String>();
        oppPriorCarriers.add("1");
        doReturn(oppPriorCarriers).when(opportunityRepoHelper).getOpportunityPriorCarriers(LineType.Personal);
        mvc.perform(get("/v3/opportunity/getOpportunityPriorCarriers")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getOpportunityPriorCarriers_Unauthorized() throws Exception {

        List<String> oppPriorCarriers = new ArrayList<String>();
        oppPriorCarriers.add("1");
        doReturn(oppPriorCarriers).when(opportunityRepoHelper).getOpportunityPriorCarriers(LineType.Personal);
        mvc.perform(get("/v3/opportunity/getOpportunityPriorCarriers")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_customFilterData() throws Exception {
        CustomFilterRequest request = new CustomFilterRequest();
        CustomFilter customFilter = new CustomFilter();
        List<CustomFilter> customFilters = new ArrayList<>();
        customFilters.add(customFilter);
        CustomFilterResponse response = new CustomFilterResponse(customFilters, 1);
        doReturn(response).when(opportunityHelper).getCustomFilterResponse(request);
        this.mvc.perform(post("/v3/opportunity/customFilterData")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_customFilterData_Unauthorized() throws Exception {
        CustomFilterRequest request = new CustomFilterRequest();
        CustomFilter customFilter = new CustomFilter();
        List<CustomFilter> customFilters = new ArrayList<>();
        customFilters.add(customFilter);
        CustomFilterResponse response = new CustomFilterResponse(customFilters, 1);
        doReturn(response).when(opportunityHelper).getCustomFilterResponse(request);
        this.mvc.perform(post("/v3/opportunity/customFilterData")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ"})
    @Test
    void testOpportunityPageOppIds() throws Exception {
        CustomFilterRequestForOppIds requestFilter = new CustomFilterRequestForOppIds();
        requestFilter.setStatuses(new ArrayList<>());
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        String requestBody = objectMapper.writeValueAsString(requestFilter);
        String expectedOpps = objectMapper.writeValueAsString(oppIds);

        doReturn(List.of(1)).when(opportunityRepoHelper).getCustomFilterOppIds(any());

        mvc.perform(post("/v3/opportunity/opportunityPageOppIds")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void testOpportunityPageOppIds_Unauthorized() throws Exception {
        CustomFilterRequestForOppIds requestFilter = new CustomFilterRequestForOppIds();
        requestFilter.setStatuses(new ArrayList<>());
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        String requestBody = objectMapper.writeValueAsString(requestFilter);
        doReturn(List.of(1)).when(opportunityRepoHelper).getCustomFilterOppIds(any());

        mvc.perform(post("/v3/opportunity/opportunityPageOppIds")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_getOppDetails() throws Exception {
        OpportunityFilterRequest request = new OpportunityFilterRequest();
        OpportunityDetails oppDetails = new OpportunityDetails();
        List<OpportunityDetails> oppDetailsList = new ArrayList<>();
        oppDetailsList.add(oppDetails);
        doReturn(oppDetailsList).when(opportunityHelper).getOppDetails(request);
        mvc.perform(post("/v3/opportunity/opportunity-details")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getOppDetails_Unauthorized() throws Exception {
        OpportunityFilterRequest request = new OpportunityFilterRequest();
        OpportunityDetails oppDetails = new OpportunityDetails();
        List<OpportunityDetails> oppDetailsList = new ArrayList<>();
        oppDetailsList.add(oppDetails);
        doReturn(oppDetailsList).when(opportunityHelper).getOppDetails(request);
        mvc.perform(post("/v3/opportunity/opportunity-details")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_getOpportunityPriorCarrierData() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        PriorCarrierData priorCarrierData = new PriorCarrierData("1", "name", "sdfsdf-sdfsdf");
        List<PriorCarrierData> priorCarrierDataList = new ArrayList<>();
        priorCarrierDataList.add(priorCarrierData);
        doReturn(priorCarrierDataList).when(opportunityHelper).findPriorCarrierData(oppIds);
        mvc.perform(post("/v3/opportunity/priorCarrierData")
                        .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithAnonymousUser
    @Test
    void test_getOpportunityPriorCarrierData_Unauthorized() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        PriorCarrierData priorCarrierData = new PriorCarrierData("1", "name", "sdfsdf-sdfsdf");
        List<PriorCarrierData> priorCarrierDataList = new ArrayList<>();
        priorCarrierDataList.add(priorCarrierData);
        doReturn(priorCarrierDataList).when(opportunityHelper).findPriorCarrierData(oppIds);
        mvc.perform(post("/v3/opportunity/priorCarrierData")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_getBookIdsForOppIds() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        Map<Integer, Integer> bookIdsForOppIds = Map.of(1, 1);
        doReturn(bookIdsForOppIds).when(opportunityRepoHelper).getBookIdsForOppIds(oppIds);
        mvc.perform(post("/v3/opportunity/getBookIdsForOppIds")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithAnonymousUser
    @Test
    void test_getBookIdsForOppIds_Unauthorized() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        Map<Integer, Integer> bookIdsForOppIds = Map.of(1, 1);
        doReturn(bookIdsForOppIds).when(opportunityRepoHelper).getBookIdsForOppIds(oppIds);
        mvc.perform(post("/v3/opportunity/getBookIdsForOppIds")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_getBrushMapData() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        String brushMapData = "brushMapData";
        doReturn(brushMapData).when(opportunityHelper).buildOutBrushMapData(oppIds);
        mvc.perform(post("/v3/opportunity/getBrushMapData")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getBrushMapData_Unauthorized() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        String brushMapData = "brushMapData";
        doReturn(brushMapData).when(opportunityHelper).buildOutBrushMapData(oppIds);
        mvc.perform(post("/v3/opportunity/getBrushMapData")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_getCustomerList() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        String customerListCSV = "customerListCSV";
        doReturn(customerListCSV).when(opportunityHelper).buildOutBrushMapData(oppIds);
        mvc.perform(post("/v3/opportunity/getCustomerList")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getCustomerList_Unauthorized() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        String customerListCSV = "customerListCSV";
        doReturn(customerListCSV).when(opportunityHelper).buildOutBrushMapData(oppIds);
        mvc.perform(post("/v3/opportunity/getCustomerList")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ"})
    @Test
    void test_getAllLobs() throws Exception {
        Map<String, List<String>> allLobs = Map.of("lob", List.of("lob1", "lob2"));
        doReturn(allLobs).when(lobGateway).getAllLobs();
        mvc.perform(get("/v3/opportunity/lobs/all")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getAllLobs_Unauthorized() throws Exception {
        Map<String, List<String>> allLobs = Map.of("lob", List.of("lob1", "lob2"));
        doReturn(allLobs).when(lobGateway).getAllLobs();
        mvc.perform(get("/v3/opportunity/lobs/all")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_changeOpportunitiesStatusForBL() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        Opportunity opportunity = new Opportunity();
        List<Opportunity> opportunities = new ArrayList<>();
        opportunities.add(opportunity);

        doReturn(opportunities).when(opportunityRepoHelper).updateOpportunitiesStatus(oppIds, 1);
        mvc.perform(post("/v3/opportunity/changeBLOpportunitiesStatus?status=1&opportunityIds="+oppIds)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_changeOpportunitiesStatusForBL_Unauthorized() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        Opportunity opportunity = new Opportunity();
        List<Opportunity> opportunities = new ArrayList<>();
        opportunities.add(opportunity);

        doReturn(opportunities).when(opportunityRepoHelper).updateOpportunitiesStatus(oppIds, 1);
        mvc.perform(post("/v3/opportunity/changeBLOpportunitiesStatus?status=1&opportunityIds="+oppIds)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_changeOpportunitiesStatus() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        String statusChangeReport = "statusChangeReport";

        doReturn(statusChangeReport).when(fieldChangeHelper).updateOppAndQRI(oppIds, "1", "Status");
        mvc.perform(post("/v3/opportunity/changeOpportunitiesStatus?status=1&opportunityIds="+oppIds)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithAnonymousUser
    @Test
    void test_changeOpportunitiesStatus_Unauthorized() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        String statusChangeReport = "statusChangeReport";

        doReturn(statusChangeReport).when(fieldChangeHelper).updateOppAndQRI(oppIds, "1", "Status");
        mvc.perform(post("/v3/opportunity/changeOpportunitiesStatus?status=1&opportunityIds="+oppIds)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_changeOpportunitiesStatusWithPersonalLineType() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        String statusChangeReport = "statusChangeReport";

        doReturn(statusChangeReport).when(fieldChangeHelper).updateOppAndQRI(oppIds, "1", "Status");
        mvc.perform(post("/v3/opportunity/changeOpportunitiesStatus/personal?status=1&opportunityIds="+oppIds)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(oppIds)))
          .andExpect(status().isForbidden());
    }

    @WithAnonymousUser
    @Test
    void test_changeOpportunitiesStatusWithPersonalLineType_Unauthorized() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        String statusChangeReport = "statusChangeReport";

        doReturn(statusChangeReport).when(fieldChangeHelper).updateOppAndQRI(oppIds, "1", "Status");
        mvc.perform(post("/v3/opportunity/changeOpportunitiesStatus/personal?status=1&opportunityIds="+oppIds)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(oppIds)))
          .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_changeOpportunitiesStatusWithBusinessLineType() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        String statusChangeReport = "statusChangeReport";

        doReturn(statusChangeReport).when(fieldChangeHelper).updateOppAndQRI(oppIds, "1", "Status");
        mvc.perform(post("/v3/opportunity/changeOpportunitiesStatus/business?status=1&opportunityIds="+oppIds)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(oppIds)))
          .andExpect(status().isForbidden());
    }

    @WithAnonymousUser
    @Test
    void test_changeOpportunitiesStatusWithBusinessLineType_Unauthorized() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        String statusChangeReport = "statusChangeReport";

        doReturn(statusChangeReport).when(fieldChangeHelper).updateOppAndQRI(oppIds, "1", "Status");
        mvc.perform(post("/v3/opportunity/changeOpportunitiesStatus/business?status=1&opportunityIds="+oppIds)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(oppIds)))
          .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_updateOppXML() throws Exception{
        mvc.perform(post("/v3/opportunity/updateOppXML")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(new Opportunity())))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_updateOppXML_Unauthorized() throws Exception{
        mvc.perform(post("/v3/opportunity/updateOppXML")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(new Opportunity())))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void testXmlValidationSuccess() throws Exception {
        Opportunity validateXML = new Opportunity();
        validateXML.setData("<ACORD></ACORD>");

        this.mvc.perform(post("/v3/opportunity/validateXML")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validateXML)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void testXmlValidationSuccess_Unauthorized() throws Exception {
        Opportunity validateXML = new Opportunity();
        validateXML.setData("<ACORD></ACORD>");

        this.mvc.perform(post("/v3/opportunity/validateXML")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validateXML)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ"})
    @Test
    void test_getOppIdsForSchedule() throws Exception {

        ScheduleRequest scheduleRequest = new ScheduleRequest();
        Map<LineType, List<Integer>> schedule = Map.of(LineType.Personal, List.of(1));
        doReturn(schedule).when(opportunityRepoHelper).getOppIdsForSchedule(scheduleRequest);
        this.mvc.perform(post("/v3/opportunity/getOppIdsForSchedule")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(scheduleRequest)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getOppIdsForSchedule_Unauthorized() throws Exception {

        ScheduleRequest scheduleRequest = new ScheduleRequest();
        Map<LineType, List<Integer>> schedule = Map.of(LineType.Personal, List.of(1));
        doReturn(schedule).when(opportunityRepoHelper).getOppIdsForSchedule(scheduleRequest);
        this.mvc.perform(post("/v3/opportunity/getOppIdsForSchedule")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(scheduleRequest)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ"})
    @Test
    void test_getLatestResultData() throws Exception {
        String oppId = "1";
        String response = "response";
        doReturn(response).when(opportunityHelper).maskAndBuildPartnerResult(oppId);
        mvc.perform(get("/v3/opportunity/getLatestResultData?Opp="+oppId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getLatestResultData_Unauthorized() throws Exception {
        String oppId = "1";
        String response = "response";
        doReturn(response).when(opportunityHelper).maskAndBuildPartnerResult(oppId);
        mvc.perform(get("/v3/opportunity/getLatestResultData?Opp="+oppId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_setLeadEffectiveDateV2() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        String leadType = "trueLead";
        ChangeEffectiveDateResponse res = new ChangeEffectiveDateResponse("partialMatch", "result");

        doReturn(res).when(updateEffectiveDateHelper).setLeadEffectiveDate(oppIds, "2021-01-01", leadType);
        this.mvc.perform(post("/v3/opportunity/setLeadEffectiveDate?startDate=2021-01-01&leadType="+leadType+"&email=<EMAIL>")
                        .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_setLeadEffectiveDateV2_Unauthorized() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        String leadType = "trueLead";
        ChangeEffectiveDateResponse res = new ChangeEffectiveDateResponse("partialMatch", "result");

        doReturn(res).when(updateEffectiveDateHelper).setLeadEffectiveDate(oppIds, "2021-01-01", leadType);
        this.mvc.perform(post("/v3/opportunity/setLeadEffectiveDate?startDate=2021-01-01&leadType="+leadType+"&email=<EMAIL>")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_reuploadOpportunities() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        this.mvc.perform(post("/v3/opportunity/reupload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_reuploadOpportunities_Unauthorized() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        this.mvc.perform(post("/v3/opportunity/reupload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_moveOpportunities() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        String response = "response";
        doReturn(response).when(fieldChangeHelper).updateOppAndQRI(oppIds, "SFDCID", "1234");
        this.mvc.perform(post("/v3/opportunity/moveOpportunities")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(new MoveOpportunitiesRequest())))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_moveOpportunities_Unauthorized() throws Exception {
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);
        String response = "response";
        doReturn(response).when(fieldChangeHelper).updateOppAndQRI(oppIds, "SFDCID", "1234");
        this.mvc.perform(post("/v3/opportunity/moveOpportunities")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(new MoveOpportunitiesRequest())))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_downloadZipFile() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        String zipFileName = "zipFileName";
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        doReturn(zipFileName).when(downloadZipFileHelper).generateZipFileName();
        doReturn(output).when(downloadZipFileHelper).downloadZipFile(oppIds);
        mvc.perform(post("/v3/opportunity/downloadZipFile")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_downloadZipFile_Unauthorized() throws Exception{
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(1);

        String zipFileName = "zipFileName";
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        doReturn(zipFileName).when(downloadZipFileHelper).generateZipFileName();
        doReturn(output).when(downloadZipFileHelper).downloadZipFile(oppIds);
        mvc.perform(post("/v3/opportunity/downloadZipFile")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(oppIds)))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.READ"})
    @Test
    void test_getOpportunities() throws Exception {
        List<Opportunity> opportunityList = new ArrayList<>();
        opportunityList.add(new Opportunity());
        Pageable pageable = PageRequest.of(0, 10);
        PageImpl mockPage = new PageImpl<>(opportunityList, pageable, opportunityList.size());
        int numOfRecords = 1;
        LineType personal = LineType.Personal;
        doReturn(mockPage).when(opportunityRepoHelper).getPagedOpportunities(numOfRecords, LineType.Personal);
        mvc.perform(get("/v3/opportunity/getOpportunities?numOfRecords="+numOfRecords+"&lineType="+personal)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_getOpportunities_Unauthorized() throws Exception {
        List<Opportunity> opportunityList = new ArrayList<>();
        opportunityList.add(new Opportunity());
        Pageable pageable = PageRequest.of(0, 10);
        PageImpl mockPage = new PageImpl<>(opportunityList, pageable, opportunityList.size());
        int numOfRecords = 1;
        LineType personal = LineType.Personal;
        doReturn(mockPage).when(opportunityRepoHelper).getPagedOpportunities(numOfRecords, LineType.Personal);
        mvc.perform(get("/v3/opportunity/getOpportunities?numOfRecords="+numOfRecords+"&lineType="+personal)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @WithMockUser(username = "spring", authorities = {"OPPORTUNITY.CREATE"})
    @Test
    void test_patchFile() throws Exception {
        MockMultipartFile mockFile = new MockMultipartFile("file", "file.csv", "text/plain", "content".getBytes());
        doReturn(null).when(fieldChangeHelper).updateOppAndQRI(any(MultipartFile.class));
        mvc.perform(multipart("/v3/opportunity/file")
                        .file(mockFile)
                        .with(request -> {
                            request.setMethod("PATCH");
                            return request;
                        }))
                .andExpect(status().isOk());
    }

    @WithAnonymousUser
    @Test
    void test_patchFile_Unauthorized() throws Exception {
        mvc.perform(patch("/v3/opportunity/file")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }
}

package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.csvbuilder;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.utilityservice.CSVBuilder;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

class UpdateEffectiveDateResponseBuilderTest {

	@Test
	// UBT-3676
	void testBuildingHeadersOfChangeEffectiveDate() {
		UpdateEffectiveDateResponseBuilder builder = new UpdateEffectiveDateResponseBuilder();
		CSVBuilder csvBuilder = builder.getOpportunityHeaders(new CSVBuilder());
		assertEquals("OpportunityID,lob,Original Value for the Effective Date,Updated Value for the Effective Date,Changed: yes/no,\r\n", csvBuilder.toString());
	}

	@Test
	void testBuildingCSVForChangeEffectiveDateWithSomeSuccessAndSomeFailure() {
		UpdateEffectiveDateResponseBuilder builder = new UpdateEffectiveDateResponseBuilder();
		List<OppChangeFieldResult> results = new ArrayList<>();
		results.add(getOppChangeFieldResult(10, LineOfBusiness.AUTOP, "10-15-2019", "10-15-2020"));

		OppChangeFieldResult oppChangeFieldResult = getOppChangeFieldResult(25, LineOfBusiness.HOME, "11-15-2019",
				"11-15-2020");
		oppChangeFieldResult.isError("Test failure");
		results.add(oppChangeFieldResult);

		builder.addOppChangeFieldResults(results);

		assertThat(builder.buildOutStatusChangeReport())
				.endsWith("25,HOME,11-15-2019,11-15-2020,NO,\r\n" + "10,AUTO,10-15-2019,10-15-2020,YES,\r\n");
	}

	@Test
	// UBT-3676
	void testBuildingCSVForChangeEffectiveDateWithSomeEmptyValues() {
		UpdateEffectiveDateResponseBuilder builder = new UpdateEffectiveDateResponseBuilder();
		List<OppChangeFieldResult> results = new ArrayList<>();
		results.add(getOppChangeFieldResult(10, LineOfBusiness.AUTOP, "", "10-15-2020"));

		OppChangeFieldResult oppChangeFieldResult = getOppChangeFieldResult(25, LineOfBusiness.HOME, "11-15-2019", "");
		oppChangeFieldResult.isError("Test failure");
		results.add(oppChangeFieldResult);

		builder.addOppChangeFieldResults(results);

		assertThat(builder.buildOutStatusChangeReport())
				.endsWith("25,HOME,11-15-2019,N/A,NO,\r\n" + "10,AUTO,N/A,10-15-2020,YES,\r\n");
	}

	private OppChangeFieldResult getOppChangeFieldResult(Integer oppId, LineOfBusiness lob, String originalValue,
														 String updatedValue) {
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(oppId);
		opportunity.setBusinessType(lob.getFirstPossibleLOBString());
		OppChangeFieldResult oppChangeFieldResult = new OppChangeFieldResult(opportunity, "effectiveDate",
				originalValue);
		oppChangeFieldResult.setFieldUpdatedValue(updatedValue);
		return oppChangeFieldResult;
	}

}

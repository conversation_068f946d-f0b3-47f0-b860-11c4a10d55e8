package com.lmig.uscm.booktransfer.opportunity.domain;

import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.MassUpdateResultsOfFieldChange;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeField;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class MassUpdateResultsOfFieldChangeTest {
	@Test
	void testAddingStatuses() throws Exception {
		MassUpdateResultsOfFieldChange massUpdateFields = new MassUpdateResultsOfFieldChange();

		OppChangeFieldResult oppChangeFieldResult1 = addItemToMassUpdateFields(massUpdateFields, 1, "1");
		OppChangeFieldResult oppChangeFieldResult2 = addItemToMassUpdateFields(massUpdateFields, 2, "1");
		OppChangeFieldResult oppChangeFieldResult3 = addItemToMassUpdateFields(massUpdateFields, 3, "3");
		OppChangeFieldResult oppChangeFieldResult4 = addItemToMassUpdateFields(massUpdateFields, 4, "3");
		OppChangeFieldResult oppChangeFieldResult5 = addItemToMassUpdateFields(massUpdateFields, 5, "5");
		OppChangeFieldResult oppChangeFieldResult6 = addItemToMassUpdateFields(massUpdateFields, 6, "6");

		assertThat(massUpdateFields.getAllFieldNamesToChange())
				.containsOnly("status");
		assertThat(massUpdateFields.getAllFieldValuesToChange("status"))
				.containsOnly("1", "3", "5", "6");
		assertThat(massUpdateFields.getAllOpportunitiesResults("status", "1"))
				.containsOnly(oppChangeFieldResult1, oppChangeFieldResult2);
		assertThat(massUpdateFields.getAllOpportunitiesResults("status", "3"))
				.containsOnly(oppChangeFieldResult3, oppChangeFieldResult4);
		assertThat(massUpdateFields.getAllOpportunitiesResults("status", "5"))
				.containsOnly(oppChangeFieldResult5);
		assertThat(massUpdateFields.getAllOpportunitiesResults("status", "6"))
				.containsOnly(oppChangeFieldResult6);

	}

	private OppChangeFieldResult addItemToMassUpdateFields(
			MassUpdateResultsOfFieldChange massUpdateFields, int opportunityId, String fieldValue)
			throws Exception {
		return addItemToMassUpdateFields(massUpdateFields, new Opportunity(opportunityId, 1), fieldValue, "status");
	}

	private OppChangeFieldResult addItemToMassUpdateFields(
			MassUpdateResultsOfFieldChange massUpdateFields, Opportunity opportunity, String fieldValue, String fieldName) {
		OppChangeField changeField1 = new OppChangeField(opportunity.getOpportunityId(), fieldName, fieldValue);
		OppChangeFieldResult oppChangeFieldResult = new OppChangeFieldResult(opportunity, changeField1);

		massUpdateFields.addOpportunityChangeResult(oppChangeFieldResult);
		return oppChangeFieldResult;
	}

	@Test
	void testAddingMultipleField() throws Exception {
		Opportunity opportunity1 = new Opportunity(1, 1);
		Opportunity opportunity2 = new Opportunity(2, 1);
		Opportunity opportunity3 = new Opportunity(3, 1);
		Opportunity opportunity4 = new Opportunity(4, 1);
		Opportunity opportunity5 = new Opportunity(5, 1);

		MassUpdateResultsOfFieldChange massUpdateFields = new MassUpdateResultsOfFieldChange();

		OppChangeFieldResult oppChangeFieldResult1 = addItemToMassUpdateFields(massUpdateFields, opportunity1, "1",
				"status");
		OppChangeFieldResult oppChangeFieldResult2 = addItemToMassUpdateFields(massUpdateFields, opportunity2, "1",
				"notStatus");
		OppChangeFieldResult oppChangeFieldResult3 = addItemToMassUpdateFields(massUpdateFields, opportunity3, "1",
				"other");
		OppChangeFieldResult oppChangeFieldResult4 = addItemToMassUpdateFields(massUpdateFields, opportunity1, "3",
				"other");
		OppChangeFieldResult oppChangeFieldResult5 = addItemToMassUpdateFields(massUpdateFields, opportunity4, "2",
				"status");
		OppChangeFieldResult oppChangeFieldResult6 = addItemToMassUpdateFields(massUpdateFields, opportunity5, "3",
				"other");

		assertThat(massUpdateFields.getAllFieldNamesToChange()).containsOnly("status", "notStatus", "other");
		assertThat(massUpdateFields.getAllFieldValuesToChange("status")).containsOnly("1", "2");
		assertThat(massUpdateFields.getAllFieldValuesToChange("notStatus")).containsOnly("1");
		assertThat(massUpdateFields.getAllFieldValuesToChange("other")).containsOnly("1", "3");
		assertThat(massUpdateFields.getAllOpportunitiesResults("status", "1")).containsOnly(oppChangeFieldResult1);
		assertThat(massUpdateFields.getAllOpportunitiesResults("status", "2")).containsOnly(oppChangeFieldResult5);
		assertThat(massUpdateFields.getAllOpportunitiesResults("notStatus", "1")).containsOnly(oppChangeFieldResult2);
		assertThat(massUpdateFields.getAllOpportunitiesResults("other", "1")).containsOnly(oppChangeFieldResult3);
		assertThat(massUpdateFields.getAllOpportunitiesResults("other", "3")).containsOnly(oppChangeFieldResult4,
				oppChangeFieldResult6);
	}

	@Test
	void testGettingAllSuccessFuelOpportunities() throws Exception {
		Opportunity failedOpp = new Opportunity(1, 1);
		Opportunity notFailedOpp = new Opportunity(2, 1);

		MassUpdateResultsOfFieldChange massUpdateFields = new MassUpdateResultsOfFieldChange();

		OppChangeFieldResult oppResultFailed = addItemToMassUpdateFields(massUpdateFields, failedOpp, "1", "status")
				.isError(null);
		OppChangeFieldResult oppResultNotFailed = addItemToMassUpdateFields(massUpdateFields, notFailedOpp, "1",
				"status");

		assertThat(massUpdateFields.getAllNotFailedOpportunities("status", "1")).containsOnly(notFailedOpp);
		assertThat(massUpdateFields.getAllOpportunitiesResults("status", "1")).containsOnly(oppResultFailed,
				oppResultNotFailed);

	}

}

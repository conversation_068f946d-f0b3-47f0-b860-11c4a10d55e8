package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.UtilityService;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mockito;
import org.w3c.dom.Document;

import javax.xml.xpath.XPathExpressionException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;

class MotorcycleCreationStrategyTest extends CreationStrategyTest {

	MotorcycleCreationStrategy creationStrategy = new MotorcycleCreationStrategy();

	@Test
	void testMotorcycleStrategyShouldResolve() throws Exception {
		OpportunityCreationRequest homeRequest = buildCreationRequest("src/test/resources/xml/accordhome.xml");
		OpportunityCreationRequest autoTwoVehRequest = buildCreationRequest("src/test/resources/xml/auto.xml");
		OpportunityCreationRequest autoTwoCarOneCycleVehRequest = buildCreationRequest("src/test/resources/xml/autoAndMtc.xml");
		OpportunityCreationRequest autoSixCycleVehRequest = buildCreationRequest("src/test/resources/xml/autoMtcOnly6thCar.xml");
		OpportunityCreationRequest autoTwoCycleByManufacturerOnlyVehRequest = buildCreationRequest("src/test/resources/xml/auto_mtrByManufacturer.xml");
		OpportunityCreationRequest autoTwoCycleByPolicyNumberOnlyVehRequest = buildCreationRequest("src/test/resources/xml/auto_mtrByPolicyNum.xml");
		OpportunityCreationRequest autoDecoyMtcRequest = buildCreationRequest("src/test/resources/xml/autoVehManufacturerContainsMtcButIsNot.xml");

		boolean shouldResolveHome = creationStrategy.shouldResolveCreationRequest(homeRequest);
		boolean shouldResolveTwoVehicleAuto = creationStrategy.shouldResolveCreationRequest(autoTwoVehRequest);
		boolean shouldResolveTwoCarOneCycleVehicleAuto = creationStrategy.shouldResolveCreationRequest(autoTwoCarOneCycleVehRequest);
		boolean shouldResolveSixCycleAuto = creationStrategy.shouldResolveCreationRequest(autoSixCycleVehRequest);
		boolean shouldResolveTwoMtrCycle = creationStrategy.shouldResolveCreationRequest(autoTwoCycleByManufacturerOnlyVehRequest);
		boolean shouldResolveTwoMtrCyclePolicyNum = creationStrategy.shouldResolveCreationRequest(autoTwoCycleByPolicyNumberOnlyVehRequest);
		boolean shouldResolveMtcDecoy = creationStrategy.shouldResolveCreationRequest(autoDecoyMtcRequest);

		assertFalse(shouldResolveHome);
		assertFalse(shouldResolveTwoVehicleAuto);
		assertFalse(shouldResolveMtcDecoy);

		assertTrue(shouldResolveTwoCarOneCycleVehicleAuto);
		assertTrue(shouldResolveSixCycleAuto);
		assertTrue(shouldResolveTwoMtrCycle);
		assertTrue(shouldResolveTwoMtrCyclePolicyNum);
	}

	@ParameterizedTest
	@CsvSource({
		"src/test/resources/xml/autoMtcOnly6thCar.xml, 6",
		"src/test/resources/xml/auto_mtrByManufacturer.xml, 2",
		"src/test/resources/xml/auto_mtrByPolicyNum.xml, 2"
	})
	 void testCreateMotorcycle(String requestFile, int expectedVehs) throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest motorcycleRequest = buildCreationRequest(requestFile);
		CreationStrategyBundle motorcycleBundle = buildCreationBundle(motorcycleRequest);

		assertEquals(LineOfBusiness.AUTOP, LineOfBusiness.determineLobForPersonalLineType(motorcycleRequest.getUploadedACORD()));

		List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(motorcycleBundle));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(1, bundles.get(0).getRequests().size());
		assertEquals(1, bundles.size());
		assertEquals(LineOfBusiness.MTR, LineOfBusiness.determineLobForPersonalLineType(bundles.get(0).getRequests().get(0).getUploadedACORD()));
		assertEquals(expectedVehs, AcordHelper.getPersVehs(bundles.get(0).getRequests().get(0).getUploadedACORD()).getLength());
	}

	@Test
	void testCreateFromMixedPolicy() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest autoRequest = buildCreationRequest("src/test/resources/xml/autoAndMtc.xml");
		CreationStrategyBundle autoBundle = buildCreationBundle(autoRequest);

		assertEquals(LineOfBusiness.AUTOP, LineOfBusiness.determineLobForPersonalLineType(autoRequest.getUploadedACORD()));

		List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(autoBundle));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(2, bundles.size());
		assertEquals(1, bundles.get(0).getRequests().size());
		assertEquals(1, bundles.get(1).getRequests().size());
		assertEquals(LineOfBusiness.AUTOP, LineOfBusiness.determineLobForPersonalLineType(bundles.get(0).getRequests().get(0).getUploadedACORD()));
		assertEquals(LineOfBusiness.MTR, LineOfBusiness.determineLobForPersonalLineType(bundles.get(1).getRequests().get(0).getUploadedACORD()));
		assertEquals(2, AcordHelper.getPersVehs(bundles.get(0).getRequests().get(0).getUploadedACORD()).getLength());
		assertEquals(1, AcordHelper.getPersVehs(bundles.get(1).getRequests().get(0).getUploadedACORD()).getLength());
	}

	@Test
	void testCreateFromMixedPolicyFailsWhenMTRFails() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest autoRequest = buildCreationRequest("src/test/resources/xml/autoAndMtc.xml");
		CreationStrategyBundle autoBundle = buildCreationBundle(autoRequest);

		assertEquals(LineOfBusiness.AUTOP, LineOfBusiness.determineLobForPersonalLineType(autoRequest.getUploadedACORD()));

		List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(autoBundle));

		MotorcycleCreationStrategy spy = Mockito.spy(MotorcycleCreationStrategy.class);

		doThrow(new XPathExpressionException("Mocked exception")).when(spy).splitMotorcycleRequests(any(), any());

		spy.resolveCreationBundles(bundles, response);

		// Check that both errored
		assertEquals(2, (int) response.getFailedCreationCount());
		assertEquals(0, bundles.size());
	}

	@Test
	public void testCreateMotorcycleFromManufacturer() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest motorcycleRequest = buildCreationRequest("src/test/resources/xml/auto_mtrByManufacturer.xml");
		CreationStrategyBundle motorcycleBundle = buildCreationBundle(motorcycleRequest);

		assertEquals(LineOfBusiness.AUTOP, LineOfBusiness.determineLobForPersonalLineType(motorcycleRequest.getUploadedACORD()));

		List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(motorcycleBundle));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(1, bundles.get(0).getRequests().size());
		assertEquals(1, bundles.size());
		assertEquals(LineOfBusiness.MTR, LineOfBusiness.determineLobForPersonalLineType(bundles.get(0).getRequests().get(0).getUploadedACORD()));
		assertEquals(2, AcordHelper.getPersVehs(bundles.get(0).getRequests().get(0).getUploadedACORD()).getLength());
	}

	@Test
	public void testCreateMotorcycleFromPolicyNum() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest motorcycleRequest = buildCreationRequest("src/test/resources/xml/auto_mtrByPolicyNum.xml");
		CreationStrategyBundle motorcycleBundle = buildCreationBundle(motorcycleRequest);

		assertEquals(LineOfBusiness.AUTOP, LineOfBusiness.determineLobForPersonalLineType(motorcycleRequest.getUploadedACORD()));

		List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(motorcycleBundle));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(1, bundles.get(0).getRequests().size());
		assertEquals(1, bundles.size());
		assertEquals(LineOfBusiness.MTR, LineOfBusiness.determineLobForPersonalLineType(bundles.get(0).getRequests().get(0).getUploadedACORD()));
		assertEquals(2, AcordHelper.getPersVehs(bundles.get(0).getRequests().get(0).getUploadedACORD()).getLength());
	}

	@Test
	public void testCreateFromMixedPolicyAndCalculateCurrentTermAmt() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest autoAndMotorcycleRequest = buildCreationRequest("src/test/resources/xml/autoMtr22Vehs.xml");
		CreationStrategyBundle autoAndMotorcycleBundle = buildCreationBundle(autoAndMotorcycleRequest);

		assertEquals(LineOfBusiness.AUTOP, LineOfBusiness.determineLobForPersonalLineType(autoAndMotorcycleRequest.getUploadedACORD()));

		List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(autoAndMotorcycleBundle));

		// Grab auto pers policy term amt before strategies are applied
		double autoPersPolicyTermAmtBefore = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(0).getUploadedACORD()));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(2, bundles.size());
		Document motorcycleDocument = bundles.get(1).getRequests().get(0).getUploadedACORD();
		assertEquals(5, AcordHelper.getPersVehs(motorcycleDocument).getLength());
		assertEquals(LineOfBusiness.MTR, LineOfBusiness.determineLobForPersonalLineType(motorcycleDocument));

		// Assert that the pers policy current term amount of motorcycle document is equal to sum of pers veh term amts
		double mtrPersPolicyTermAmt = UtilityService.convertStringToDouble(AcordHelper.getPolicyCurrentTermAmt(motorcycleDocument));
		double mtrTotalPersVehCoverageTermAmt = AcordHelper.getPersVehTotalCoverageCurrentTermAmt(motorcycleDocument);
		assertEquals((int) mtrPersPolicyTermAmt, (int) mtrTotalPersVehCoverageTermAmt);

		// Assert that the new auto pers policy is the old amount minus the sum of motorcycle pers policy amts
		double autoPersPolicyTermAmtAfter = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(0).getUploadedACORD()));
		assertEquals((int) (autoPersPolicyTermAmtBefore - mtrPersPolicyTermAmt), (int) autoPersPolicyTermAmtAfter);
	}
}

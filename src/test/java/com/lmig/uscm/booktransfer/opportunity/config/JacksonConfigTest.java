package com.lmig.uscm.booktransfer.opportunity.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.uscm.booktransfer.opportunity.client.domain.ScheduleRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import org.javatuples.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ContextConfiguration(classes = {JacksonConfig.class})
@ExtendWith(SpringExtension.class)
public class JacksonConfigTest {

    @Autowired
    ObjectMapper objectMapper;

    @Test
    void testLocalDateTimeDeserialization() throws JsonProcessingException {
        String msg = "{" +
                "\"effectiveDate\":\"2023-05-31\"," +
                "\"timestampCallPartner\":\"2023-05-18 12:47:46.243\"," +
                "\"timestampFirstCallPartner\":\"2023-05-18 12:47:33.260\"," +
                "\"timestampUpload\":\"2023-03-17T14:57:05.017\"" +
                "}";

        Opportunity opp = objectMapper.readValue(msg, Opportunity.class);

        assertEquals("2023-05-31", opp.getEffectiveDate());
        assertEquals(
                LocalDateTime.of(2023, 5, 18, 12, 47, 46, 243000000), opp.getTimestampCallPartner());
        assertEquals(
                LocalDateTime.of(2023, 5, 18, 12, 47, 33, 260000000), opp.getTimestampFirstCallPartner());
        assertEquals(
                LocalDateTime.of(2023, 3, 17, 14, 57, 5, 17000000), opp.getTimestampUpload());
    }

    @Test
    void testJavaTuplesPairConversion() throws JsonProcessingException {
        String msg = "{" +
                "\"assignedUser\":{\"key\" : \"a\", \"value\" : \"b\"}" +
                "}";

        ScheduleRequest req = objectMapper.readValue(msg, ScheduleRequest.class);

        assertEquals(Pair.with("a", "b"), req.getAssignedUser());
    }
}

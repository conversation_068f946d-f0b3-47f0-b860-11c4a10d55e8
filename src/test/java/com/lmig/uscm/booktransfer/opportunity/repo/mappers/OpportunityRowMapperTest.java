package com.lmig.uscm.booktransfer.opportunity.repo.mappers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

class OpportunityRowMapperTest {
    private OpportunityRowMapper opportunityRowMapper;

    @BeforeEach
    public void setup() {
        opportunityRowMapper = new OpportunityRowMapper();
    }

    @Test
    void testDefaultStatusIfNull_withNonNullArgument_shouldReturnArgument() {
        final Integer result = opportunityRowMapper.defaultStatusIfNull(1);
        assertEquals(1, result);
    }

    @Test
    void testDefaultStatusIfNull_withNullArgument_shouldReturnDefaultValue() {
        final Integer result = opportunityRowMapper.defaultStatusIfNull(null);
        assertEquals(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode(), result);
    }

    @Test
    void testConvertTimestampToLocalDateTime_withValidTimestamp_shouldReturnCorrectLocalDateTime() {
        final LocalDateTime inputLocalDateTime = LocalDateTime.now();
        final Timestamp input = Timestamp.valueOf(inputLocalDateTime);
        final LocalDateTime result = opportunityRowMapper.convertTimestampToLocalDateTime(input);
        assertEquals(inputLocalDateTime, result);
    }

    @Test
    void testConvertTimestampToLocalDateTime_withNullTimestamp_shouldReturnNull() {
        final LocalDateTime result = opportunityRowMapper.convertTimestampToLocalDateTime(null);
        assertNull(result);
    }
}

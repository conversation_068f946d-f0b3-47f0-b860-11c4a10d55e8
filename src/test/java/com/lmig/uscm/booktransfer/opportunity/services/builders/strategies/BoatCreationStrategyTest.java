package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.PersonalAcordXPaths;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertEquals;

class BoatCreationStrategyTest extends CreationStrategyTest {

    BoatCreationStrategy creationStrategy = new BoatCreationStrategy();
    FifthBoatCreationStrategy fifthBoatCreationStrategy = new FifthBoatCreationStrategy();

    @ParameterizedTest
    @MethodSource("buildArgumentsForShouldResolve")
    void testBoatStrategyShouldResolve(OpportunityCreationRequest creationRequest, boolean expected) {
        assertEquals(expected, creationStrategy.shouldResolveCreationRequest(creationRequest));
    }

    @Test
    void testBoatCreationFromHomeXml() throws ParserConfigurationException, IOException, SAXException, XPathExpressionException {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        String xml = "<ACORD><InsuranceSvcRq><HomePolicyQuoteInqRq><PersPolicy><CurrentTermAmt><Amt>1000</Amt></CurrentTermAmt></PersPolicy><HomeLineBusiness>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>64</Amt></CurrentTermAmt></Coverage>" +
                "<Coverage><CurrentTermAmt><Amt>43</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>32</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>98</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>62</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "<LOBCd>HOME</LOBCd></HomeLineBusiness></HomePolicyQuoteInqRq></InsuranceSvcRq></ACORD>";
        OpportunityCreationRequest homeRequest = buildCreationRequestFromXml(xml);
        CreationStrategyBundle homeBundle = buildCreationBundle(homeRequest);

        assertEquals(LineOfBusiness.HOME, LineOfBusiness.determineLobForPersonalLineType(homeRequest.getUploadedACORD()));

        List<CreationStrategyBundle> bundles = List.of(homeBundle);

        creationStrategy.resolveCreationBundles(bundles, response);

        // Check that none errored
        assertEquals(0, (int) response.getFailedCreationCount());
        assertEquals(2, bundles.get(0).getRequests().size());
        assertEquals(1, bundles.size());

        Document homeDocument = bundles.get(0).getRequests().get(0).getUploadedACORD();
        assertEquals(LineOfBusiness.HOME, LineOfBusiness.determineLobForPersonalLineType(homeDocument));
        assertEquals("701.00", AcordHelper.getPolicyCurrentTermAmt(homeDocument));

        Document boatDocument = bundles.get(0).getRequests().get(1).getUploadedACORD();
        assertEquals(LineOfBusiness.BOAT, LineOfBusiness.determineLobForPersonalLineType(boatDocument));
        assertEquals("299.00", AcordHelper.getPolicyCurrentTermAmt(boatDocument));

        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
        assertFalse(bundles.get(0).getRequests().get(1).isMasterOpp());
    }

    @Test
    void testSplitFifthBoatStrategy_FromHomelinePolicy_CalculatesCorrectPersPolicyQuotes() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest boatRequest = buildCreationRequestFromXml("<ACORD><InsuranceSvcRq><HomePolicyQuoteInqRq>" +
                "<PersPolicy><CurrentTermAmt><Amt>5000</Amt></CurrentTermAmt></PersPolicy><HomeLineBusiness>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>50</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>25</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "</HomeLineBusiness></HomePolicyQuoteInqRq></InsuranceSvcRq></ACORD>");

        CreationStrategyBundle splitFifthBoatBundle = buildCreationBundle(boatRequest);

        List<CreationStrategyBundle> bundles = List.of(splitFifthBoatBundle);

        creationStrategy.resolveCreationBundles(bundles, response);
        fifthBoatCreationStrategy.resolveCreationBundles(bundles, response);

        assertEquals(3, bundles.get(0).getRequests().size());

        assertEquals("4025.00", AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(0).getUploadedACORD()));
        assertEquals("725.00", AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(1).getUploadedACORD()));
        assertEquals("250.00", AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(2).getUploadedACORD()));

        assertFalse(bundles.get(0).getRequests().get(0).isDerivedFromHome());
        assertTrue(bundles.get(0).getRequests().get(1).isDerivedFromHome());

        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
        assertFalse(bundles.get(0).getRequests().get(1).isMasterOpp());
    }

    @Test
    void splitFifthBoatStrategyFromHomePolicyQuoteInqRqCalculatesCorrectPersPolicyQuotes() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest boatRequest = buildCreationRequestFromXml("<ACORD><InsuranceSvcRq><HomePolicyQuoteInqRq>" +
                "<PersPolicy><CurrentTermAmt><Amt>5000</Amt></CurrentTermAmt></PersPolicy>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>50</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>25</Amt></CurrentTermAmt></Coverage></WatercraftAccessory><HomeLineBusiness>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft> </HomeLineBusiness>" +
                "</HomePolicyQuoteInqRq></InsuranceSvcRq></ACORD>");

        CreationStrategyBundle splitFifthBoatBundle = buildCreationBundle(boatRequest);

        List<CreationStrategyBundle> bundles = List.of(splitFifthBoatBundle);

        creationStrategy.resolveCreationBundles(bundles, response);
        fifthBoatCreationStrategy.resolveCreationBundles(bundles, response);

        assertEquals(3, bundles.get(0).getRequests().size());

        assertEquals("3825.00", AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(0).getUploadedACORD()));
        assertEquals("725.00", AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(1).getUploadedACORD()));
        assertEquals("450.00", AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getRequests().get(2).getUploadedACORD()));

        assertFalse(bundles.get(0).getRequests().get(0).isDerivedFromHome());
        assertTrue(bundles.get(0).getRequests().get(1).isDerivedFromHome());

        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
        assertFalse(bundles.get(0).getRequests().get(1).isMasterOpp());
    }

    @Test
    void testBoatCreationStrategy_FromWatercraftPolicy_SetsMasterOpportunity() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest boatRequest = buildCreationRequestFromXml("<ACORD><InsuranceSvcRq><WatercraftPolicyQuoteInqRq>" +
                "<PersPolicy><CurrentTermAmt><Amt>5000</Amt></CurrentTermAmt></PersPolicy><WatercraftLineBusiness>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>200</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>100</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>25</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "</WatercraftLineBusiness></WatercraftPolicyQuoteInqRq></InsuranceSvcRq></ACORD>");

        CreationStrategyBundle splitFifthBoatBundle = buildCreationBundle(boatRequest);

        List<CreationStrategyBundle> bundles = List.of(splitFifthBoatBundle);

        creationStrategy.resolveCreationBundles(bundles, response);

        assertEquals(1, bundles.get(0).getRequests().size());

        assertFalse(bundles.get(0).getRequests().get(0).isDerivedFromHome());
        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
    }

    private static Stream<Arguments> buildArgumentsForShouldResolve() throws ParserConfigurationException, IOException, SAXException {
        String homeXmlWithWatercraft = "<ACORD><HomeLineBusiness><Watercraft></Watercraft></HomeLineBusiness></ACORD>";
        String homeXmlWithWatercraftAccessory = "<ACORD><HomeLineBusiness><WatercraftAccessory></WatercraftAccessory></HomeLineBusiness></ACORD>";
        String homeXmlWithoutWatercraft = "<ACORD><HomeLineBusiness></HomeLineBusiness></ACORD>";
        return Stream.of(
                Arguments.of(buildCreationRequestFromXml(homeXmlWithWatercraft), true),
                Arguments.of(buildCreationRequestFromXml(homeXmlWithWatercraftAccessory), true),
                Arguments.of(buildCreationRequestFromXml(homeXmlWithoutWatercraft), false)
        );
    }

    @Test
    void testBoatCreationStrategy_FromHomeLinePolicy_CopiesPLAndMEDPMCoveragesToWatercraftNodes() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest homeRequest = buildCreationRequestFromXml("<ACORD><InsuranceSvcRq><HomePolicyQuoteInqRq>" +
                "<PersPolicy><CurrentTermAmt><Amt>1000</Amt></CurrentTermAmt></PersPolicy><HomeLineBusiness>" +
                "<Dwell><Coverage><CoverageCd>MEDPM</CoverageCd></Coverage><Coverage><CoverageCd>PL</CoverageCd></Coverage></Dwell>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>64</Amt></CurrentTermAmt></Coverage>" +
                "<Coverage><CurrentTermAmt><Amt>43</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<Watercraft><Coverage><CurrentTermAmt><Amt>32</Amt></CurrentTermAmt></Coverage></Watercraft>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>98</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "<WatercraftAccessory><Coverage><CurrentTermAmt><Amt>62</Amt></CurrentTermAmt></Coverage></WatercraftAccessory>" +
                "<LOBCd>HOME</LOBCd></HomeLineBusiness></HomePolicyQuoteInqRq></InsuranceSvcRq></ACORD>");

        CreationStrategyBundle homeBundle = buildCreationBundle(homeRequest);

        assertEquals(LineOfBusiness.HOME, LineOfBusiness.determineLobForPersonalLineType(homeRequest.getUploadedACORD()));

        List<CreationStrategyBundle> bundles = List.of(homeBundle);

        creationStrategy.resolveCreationBundles(bundles, response);

        // Check that none errored
        assertEquals(0, (int) response.getFailedCreationCount());
        assertEquals(2, bundles.get(0).getRequests().size());
        assertEquals(1, bundles.size());

        Document homeDocument = bundles.get(0).getRequests().get(0).getUploadedACORD();
        assertEquals(LineOfBusiness.HOME, LineOfBusiness.determineLobForPersonalLineType(homeDocument));
        assertEquals("701.00", AcordHelper.getPolicyCurrentTermAmt(homeDocument));

        Document boatDocument = bundles.get(0).getRequests().get(1).getUploadedACORD();
        assertEquals(LineOfBusiness.BOAT, LineOfBusiness.determineLobForPersonalLineType(boatDocument));
        assertEquals("299.00", AcordHelper.getPolicyCurrentTermAmt(boatDocument));

        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
        assertFalse(bundles.get(0).getRequests().get(1).isMasterOpp());

        // Assert that PL and MEDPM nodes were copied to both watercraft nodes
        NodeList dwellPLNodesInWatercraftDocument = XmlHelper.getNodeList(boatDocument, String.format("//%s", PersonalAcordXPaths.COVERAGE_PL));
        NodeList dwellMEDPMNodesInWatercraftDocument = XmlHelper.getNodeList(boatDocument, String.format("//%s", PersonalAcordXPaths.COVERAGE_MEDPM));
        assertEquals(2, dwellPLNodesInWatercraftDocument.getLength());
        assertEquals(2, dwellMEDPMNodesInWatercraftDocument.getLength());
    }
}

package com.lmig.uscm.booktransfer.opportunity.controller;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.PolicyType;
import com.lmig.uscm.booktransfer.opportunity.repo.LobGateway;
import com.lmig.uscm.booktransfer.opportunity.repo.LobInMemoryPersistence;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class LobControllerTest {
    private static LobController objectUnderTest;

    @BeforeEach
    public void setUp() {
        LobGateway lobGateway = new LobInMemoryPersistence();
        objectUnderTest = new LobController(lobGateway);
    }

    @Test
    void testGetAllLobs() {
        Map<String, List<String>> allLobs = objectUnderTest.getAllLobs();
        assertNotNull(allLobs);
        assertEquals(3, allLobs.size());
        // Should have Condo and Renters in Personal lines
        assertTrue(allLobs.get(LineType.Personal.name()).contains(PolicyType.CONDO.name()));
        assertTrue(allLobs.get(LineType.Personal.name()).contains(PolicyType.RENTERS.name()));
        assertTrue(allLobs.get(LineType.Business.name()).contains(LineOfBusiness.INMRC.name()));
        assertTrue(allLobs.get(LineType.Business.name()).contains(LineOfBusiness.CFRM.name()));
    }
}

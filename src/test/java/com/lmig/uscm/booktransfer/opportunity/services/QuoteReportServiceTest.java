package com.lmig.uscm.booktransfer.opportunity.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.times;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.lmig.uscm.booktransfer.opportunity.domain.QuoteReportBulkUpdate;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.ResponseEntity;

import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.config.properties.ServiceUrlProperties;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class QuoteReportServiceTest {
  @Mock
  WebClient quoteReportServiceWebClient;

  @Mock
  private WebClient.RequestHeadersUriSpec requestHeadersUriSpecMock;

  @Mock
  private WebClient.RequestBodyUriSpec requestBodyUriSpecMock;

  @Mock
  private WebClient.RequestBodySpec requestBodySpecMock;

  @SuppressWarnings("rawtypes")
  @Mock
  private WebClient.RequestHeadersSpec requestHeadersSpecMock;

  @Mock
  private WebClient.ResponseSpec responseSpecMock;

  @Test
  void getQuoteReportByOpportunityId() {
    QuoteReportService instance = new QuoteReportService(null, null) {
      @Override
      public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityId(final int opportunityId) {
        QuoteReportItemLegacy item = new QuoteReportItemLegacy();
        item.setQuoteReportID(4);
        QuoteReportItemLegacy item1 = new QuoteReportItemLegacy();
        item1.setQuoteReportID(44);
        return Arrays.asList(item, item1);
      }
    };
    assertEquals(44, instance.getQuoteReportByOpportunityId(0).getQuoteReportID());
  }

  @Test
  void getQuoteReportByOpportunityIdNoneReturned() {
    QuoteReportService instance = new QuoteReportService(null, null) {
      @Override
      public List<QuoteReportItemLegacy> getAllQuoteReportsForOpportunityId(final int opportunityId) {
        return new ArrayList<>();
      }
    };
    assertNull(instance.getQuoteReportByOpportunityId(0));
  }

  @Test
  void getAllQuoteReportsForOpportunityId() {
    ServiceUrlProperties properties = new ServiceUrlProperties();
    List<QuoteReportItemLegacy> quoteReports = new ArrayList<>();
    QuoteReportItemLegacy quoteReportLegacy = new QuoteReportItemLegacy();
    quoteReports.add(quoteReportLegacy);

    when(quoteReportServiceWebClient.get()).thenReturn(requestHeadersUriSpecMock);
    when(requestHeadersUriSpecMock.uri(anyString())).thenReturn(requestHeadersUriSpecMock);
    when(requestHeadersUriSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.bodyToMono(any(ParameterizedTypeReference.class))).thenReturn(Mono.just(quoteReports));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    assertNotNull(instance.getAllQuoteReportsForOpportunityId(1));
  }

  @Test
  void getAllQuoteReportsForOpportunityIdNoContent() {
    ServiceUrlProperties properties = new ServiceUrlProperties();
    List<QuoteReportItemLegacy> quoteReports = new ArrayList<>();
    when(quoteReportServiceWebClient.get()).thenReturn(requestHeadersUriSpecMock);
    when(requestHeadersUriSpecMock.uri(anyString())).thenReturn(requestHeadersUriSpecMock);
    when(requestHeadersUriSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.bodyToMono(any(ParameterizedTypeReference.class))).thenReturn(Mono.just(quoteReports));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    assertNull(instance.getAllQuoteReportsForOpportunityId(1));
  }

  //@SuppressWarnings("unchecked")
  @Test
  void getAllQuoteReportsForOpportunityIdsWithEmptyResponse() throws OpportunityException {
    List<QuoteReportItemLegacy> quoteReports = new ArrayList<>();

    when(quoteReportServiceWebClient.post()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.bodyToMono(any(ParameterizedTypeReference.class))).thenReturn(Mono.just(quoteReports));
    ServiceUrlProperties properties = new ServiceUrlProperties();

    List<Integer> quoteReportIds = new ArrayList<>();
    quoteReportIds.add(1);
    quoteReportIds.add(2);
    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    List<QuoteReportItemLegacy> quoteReportsResponse=  instance.getAllQuoteReportsForOpportunityIds(quoteReportIds);

    assertEquals(quoteReportsResponse.size(), quoteReports.size());
  }

  @SuppressWarnings("unchecked")
  @Test
  void getAllQuoteReportsForOpportunityIds() throws OpportunityException {
    List<QuoteReportItemLegacy> quoteReports = new ArrayList<>();
    QuoteReportItemLegacy quoteReportLegacy = new QuoteReportItemLegacy();
    quoteReports.add(quoteReportLegacy);
    ServiceUrlProperties properties = new ServiceUrlProperties();

    when(quoteReportServiceWebClient.post()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.bodyToMono(any(ParameterizedTypeReference.class))).thenReturn(Mono.just(quoteReports));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    assertEquals(1, instance.getAllQuoteReportsForOpportunityIds(List.of(1)).size());
  }

  @SuppressWarnings("unchecked")
  @Test
  void getAllQuoteReportsForOpportunityIdsErrorResponse() {
    ServiceUrlProperties properties = new ServiceUrlProperties();

    Exception e = new Exception();
    when(quoteReportServiceWebClient.post()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.bodyToMono(any(ParameterizedTypeReference.class))).thenReturn(Mono.error(new OpportunityException(e)));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    assertThrows(OpportunityException.class, () -> instance.getAllQuoteReportsForOpportunityIds(List.of(1)));
  }

  @SuppressWarnings("unchecked")
  @Test
  void addQuoteReport() {
    Integer quoteReportId = 9;
    ServiceUrlProperties properties = new ServiceUrlProperties();

    when(quoteReportServiceWebClient.post()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.bodyToMono(ArgumentMatchers.<Class<Integer>>notNull())).thenReturn(Mono.just(quoteReportId));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    assertNotNull(instance.addQuoteReport(new QuoteReportItemLegacy()));
  }

  @SuppressWarnings("unchecked")
  @Test
  void updateQuoteReport() {
    ServiceUrlProperties properties = new ServiceUrlProperties();

    when(quoteReportServiceWebClient.put()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.toEntity(Void.class)).thenReturn(Mono.just(ResponseEntity.ok().build()));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    assertNotNull(instance.updateQuoteReport(new QuoteReportItemLegacy()));
  }

  @SuppressWarnings("unchecked")
  @Test
  void updateQuoteReportErrorResponse() {
    ServiceUrlProperties properties = new ServiceUrlProperties();

    when(quoteReportServiceWebClient.put()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.toEntity(Void.class)).thenReturn(Mono.just(ResponseEntity.badRequest().build()));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    assertNull(instance.updateQuoteReport(new QuoteReportItemLegacy()));
  }

  @SuppressWarnings("unchecked")
  @Test
  void updateBulkQuoteReports() {
    ServiceUrlProperties properties = new ServiceUrlProperties();
    when(quoteReportServiceWebClient.put()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.toEntity(String.class)).thenReturn(Mono.just(ResponseEntity.ok().build()));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    assertNotNull(instance.updateBulkQuoteReports(List.of(new QuoteReportItemLegacy())));
  }

  @SuppressWarnings("unchecked")
  @Test
  void updateBulkQuoteReportsErrorResponse() {
    ServiceUrlProperties properties = new ServiceUrlProperties();

    when(quoteReportServiceWebClient.put()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.toEntity(String.class)).thenReturn(Mono.just(ResponseEntity.badRequest().build()));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    assertNull(instance.updateBulkQuoteReports(List.of(new QuoteReportItemLegacy())));
  }

  @Test
  void patchQuoteReport() {
    QuoteReportBulkUpdate bulkPatch = new QuoteReportBulkUpdate();
    ServiceUrlProperties properties = new ServiceUrlProperties();

    when(quoteReportServiceWebClient.patch()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.toEntity(String.class)).thenReturn(Mono.just(ResponseEntity.ok().build()));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    instance.patchQuoteReportFields(bulkPatch);

    verify(quoteReportServiceWebClient, times(1)).patch();
  }

  @Test
  void patchQuoteReportErrorResponse() {
    QuoteReportBulkUpdate bulkPatch = new QuoteReportBulkUpdate();
    ServiceUrlProperties properties = new ServiceUrlProperties();

    when(quoteReportServiceWebClient.patch()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.toEntity(String.class)).thenReturn(Mono.just(ResponseEntity.badRequest().build()));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    instance.patchQuoteReportFields(bulkPatch);

    verify(quoteReportServiceWebClient, times(1)).patch();
  }

  @Test
  void patchQuoteReportFieldsWithEffectiveDate() {
    List<QuoteReportItemLegacy> quoteReports = new ArrayList<>();
    QuoteReportItemLegacy quoteReportLegacy = new QuoteReportItemLegacy();
    quoteReports.add(quoteReportLegacy);
    ServiceUrlProperties properties = new ServiceUrlProperties();

    when(quoteReportServiceWebClient.patch()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.toEntity(String.class)).thenReturn(Mono.just(ResponseEntity.ok().build()));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    instance.patchQuoteReportFields(quoteReports);

    verify(quoteReportServiceWebClient, times(1)).patch();
  }

  @Test
  void patchQuoteReportFieldsWithEffectiveDateErrorResponse() {
    List<QuoteReportItemLegacy> quoteReports = new ArrayList<>();
    QuoteReportItemLegacy quoteReportLegacy = new QuoteReportItemLegacy();
    quoteReports.add(quoteReportLegacy);
    ServiceUrlProperties properties = new ServiceUrlProperties();

    when(quoteReportServiceWebClient.patch()).thenReturn(requestBodyUriSpecMock);
    when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
    when(requestBodySpecMock.bodyValue(any())).thenReturn(requestHeadersSpecMock);
    when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
    when(responseSpecMock.toEntity(String.class)).thenReturn(Mono.just(ResponseEntity.badRequest().build()));

    QuoteReportService instance = new QuoteReportService(quoteReportServiceWebClient, properties);
    instance.patchQuoteReportFields(quoteReports);
    verify(quoteReportServiceWebClient, times(1)).patch();
  }
}
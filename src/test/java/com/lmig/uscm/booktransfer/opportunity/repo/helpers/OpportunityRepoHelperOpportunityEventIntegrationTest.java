package com.lmig.uscm.booktransfer.opportunity.repo.helpers;

import com.lmig.uscm.booktransfer.opportunity.OpportunityApplication;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.config.UnitTestConfig;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.OpportunityEvent;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityEventRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJpaRepository;
import com.lmig.uscm.booktransfer.rating.client.domain.QuoteAndIssueUpdate;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

/**
 * Integration test for: Saving OpportunityEvent records after Methods annotated with @PublishOpportunityEventAnno
 * Are called. All annotations currently in OpportunityRepoHelper for Save and Update Opp repo methods only.
 * <p>
 * OpportunityRepos - Real repos with H2 db
 * OpportunityEventRepo - Real repo with test containers
 */
@SpringBootTest(classes = {OpportunityApplication.class, UnitTestConfig.class, OpportunityRepoAspect.class})
@ExtendWith(SpringExtension.class)
@TestPropertySource(properties = {
        "spring.profiles.active=unit,testing,mongoTestContainer"
})
class OpportunityRepoHelperOpportunityEventIntegrationTest {

    @Autowired
    OpportunityEventRepo opportunityEventRepo;

    @Autowired
    OpportunityJDBCRepo opportunityJdbcRepo;

    @Autowired
    OpportunityJpaRepository opportunityRepository;

    @Autowired
    OpportunityRepoHelper opportunityRepoHelper;

    @AfterEach
    public void afterEach() {
        // if we mocked the repos in a test, re replace them with the real instances
        opportunityRepoHelper.setOpportunityJdbcRepo(opportunityJdbcRepo);
        opportunityRepoHelper.setOpportunityJpaRepo(opportunityRepository);
        // cleanup any saved records
        opportunityRepository.deleteAll();
        opportunityEventRepo.deleteAll();
    }

    private OpportunityJpaRepository mockTheJpaRepo() {
        OpportunityJpaRepository oppRepoMock = mock(OpportunityJpaRepository.class);
        opportunityRepoHelper.setOpportunityJpaRepo(oppRepoMock);
        return oppRepoMock;
    }

    private OpportunityJDBCRepo mockTheJdbcRepo() {
        OpportunityJDBCRepo oppRepoMock = mock(OpportunityJDBCRepo.class);
        opportunityRepoHelper.setOpportunityJdbcRepo(oppRepoMock);
        return oppRepoMock;
    }

    /**
     * GIVEN I have a brand new Opportunity
     * WHEN I save it
     * THEN an OppEvent record is saved
     * AND the OppEvent record has a CREATED status
     */
    @Test
    void test_NewOpp_SaveSuccess_DoesSaveA_CreatedOppEvent() {
        Opportunity opp = emptyOpp();
        Opportunity savedOpp = opportunityRepoHelper.save(opp);
        OpportunityEvent savedOppEvent = opportunityEventRepo.findByOpportunityId(String.valueOf(savedOpp.getOpportunityId()));
        List<OpportunityEvent> allOppEvents = opportunityEventRepo.findAll();
        assertNotNull(savedOppEvent);
        assertEquals(1, allOppEvents.size());
    }

    /**
     * GIVEN I have a list of new Opportunities
     * WHEN I save them
     * THEN an OppEvent record is saved for each Opp
     * AND each OppEvent record has a CREATED status
     */
    @SuppressWarnings("unused")
    @Test
    void test_ListOfNewOpp_SaveSuccess_DoesSaveA_CreatedOppEvent() {
        List<Opportunity> opps = new ArrayList<>();
        opps.add(emptyOpp().setLineType(LineType.Personal));
        opps.add(emptyOpp().setLineType(LineType.Personal));

        List<Opportunity> savedOpps = opportunityRepoHelper.saveAll(opps);
        List<OpportunityEvent> allOppEvents = opportunityEventRepo.findAll();
        assertEquals(2, allOppEvents.size());
        // both events are for our Opps
        assertEquals(String.valueOf(opps.get(0).getOpportunityId()), allOppEvents.get(0).getOpportunityId());
        assertEquals(String.valueOf(opps.get(1).getOpportunityId()), allOppEvents.get(1).getOpportunityId());
        // both should be create status
        assertEquals(OpportunityEvent.getCreatedEvent(), allOppEvents.get(0).getEventStatus());
        assertEquals(OpportunityEvent.getCreatedEvent(), allOppEvents.get(1).getEventStatus());
        //both LineTypes should be created
        assertEquals(String.valueOf(opps.get(0).getLineType()), allOppEvents.get(0).getLineType());
        assertEquals(String.valueOf(opps.get(1).getLineType()), allOppEvents.get(1).getLineType());
    }

    /**
     * GIVEN I have a list of 1 new and 1 existing Opportunities
     * WHEN I save them
     * THEN an OppEvent record is saved for each Opp
     * AND 1 OppEvent has a CREATED status and 1 an UPDATED status
     *
     */
    @Test
    void test_ListOfMixedOpp_SaveSuccess_DoesSaveA_CreatedOppEvent() {
        // create an existing Opp
        Opportunity existingOpp = emptyOpp();
        existingOpp.setLineType(LineType.Personal);
        existingOpp = opportunityRepoHelper.save(existingOpp);

        // Prepare a list of the existing and a new opp
        List<Opportunity> opps = new ArrayList<>();
        opps.add(existingOpp);
        opps.add(emptyOpp().setLineType(LineType.Personal));

        // save the opps
        List<Opportunity> savedOpps = opportunityRepoHelper.saveAll(opps);
        List<OpportunityEvent> allOppEvents = opportunityEventRepo.findAll();
        assertEquals(3, allOppEvents.size());

        // Should result in 3 total OppEvents (1 for the pre-existing save, 1 for the update, and 1 save for the brand new opp)
        assertEquals(String.valueOf(savedOpps.get(0).getOpportunityId()), allOppEvents.get(0).getOpportunityId());
        assertEquals(String.valueOf(savedOpps.get(0).getOpportunityId()), allOppEvents.get(1).getOpportunityId());
        assertEquals(String.valueOf(savedOpps.get(1).getOpportunityId()), allOppEvents.get(2).getOpportunityId());
        // check the statuses. 2 CREATES, 1 UPDATE
        assertEquals(OpportunityEvent.getCreatedEvent(), allOppEvents.get(0).getEventStatus()); // the initial save for the pre-existing opp
        assertEquals(OpportunityEvent.getUpdatedEvent(), allOppEvents.get(1).getEventStatus()); // the update for the pre-existing opp
        assertEquals(OpportunityEvent.getCreatedEvent(), allOppEvents.get(2).getEventStatus()); // the save of the new opp
        //check for linetypes
        assertEquals(String.valueOf(savedOpps.get(0).getLineType()), allOppEvents.get(0).getLineType());
        assertEquals(String.valueOf(savedOpps.get(0).getLineType()), allOppEvents.get(1).getLineType());
        assertEquals(String.valueOf(savedOpps.get(1).getLineType()), allOppEvents.get(2).getLineType());
    }

    /**
     * GIVEN I have an existing Opportunity
     * WHEN I re-save it
     * THEN an OppEvent record is saved
     * AND the OppEvent record has an UPDATED status
     */
    @Test
    void test_ExistingOpp_SaveSuccess_DoesSaveA_UpdatedOppEvent() {
        // Create an existing Opp
        Opportunity opp = emptyOpp();
        Opportunity savedOpp = opportunityRepoHelper.save(opp);

        // now update the opp
        savedOpp.setBookTransferID(0); // just a random update
        opportunityRepoHelper.save(savedOpp);

        // we should have 2 total OpportunityEvents. 1 save, 1 update for our opp.
        List<OpportunityEvent> allOppEvents = opportunityEventRepo.findAll();
        assertEquals(2, allOppEvents.size());
        // both events are for our Opp
        assertEquals(String.valueOf(savedOpp.getOpportunityId()), allOppEvents.get(0).getOpportunityId());
        assertEquals(String.valueOf(savedOpp.getOpportunityId()), allOppEvents.get(1).getOpportunityId());
        // we should have an update and a save event
        assertEquals(OpportunityEvent.getCreatedEvent(), allOppEvents.get(0).getEventStatus());
        assertEquals(OpportunityEvent.getUpdatedEvent(), allOppEvents.get(1).getEventStatus());
    }

    /**
     * GIVEN an Opportunity Fails to save
     * Then no OppEvent should be saved
     */
    @Test
    void testSaveFail_DoesNot_SaveOppEvent() {
        try {
            // mock the jpa repo to throw an exception when saving an Opp
            OpportunityJpaRepository mockRepo = mockTheJpaRepo();
            Mockito.doAnswer(invocation -> {
                throw(new RuntimeException());
            }).when(mockRepo).saveAndFlush(any(Opportunity.class));

            opportunityRepoHelper.save(emptyOpp());
        } catch(Exception e) {
            // we should not have saved any oppEvent records
            List<OpportunityEvent> allOppEvents = opportunityEventRepo.findAll();
            assertEquals(0, allOppEvents.size());
        }

    }

    @Test
    void testUpdateSuccess_Does_SaveOppEvent() {
        // save an opp first
        Opportunity opp = emptyOpp();
        Opportunity savedOpp = opportunityRepoHelper.save(opp);

        // now update the opp
        opportunityRepoHelper.updateOpportunity(savedOpp);

        // we should have 2 total OpportunityEvents. 1 save, 1 update for our opp.
        List<OpportunityEvent> allOppEvents = opportunityEventRepo.findAll();
        assertEquals(2, allOppEvents.size());
        // both events are for our Opp
        assertEquals(String.valueOf(savedOpp.getOpportunityId()), allOppEvents.get(0).getOpportunityId());
        assertEquals(String.valueOf(savedOpp.getOpportunityId()), allOppEvents.get(1).getOpportunityId());
        // we should have an update and a save event
        assertEquals(OpportunityEvent.getCreatedEvent(), allOppEvents.get(0).getEventStatus());
        assertEquals(OpportunityEvent.getUpdatedEvent(), allOppEvents.get(1).getEventStatus());

    }

    /**
     * GIVEN an Opportunity Fails to update
     * Then no OppEvent should be saved
     */
    @Test
    void testUpdateFail_DoesNot_SaveOppEvent() {
        try {
            // mock the jdbc repo to throw an exception when saving an Opp
            OpportunityJDBCRepo mockRepo = mockTheJdbcRepo();
            Mockito.doAnswer(invocation -> {
                throw(new RuntimeException());
            }).when(mockRepo).updateOpportunity(any(Opportunity.class));

            opportunityRepoHelper.updateOpportunity(emptyOpp());
        } catch(Exception e) {
            // we should not have saved any oppEvent records
            List<OpportunityEvent> allOppEvents = opportunityEventRepo.findAll();
            assertEquals(0, allOppEvents.size());
        }

    }

    /**
     * GIVEN I have an existing BL Opportunity
     * WHEN I updateOpportunityForBLCall
     * THEN an UPDATED OppEvent is saved
     */
    @Test
    void test_updateOpportunityForBLCall_SaveSuccess_DoesSaveA_UpdatedOppEvent() {
        // create a pre-existing opp
        Opportunity savedOpp = opportunityRepoHelper.save(emptyOpp());
        // update the BL opp
        opportunityRepoHelper.updateOpportunityForBLCall(savedOpp.getOpportunityId(), String.valueOf(Timestamp.from(Instant.now())), "100", true);
        // an Update OppEvent should be created
        OpportunityEvent savedOppEvent = opportunityEventRepo.findTopByOpportunityIdOrderByIdDesc(String.valueOf(savedOpp.getOpportunityId()));
        assertEquals(OpportunityEvent.getUpdatedEvent(), savedOppEvent.getEventStatus());
    }

    /**
     * GIVEN repoHelper.getUpdatedEvent is called on 2 existing Opps
     * THEN 2 UPDATE OppEvents should be saved
     */
    @Test
    void test_updateOpportunitiesStatus_SaveSuccess_DoesSaveA_UpdatedOppEvent() {
        List<Opportunity> opps = new ArrayList<>();
        Opportunity opportunity1 = emptyOpp();
        opportunity1.setLineType(LineType.Personal);
        opps.add(opportunity1);

        Opportunity opportunity2 = emptyOpp();
        opportunity2.setLineType(LineType.Business);
        opps.add(opportunity2);

        List<Opportunity> savedOpps = opportunityRepoHelper.saveAll(opps);
        // now update the opps status
        opportunityRepoHelper.saveAll(savedOpps);


        List<OpportunityEvent> allOppEvents = opportunityEventRepo.findAll();
        assertEquals(2, allOppEvents.size()); // 1 for original saves, 1 for updates for PL and skip SC

        // check the opp ids
        assertEquals(String.valueOf(savedOpps.get(0).getOpportunityId()), allOppEvents.get(1).getOpportunityId());
        // we should 2 update events
        assertEquals(OpportunityEvent.getUpdatedEvent(), allOppEvents.get(1).getEventStatus());
        assertEquals(String.valueOf(opportunity1.getLineType()), allOppEvents.get(1).getLineType());
    }

    /**
     * GIVEN a QNI update is executed for an Opportunity
     * THEN an UPDATE oppEvent should be saved
     */
    @Test
    void test_updateOpportunityWithQnIData_SaveSuccess_DoesSaveA_UpdatedOppEvent() throws Exception {

        // save an Opportunity so we can update it
        String lastPolicyGuid = "123";
        Opportunity opp = emptyOpp();
        opp.setLastPolicyGuid(lastPolicyGuid);
        Opportunity savedOpp = opportunityRepoHelper.save(opp);

        // I have not been able to get the query to work with H2 so mocking instead
        // mock the jdbc repo to throw an exception when saving an Opp
        OpportunityJDBCRepo mockRepo = mockTheJdbcRepo();
        doReturn(savedOpp).when(mockRepo).findOpportunityByLastPolicyGuid(anyString());

        // setup the params for the QNI call
        String xmlString = XmlHelper.getDocumentString(XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/lobAuto.xml"));
        QuoteAndIssueUpdate qniUpdate = new QuoteAndIssueUpdate();
        qniUpdate.setLastPolicyGuid(lastPolicyGuid);

        // method under test
        opportunityRepoHelper.updateOpportunityWithQnIData(xmlString, qniUpdate);

        List<OpportunityEvent> allOppEvents = opportunityEventRepo.findAll();
        assertEquals(2, allOppEvents.size());
        assertEquals(OpportunityEvent.getCreatedEvent(), allOppEvents.get(0).getEventStatus());
        assertEquals(OpportunityEvent.getUpdatedEvent(), allOppEvents.get(1).getEventStatus());
    }


    private Opportunity emptyOpp() {
        return new Opportunity("<ACORD></ACORD>", "<ACORD></ACORD>");
    }
}

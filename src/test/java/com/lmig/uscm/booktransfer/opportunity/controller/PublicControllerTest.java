package com.lmig.uscm.booktransfer.opportunity.controller;

import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class PublicControllerTest {

	@Mock
	OpportunityRepoHelper opportunityRepoHelper;

	PublicController publicController;

	MockMvc mvc;

	@BeforeEach
	public void setup() {
		publicController = new PublicController(opportunityRepoHelper);
		mvc = MockMvcBuilders
				.standaloneSetup(publicController)
				.setControllerAdvice(new OpportunityAdvice(null))
				.build();
	}

	@Test
	void test_GET_opportunity_safecoPolicyGUID() throws Exception {
		Opportunity opp = new Opportunity();
		opp.setLastPolicyGuid("guid");
		doReturn(opp).when(opportunityRepoHelper).findOpportunityById(anyInt());
		mvc.perform(get("/public/opportunity/safecoPolicyGUID?oppId=123"))
				.andExpect(status().isOk())
				.andExpect(content().string("guid"));
	}

	@Test
	void test_GET_opportunity_oppId() throws Exception {
		doReturn(List.of(123)).when(opportunityRepoHelper).getCustomFilterOppIds(any());
		mvc.perform(get("/public/opportunity/oppId?safecoPolicyGUID=guid"))
				.andExpect(status().isOk())
				.andExpect(content().string("[123]"));
	}
}

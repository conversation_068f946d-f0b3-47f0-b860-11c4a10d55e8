package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;

class DriverDataTest {
	@Test
	void testBirthDateMissing()
			throws ParserConfigurationException, IOException, SAXException, XPathExpressionException {
		PreviewDataResponse response = new PreviewDataResponse();
		DriverData driverData = new DriverData();
		driverData.setRequiredData(createDriverNode("1234567", ""), response);
		assertThat(response.getMissingData(), containsInAnyOrder("BirthDate"));
	}
	@Test
	void testDriverLicenseMissing()
			throws ParserConfigurationException, IOException, SAXException, XPathExpressionException {
		PreviewDataResponse response = new PreviewDataResponse();
		DriverData driverData = new DriverData();
		driverData.setRequiredData(createDriverNode("", "1962-01-01"), response);
		assertThat(response.getMissingData(), containsInAnyOrder("DrvLicenseNumber"));
	}
	private Node createDriverNode(String licenseNumber, String birthDate)
			throws ParserConfigurationException, IOException, SAXException {
		return XmlHelper.createNode("<PersDriver><DriverInfo><DriversLicense><DriversLicenseNumber>" +
				licenseNumber + "</DriversLicenseNumber></DriversLicense><PersonInfo><BirthDt>" +
				birthDate + "</BirthDt></PersonInfo></DriverInfo></PersDriver>");
	}
}

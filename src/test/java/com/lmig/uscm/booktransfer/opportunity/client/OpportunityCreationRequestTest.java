package com.lmig.uscm.booktransfer.opportunity.client;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.MasterOppSPQEMeta;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Document;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotSame;

class OpportunityCreationRequestTest {

	@Test
	void testCloneOpportunityCreationRequest() throws Exception {
		Document acord = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		OpportunityCreationRequest initialRequest = new OpportunityCreationRequest();
		// fields to be cloned
		initialRequest.setUploadedACORD(acord);
		initialRequest.setBookTransferID(123456);
		initialRequest.setLineType(LineType.Personal);
		initialRequest.setSFDCID(234567);
		initialRequest.setSubCode("subCode");
		initialRequest.setUploadEventID(345678);
		initialRequest.setMasterOppID(000000);
		initialRequest.setPriorCarrier("priorCarrier");
		initialRequest.setOriginatingId("originatingId");
		initialRequest.setNNumber("n0264961");
		// fields not to be cloned
		initialRequest.setMasterOppSPQEMeta(new MasterOppSPQEMeta());
		initialRequest.setExistingOpportunityID(456789);
		initialRequest.setAsMasterOpp();

		OpportunityCreationRequest clone = initialRequest.clone();

		// fields to be the same
		assertEquals(initialRequest.getBookTransferID(), clone.getBookTransferID());
		assertEquals(initialRequest.getLineType(), clone.getLineType());
		assertEquals(initialRequest.getSFDCID(), clone.getSFDCID());
		assertEquals(initialRequest.getSubCode(), clone.getSubCode());
		assertEquals(initialRequest.getUploadEventID(), clone.getUploadEventID());
		assertEquals(initialRequest.getOriginSource(), clone.getOriginSource());
		assertEquals(initialRequest.getMasterOppID(), clone.getMasterOppID());
		assertEquals(initialRequest.getPriorCarrier(), clone.getPriorCarrier());
		assertEquals(initialRequest.getOriginatingId(), clone.getOriginatingId());
		assertEquals(initialRequest.getNNumber(), clone.getNNumber());
		// fields to be different
		assertNotEquals(initialRequest.getMasterOppSPQEMeta(), clone.getMasterOppSPQEMeta());
		assertNotEquals(initialRequest.getExistingOpportunityID(), clone.getExistingOpportunityID());
		assertNotEquals(initialRequest.isMasterOpp(), clone.isMasterOpp());
		assertNotSame(initialRequest.getUploadedACORD(), clone.getUploadedACORD());
	}
}

package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.domain.creation.FileContent;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ZipFileHelperTest {

	private static final String ZIP_OUT_PATH = "src/test/resources/xml/multiCompressed.zip";

	@AfterEach
	public void cleanup() {
		File zipFile = new File(ZIP_OUT_PATH);
		zipFile.delete();
	}

	@Test
	void testIsArchive() throws Exception {
		String normalFilePath = "src/test/resources/xml/auto12Vehs.xml";
		String archiveFilePath = "src/test/resources/xml/testArchiveDeep.zip";

		String archiveWithoutExtension = "src/test/resources/xml/testArchive";

		ZipFileHelper.createZipFile(archiveWithoutExtension, normalFilePath);

		File archiveFile = new File(archiveFilePath);
		File normalFile = new File(normalFilePath);
		File noExtensionFile = new File(archiveWithoutExtension);

		assertFalse(ZipFileHelper.isArchive(normalFile));
		assertTrue(ZipFileHelper.isArchive(archiveFile));
		assertTrue(ZipFileHelper.isArchive(noExtensionFile));

		noExtensionFile.delete();
	}

	@Test
	void testUnzipDeepAndShallowFiles() throws Exception {
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String archiveDeep = "src/test/resources/xml/testArchiveDeep.zip";
		String archiveShallow = "src/test/resources/xml/testArchiveShallow.zip";

		String file1Content = XmlHelper.getFileContentFromPath(file1);
		String file2Content = XmlHelper.getFileContentFromPath(file2);

		File zipFile = new File(archiveShallow);

		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		assertEquals(file1Content, unzippedFiles.get(0).getContent());
		assertEquals("auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals(file2Content, unzippedFiles.get(1).getContent());
		assertEquals("home.xml", unzippedFiles.get(1).getFileName());

		zipFile = new File(archiveDeep);

		unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		assertEquals(file1Content, unzippedFiles.get(0).getContent());
		assertEquals("SomeFolder/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals(file2Content, unzippedFiles.get(1).getContent());
		assertEquals("SomeFolder/home.xml", unzippedFiles.get(1).getFileName());
	}

	@Test
	void testZipAndUnzipFile() throws Exception {
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String[] filePaths = new String[]{file1, file2};
		ZipFileHelper.createZipFile(ZIP_OUT_PATH, filePaths);

		String file1Content = XmlHelper.getFileContentFromPath(file1);
		String file2Content = XmlHelper.getFileContentFromPath(file2);

		File zipFile = new File(ZIP_OUT_PATH);

		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		assertEquals(file1Content, unzippedFiles.get(0).getContent());
		assertEquals("auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals(file2Content, unzippedFiles.get(1).getContent());
		assertEquals("home.xml", unzippedFiles.get(1).getFileName());
	}

	@Test
	void testReadFileContents_ZipOrNotZip() throws Exception {
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String[] filePaths = new String[]{file1, file2};
		ZipFileHelper.createZipFile(ZIP_OUT_PATH, filePaths);

		String file1Content = XmlHelper.getFileContentFromPath(file1);
		String file2Content = XmlHelper.getFileContentFromPath(file2);

		File zipFile = new File(ZIP_OUT_PATH);

		List<FileContent> archiveFileContents = ZipFileHelper.readFileContents(zipFile);

		assertEquals(file1Content, archiveFileContents.get(0).getContent());
		assertEquals("auto12Vehs.xml", archiveFileContents.get(0).getFileName());
		assertEquals(file2Content, archiveFileContents.get(1).getContent());
		assertEquals("home.xml", archiveFileContents.get(1).getFileName());

		File notZipFile = new File(file1);

		List<FileContent> fileContents = ZipFileHelper.readFileContents(notZipFile);

		assertEquals(file1Content, fileContents.get(0).getContent());
		assertEquals("auto12Vehs.xml", fileContents.get(0).getFileName());
	}
}

package com.lmig.uscm.booktransfer.opportunity.domain.preview;

import com.lmig.uscm.booktransfer.opportunity.Helper.OpportunityTestUtil;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.jupiter.api.Assertions.assertEquals;

class PreviewTest {
	/**
	 * Given we have a opportunity xml with no missing values for Auto When we try
	 * to build the preview data Then we should vehicle details but not missing data
	 */
	@Test
	void testAutoPreview() throws Exception {
		AutoPreview expectedAutoPreview = new AutoPreview();

		VehicleInfo vehicleInfo = new VehicleInfo();
		vehicleInfo.setVin("1FAFP55S64G200624");
		vehicleInfo.setVehiclePremium("5289");
		vehicleInfo.setVehAddress("1025 5TH ST DEL NORTE,CO 81132");
		vehicleInfo.setVehMake("VOLKSWAGEN");
		vehicleInfo.setVehModel("PASSAT GLX");
		vehicleInfo.setVehYear("2002");
		vehicleInfo.setVehLiabilityLimit("50/100");
		expectedAutoPreview.addVehicle(vehicleInfo);

		vehicleInfo = new VehicleInfo();
		vehicleInfo.setVin("VIN2");
		vehicleInfo.setVehiclePremium("5289");
		vehicleInfo.setVehAddress("1025 5TH ST DEL NORTE,CO 81132");
		vehicleInfo.setVehMake("Jeep");
		vehicleInfo.setVehModel("Wrangler");
		vehicleInfo.setVehYear("1993");
		vehicleInfo.setVehLiabilityLimit("50/100");
		expectedAutoPreview.addVehicle(vehicleInfo);

		vehicleInfo = new VehicleInfo();
		vehicleInfo.setVin("VIN3");
		vehicleInfo.setVehiclePremium("1234");
		vehicleInfo.setVehAddress("1025 5TH ST DEL NORTE,CO 81132");
		vehicleInfo.setVehMake("Toyota");
		vehicleInfo.setVehModel("Camry");
		vehicleInfo.setVehYear("2014");
		vehicleInfo.setVehLiabilityLimit("100/200");
		expectedAutoPreview.addVehicle(vehicleInfo);

		vehicleInfo = new VehicleInfo();
		vehicleInfo.setVin("Vin4");
		vehicleInfo.setVehiclePremium("4789");
		vehicleInfo.setVehAddress("123 W. Main St. Carmel,IN 46220");
		vehicleInfo.setVehMake("Dodge");
		vehicleInfo.setVehModel("Charger");
		vehicleInfo.setVehYear("2017");
		vehicleInfo.setVehLiabilityLimit("50/100");
		expectedAutoPreview.addVehicle(vehicleInfo);

		PreviewDataResponse actualAutoPreview = getPreviewData(
				"src/test/resources/xml/AutoRatingDefaultsPostRuleEngine.xml", LineType.Personal);

		assertAutoData((AutoPreview) actualAutoPreview.getPreview(), expectedAutoPreview);

		assertEquals(0, actualAutoPreview.getMissingData().size());
	}

	@Test
	void testMtrPreview() throws Exception {
		AutoPreview expectedMtrPreview = new AutoPreview();

		VehicleInfo vehicleInfo = new VehicleInfo();
		vehicleInfo.setVin("1HD1TCL13KB951865");
		vehicleInfo.setVehAddress("7 CARA COURT DEER PARK,NY 11729");
		vehicleInfo.setVehMake("HARLEY-DAVIDSON");
		vehicleInfo.setVehModel("FLTRXSE CVO ROAD GLI");
		vehicleInfo.setVehYear("2019");
		vehicleInfo.setVehLiabilityLimit("250/500");
		expectedMtrPreview.addVehicle(vehicleInfo);

		vehicleInfo = new VehicleInfo();
		vehicleInfo.setVin("1HD1YRK19LBO46747");
		vehicleInfo.setVehAddress("7 CARA COURT DEER PARK,NY 11729");
		vehicleInfo.setVehMake("HARLEY-DAVIDSON");
		vehicleInfo.setVehModel("FLFBS FAT BOY");
		vehicleInfo.setVehYear("2020");
		vehicleInfo.setVehLiabilityLimit("250/500");
		expectedMtrPreview.addVehicle(vehicleInfo);

		PreviewDataResponse actualAutoPreview = getPreviewData(
				"src/test/resources/xml/mtrAfterStrategy.xml", LineType.Personal);

		assertAutoData((AutoPreview) actualAutoPreview.getPreview(), expectedMtrPreview);

		assertEquals(0, actualAutoPreview.getMissingData().size());
	}

	/**
	 * Given we have an opportunity xml with missing values for Auto When we try to
	 * build the preview data Then we should get missing data
	 */
	@Test
	void testAutoMissingData() throws Exception {

		PreviewDataResponse actualAutoPreview = getPreviewData(
				"src/test/resources/xml/lobAuto.xml", LineType.Personal);

		assertThat(actualAutoPreview.getMissingData(), containsInAnyOrder("ExpirationDate", "BirthDate",
				"DrvLicenseNumber", "VIN", "VehPremium", "Applicant"));
	}

	@Test
	void testDriverBirthDateMissing() throws Exception {
		PreviewDataResponse actualAutoPreview = getPreviewData(
				"src/test/resources/xml/effdate-auto-02-21-2017.xml", LineType.Personal);

		assertThat(actualAutoPreview.getMissingData(), containsInAnyOrder("BirthDate"));
	}

	@Test
	void testDriverLicenseNumberMissing() throws Exception {
		PreviewDataResponse actualAutoPreview = getPreviewData(
				"src/test/resources/xml/effdate-auto-03-10-2017.xml", LineType.Personal);

		assertThat(actualAutoPreview.getMissingData(), containsInAnyOrder("DrvLicenseNumber"));
	}

	/**
	 * Given we have a opportunity xml with no missing values for Home When we try
	 * to build the preview data Then we should get home details but not missing
	 * data
	 */
	@Test
	void testHome() throws Exception {

		HomePreview expectedPreview = new HomePreview();

		expectedPreview.setCompleteAddress("1259 E CAMBRIDGE ST SPRINGFIELD,MO 658043610");
		expectedPreview.setCovA("166000");
		expectedPreview.setPolicyTypeCd("03");

		PreviewDataResponse actualPreview = getPreviewData(
				"src/test/resources/xml/HomeRatingDefaultsAfterRulesEngineCall.xml", LineType.Personal);
		assertHomeRequiredData((HomePreview) actualPreview.getPreview(), expectedPreview);

		assertEquals(0, actualPreview.getMissingData().size());
	}

	/**
	 * Given we have an opportunity xml with no missing values for Home When we try
	 * to build the preview data Then we should get home details but not missing
	 * data
	 */
	@Test
	void testBusiness() throws Exception {
		PreviewDataResponse actualPreview = getPreviewData(
				"src/test/resources/xml/converterV2_BL_WORK.xml", LineType.Business);

		assertEquals(0, actualPreview.getMissingData().size());
	}

	/**
	 * Given we have an opportunity xml with missing values for Home When we try to
	 * build the preview data Then we should get missing data
	 */
	@Test
	void testHomeMissingData() throws Exception {
		PreviewDataResponse actualPreview = getPreviewData(
				"src/test/resources/xml/validateHome.xml", LineType.Personal);

		assertThat(actualPreview.getMissingData(), containsInAnyOrder("CovA", "PolicyTypeCd"));
	}

	/**
	 * Given we have an opportunity xml with no missing values for Boat When we try
	 * to build the preview data Then we shouldn't get missing data details
	 */
	@Test
	void testBoat() throws Exception {
		PreviewDataResponse waterCraftActual = getPreviewData(
				"src/test/resources/xml/boatWithAmt.xml", LineType.Personal);

		assertEquals(0, waterCraftActual.getMissingData().size());
	}

	/**
	 * Given we have an opportunity xml with missing values for Boat When we try to
	 * build the preview data Then we should get missing data details
	 */
	@Test
	void testBoatMissingData() throws Exception {
		PreviewDataResponse waterCraftActual = getPreviewData(
				"src/test/resources/xml/validateBoat.xml", LineType.Personal);

		assertThat(waterCraftActual.getMissingData(), containsInAnyOrder("Make", "Model", "ModelYear",
				"WatercraftStyle", "PropulsionType", "TotalHorsePower", "Length", "HullValue"));
	}

	/**
	 * Given we have an opportunity xml with no missing values for Fire When we try
	 * to build the preview data Then we shouldn't get missing data details
	 */
	@Test
	void testDFire() throws Exception {
		PreviewDataResponse actualFirePreview = getPreviewData(
				"src/test/resources/xml/effdate-dfire-03-05-2017.xml", LineType.Personal);

		assertEquals(0, actualFirePreview.getMissingData().size());

	}

	/**
	 * Given we have an opportunity xml with missing values for Fire When we try to
	 * build the preview data Then we shouldn't get missing data details
	 */
	@Test
	void testDFireMissingData() throws Exception {
		PreviewDataResponse actualFirePreview = getPreviewData(
				"src/test/resources/xml/validateFire.xml", LineType.Personal);

		assertThat(actualFirePreview.getMissingData(), containsInAnyOrder("MailingAddress", "CovA"));

	}

	/**
	 * Given we have an opportunity xml for umbrella When we try to build the preview
	 * data Then we should get missing data details
	 */
	@Test
	void testUMBRP() throws Exception {
		PreviewDataResponse actualPreview = getPreviewData(
				"src/test/resources/xml/UMBRP_2021-12-01.xml", LineType.Personal);

		assertThat(actualPreview.getMissingData(),
				containsInAnyOrder("PriorCarrier", "EffectiveDate", "Applicant", "Location"));
	}

	private PreviewDataResponse getPreviewData(String pathOfOppXml, LineType lineType) throws Exception {
		PreviewDataResponse response = new PreviewDataResponse();
		Document xmlDoc = OpportunityTestUtil.generateXmlDocument(Paths.get(pathOfOppXml));
		response.setPreview(Preview.buildPreview(xmlDoc, response, lineType));
		return response;
	}


	private void assertAutoData(AutoPreview actual, AutoPreview expected) {
		assertVehicleData(actual.getVehicles(), expected.getVehicles());
	}

	private void assertHomeRequiredData(HomePreview actual, HomePreview expected) {
		assertEquals(expected.getCompleteAddress(), actual.getCompleteAddress());
		assertEquals(expected.getCovA(), actual.getCovA());
		assertEquals(expected.getPolicyTypeCd(), actual.getPolicyTypeCd());
	}

	private void assertVehicleData(List<VehicleInfo> vehicleInfoActualList, List<VehicleInfo> vehicleInfoExpectedList) {
		assertEquals(vehicleInfoActualList.size(), vehicleInfoExpectedList.size());

		for (int i = 0; i < vehicleInfoActualList.size(); i++) {
			VehicleInfo vehicleInfoExpected = vehicleInfoExpectedList.get(i);
			VehicleInfo vehicleInfoActual = vehicleInfoActualList.get(i);
			assertEquals(vehicleInfoExpected.getVehAddress(), vehicleInfoActual.getVehAddress());
			assertEquals(vehicleInfoExpected.getVehMake(), vehicleInfoActual.getVehMake());
			assertEquals(vehicleInfoExpected.getVehModel(), vehicleInfoActual.getVehModel());
			assertEquals(vehicleInfoExpected.getVehYear(), vehicleInfoActual.getVehYear());
			assertEquals(vehicleInfoExpected.getVehLiabilityLimit(), vehicleInfoActual.getVehLiabilityLimit());
			assertEquals(vehicleInfoExpected.getVin(), vehicleInfoActual.getVin());
		}
	}

	/**
	 * Given I have an empty address nodes When I go to build the address Then it
	 * will return empty
	 */
	@Test
	void testBuildAddress()
			throws IOException, SAXException, ParserConfigurationException, XPathExpressionException {
		String addressPath = "<AddressNode>	<Addr1> </Addr1>	<Addr2></Addr2>	<City>	</City></AddressNode>";
		String address = Preview.buildAddress(XmlHelper.createNode(addressPath));

		assertEquals("", address);

	}

	/**
	 * Given that I have a xml with split limits When I try to get them then they
	 * will be returned
	 */
	@Test
	void testSplitLimits() throws Exception {
		AutoPreview preview = (AutoPreview) getPreviewData(
				"src/test/resources/xml/VehLiabilityLimitExample.xml", LineType.Personal)
				.getPreview();

		assertEquals("500/500", preview.getVehicles().get(0).getVehLiabilityLimit());
	}
}

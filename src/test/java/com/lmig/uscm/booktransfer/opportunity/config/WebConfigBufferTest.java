package com.lmig.uscm.booktransfer.opportunity.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.buffer.DataBufferLimitException;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
public class WebConfigBufferTest {

    private MockWebServer mockWebServer;
    private WebConfig webConfig;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
        webConfig = new WebConfig();
        webConfig.bookTransferServiceUrl = mockWebServer.url("/").toString();
        objectMapper = new ObjectMapper();
    }

    @AfterEach
    void tearDown() throws IOException {
        mockWebServer.shutdown();
    }

    @Test
    void testBookTransferServiceWebClientHandlesLargeResponse() throws Exception {
        // Given: Large MSA mapping response (~2MB)
        Map<String, Object> largeResponse = createMsaMappingResponse(2000000);
        String jsonResponse = objectMapper.writeValueAsString(largeResponse);
        
        // Log actual size for verification
        System.out.println("Actual JSON size: " + jsonResponse.getBytes().length + " bytes");

        mockWebServer.enqueue(new MockResponse()
                .setBody(jsonResponse)
                .addHeader("Content-Type", "application/json")
                .setResponseCode(200));

        WebClient webClient = webConfig.getBookTransferServiceWebClient();

        // When & Then: Should handle large response without DataBufferLimitException
        assertDoesNotThrow(() -> {
            String response = webClient.post()
                    .uri("/btCodesByAgencyId/Main%20Street%20America")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();
            
            assertNotNull(response);
            assertThat(response).contains("msaMappingData");
        });
    }

    @ParameterizedTest
    @ValueSource(ints = {100000, 500000, 1000000, 2000000})
    void testBookTransferServiceWebClientHandlesVariousResponseSizes(int responseSize) throws Exception {
        // Given: Various response sizes up to 2MB (well within 16MB limit)
        Map<String, Object> response = createMsaMappingResponse(responseSize);
        String jsonResponse = objectMapper.writeValueAsString(response);
        
        // Log actual size to verify it's within limits
        int actualSize = jsonResponse.getBytes().length;
        System.out.println("Target: " + responseSize + " bytes, Actual: " + actualSize + " bytes");
        
        mockWebServer.enqueue(new MockResponse()
                .setBody(jsonResponse)
                .addHeader("Content-Type", "application/json"));

        WebClient webClient = webConfig.getBookTransferServiceWebClient();

        // When & Then: Should handle all sizes within 16MB limit
        assertDoesNotThrow(() -> {
            String result = webClient.post()
                    .uri("/btCodesByAgencyId/Main%20Street%20America")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();
            
            assertThat(result).isNotNull();
        });
    }

    @Test
    void testBookTransferServiceWebClientHandlesEmptyResponse() throws Exception {
        // Given: Empty response
        mockWebServer.enqueue(new MockResponse()
                .setBody("{}")
                .addHeader("Content-Type", "application/json"));

        WebClient webClient = webConfig.getBookTransferServiceWebClient();

        // When & Then: Should handle empty response
        assertDoesNotThrow(() -> {
            String response = webClient.post()
                    .uri("/btCodesByAgencyId/Main%20Street%20America")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            assertThat(response).isEqualTo("{}");
        });
    }

    @Test
    void testBookTransferServiceWebClientHandlesServerError() throws Exception {
        // Given: Server error response
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(500)
                .setBody("Internal Server Error"));

        WebClient webClient = webConfig.getBookTransferServiceWebClient();

        // When & Then: Should handle server errors appropriately
        assertThatThrownBy(() -> {
            webClient.post()
                    .uri("/btCodesByAgencyId/Main%20Street%20America")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();
        }).isInstanceOf(WebClientResponseException.class);
    }

    @Test
    void testBookTransferServiceWebClientRejectsExcessivelyLargeResponse() throws Exception {
        // Given: Response that exceeds 16MB limit
        String largeString = "x".repeat(17 * 1024 * 1024); // 17MB string
        
        mockWebServer.enqueue(new MockResponse()
                .setBody(largeString)
                .addHeader("Content-Type", "application/json"));

        WebClient webClient = webConfig.getBookTransferServiceWebClient();

        // When & Then: Should throw WebClientResponseException caused by DataBufferLimitException
        assertThatThrownBy(() -> {
            webClient.post()
                    .uri("/btCodesByAgencyId/Main%20Street%20America")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();
        }).isInstanceOf(WebClientResponseException.class)
          .hasCauseInstanceOf(DataBufferLimitException.class);
    }

    private Map<String, Object> createMsaMappingResponse(int targetSize) {
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> msaData = new HashMap<>();
        
        // Calculate entries more conservatively to avoid exceeding target size
        int estimatedBytesPerEntry = 300; // Conservative estimate including JSON overhead
        int entryCount = Math.max(1, targetSize / estimatedBytesPerEntry);
        
        for (int i = 0; i < entryCount; i++) {
            Map<String, Object> agentData = new HashMap<>();
            agentData.put("agentId", "AGENT_" + String.format("%06d", i));
            agentData.put("bookTransferId", "BT-" + String.format("%06d", i));
            agentData.put("salesforceCode", "SF-" + String.format("%06d", i));
            agentData.put("agencyName", "MSA Agency " + i);
            agentData.put("status", "ACTIVE");
            agentData.put("region", "REGION_" + (i % 5));
            agentData.put("lastUpdated", "2024-01-15T10:30:00Z");
            
            msaData.put("agent_" + i, agentData);
        }
        
        response.put("msaMappingData", msaData);
        response.put("totalCount", entryCount);
        response.put("status", "SUCCESS");
        response.put("timestamp", "2024-01-15T10:30:00Z");
        response.put("version", "1.0");
        
        return response;
    }
}

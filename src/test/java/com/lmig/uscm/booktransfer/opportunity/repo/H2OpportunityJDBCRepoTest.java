/*
 * Copyright (c) 2019, Liberty Mutual
 * Proprietary and Confidential
 * All Rights Reserved
 */

package com.lmig.uscm.booktransfer.opportunity.repo;

import com.lmig.uscm.booktransfer.opportunity.Helper.OpportunityTestUtil;
import com.lmig.uscm.booktransfer.opportunity.OpportunityApplication;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityDetails;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.client.domain.QuoteType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.ScheduleRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilter;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequestForOppIds;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.EffectiveDateRange;
import com.lmig.uscm.booktransfer.opportunity.mockito.domain.BookTransfer;
import com.lmig.uscm.booktransfer.opportunity.mockito.repos.BookTransferRepository;
import com.lmig.uscm.booktransfer.rating.client.domain.QuoteAndIssueUpdate;
import com.lmig.uscm.booktransfer.sql.domain.PaginationRequest;
import com.lmig.uscm.booktransfer.utilityservice.DateUtils;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.assertj.core.util.Lists;
import org.javatuples.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.IOException;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test the actual query against a db
 */
@SpringBootTest(classes = OpportunityApplication.class)
@ExtendWith(SpringExtension.class)
@TestPropertySource(properties = {
        "spring.profiles.active=unit,testing,transformationClient,mongoTestContainer"
})
@EntityScan({"com.lmig.uscm.booktransfer.opportunity.mockito.domain"})
@EnableJpaRepositories(basePackages = {"com.lmig.uscm.booktransfer.opportunity.mockito.repos"})
class H2OpportunityJDBCRepoTest {
    @Autowired
    private OpportunityJDBCRepo opportunityJDBCRepo;
    @Autowired
    private BookTransferRepository bookTransferRepository;
    @Autowired
    private OpportunityJpaRepository opportunityRepository;
    private List<BookTransfer> bookTransfers;

    @BeforeEach
    void setUp() {
        bookTransferRepository.deleteAll();
        opportunityRepository.deleteAll();
        bookTransfers = buildBookTransfers();
    }

    /**
     * Testing a basic scheduler query with only one item in each category.
     */
    @Test
    void testWithBasicQueryWithOneItem() {
        int sla = 65;
        List<Integer> correctOppIds = buildOpportunities(bookTransfers, sla);
        ScheduleRequest schedule = buildBasicSchedule(sla);

        Map<LineType, List<Integer>> results = opportunityJDBCRepo.getOppIdsForTheGivenSchedule(schedule);
        List<Integer> returnedIds = results.get(LineType.Personal);

        assertEquals(2, returnedIds.size());
        assertTrue(returnedIds.contains(correctOppIds.get(0)));
        assertTrue(returnedIds.contains(correctOppIds.get(5)));
    }

    /**
     * Testing to make sure that this query will work when we have mutiple items
     */
    @Test
    void testWithBasicQueryWithMultipleItems() {
        int sla = 65;
        // not sure if this is a good way to do this the best I could think of though
        List<Integer> builtOutOppIds = buildOpportunities(bookTransfers, sla);
        ScheduleRequest schedule = buildBasicSchedule(sla);
        schedule.addQuoteStatuses(
                Pair.with(String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_QUOTE_CLEAN_UP.getOppStatusCode()),
                        OpportunityStatus.OPPORTUNITY_STATUS_QUOTE_CLEAN_UP.getOppStatusValue()));
        schedule.addBusinessType(LineOfBusiness.AUTOP);
        schedule.addRatingState("WA");
        schedule.addPriorCarriers("Not Liberty");
        schedule.addSalesForceCodes("BT - 24567");

        Map<LineType, List<Integer>> results = opportunityJDBCRepo.getOppIdsForTheGivenSchedule(schedule);
        List<Integer> returnedIds = results.get(LineType.Personal);

        assertEquals(7, returnedIds.size());
        assertTrue(returnedIds.contains(builtOutOppIds.get(0)));
        assertTrue(returnedIds.contains(builtOutOppIds.get(1)));
        assertTrue(returnedIds.contains(builtOutOppIds.get(2)));
        assertTrue(returnedIds.contains(builtOutOppIds.get(3)));
        assertTrue(returnedIds.contains(builtOutOppIds.get(4)));
        assertTrue(returnedIds.contains(builtOutOppIds.get(5)));
        assertTrue(returnedIds.contains(builtOutOppIds.get(7)));
    }

    /**
     * Test a basic query with a master oppId (Agent code) to make sure stat code works correctly
     */
    @Test
    void testWithBasicQueryWithMasterOppId() {
        int sla = 65;
        // not sure if this is a good way to do this the best I could think of though
        List<Integer> correctOppIds = buildOpportunities(bookTransfers, sla);
        ScheduleRequest schedule = buildBasicSchedule(sla);
        schedule.setMasterStatCode("54321");

        Map<LineType, List<Integer>> results = opportunityJDBCRepo.getOppIdsForTheGivenSchedule(schedule);
        List<Integer> returnedIds = results.get(LineType.Personal);

        assertEquals(2, returnedIds.size());
        assertTrue(returnedIds.contains(correctOppIds.get(0)));
        assertTrue(returnedIds.contains(correctOppIds.get(5)));
    }

    /**
     * Test a basic JDBC query with a stat code to make sure stat code works correctly
     */
    @Test
    void testWithBasicQueryWithStatCode() {
        int sla = 65;
        // not sure if this is a good way to do this the best I could think of though
        List<Integer> correctOppIds = buildOpportunities(bookTransfers, sla);
        ScheduleRequest schedule = buildBasicSchedule(sla);
        schedule.setStatCode("34566");

        Map<LineType, List<Integer>> results = opportunityJDBCRepo.getOppIdsForTheGivenSchedule(schedule);
        List<Integer> returnedIds = results.get(LineType.Personal);

        assertEquals(2, returnedIds.size());
        assertTrue(returnedIds.contains(correctOppIds.get(0)));
        assertTrue(returnedIds.contains(correctOppIds.get(5)));
    }

    /**
     * Given that I have a policy that effective date in the past When I run Scheduler Then it should not quote that
     * policy
     */
    @Test
    void testEffectiveDateInThePast() {
        String pastEffectiveDate = DateUtils.getDateXDaysInTheFuture(-1);

        OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransfers.get(0).getBookTransferID(), "Liberty",
                pastEffectiveDate, opportunityRepository, "IN");
        ScheduleRequest schedule = buildBasicSchedule(65);
        Map<LineType, List<Integer>> results = opportunityJDBCRepo.getOppIdsForTheGivenSchedule(schedule);
        List<Integer> returnedIds = results.get(LineType.Personal);

        assertEquals(0, returnedIds.size());
    }

    /**
     * Given that I have a policy that effective date is tommorrow When I run Scheduler Then it should quote that policy
     */
    @Test
    void testEffectiveDateTomorrow() {
        List<Integer> oppIds = new ArrayList<>();
        String pastEffectiveDate = DateUtils.getDateXDaysInTheFuture(1);

        oppIds.add(OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransfers.get(0).getBookTransferID(), "Liberty",
                pastEffectiveDate, opportunityRepository, "IN").getOpportunityId());
        ScheduleRequest schedule = buildBasicSchedule(65);
        Map<LineType, List<Integer>> results = opportunityJDBCRepo.getOppIdsForTheGivenSchedule(schedule);
        List<Integer> returnedIds = results.get(LineType.Personal);

        assertEquals(1, returnedIds.size());
        assertEquals(returnedIds.get(0), oppIds.get(0));
    }

    private List<BookTransfer> buildBookTransfers() {
        List<BookTransfer> bookTransfers = new ArrayList<>();
        bookTransfers.add(OpportunityTestUtil.saveCreatedBookTransfer(1, 12345, "54321", bookTransferRepository,
                "34566", "active", "QBE"));
        bookTransfers.add(OpportunityTestUtil.saveCreatedBookTransfer(2, 24567, "54322", bookTransferRepository,
                "34567", "active", "QBE"));
        return bookTransfers;
    }

    private List<Integer> buildOpportunities(List<BookTransfer> bookTransferList, int sla) {
        String validEffectiveDate = DateUtils.getDateXDaysInTheFuture(sla);
        String passedSLAEffectiveDate = DateUtils.getDateXDaysInTheFuture(sla - 1);
        String futureSLAEffectiveDate = DateUtils.getDateXDaysInTheFuture(sla + 1);
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransferList.get(0).getBookTransferID(), "Liberty",
                validEffectiveDate, opportunityRepository, "IN").getOpportunityId());
        oppIds.add(OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.AUTOP,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransferList.get(0).getBookTransferID(), "Liberty",
                validEffectiveDate, opportunityRepository, "IN").getOpportunityId());
        oppIds.add(OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransferList.get(1).getBookTransferID(), "Liberty",
                validEffectiveDate, opportunityRepository, "IN").getOpportunityId());
        oppIds.add(OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransferList.get(0).getBookTransferID(),
                "Not Liberty", validEffectiveDate, opportunityRepository, "IN").getOpportunityId());
        oppIds.add(OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_QUOTE_CLEAN_UP, bookTransferList.get(0).getBookTransferID(),
                "Liberty", validEffectiveDate, opportunityRepository, "IN").getOpportunityId());

        oppIds.add(OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransferList.get(0).getBookTransferID(), "Liberty",
                passedSLAEffectiveDate, opportunityRepository, "IN").getOpportunityId());
        oppIds.add(OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransferList.get(0).getBookTransferID(), "Liberty",
                futureSLAEffectiveDate, opportunityRepository, "IN").getOpportunityId());
        oppIds.add(OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransferList.get(0).getBookTransferID(), "Liberty",
                validEffectiveDate, opportunityRepository, "WA").getOpportunityId());
        return oppIds;
    }

    private ScheduleRequest buildBasicSchedule(int sla) {
        ScheduleRequest schedule = new ScheduleRequest();
        schedule.addSalesForceCodes("BT - 12345");
        schedule.addBusinessType(LineOfBusiness.HOME);
        schedule.addQuoteStatuses(
                Pair.with(String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()),
                        OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusValue()));
        schedule.setExportSLA(sla);
        schedule.addPriorCarriers("Liberty");
        schedule.addRatingState("IN");
        return schedule;
    }

    /**
     * Given I am sorting by masteroppid in asc and have pages with size 5 When I go through all the pages Then they
     * will return correctly in the right order.
     */
    @Test
    void testAllPaginationPages() {
        List<Integer> oppIds = buildXPagesOfOppsWithBookTransferOne(3, 5);
        CustomFilterRequest customFilterRequest = new CustomFilterRequest();
        customFilterRequest.setOppIds(oppIds);
        PaginationRequest paginationRequest = new PaginationRequest();
        customFilterRequest.setPaginationRequest(paginationRequest);
        paginationRequest.setSize(5);
        paginationRequest.setPageNumber(0);
        paginationRequest.setSorting("OPPORTUNITYID");
        paginationRequest.setSortDirection("asc");

        List<CustomFilter> response = opportunityJDBCRepo.getCustomFilteredOpportunities(customFilterRequest);
        assertEquals(5, response.size());
        assertEquals("1", response.get(0).getMasterOppId());
        assertEquals("5", response.get(4).getMasterOppId());

        paginationRequest.setPageNumber(1);
        response = opportunityJDBCRepo.getCustomFilteredOpportunities(customFilterRequest);
        assertEquals(5, response.size());
        assertEquals("6", response.get(0).getMasterOppId());
        assertEquals("10", response.get(4).getMasterOppId());

        paginationRequest.setPageNumber(2);
        response = opportunityJDBCRepo.getCustomFilteredOpportunities(customFilterRequest);
        assertEquals(5, response.size());
        assertEquals("11", response.get(0).getMasterOppId());
        assertEquals("15", response.get(4).getMasterOppId());

        paginationRequest.setPageNumber(3);
        response = opportunityJDBCRepo.getCustomFilteredOpportunities(customFilterRequest);
        assertEquals(0, response.size());

    }

    private List<Integer> buildXPagesOfOppsWithBookTransferOne(int pages, int size) {
        int btId = bookTransfers.get(0).getBookTransferID();
        List<Integer> oppIds = new ArrayList<>();
        // database starts at one with ids so we start at 1.
        for (int i = 1; i < pages * size + 1; i++) {
            Opportunity opportunity = emptyOpportunity();
            opportunity.setOpportunityId(i);
            opportunity.setMasterOppID(String.valueOf(i));
            opportunity.setBookTransferID(btId);
            opportunity.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
            oppIds.add(opportunityRepository.save(opportunity).getOpportunityId());
        }
        return oppIds;
    }

    /**
     * Given I make a custom filter request Then I expect that it will return the correct oppIds to us that I expect to
     * use
     */
    @Test
    void testCustomFilterQueryGettingIDs() throws Exception {
        BookTransfer dummyBT1 =
                OpportunityTestUtil.saveCreatedBookTransfer(1, 4, "6", bookTransferRepository, "active", "QBE");
        dummyBT1.setSubCode("03431885");
        dummyBT1.setCarrier("Liberty Mutual");
        dummyBT1.setNnumber("n0130627");
        dummyBT1.setSalesforceCode("BT - 29999");
        dummyBT1.setAgentNum("42");
        dummyBT1.setsFDCID(29999);
        dummyBT1 = bookTransferRepository.save(dummyBT1);

        // Creating opps for CustomFilter to return
        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", 4, "x", opportunityRepository);
        dummyOpp1.setEffectiveDate("2010-06-23");
        dummyOpp1.setLastPolicyGuid("EBB22D43-F31B-4A08-9D39-43114C1984B9");
        dummyOpp1.setNAICCd("39012");
        dummyOpp1.setPriorPremium(28.50);
        dummyOpp1.setBusinessType("HOME");
        dummyOpp1.setCustomerName("Collin Williams");
        dummyOpp1.setPriorCarrierGuid("37f4b3d2-6559-4db6-b988-86a9c9a9bb9c");
        dummyOpp1.setState("TX");
        dummyOpp1.setMasterOppID("11792");
        dummyOpp1.setBookTransferID(dummyBT1.getBookTransferID());
        dummyOpp1.setOppPriorCarrier("Allstate");
        dummyOpp1.setStatus(4);
        dummyOpp1.setLastQuotedPremium(349.40);
        dummyOpp1.setNNumber("n0264961");
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        Opportunity dummyOpp2 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", 4, "x", opportunityRepository);
        dummyOpp2.setState("TX");
        dummyOpp2.setBookTransferID(dummyBT1.getBookTransferID());
        opportunityRepository.save(dummyOpp2);

        CustomFilterRequestForOppIds cfRequest = new CustomFilterRequestForOppIds();
        cfRequest.setOpportunityIDs(List.of(dummyOpp1.getOpportunityId()));
        cfRequest.setStatuses(List.of(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS.getOppStatusValue()));
        cfRequest.setStatCode("03431885");
        cfRequest.setStartDate("2010-05-23");
        cfRequest.setEndDate("2010-08-23");
        cfRequest.setPriorCarriers(List.of("Liberty Mutual"));
        cfRequest.setStates(List.of("TX"));
        cfRequest.setLobs(List.of("HOME"));
        cfRequest.setPriorPolicyGuid("37f4b3d2-6559-4db6-b988-86a9c9a9bb9c");
        cfRequest.setSafecoPolicyGuid("EBB22D43-F31B-4A08-9D39-43114C1984B9");
        cfRequest.setCustomerName("Collin Williams");
        cfRequest.setAssignedUser("n0130627");
        cfRequest.setAgentNumber("42");
        cfRequest.setOppPriorCarriers(List.of("Allstate"));
        cfRequest.setSfdcids(List.of(29999));

        List<Integer> oppIds = opportunityJDBCRepo.getCustomFiltersOppIds(cfRequest);

        assertEquals(1, oppIds.size());
        assertTrue(oppIds.contains(dummyOpp1.getOpportunityId()));
    }

    @Test
    void testThatMtrSavesToDatabase() {
        Opportunity mtrOpp = emptyOpportunity();
        mtrOpp.setBusinessType(LineOfBusiness.MTR.toString());
        mtrOpp = opportunityRepository.saveAndFlush(mtrOpp);

        assertEquals("MTR", mtrOpp.getBusinessType());
    }

    @Test
    void testCustomFilterRequest_QUOTE_TYPES() {
        BookTransfer dummyBT1 =
                OpportunityTestUtil.saveCreatedBookTransfer(1, 4, "6", bookTransferRepository, "active", "QBE");
        dummyBT1.setSubCode("03431885");
        dummyBT1.setCarrier("Liberty Mutual");
        dummyBT1.setNnumber("n0130627");
        dummyBT1.setSalesforceCode("BT - 29999");
        dummyBT1.setAgentNum("42");
        dummyBT1.setsFDCID(29999);
        dummyBT1 = bookTransferRepository.save(dummyBT1);

        Opportunity automatedOpportunity = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", 4, "x", opportunityRepository);
        automatedOpportunity.setEffectiveDate("2010-06-23");
        automatedOpportunity.setLastPolicyGuid("EBB22D43-F31B-4A08-9D39-43114C1984B9");
        automatedOpportunity.setNAICCd("39012");
        automatedOpportunity.setPriorPremium(28.50);
        automatedOpportunity.setBusinessType("HOME");
        automatedOpportunity.setCustomerName("Collin Williams");
        automatedOpportunity.setPriorCarrierGuid("APV201197505");
        automatedOpportunity.setState("TX");
        automatedOpportunity.setMasterOppID("11792");
        automatedOpportunity.setBookTransferID(dummyBT1.getBookTransferID());
        automatedOpportunity.setOppPriorCarrier("Allstate");
        automatedOpportunity.setStatus(4);
        automatedOpportunity.setLastQuotedPremium(349.40);
        automatedOpportunity.setNNumber("n000000");
        automatedOpportunity = opportunityRepository.save(automatedOpportunity);

        CustomFilterRequestForOppIds cfr = new CustomFilterRequestForOppIds();

        //All options
        cfr.setQuoteTypes(Arrays.asList("Automated"));
        assertEquals(1, opportunityJDBCRepo.getCustomFiltersOppIds(cfr).size());
        assertEquals(1, opportunityJDBCRepo.getCustomFiltersOppIds(cfr).size());
        //Automated Only
        cfr.setQuoteTypes(List.of("Automated"));
        assertEquals(1, opportunityJDBCRepo.getCustomFiltersOppIds(cfr).size());
        assertEquals(opportunityJDBCRepo.getCustomFiltersOppIds(cfr).get(0), automatedOpportunity.getOpportunityId());
    }

    /**
     * Given I make a custom filter request Then I expect that it will return the correct oppIds to us that I expect to
     * use
     */
    @Test
    void testCustomFilterQuery2ReturnOppIds() {
        BookTransfer dummyBT1 =
                OpportunityTestUtil.saveCreatedBookTransfer(1, 4, "6", bookTransferRepository, "active", "QBE");
        dummyBT1.setSubCode("03431885");
        dummyBT1.setCarrier("Liberty Mutual");
        dummyBT1.setNnumber("n0130627");
        dummyBT1.setSalesforceCode("BT - 29999");
        dummyBT1.setAgentNum("42");
        dummyBT1.setsFDCID(29999);
        dummyBT1 = bookTransferRepository.save(dummyBT1);

        // Creating opps for CustomFilter to return
        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", 4, "x", opportunityRepository);
        dummyOpp1.setEffectiveDate("2010-06-23");
        dummyOpp1.setLastPolicyGuid("EBB22D43-F31B-4A08-9D39-43114C1984B9");
        dummyOpp1.setNAICCd("39012");
        dummyOpp1.setPriorPremium(28.50);
        dummyOpp1.setBusinessType("HOME");
        dummyOpp1.setCustomerName("Collin Williams");
        dummyOpp1.setPriorCarrierGuid("APV201197505");
        dummyOpp1.setState("TX");
        dummyOpp1.setMasterOppID("11792");
        dummyOpp1.setBookTransferID(dummyBT1.getBookTransferID());
        dummyOpp1.setOppPriorCarrier("Allstate");
        dummyOpp1.setStatus(4);
        dummyOpp1.setLastQuotedPremium(349.40);
        dummyOpp1.setNNumber("n0264961");
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        Opportunity dummyOpp2 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", 4, "x", opportunityRepository);
        dummyOpp2.setState("TX");
        dummyOpp2.setBookTransferID(dummyBT1.getBookTransferID());
        opportunityRepository.save(dummyOpp2);

        Opportunity dummyOpp3 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", 4, "x", opportunityRepository);
        dummyOpp3.setState("CA");
        dummyOpp3.setBookTransferID(dummyBT1.getBookTransferID());
        opportunityRepository.save(dummyOpp3);

        CustomFilterRequestForOppIds cfRequest = new CustomFilterRequestForOppIds();
        cfRequest.setStates(List.of("TX"));
        cfRequest.setSfdcids(List.of(29999));

        List<Integer> oppIds = opportunityJDBCRepo.getCustomFiltersOppIds(cfRequest);

        assertEquals(2, oppIds.size());
        assertTrue(oppIds.contains(dummyOpp1.getOpportunityId()));
        assertTrue(oppIds.contains(dummyOpp2.getOpportunityId()));
    }

    /**
     * Given I make a custom filter request Then I expect that it will return the correct oppids to us that i expect to
     * use
     */
    @Test
    void testCustomFilterQueryGettingIDsByLineType() {
        BookTransfer dummyBT1 =
                OpportunityTestUtil.saveCreatedBookTransfer(1, 4, "6", bookTransferRepository, "active", "QBE");
        dummyBT1.setSubCode("03431885");
        dummyBT1.setCarrier("Liberty Mutual");
        dummyBT1.setNnumber("n0130627");
        dummyBT1.setSalesforceCode("BT - 29999");
        dummyBT1.setAgentNum("42");
        dummyBT1.setsFDCID(29999);
        dummyBT1 = bookTransferRepository.save(dummyBT1);

        // Creating opps for CustomFilter to return
        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", 4, "x", opportunityRepository);
        dummyOpp1.setEffectiveDate("2010-06-23");
        dummyOpp1.setLastPolicyGuid("EBB22D43-F31B-4A08-9D39-43114C1984B9");
        dummyOpp1.setNAICCd("39012");
        dummyOpp1.setPriorPremium(28.50);
        dummyOpp1.setBusinessType("HOME");
        dummyOpp1.setCustomerName("Collin Williams");
        dummyOpp1.setPriorCarrierGuid("37f4b3d2-6559-4db6-b988-86a9c9a9bb9c");
        dummyOpp1.setState("TX");
        dummyOpp1.setMasterOppID("11792");
        dummyOpp1.setBookTransferID(dummyBT1.getBookTransferID());
        dummyOpp1.setOppPriorCarrier("Allstate");
        dummyOpp1.setStatus(4);
        dummyOpp1.setLastQuotedPremium(349.40);
        dummyOpp1.setLineType(LineType.Personal);
        dummyOpp1.setNNumber("n0264961");
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        Opportunity dummyOpp2 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", 4, "x", opportunityRepository);
        dummyOpp2.setState("TX");
        dummyOpp2.setBookTransferID(dummyBT1.getBookTransferID());
        dummyOpp2.setLineType(LineType.Business);
        dummyOpp2.setNNumber("n0264961");
        opportunityRepository.save(dummyOpp2);

        CustomFilterRequestForOppIds cfRequest = new CustomFilterRequestForOppIds();
        cfRequest.setLineType(LineType.Personal);

        List<Integer> oppIds = opportunityJDBCRepo.getCustomFiltersOppIds(cfRequest);

        assertEquals(1, oppIds.size());
        assertTrue(oppIds.contains(dummyOpp1.getOpportunityId()));
    }

    @Test
    void testCustomFilterQueryBillingAccountNumber() {
        BookTransfer dummyBT1 =
                OpportunityTestUtil.saveCreatedBookTransfer(1, 4, "6", bookTransferRepository, "active", "QBE");
        dummyBT1 = bookTransferRepository.save(dummyBT1);

        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", 4, "x", opportunityRepository);
        dummyOpp1.setBillingAccountNumber("ban");
        dummyOpp1.setBookTransferID(dummyBT1.getBookTransferID());
        dummyOpp1.setStatus(4);
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        CustomFilterRequestForOppIds cfRequest1 = new CustomFilterRequestForOppIds();
        cfRequest1.setPriorCarrierBillingAccountNumbers(List.of("ban"));
        CustomFilterRequestForOppIds cfRequest2 = new CustomFilterRequestForOppIds();
        cfRequest2.setPriorCarrierBillingAccountNumbers(List.of("different ban"));

        List<Integer> oppIds1 = opportunityJDBCRepo.getCustomFiltersOppIds(cfRequest1);
        List<Integer> oppIds2 = opportunityJDBCRepo.getCustomFiltersOppIds(cfRequest2);

        assertEquals(1, oppIds1.size());
        assertTrue(oppIds1.contains(dummyOpp1.getOpportunityId()));
        assertEquals(0, oppIds2.size());
    }

    /**
     * Given I have 2 opps with texas and one with in When I search for texa Then I will get the two opps from tx.
     */
    @Test
    void testBasicSearchQuery() {
        // Creating opps for CustomFilter to return
        Opportunity dummyOpp1 = emptyOpportunity();
        dummyOpp1.setState("TEXAS");
        dummyOpp1.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp1.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS.getOppStatusCode());
        dummyOpp1.setNNumber("n0264961");
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        Opportunity dummyOpp2 = emptyOpportunity();
        dummyOpp2.setState("TEXAS");
        dummyOpp2.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp2.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode());
        dummyOpp2.setNNumber("n0264961");
        dummyOpp2 = opportunityRepository.save(dummyOpp2);

        Opportunity dummyOpp3 = emptyOpportunity();
        dummyOpp3.setState("IN");
        dummyOpp3.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp3.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS.getOppStatusCode());
        dummyOpp3.setNNumber("n0264961");
        dummyOpp3 = opportunityRepository.save(dummyOpp3);

        CustomFilterRequest cfRequest2 = new CustomFilterRequest();
        PaginationRequest paginationRequest = new PaginationRequest();
        paginationRequest.setSearch("TEXA");
        paginationRequest.setPageNumber(0);
        paginationRequest.setSize(50);

        cfRequest2.setPaginationRequest(paginationRequest);
        cfRequest2.setOppIds(
                Lists.list(dummyOpp1.getOpportunityId(), dummyOpp2.getOpportunityId(), dummyOpp3.getOpportunityId()));
        List<CustomFilter> testFilter2 = opportunityJDBCRepo.getCustomFilteredOpportunities(cfRequest2);

        // Testing that two CFs are returned to a single query
        assertEquals(2, testFilter2.size());
        assertEquals(testFilter2.get(0).getOpportunityId(), String.valueOf(dummyOpp1.getOpportunityId()));
        assertEquals(testFilter2.get(1).getOpportunityId(), String.valueOf(dummyOpp2.getOpportunityId()));

    }

    /**
     * Given I have 1 opps from IN, One opp with missing required data, one with the name justin, and one opp with on
     * "in" in it When I search for in Then I will get the first three opps back because they all have in in it.
     */
    @Test
    void testMoreAdvancedSearchQuery() {

        // Creating opps for CustomFilter to return
        Opportunity dummyOpp1 = emptyOpportunity();
        dummyOpp1.setState("TEXAS");
        dummyOpp1.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp1.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode());
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        Opportunity dummyOpp2 = emptyOpportunity();
        dummyOpp2.setCustomerName("JUSTIN");
        dummyOpp2.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp2.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS.getOppStatusCode());
        dummyOpp2 = opportunityRepository.save(dummyOpp2);

        Opportunity dummyOpp3 = emptyOpportunity();
        dummyOpp3.setState("IN");
        dummyOpp3.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp3.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS.getOppStatusCode());
        dummyOpp3 = opportunityRepository.save(dummyOpp3);

        Opportunity dummyOpp4 = emptyOpportunity();
        dummyOpp4.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp4.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS.getOppStatusCode());
        dummyOpp4 = opportunityRepository.save(dummyOpp4);

        CustomFilterRequest cfRequest2 = new CustomFilterRequest();
        PaginationRequest paginationRequest = new PaginationRequest();
        paginationRequest.setSearch("in");
        paginationRequest.setPageNumber(0);
        paginationRequest.setSize(50);

        cfRequest2.setPaginationRequest(paginationRequest);
        cfRequest2.setOppIds(Lists.list(dummyOpp1.getOpportunityId(), dummyOpp2.getOpportunityId(),
                dummyOpp3.getOpportunityId(), dummyOpp4.getOpportunityId()));
        List<CustomFilter> testFilter2 = opportunityJDBCRepo.getCustomFilteredOpportunities(cfRequest2);

        // Testing that three CFs are returned to a single query
        assertEquals(3, testFilter2.size());
        assertEquals(testFilter2.get(0).getOpportunityId(), String.valueOf(dummyOpp1.getOpportunityId()));
        assertEquals(testFilter2.get(1).getOpportunityId(), String.valueOf(dummyOpp2.getOpportunityId()));
        assertEquals(testFilter2.get(2).getOpportunityId(), String.valueOf(dummyOpp3.getOpportunityId()));

    }

    /**
     * Given I Have one opp with co in bt. carrier And i have another opp with the state co When I do a search for CO
     * only those with the CO in the state will come up
     */
    @Test
    void testSearchQueryWithItemsOffPage() {

        // Creating opps for CustomFilter to return
        Opportunity dummyOpp1 = emptyOpportunity();
        dummyOpp1.setState("co");
        dummyOpp1.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode());
        dummyOpp1.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        Opportunity dummyOpp2 = emptyOpportunity();
        dummyOpp2.setState("in");
        dummyOpp2.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode());
        BookTransfer bookTransferWithCo = bookTransfers.get(1);
        bookTransferWithCo.setCarrier("Some Type of co.");
        bookTransferRepository.save(bookTransferWithCo);
        dummyOpp2.setBookTransferID(bookTransferWithCo.getBookTransferID());
        dummyOpp2 = opportunityRepository.save(dummyOpp2);

        CustomFilterRequest cfRequestForCo = new CustomFilterRequest();
        PaginationRequest paginationRequest = new PaginationRequest();
        paginationRequest.setSearch("co");
        paginationRequest.setPageNumber(0);
        paginationRequest.setSize(50);

        cfRequestForCo.setPaginationRequest(paginationRequest);
        cfRequestForCo.setOppIds(Lists.list(dummyOpp1.getOpportunityId(), dummyOpp2.getOpportunityId()));

        List<CustomFilter> testFilter2 = opportunityJDBCRepo.getCustomFilteredOpportunities(cfRequestForCo);

        assertEquals(1, testFilter2.size());
        assertEquals(testFilter2.get(0).getOpportunityId(), String.valueOf(dummyOpp1.getOpportunityId()));

    }

    /**
     * Given I Have one opp with a date When I do a search for that year Then that opp will come up
     */
    @ParameterizedTest
    @ValueSource(strings = {
            "2019", // Search by year
            "07/15", // Search by month/day
            "07/15/2019" // search by full date
    })
    void testSearchingByYearGettingCorrectDateFormat(String searchString) {
        // Creating opps for CustomFilter to return
        Opportunity dummyOpp1 = emptyOpportunity();
        dummyOpp1.setEffectiveDate("2019-07-15");
        dummyOpp1.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode());
        dummyOpp1.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        CustomFilterRequest cfRequestForCo = new CustomFilterRequest();
        PaginationRequest paginationRequest = new PaginationRequest();
        paginationRequest.setSearch(searchString);
        paginationRequest.setPageNumber(0);
        paginationRequest.setSize(50);

        cfRequestForCo.setPaginationRequest(paginationRequest);
        cfRequestForCo.setOppIds(Lists.list(dummyOpp1.getOpportunityId()));

        List<CustomFilter> testFilter2 = opportunityJDBCRepo.getCustomFilteredOpportunities(cfRequestForCo);

        assertEquals(1, testFilter2.size());
        assertEquals(testFilter2.get(0).getOpportunityId(), String.valueOf(dummyOpp1.getOpportunityId()));

    }

    /**
     * Given I Have one opp with a value with a / in that is not a date When I search for that value Then that value
     * will still be found
     */
    @Test
    void testSearchWithSlashOnNonDate() {
        // Creating opps for CustomFilter to return
        Opportunity dummyOpp1 = emptyOpportunity();
        dummyOpp1.setCustomerName("name1/name2");
        dummyOpp1.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode());
        dummyOpp1.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        CustomFilterRequest cfRequestForCo = new CustomFilterRequest();
        PaginationRequest paginationRequest = new PaginationRequest();
        paginationRequest.setSearch("name1/n");
        paginationRequest.setPageNumber(0);
        paginationRequest.setSize(50);

        cfRequestForCo.setPaginationRequest(paginationRequest);
        cfRequestForCo.setOppIds(Lists.list(dummyOpp1.getOpportunityId()));

        List<CustomFilter> testFilter2 = opportunityJDBCRepo.getCustomFilteredOpportunities(cfRequestForCo);

        assertEquals(1, testFilter2.size());
        assertEquals(testFilter2.get(0).getOpportunityId(), String.valueOf(dummyOpp1.getOpportunityId()));
    }

    /**
     * Given I have a customFilter Request with a pagination request, but without a search Then the filter items count
     * will come back to be the same as the amount of items put into the query
     */
    @Test
    void testGettingFilteredCountWithoutSearch() {
        CustomFilterRequest request = new CustomFilterRequest();
        PaginationRequest paginationRequest = new PaginationRequest(0, 2);
        paginationRequest.setSorting("asc", "bookTransferID");

        List<Integer> oppIds = buildXPagesOfOppsWithBookTransferOne(10, 2);
        request.setOppIds(oppIds);

        request.setPaginationRequest(paginationRequest);
        int count = opportunityJDBCRepo.getCustomFilteredOpportunitiesCount(request);
        assertEquals(count, oppIds.size());
    }

    /**
     * Given I have a customFilter Request with a pagination request, but with a search Then the filter items count will
     * come back to be the same as the amount of items put into the query
     */
    @Test
    void testGettingFilteredCountWithSearch() {
        CustomFilterRequest request = new CustomFilterRequest();
        PaginationRequest paginationRequest = new PaginationRequest(0, 2);
        paginationRequest.setSorting("asc", "bookTransferID");
        paginationRequest.setSearch("i");

        List<Integer> oppIds = buildXPagesOfOppsWithBookTransferOne(10, 2);
        request.setOppIds(oppIds);
        request.setPaginationRequest(paginationRequest);

        // set search data
        // search for items that wouldn't naturally be on that page
        Opportunity opp1 = opportunityRepository.findByOpportunityId(oppIds.get(10));
        opp1.setState("in");
        opportunityRepository.save(opp1);

        Opportunity opp2 = opportunityRepository.findByOpportunityId(oppIds.get(15));
        opp2.setState("il");
        opportunityRepository.save(opp2);

        int count = opportunityJDBCRepo.getCustomFilteredOpportunitiesCount(request);
        assertEquals(2, count);
    }

    /**
     * Given I want to search by oppIds Then I will ignore all other oppIds.
     */
    @Test
    void testQueryWithOppIds() {
        // Creating opps for CustomFilter to return
        Opportunity dummyOpp1 = emptyOpportunity();
        dummyOpp1.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp1.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode());
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        Opportunity dummyOpp2 = emptyOpportunity();
        dummyOpp2.setBookTransferID(bookTransfers.get(0).getBookTransferID());
        dummyOpp2.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS.getOppStatusCode());
        opportunityRepository.save(dummyOpp2);

        CustomFilterRequest cfRequest2 = new CustomFilterRequest();
        PaginationRequest paginationRequest = new PaginationRequest();
        paginationRequest.setSearch("in");
        paginationRequest.setPageNumber(0);
        paginationRequest.setSize(50);
        cfRequest2.setPaginationRequest(paginationRequest);
        cfRequest2.setOppIds(Lists.list(dummyOpp1.getOpportunityId()));
        List<CustomFilter> testFilter2 = opportunityJDBCRepo.getCustomFilteredOpportunities(cfRequest2);

        assertEquals(1, testFilter2.size());
        assertEquals(testFilter2.get(0).getOpportunityId(), String.valueOf(dummyOpp1.getOpportunityId()));
    }

    /**
     * Given I want to search by oppIds Then I will return a opp that matches the data needed for custom filter page
     */
    @Test
    @Disabled("Temporarily ignore. Failing due to a difference of 3 hours - timezone.")
    void testThatQueryBuildsOutOppCorrectly() throws Exception {
        // Creating opps for CustomFilter to return
        Opportunity dummyOpp1 = emptyOpportunity();
        BookTransfer bookTransfer = bookTransfers.get(0);
        dummyOpp1.setBookTransferID(bookTransfer.getBookTransferID());
        dummyOpp1.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode());
        dummyOpp1.setState("IN");
        dummyOpp1.setBusinessType("AUTOP");
        dummyOpp1.setPriorCarrierGuid("37f4b3d2-6559-4db6-b988-86a9c9a9bb9c");
        dummyOpp1.setLastPolicyGuid("36f4b3d2-6559-4db6-b988-86a9c9a9bb9c");
        dummyOpp1.setCustomerName("Gopi");
        dummyOpp1.setEffectiveDate("2019-10-31");
        dummyOpp1.setNAICCd("12345");
        dummyOpp1.setPriorPremium(10.00);
        dummyOpp1.setMasterOppID("2");
        dummyOpp1.setLastQuotedPremium(20.00);
        dummyOpp1 = opportunityRepository.save(dummyOpp1);

        CustomFilterRequest cfRequest2 = new CustomFilterRequest();
        PaginationRequest paginationRequest = new PaginationRequest();
        paginationRequest.setPageNumber(0);
        paginationRequest.setSize(50);
        cfRequest2.setPaginationRequest(paginationRequest);
        cfRequest2.setOppIds(Lists.list(dummyOpp1.getOpportunityId()));

        List<CustomFilter> testFilter2 = opportunityJDBCRepo.getCustomFilteredOpportunities(cfRequest2);

        assertEquals(1, testFilter2.size());
        CustomFilter opp = testFilter2.get(0);
        assertEquals(opp.getOpportunityId(), String.valueOf(dummyOpp1.getOpportunityId()));
        assertEquals(opp.getSubCode(), bookTransfer.getSubCode());
        assertEquals(opp.getNNumber(), bookTransfer.getNnumber());
        assertEquals(opp.getSalesforceCode(), bookTransfer.getSalesforceCode());
        assertEquals(opp.getBookTransferId(), String.valueOf(dummyOpp1.getBookTransferID()));
        assertEquals(opp.getStatus(),
                OpportunityStatus.getOppStatusFromCode(dummyOpp1.getStatus()).getOppStatusValue());
        assertEquals(opp.getEffectiveDate(), DateUtils.convertStringToDate(dummyOpp1.getEffectiveDate()));
        assertEquals(opp.getSafecoPolicyGuid(), dummyOpp1.getLastPolicyGuid());
        assertEquals(opp.getNAICCd(), String.valueOf(dummyOpp1.getNAICCd()));
        assertEquals(opp.getPriorPremium(), String.valueOf(dummyOpp1.getPriorPremium()));
        assertEquals(opp.getLastQuotedPremium(), String.valueOf(dummyOpp1.getLastQuotedPremium()));
        assertEquals(opp.getLob(), dummyOpp1.getBusinessType());
        assertEquals(opp.getCustomerName(), dummyOpp1.getCustomerName());
        assertEquals(opp.getPriorCarrierGuid(), dummyOpp1.getPriorCarrierGuid());
        assertEquals(opp.getMasterOppId(), dummyOpp1.getMasterOppID());
    }

    /**
     * Given I have an nbdRelationship I want to search for When I search for those opportunities Then it will return
     * the correct opportunities ids
     */
    @Test
    void customFiltersNBDRelationshipReturnOppIds() {
        BookTransfer dummyBT1 =
                OpportunityTestUtil.saveCreatedBookTransfer(1, 4, "6", bookTransferRepository, "active", "QBE");
        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", dummyBT1.getBookTransferID(), "x",
                opportunityRepository);
        List<String> nbdRelationships = new ArrayList<>();
        nbdRelationships.add("QBE");
        CustomFilterRequestForOppIds cfRequest = new CustomFilterRequestForOppIds();
        cfRequest.setNbdRelationship(nbdRelationships);
        List<Integer> oppIds = opportunityJDBCRepo.getCustomFiltersOppIds(cfRequest);
        assertEquals(1, oppIds.size());
        assertTrue(oppIds.contains(dummyOpp1.getOpportunityId()));
    }

    /**
     * Given I have an uploadEvent I want to search for When I search for those opportunities Then it will return the
     * correct opportunities ids
     */
    @Test
    void customUploadEventIdReturnOppIds() {
        BookTransfer dummyBT1 =
                OpportunityTestUtil.saveCreatedBookTransfer(1, 4, "6", bookTransferRepository, "active", "QBE");
        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", dummyBT1.getBookTransferID(), "x",
                opportunityRepository);
        dummyOpp1.setUploadEventID(12345);
        opportunityRepository.save(dummyOpp1);
        Opportunity dummyOpp2 = OpportunityTestUtil.saveCreatedDummyOpp(4, "data", dummyBT1.getBookTransferID(), "x",
                opportunityRepository);
        dummyOpp2.setUploadEventID(12346);
        opportunityRepository.save(dummyOpp2);
        CustomFilterRequestForOppIds cfRequest = new CustomFilterRequestForOppIds();
        cfRequest.setUploadEventId(12345);
        List<Integer> oppIds = opportunityJDBCRepo.getCustomFiltersOppIds(cfRequest);
        assertEquals(1, oppIds.size());
        assertTrue(oppIds.contains(dummyOpp1.getOpportunityId()));
    }

    /**
     * Given the last policy guild not present in opportunity table When we query opportunity table for data xml Then we
     * should get null result
     */
    @Test
    void testGetDataXmlNullWithPolicyGuid() {
        assertNull(opportunityJDBCRepo.getDataXmlStringByLastPolicyGuid("fakeguid"));
    }

    /**
     * Given the last policy guild present in opportunity table When we query opportunity table for data xml Then we
     * should get the opportunity associated to that policy guid
     */
    @Test
    void testGetDataXmlWithPolicyGuid() {
        String policyGuid = "EBB22D43-F31B-4A08-9D39-43114C1984B9";
        String expectedDataXml = "data";
        Opportunity dummyOpp1 =
                OpportunityTestUtil.saveCreatedDummyOpp(4, expectedDataXml, 4, "x", opportunityRepository);
        dummyOpp1.setLastPolicyGuid(policyGuid);
        opportunityRepository.save(dummyOpp1);

        String dataXml = opportunityJDBCRepo.getDataXmlStringByLastPolicyGuid(policyGuid);

        assertNotNull(dataXml);
        assertEquals(expectedDataXml, dataXml);
    }

    /**
     * Test updating opportunity with the qni data with valid data
     */
    @Test
    void testUpdateOppWithQnIDataTrue() throws Exception {
        String acordXml = OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml"));
        acordXml = XmlHelper.getDocumentString(XmlHelper.getDocument(acordXml));
        String policyGuid = "EBB22D43-F31B-4A08-9D39-43114C1984B9";
        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(4, acordXml, 4, "x", opportunityRepository);
        dummyOpp1.setLastPolicyGuid(policyGuid);
        opportunityRepository.save(dummyOpp1);
        int expectedStatus = 10;
        Date expectedEffectiveDate = new Date();
        String firstName = "firstname";
        String lastName = "lastname";
        String expectedRatingState = "WA";
        Double expectedPremium = 357.0;
        String expectedCustomerName = firstName + " " + lastName;
        QuoteAndIssueUpdate qniData =
                new QuoteAndIssueUpdate(expectedStatus, expectedEffectiveDate, firstName, lastName, expectedRatingState,
                        "1", expectedPremium, 10.00, null, policyGuid, "A111111", "1", "1234567890", true, true);
        assertTrue(opportunityJDBCRepo.updateOpportunityWithQnIData(acordXml, qniData));
        Opportunity opp = opportunityRepository.findByOpportunityId(dummyOpp1.getOpportunityId());
        assertEquals(opp.getStatus(), expectedStatus);
        assertEquals(opp.getState(), expectedRatingState);
        assertEquals(opp.getCustomerName(), expectedCustomerName);
        assertEquals(opp.getLastQuotedPremium(), expectedPremium);
        assertEquals(opp.getEffectiveDate(), DateUtils.getSQLDateString(expectedEffectiveDate));
        assertNotNull(opp.getTimestampIssued());
    }

    @Test
    void testUpdateOppWithQnIDataTrue_ProdIssue() throws Exception {
        String acordXml = OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml"));
        acordXml = XmlHelper.getDocumentString(XmlHelper.getDocument(acordXml));
        String policyGuid = "EBB22D43-F31B-4A08-9D39-43114C1984B9";
        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(4, acordXml, 4, "x", opportunityRepository);
        dummyOpp1.setLastPolicyGuid(policyGuid);
        opportunityRepository.save(dummyOpp1);

        Opportunity foundOpp = opportunityJDBCRepo.findOpportunityByLastPolicyGuid(policyGuid);

        assertEquals(foundOpp.getLastPolicyGuid(), policyGuid);
        assertEquals(4, foundOpp.getStatus());
    }

    /**
     * Test updating opportunity with the qni data with invalid data
     */
    @Test
    void testUpdateOppWithInvalidQnIDataTrue() throws Exception {
        String acordXml = OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml"));
        acordXml = XmlHelper.getDocumentString(XmlHelper.getDocument(acordXml));
        String policyGuid = "EBB22D43-F31B-4A08-9D39-43114C1984B9";
        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(4, acordXml, 4, "x", opportunityRepository);
        dummyOpp1.setEffectiveDate("2018-08-12");
        dummyOpp1.setState("WA");
        dummyOpp1.setCustomerName("RRR");
        dummyOpp1.setLastQuotedPremium(1234.0);
        dummyOpp1.setLastPolicyGuid(policyGuid);
        opportunityRepository.save(dummyOpp1);
        int expectedStatus = 0;
        Date expectedEffectiveDate = new Date();
        String firstName = "";
        String lastName = "";
        String expectedRatingState = "";
        Double expectedPremium = null;
        String expectedCustomerName = firstName + " " + lastName;
        QuoteAndIssueUpdate qniData =
                new QuoteAndIssueUpdate(expectedStatus, expectedEffectiveDate, firstName, lastName, expectedRatingState,
                        "1", null, 10.00, null, policyGuid,
                        "A111111", "1", "1234567890", true, true);
        assertTrue(opportunityJDBCRepo.updateOpportunityWithQnIData(acordXml, qniData));
        Opportunity opp = opportunityRepository.findByOpportunityId(dummyOpp1.getOpportunityId());
        assertNotEquals(expectedStatus, opp.getStatus());
        assertNotEquals(expectedRatingState, opp.getState());
        assertNotEquals(expectedCustomerName, opp.getCustomerName());
        assertEquals(DateUtils.getSQLDateString(expectedEffectiveDate), opp.getEffectiveDate());
        assertNull(opp.getTimestampIssued());
    }

    /**
     * Test not updating opportunity with the qni data
     */
    @Test
    void testUpdateOppWithQnIDataFalse() {
        String acordXml = "fakexml";
        int expectedStatus = 10;
        Date expectedEffectiveDate = new Date();
        String firstName = "firstname";
        String lastName = "lastname";
        String expectedRatingState = "WA";
        Double expectedPremium = 357.0;

        QuoteAndIssueUpdate qniData = new QuoteAndIssueUpdate(expectedStatus, expectedEffectiveDate, firstName,
                lastName, expectedRatingState, "1", expectedPremium, 10.00, null,
                "D52C22A9-DD59-44E7-A4F6-1B2A286AD7D2", "A111111", "1", "1234567890", true, true);

        assertFalse(opportunityJDBCRepo.updateOpportunityWithQnIData(acordXml, qniData));
    }

    /**
     * Given that I have 4 books each with one opp that should run on my schedule And 2 of them are active And 1 of them
     * is Complete And 1 of them is Withdrawn When I search for opps to use Then it will ignore all of opps that parent
     * books that are not active
     */
    @Test
    @Disabled("This randomly fails in bamboo and cannot reproduce locally. Something ot do with h2 behind the scenes.")
    void testWithAFewActiveAndAFewInActiveBooks() {
        ScheduleRequest schedule = buildBasicSchedule(65);
        String validEffectiveDate = DateUtils.getDateXDaysInTheFuture(1);

        schedule.addSalesForceCodes(bookTransfers.get(0).getSalesforceCode());
        Opportunity activeOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransfers.get(0).getBookTransferID(), "Liberty",
                validEffectiveDate, opportunityRepository, "IN");

        schedule.addSalesForceCodes(bookTransfers.get(1).getSalesforceCode());
        Opportunity activeOpp2 = OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, bookTransfers.get(1).getBookTransferID(), "Liberty",
                validEffectiveDate, opportunityRepository, "IN");

        BookTransfer completedBookTransfer = OpportunityTestUtil.saveCreatedBookTransfer(3, 47123, "78945",
                bookTransferRepository, "54796", "completed");
        schedule.addSalesForceCodes(completedBookTransfer.getSalesforceCode());
        OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, completedBookTransfer.getBookTransferID(), "Liberty",
                validEffectiveDate, opportunityRepository, "IN");

        BookTransfer withdrawnBookTransfer = OpportunityTestUtil.saveCreatedBookTransfer(4, 47123, "78945",
                bookTransferRepository, "54796", "withdrawn");
        schedule.addSalesForceCodes(withdrawnBookTransfer.getSalesforceCode());
        OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, withdrawnBookTransfer.getBookTransferID(), "Liberty",
                validEffectiveDate, opportunityRepository, "IN");
        Map<LineType, List<Integer>> results = opportunityJDBCRepo.getOppIdsForTheGivenSchedule(schedule);
        List<Integer> returnedIds = results.get(LineType.Personal);

        assertTrue(returnedIds.contains(activeOpp1.getOpportunityId()));
        assertTrue(returnedIds.contains(activeOpp2.getOpportunityId()));
    }

    /**
     * Test a basic JDBC query with a nbdrelationship to make sure nbdrelationship works correctly
     */
    @Test
    void testWithBasicQueryWithNbdRelationship() {
        int sla = 65;
        // not sure if this is a good way to do this the best I could think of though
        List<Integer> correctOppIds = buildOpportunities(bookTransfers, sla);
        ScheduleRequest schedule = buildBasicSchedule(sla);
        schedule.setNbdRelationship("QBE");
        Map<LineType, List<Integer>> results = opportunityJDBCRepo.getOppIdsForTheGivenSchedule(schedule);
        List<Integer> returnedIds = results.get(LineType.Personal);

        assertEquals(2, returnedIds.size());
        assertTrue(returnedIds.contains(correctOppIds.get(0)));
        assertTrue(returnedIds.contains(correctOppIds.get(5)));
    }

    /**
     * Given I have a booktransferid When I query for the opportunities Then I should get the opportunities associated
     * for that booktransferid
     */
    @Test
    void testFindByBookTransferId() {
        buildOpportunities(bookTransfers, 0);
        assertEquals(7, opportunityJDBCRepo.findByBookTransferID(bookTransfers.get(0).getBookTransferID()).size());
        assertEquals(1, opportunityJDBCRepo.findByBookTransferID(bookTransfers.get(1).getBookTransferID()).size());
        assertEquals(0, opportunityJDBCRepo.findByBookTransferID(466488).size());
    }

    /**
     * Given I have a customFilter Request with a pagination request When I query for effective date range Then i should
     * get the min and max effective date for the filtered list
     */
    @Test
    void testGetCustomFilterOpportunitiesEffectiveDateRange() {
        String minEffectiveDate = "2019-01-01";
        String maxEffectiveDate = "2019-01-31";
        List<Integer> oppIds = new ArrayList<>();
        BookTransfer dummyBT1 =
                OpportunityTestUtil.saveCreatedBookTransfer(1, 4, "6", bookTransferRepository, "active", "QBE");
        Opportunity opp = OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, dummyBT1.getBookTransferID(), "test", minEffectiveDate,
                opportunityRepository, "WA");
        oppIds.add(opp.getOpportunityId());

        opp = OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, dummyBT1.getBookTransferID(), "test", maxEffectiveDate,
                opportunityRepository, "WA");
        oppIds.add(opp.getOpportunityId());

        EffectiveDateRange dateRange = opportunityJDBCRepo.getCustomFilterOpportunitiesEffectiveDateRange(oppIds);
        assertEquals(dateRange.getMinEffectiveDate(), minEffectiveDate);
        assertEquals(dateRange.getMaxEffectiveDate(), maxEffectiveDate);
    }

    /**
     * Given I have a list of oppids When I query for the bookids Then I should get the booktransferids associated with
     * the oppids
     */
    @Test
    void testGetBookTransferIds() {
        int bookId1 = 4;
        int bookId2 = 5;
        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(4, "xml", bookId1, "x", opportunityRepository);
        Opportunity dummyOpp2 = OpportunityTestUtil.saveCreatedDummyOpp(4, "xml", bookId2, "x", opportunityRepository);
        List<Integer> oppIds = new ArrayList<>();
        oppIds.add(dummyOpp1.getOpportunityId());
        oppIds.add(dummyOpp2.getOpportunityId());
        Map<Integer, Integer> map = opportunityJDBCRepo.getBookIds(oppIds);
        assertEquals(2, map.size());
        assertEquals(map.get(dummyOpp1.getOpportunityId()), bookId1);
        assertEquals(map.get(dummyOpp2.getOpportunityId()), bookId2);
    }

    /**
     * Given I have a list of oppids When I query for the bookids Then I should get the booktransferids associated with
     * the oppids
     */
    @Test
    void testGetOppDetailsForQuoteResult() {
        int bookId1 = 4;
        int bookId2 = 5;
        int bookId3 = 3;

        Opportunity dummyOpp1 = OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.AUTOP,
                OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS, bookId1, "x", "2019-12-31", opportunityRepository,
                "WA");
        dummyOpp1.setLineType(LineType.Personal);
        Opportunity dummyOpp2 = OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS, bookId2, "x", "2019-12-31", opportunityRepository,
                "WA");
        dummyOpp2.setLineType(LineType.Personal);
        Opportunity dummyOpp3 = OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.HOME,
                OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS, bookId3, "x", "2019-12-31", opportunityRepository,
                "WA");
        dummyOpp3.setLineType(LineType.Business);
        opportunityRepository.save(dummyOpp1);
        opportunityRepository.save(dummyOpp2);
        opportunityRepository.save(dummyOpp3);

        Set<Integer> allBookIds = new HashSet<>();
        allBookIds.add(bookId1);
        allBookIds.add(bookId2);
        allBookIds.add(bookId3);

        List<OpportunityDetails> personalOppDetails = opportunityJDBCRepo.getOppDetails(allBookIds, new ArrayList<>(), "", "", LineType.Personal);
        assertEquals(2, personalOppDetails.size());

        List<OpportunityDetails> businessOppDetails = opportunityJDBCRepo.getOppDetails(allBookIds, new ArrayList<>(), "", "", LineType.Business);
        assertEquals(1, businessOppDetails.size());

        List<OpportunityDetails> bothOppDetails = opportunityJDBCRepo.getOppDetails(allBookIds, new ArrayList<>(), "", "", LineType.All);
        assertEquals(3, bothOppDetails.size());
    }

    @Test
    void testCreateOppWithAgencyIdLessThan30Chars() throws IOException {
        String agencyId = "abcdefghijklmnop";
        Opportunity opp = createOpportunity(agencyId);
        assertEquals(opp.getAgencyId(), agencyId);
    }

    @Test
    void testCreateOppWithAgencyId30Chars() throws IOException {
        String agencyId = "abcdefghijklmnopqrstuvwxyz1234";
        Opportunity opp = createOpportunity(agencyId);
        assertEquals(opp.getAgencyId(), agencyId);
    }

    @Test
    void testCreateOppWithAgencyIdGreaterThan30Chars() throws IOException {
        String agencyId = "abcdefghijklmnopqrstuvwxyz1234567890,.?$@^%&*()#";
        String expeced = "abcdefghijklmnopqrstuvwxyz1234";
        Opportunity opp = createOpportunity(agencyId);
        assertEquals(opp.getAgencyId(), expeced);
    }

    private Opportunity createOpportunity(String agencyId) throws IOException {
        return OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.AUTOP,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, 123456, "USAA", "2020-06-12", opportunityRepository,
                "WA", agencyId, "src/test/resources/xml/lobAuto.xml");
    }

    @Test
    void testUpdateOpportunity() throws IOException {
        Opportunity opp = createOpportunity("456789");
        opp.setAgencyId("003002");
        opportunityJDBCRepo.updateOpportunity(opp);
        Opportunity updatedOpp = opportunityRepository.findByOpportunityId(opp.getOpportunityId());
        assertEquals(opp.getAgencyId(), updatedOpp.getAgencyId());
    }

    @Test
    @Disabled("Temporarily ignore. Failing due to a difference of 3 hours - timezone.")
    void testUpdateOpportunityPostQuote() throws IOException {
        Opportunity opp = createOpportunity("456789");
        String expectedString = "someValue";
        LocalDateTime expectedD1 = LocalDateTime.of(2000, 1, 1, 0, 0);
        LocalDateTime expectedD2 = LocalDateTime.of(2001, 1, 1, 0, 0);

        opp.setData(null);
        opp.setStatus(5);
        opp.setLastQuotedPremium(5.0);
        opp.setLastPolicyGuid(expectedString);
        opp.setTimestampCallPartner(expectedD1);
        opp.setTimestampFirstCallPartner(expectedD2);

        opportunityJDBCRepo.updateOpportunityPostQuote(opp);
        Opportunity updatedOpp = opportunityRepository.findByOpportunityId(opp.getOpportunityId());

        // assert dataXml does not get overwritten
        assertNotNull(updatedOpp.getData());
        assertEquals(5, updatedOpp.getStatus());
        assertEquals(5.0, updatedOpp.getLastQuotedPremium());
        assertEquals(expectedString, updatedOpp.getLastPolicyGuid());
        assertEquals(expectedD1, updatedOpp.getTimestampCallPartner());
        assertEquals(expectedD2, updatedOpp.getTimestampFirstCallPartner());
    }

    private Opportunity createOpportunityForErrorInfo(Integer booktransferId) throws IOException {
        return OpportunityTestUtil.saveCreatedDummyOpp(LineOfBusiness.AUTOP,
                OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED, booktransferId, "USAA", "2020-06-12", opportunityRepository,
                "WA", "1234", "src/test/resources/xml/lobAuto.xml");
    }

    @Test
    void testGetErrorInfoByOpportunityIds() throws IOException {
        Opportunity opportunity = createOpportunityForErrorInfo(1);
        opportunity = opportunityRepository.save(opportunity);

        List<Integer> opportunityIds = new ArrayList<>();
        opportunityIds.add(opportunity.getOpportunityId());

        opportunity = createOpportunityForErrorInfo(2);
        opportunity = opportunityRepository.save(opportunity);

        opportunityIds.add(opportunity.getOpportunityId());
        List<OpportunityErrorInfo> errorItems = opportunityJDBCRepo.getErrorItemsByOppIds(opportunityIds);
        assertNotNull(errorItems);
        assertEquals(2, errorItems.size());

        assertEquals(errorItems.get(0).getOpportunityId(), opportunityIds.get(0));
        assertEquals(1, errorItems.get(0).getBookTransferId());
        assertEquals("WA", errorItems.get(0).getState());
        assertEquals("AUTO", errorItems.get(0).getProductLine());

        assertEquals(errorItems.get(1).getOpportunityId(), opportunityIds.get(1));
        assertEquals(2, errorItems.get(1).getBookTransferId());
        assertEquals("WA", errorItems.get(1).getState());
        assertEquals("AUTO", errorItems.get(1).getProductLine());
    }

    @Test
    void testDoesFirstTimestampExist() {
        Opportunity opportunity = emptyOpportunity();
        opportunity.setTimestampFirstCallPartner(LocalDateTime.parse("2021-04-20T04:20:08.644"));
        opportunity = opportunityRepository.save(opportunity);

        boolean doesFirstTimestampExist = opportunityJDBCRepo.doesFirstTimestampExist(opportunity.getOpportunityId());

        assertTrue(doesFirstTimestampExist);
    }

    @Test
    void testDoesFirstTimestampNotExist() {
        Opportunity opportunity = emptyOpportunity();
        opportunity = opportunityRepository.save(opportunity);

        boolean doesFirstTimestampExist = opportunityJDBCRepo.doesFirstTimestampExist(opportunity.getOpportunityId());

        assertFalse(doesFirstTimestampExist);
    }

    @Test
    @Disabled("Temporarily ignore. Failing due to a difference of 3 hours - timezone.")
    void testUpdateOpportunityForBLCall() {
        final Instant instantTimestamp = Instant.ofEpochSecond(1618935600000L);
        Timestamp timestamp = Timestamp.from(instantTimestamp);
        String premiumValue = "";

        Opportunity opportunity = emptyOpportunity();
        opportunity = opportunityRepository.saveAndFlush(opportunity);

        opportunityJDBCRepo.updateOpportunityForBLCall(opportunity.getOpportunityId(), timestamp.toString(), premiumValue, false);

        Opportunity changedOpportunity = opportunityRepository.findByOpportunityId(opportunity.getOpportunityId());

        assertEquals(changedOpportunity.getTimestampCallPartner(), timestamp.toLocalDateTime());
        assertEquals(changedOpportunity.getTimestampFirstCallPartner(), timestamp.toLocalDateTime());
        assertNull(changedOpportunity.getLastQuotedPremium());
    }

    @Test
    @Disabled("Temporarily ignore. Failing due to a difference of 3 hours - timezone.")
    void testUpdateOpportunityForBLCallPremium() {
        final Instant instantTimestamp = Instant.ofEpochSecond(1618935600000L);
        Timestamp timestamp = Timestamp.from(instantTimestamp);
        String premiumValue = "500.00";

        Opportunity opportunity = emptyOpportunity();
        opportunity = opportunityRepository.saveAndFlush(opportunity);

        opportunityJDBCRepo.updateOpportunityForBLCall(opportunity.getOpportunityId(), timestamp.toString(), premiumValue, false);

        Opportunity changedOpportunity = opportunityRepository.findByOpportunityId(opportunity.getOpportunityId());

        assertEquals(changedOpportunity.getTimestampCallPartner(), timestamp.toLocalDateTime());
        assertEquals(changedOpportunity.getTimestampFirstCallPartner(), timestamp.toLocalDateTime());
        assertEquals(changedOpportunity.getLastQuotedPremium(), Double.parseDouble(premiumValue));
    }

    @Test
    @Disabled("Temporarily ignore. Failing due to a difference of 3 hours - timezone.")
    void testUpdateOpportunityForBLCallTimestamp() {
        final Instant instantTimestamp = Instant.ofEpochSecond(1618935600000L);
        Timestamp timestamp = Timestamp.from(instantTimestamp);
        String premiumValue = "";

        Opportunity opportunity = emptyOpportunity();
        opportunity.setTimestampFirstCallPartner(timestamp.toLocalDateTime());
        opportunity = opportunityRepository.saveAndFlush(opportunity);

        opportunityJDBCRepo.updateOpportunityForBLCall(opportunity.getOpportunityId(), timestamp.toString(), premiumValue, true);

        Opportunity changedOpportunity = opportunityRepository.findByOpportunityId(opportunity.getOpportunityId());
        //TODO  fix timezone
        //assertThat(changedOpportunity.getTimestampCallPartner(), is(timestamp.toLocalDateTime()));
        //assertThat(changedOpportunity.getTimestampFirstCallPartner(), is(opportunity.getTimestampFirstCallPartner()));
        assertNull(changedOpportunity.getLastQuotedPremium());
    }

    @Test
    @Disabled("Temporarily ignore. Failing due to a difference of 3 hours - timezone.")
    void testUpdateOpportunityForBLCallPremiumFirstTimestamp() {
        final Instant instantTimestamp = Instant.ofEpochSecond(1618935600000L);
        Timestamp timestamp = Timestamp.from(instantTimestamp);
        String premiumValue = "500.00";

        Opportunity opportunity = emptyOpportunity();
        opportunity = opportunityRepository.saveAndFlush(opportunity);

        opportunityJDBCRepo.updateOpportunityForBLCall(opportunity.getOpportunityId(), timestamp.toString(), premiumValue, true);

        Opportunity changedOpportunity = opportunityRepository.findByOpportunityId(opportunity.getOpportunityId());

        assertEquals(changedOpportunity.getTimestampCallPartner(), timestamp.toLocalDateTime());
        assertEquals(changedOpportunity.getTimestampFirstCallPartner(), opportunity.getTimestampFirstCallPartner());
        assertEquals(changedOpportunity.getLastQuotedPremium(), Double.parseDouble(premiumValue));
    }

    @Test
    void test_shouldInsertOpportunityRowWithAutomatedQuoteType() {
        Opportunity opportunity = emptyOpportunity();
        opportunity.setQuoteType(QuoteType.AUTOMATED);
        opportunity = opportunityRepository.saveAndFlush(opportunity);
        Opportunity actualOpportunity = opportunityRepository.findByOpportunityId(opportunity.getOpportunityId());

        assertEquals(QuoteType.AUTOMATED, actualOpportunity.getQuoteType());
    }

    @Test
    void testFindByOpportunityForEdit() {
        String xml = "<ACORD><BookTransferMeta><QuoteGuideline></QuoteGuideline></BookTransferMeta></ACORD>";
        Opportunity opportunity = new Opportunity(xml, xml);
        int bookTransferID = 12345;
        opportunity.setBookTransferID(bookTransferID);
        String nAICCd = "98765";
        opportunity.setNAICCd(nAICCd);
        opportunity.setBusinessType(LineOfBusiness.HOME.name());
        opportunity = opportunityRepository.saveAndFlush(opportunity);
        Opportunity actualOpportunity = opportunityJDBCRepo.findOpportunityForEdit(opportunity.getOpportunityId());

        assertEquals(actualOpportunity.getData(), xml);
        assertEquals(actualOpportunity.getBusinessType(), LineOfBusiness.HOME.name());
        assertEquals(actualOpportunity.getBookTransferID(), bookTransferID);
        assertEquals(actualOpportunity.getNAICCd(), nAICCd);
    }

    private static Opportunity emptyOpportunity() {
        return new Opportunity("", "");
    }
}

package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.csvbuilder;

import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeField;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class UpdateOtherFieldsResponseBuilderTest {

	@Test
	void basicFileBuildOutTest() {
		List<OppChangeFieldResult> results = new ArrayList<>();
		OppChangeFieldResult successResult = new OppChangeFieldResult(null, new OppChangeField(1, "field", "new value"),
				"original value");
		results.add(successResult);

		UpdateOtherFieldsResponseBuilder csvBuilder = new UpdateOtherFieldsResponseBuilder();
		csvBuilder.addOppChangeFieldResults(results);
		String file = csvBuilder.buildOutStatusChangeReport();
		String expectedFile = "OpportunityID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "1,field,original value,new value,YES,Success,\r\n" + "\r\n"
				+ "Quote_SalesforceID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "1,field,original value,new value,YES,Success,\r\n\r\n"
				+ "Total number of opportunities successfully updated,1,\r\n"
				+ "Total number of quote report items successfully updated,1,\r\n";
		assertThat(file).isEqualTo(expectedFile);
	}

	@Test
	void basicFileBuildOutTestWithDifferentQuoteReportItemValues() {
		List<OppChangeFieldResult> results = new ArrayList<>();
		OppChangeFieldResult successResult = new OppChangeFieldResult(null, new OppChangeField(1, "field", "new value"),
				"original value", "qriValue");
		results.add(successResult);

		UpdateOtherFieldsResponseBuilder csvBuilder = new UpdateOtherFieldsResponseBuilder();
		csvBuilder.addOppChangeFieldResults(results);
		String file = csvBuilder.buildOutStatusChangeReport();
		String expectedFile = "OpportunityID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "1,field,original value,new value,YES,Success,\r\n" + "\r\n"
				+ "Quote_SalesforceID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "1,field,qriValue,new value,YES,Success,\r\n\r\n"
				+ "Total number of opportunities successfully updated,1,\r\n"
				+ "Total number of quote report items successfully updated,1,\r\n";
		assertThat(file).isEqualTo(expectedFile);
	}

	@Test
	void testForFailedResultTest() {
		List<OppChangeFieldResult> results = new ArrayList<>();
		OppChangeFieldResult failedResult = new OppChangeFieldResult(null, new OppChangeField(1, "field", "new value"),
				"original value");
		failedResult.isError("Failed to update for xyz reason");

		results.add(failedResult);
		UpdateOtherFieldsResponseBuilder csvBuilder = new UpdateOtherFieldsResponseBuilder();
		csvBuilder.addOppChangeFieldResults(results);
		String file = csvBuilder.buildOutStatusChangeReport();
		String expectedFile = "OpportunityID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "1,field,original value,new value,NO,Failed to update for xyz reason,\r\n" + "\r\n"
				+ "Quote_SalesforceID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "1,field,original value,new value,NO,Failed to update for xyz reason,\r\n\r\n"
				+ "Total number of opportunities successfully updated,0,\r\n"
				+ "Total number of quote report items successfully updated,0,\r\n";
		assertThat(file).isEqualTo(expectedFile);
	}

	@Test
	void testForValueDidNotChangeTest() {
		List<OppChangeFieldResult> results = new ArrayList<>();
		OppChangeFieldResult valueDidNotChangeResult = new OppChangeFieldResult(null,
				new OppChangeField(1, "field", "same value"), "same value");

		results.add(valueDidNotChangeResult);
		UpdateOtherFieldsResponseBuilder csvBuilder = new UpdateOtherFieldsResponseBuilder();
		csvBuilder.addOppChangeFieldResults(results);
		String file = csvBuilder.buildOutStatusChangeReport();
		String expectedFile = "OpportunityID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "1,field,same value,same value,NO,Success,\r\n" + "\r\n"
				+ "Quote_SalesforceID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "1,field,same value,same value,NO,Success,\r\n\r\n"
				+ "Total number of opportunities successfully updated,1,\r\n"
				+ "Total number of quote report items successfully updated,1,\r\n";
		assertThat(file).isEqualTo(expectedFile);
	}
}

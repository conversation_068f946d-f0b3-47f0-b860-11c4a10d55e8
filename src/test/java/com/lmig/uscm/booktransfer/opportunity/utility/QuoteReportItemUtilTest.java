package com.lmig.uscm.booktransfer.opportunity.utility;

import com.lmig.uscm.booktransfer.opportunity.services.utility.QuoteReportItemUtil;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class QuoteReportItemUtilTest {
	@Test
	void testGettingHashMapFromListOfQuoteReportItems() {
		List<QuoteReportItemLegacy> quoteReportItems = new ArrayList<>();
		quoteReportItems.add(null);
		QuoteReportItemLegacy quoteReportItem = new QuoteReportItemLegacy();
		quoteReportItem.setQuoteSalesforceID("1");
		quoteReportItems.add(quoteReportItem);

		QuoteReportItemLegacy quoteReportItem2 = new QuoteReportItemLegacy();
		quoteReportItem2.setQuoteSalesforceID("2");
		quoteReportItems.add(quoteReportItem2);

		Map<String, QuoteReportItemLegacy> opportunityMap = QuoteReportItemUtil
				.getQuoteSalesForceIdToQuoteReportItemMap(quoteReportItems);
		assertThat(opportunityMap).containsOnlyKeys(quoteReportItem.getQuoteSalesforceID(),
				quoteReportItem2.getQuoteSalesforceID());
		assertThat(opportunityMap).containsValues(quoteReportItem, quoteReportItem2);
	}

	@Test
	void getQuoteSalesForceIdToQuoteReportItemMap() {
		List<QuoteReportItemLegacy> quoteReportItems = new ArrayList<>();
		QuoteReportItemLegacy quoteReportItem = new QuoteReportItemLegacy();
		quoteReportItem.setQuoteSalesforceID("12345");
		quoteReportItems.add(quoteReportItem);

		quoteReportItem = new QuoteReportItemLegacy();
		quoteReportItem.setQuoteSalesforceID("12346");
		quoteReportItems.add(quoteReportItem);
		// test adding null to see if it is ignored
		quoteReportItems.add(null);

		Map<String, QuoteReportItemLegacy> quoteReportItemMap = QuoteReportItemUtil
				.getQuoteSalesForceIdToQuoteReportItemMap(quoteReportItems);

		// since we put a null object in there it should be ignored
		assertEquals(2, quoteReportItemMap.size());

		String quoteSalesforceID = quoteReportItems.get(0).getQuoteSalesforceID();
		assertEquals(quoteSalesforceID, quoteReportItemMap.get(quoteSalesforceID).getQuoteSalesforceID());

		quoteSalesforceID = quoteReportItems.get(1).getQuoteSalesforceID();
		assertEquals(quoteSalesforceID, quoteReportItemMap.get(quoteSalesforceID).getQuoteSalesforceID());

	}

	@Test
	void getQuoteSalesForceIdToQuoteReportItemMapNoQuoteReportItems() {
		Map<String, QuoteReportItemLegacy> quoteReportItemMap = QuoteReportItemUtil
				.getQuoteSalesForceIdToQuoteReportItemMap(new ArrayList<>());

		assertTrue(quoteReportItemMap.isEmpty());
	}

	@Test
	void getQuoteSalesForceIdToQuoteReportItemMapNullList() {
		Map<String, QuoteReportItemLegacy> quoteReportItemMap = QuoteReportItemUtil
				.getQuoteSalesForceIdToQuoteReportItemMap(null);

		assertTrue(quoteReportItemMap.isEmpty());
	}
}

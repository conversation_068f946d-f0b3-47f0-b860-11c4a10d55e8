package com.lmig.uscm.booktransfer.opportunity.mockito.domain;


import com.lmig.uscm.booktransfer.quotereport.client.domain.ETLBookTransferDomainObject;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * This class is bc introducing spring boot 2.0 we need to change the generated value. With process result no longer being availbe to us in domain service I created a fake on here
 * This is used to put data in the db for joins.
 * Probably in the future just use sql scripts for this
 * This will need to be removed in the future.
 */
@Entity
@Table(
		name = "BookTransfer"
)
public class BookTransfer extends ETLBookTransferDomainObject {
	private static final String[] columnsNeedFromSalesForce = new String[]{"agencyAddress1", "agencyAddress2", "agencyCity", "agencyPhoneNumber", "agencyState", "agencyZip", "agentNum", "assignedQuotingUT", "autoPIF", "autoPremium", "bookTransferExpectedPLItems", "carrier", "estimatedLastEffectiveDate", "expectedIssuedPremium", "firstEffectiveDate", "lSCIndicator", "name", "nBDRelationship", "nBIssuedPremiumForecast", "otherPIF", "otherPremium", "peerlessBookTransfer", "premiumConversionRateTarget", "premiumFlatCancellationRateForecast", "pRMNnumber", "propertyPIF", "propertyPremium", "quotePhase", "region", "salesforceCode", "sFDCID", "status", "subCode", "totalCompetitorBookWPForecast"};
	@Id
	@GeneratedValue(
			strategy = GenerationType.IDENTITY
	)
	private Integer bookTransferID;
	@Column
	private String agencyAddress1;
	@Column
	private String agencyAddress2;
	@Column
	private String agencyCity;
	@Column
	private String agencyPhoneNumber;
	@Column
	private String agencyState;
	@Column
	private String agencyZip;
	@Column
	private String agentNum;
	@Column
	private String assignedQuotingUT;
	@Column
	private Integer autoPIF;
	@Column
	private Double autoPremium;
	@Column
	private Integer bookTransferExpectedPLItems;
	@Column
	private String carrier;
	@Column
	private Date estimatedLastEffectiveDate;
	@Column
	private Double expectedIssuedPremium;
	@Column
	private Date firstEffectiveDate;
	@Column
	private String lSCIndicator;
	@Column
	private String name;
	@Column
	private String nBDRelationship;
	@Column
	private Double nBIssuedPremiumForecast;
	@Column
	private String nnumber;
	@Column
	private Integer otherPIF;
	@Column
	private Double otherPremium;
	@Column
	private String peerlessBookTransfer;
	@Column
	private Double premiumConversionRateTarget;
	@Column
	private Double premiumFlatCancellationRateForecast;
	@Column
	private String pRMNnumber;
	@Column
	private Integer propertyPIF;
	@Column
	private Double propertyPremium;
	@Column
	private String quotePhase;
	@Column
	private String region;
	@Column
	private String salesforceCode;
	@Column
	private Integer sFDCID;
	@Column
	private String status;
	@Column
	private String subCode;
	@Column
	private Double totalCompetitorBookWPForecast;

	public BookTransfer() {
	}

	public BookTransfer(Integer sFDCID) {
		this.sFDCID = sFDCID;
	}

	public BookTransfer(String salesforceCode, Integer bookTransferID) {
		this.bookTransferID = bookTransferID;
		this.salesforceCode = salesforceCode;
	}

	public BookTransfer(Integer bookTransferID, String salesforceCode, String subCode, String agentNum, String status) {
		this.bookTransferID = bookTransferID;
		this.salesforceCode = salesforceCode;
		this.subCode = subCode;
		this.agentNum = agentNum;
		this.status = status;
	}

	public Integer getBookTransferID() {
		return this.bookTransferID;
	}

	public void setBookTransferID(Integer bookTransferID) {
		this.bookTransferID = bookTransferID;
	}


	public String getAgencyAddress1() {
		return this.agencyAddress1;
	}

	public void setAgencyAddress1(String agencyAddress1) {
		this.agencyAddress1 = agencyAddress1;
	}

	public String getAgencyAddress2() {
		return this.agencyAddress2;
	}

	public void setAgencyAddress2(String agencyAddress2) {
		this.agencyAddress2 = agencyAddress2;
	}

	public String getAgencyCity() {
		return this.agencyCity;
	}

	public void setAgencyCity(String agencyCity) {
		this.agencyCity = agencyCity;
	}

	public String getAgencyPhoneNumber() {
		return this.agencyPhoneNumber;
	}

	public void setAgencyPhoneNumber(String agencyPhoneNumber) {
		this.agencyPhoneNumber = agencyPhoneNumber;
	}

	public String getAgencyState() {
		return this.agencyState;
	}

	public void setAgencyState(String agencyState) {
		this.agencyState = agencyState;
	}

	public String getAgencyZip() {
		return this.agencyZip;
	}

	public void setAgencyZip(String agencyZip) {
		this.agencyZip = agencyZip;
	}

	public String getAgentNum() {
		return this.agentNum;
	}

	public void setAgentNum(String agentNum) {
		this.agentNum = agentNum;
	}

	public String getAssignedQuotingUT() {
		return this.assignedQuotingUT;
	}

	public void setAssignedQuotingUT(String assignedQuotingUT) {
		this.assignedQuotingUT = assignedQuotingUT;
	}

	public Integer getAutoPIF() {
		return this.autoPIF;
	}

	public void setAutoPIF(Integer autoPIF) {
		this.autoPIF = autoPIF;
	}

	public Double getAutoPremium() {
		return this.autoPremium;
	}

	public void setAutoPremium(Double autoPremium) {
		this.autoPremium = autoPremium;
	}

	public Integer getBookTransferExpectedPLItems() {
		return this.bookTransferExpectedPLItems;
	}

	public void setBookTransferExpectedPLItems(Integer bookTransferExpectedPLItems) {
		this.bookTransferExpectedPLItems = bookTransferExpectedPLItems;
	}

	public String getCarrier() {
		return this.carrier;
	}

	public void setCarrier(String carrier) {
		this.carrier = carrier;
	}

	public Date getEstimatedLastEffectiveDate() {
		return this.estimatedLastEffectiveDate;
	}

	public void setEstimatedLastEffectiveDate(Date estimatedLastEffectiveDate) {
		this.estimatedLastEffectiveDate = estimatedLastEffectiveDate;
	}

	public Double getExpectedIssuedPremium() {
		return this.expectedIssuedPremium;
	}

	public void setExpectedIssuedPremium(Double expectedIssuedPremium) {
		this.expectedIssuedPremium = expectedIssuedPremium;
	}

	public Date getFirstEffectiveDate() {
		return this.firstEffectiveDate;
	}

	public void setFirstEffectiveDate(Date firstEffectiveDate) {
		this.firstEffectiveDate = firstEffectiveDate;
	}

	public String getlSCIndicator() {
		return this.lSCIndicator;
	}

	public void setlSCIndicator(String lSCIndicator) {
		this.lSCIndicator = lSCIndicator;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getnBDRelationship() {
		return this.nBDRelationship;
	}

	public void setnBDRelationship(String nBDRelationship) {
		this.nBDRelationship = nBDRelationship;
	}

	public Double getnBIssuedPremiumForecast() {
		return this.nBIssuedPremiumForecast;
	}

	public void setnBIssuedPremiumForecast(Double nBIssuedPremiumForecast) {
		this.nBIssuedPremiumForecast = nBIssuedPremiumForecast;
	}

	public String getNnumber() {
		return this.nnumber;
	}

	public void setNnumber(String nnumber) {
		this.nnumber = nnumber;
	}

	public Integer getOtherPIF() {
		return this.otherPIF;
	}

	public void setOtherPIF(Integer otherPIF) {
		this.otherPIF = otherPIF;
	}

	public Double getOtherPremium() {
		return this.otherPremium;
	}

	public void setOtherPremium(Double otherPremium) {
		this.otherPremium = otherPremium;
	}

	public String getPeerlessBookTransfer() {
		return this.peerlessBookTransfer;
	}

	public void setPeerlessBookTransfer(String peerlessBookTransfer) {
		this.peerlessBookTransfer = peerlessBookTransfer;
	}

	public Double getPremiumConversionRateTarget() {
		return this.premiumConversionRateTarget;
	}

	public void setPremiumConversionRateTarget(Double premiumConversionRateTarget) {
		this.premiumConversionRateTarget = premiumConversionRateTarget;
	}

	public Double getPremiumFlatCancellationRateForecast() {
		return this.premiumFlatCancellationRateForecast;
	}

	public void setPremiumFlatCancellationRateForecast(Double premiumFlatCancellationRateForecast) {
		this.premiumFlatCancellationRateForecast = premiumFlatCancellationRateForecast;
	}

	public String getpRMNnumber() {
		return this.pRMNnumber;
	}

	public void setpRMNnumber(String pRMNnumber) {
		this.pRMNnumber = pRMNnumber;
	}

	public Integer getPropertyPIF() {
		return this.propertyPIF;
	}

	public void setPropertyPIF(Integer propertyPIF) {
		this.propertyPIF = propertyPIF;
	}

	public Double getPropertyPremium() {
		return this.propertyPremium;
	}

	public void setPropertyPremium(Double propertyPremium) {
		this.propertyPremium = propertyPremium;
	}

	public String getQuotePhase() {
		return this.quotePhase;
	}

	public void setQuotePhase(String quotePhase) {
		this.quotePhase = quotePhase;
	}

	public String getRegion() {
		return this.region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getSalesforceCode() {
		return this.salesforceCode;
	}

	public void setSalesforceCode(String salesforceCode) {
		this.salesforceCode = salesforceCode;
	}

	public Integer getsFDCID() {
		return this.sFDCID;
	}

	public void setsFDCID(Integer sFDCID) {
		this.sFDCID = sFDCID;
	}

	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getSubCode() {
		return this.subCode;
	}

	public void setSubCode(String subCode) {
		this.subCode = subCode;
	}

	public Double getTotalCompetitorBookWPForecast() {
		return this.totalCompetitorBookWPForecast;
	}

	public void setTotalCompetitorBookWPForecast(Double totalCompetitorBookWPForecast) {
		this.totalCompetitorBookWPForecast = totalCompetitorBookWPForecast;
	}

	public String getIdentifier() {
		return String.valueOf(this.getSalesforceCode());
	}

	public boolean getDeleteFlag() {
		return false;
	}

	public String[] getColumnsNeededFromSalesforce() {
		return columnsNeedFromSalesForce;
	}

	public Map<String, PreventChangeLambda> getFieldNameToPreventChangeFunction() {
		return new HashMap<>();
	}
}


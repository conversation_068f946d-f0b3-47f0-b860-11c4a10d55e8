package com.lmig.uscm.booktransfer.opportunity.repo.mappers;

import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class OpportunityForStatusChangeRowMapperTest {
    private OpportunityForStatusChangeRowMapper opportunityForStatusChangeRowMapper;

    @BeforeEach
    public void setup() {
        opportunityForStatusChangeRowMapper = new OpportunityForStatusChangeRowMapper();
    }

    @Test
    void testDefaultStatusIfNull_withNonNullArgument_shouldReturnArgument() {
        final Integer result = opportunityForStatusChangeRowMapper.defaultStatusIfNull(1);
        assertEquals(1, result);
    }

    @Test
    void testDefaultStatusIfNull_withNullArgument_shouldReturnDefaultValue() {
        final Integer result = opportunityForStatusChangeRowMapper.defaultStatusIfNull(null);
        assertEquals(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode(), result);
    }
}

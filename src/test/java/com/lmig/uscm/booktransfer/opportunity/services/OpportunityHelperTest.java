package com.lmig.uscm.booktransfer.opportunity.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.booktransfer.transformationservice.client.domain.ExecutionStatus;
import com.lmig.booktransfer.transformationservice.client.domain.TransformationResult;
import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.masking.MaskHelper;
import com.lmig.uscm.booktransfer.opportunity.Helper.OpportunityTestUtil;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityHomeErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityXmlData;
import com.lmig.uscm.booktransfer.opportunity.client.domain.PreviewDataResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.DetokenizeRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.MasterOppSPQEMeta;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.client.domain.quoteadapter.Message;
import com.lmig.uscm.booktransfer.opportunity.client.domain.quoteadapter.QuoteOrigin;
import com.lmig.uscm.booktransfer.opportunity.client.domain.quoteadapter.QuoteResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.quoteadapter.QuoteStatus;
import com.lmig.uscm.booktransfer.opportunity.client.domain.quoteadapter.RequestEnvironment;
import com.lmig.uscm.booktransfer.opportunity.client.domain.quoteadapter.Response;
import com.lmig.uscm.booktransfer.opportunity.config.JacksonConfig;
import com.lmig.uscm.booktransfer.opportunity.domain.ActionableOpportunitiesResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.PartnerResult;
import com.lmig.uscm.booktransfer.opportunity.domain.PriorCarrierData;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.EffectiveDateRange;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.OpportunitySensitiveDataException;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.rating.client.domain.QuoteAndIssueUpdate;
import com.lmig.uscm.booktransfer.spipolicydata.client.AccountDetails;
import com.lmig.uscm.booktransfer.sql.domain.PaginationRequest;
import com.lmig.uscm.booktransfer.utilityservice.DateUtils;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.BusinessAcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.PersonalAcordXPaths;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import com.lmig.usconsumermarkets.booktransfer.btpaymentservice.client.domain.BillingDetails;
import com.lmig.usconsumermarkets.booktransfer.btpaymentservice.client.domain.PaymentDataDTO;
import com.lmig.usconsumermarkets.booktransfer.btpaymentservice.client.domain.SafecoBillingDataDTO;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.domain.QGFunctionalXmlRequest;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.domain.QuotingGuidelineException;
import com.uscm.lmig.booktransfer.processresult.client.ProcessResultService;
import com.uscm.lmig.booktransfer.processresult.client.domain.LegacyQuoteDataResponse;
import com.uscm.lmig.booktransfer.processresult.client.domain.ProcessResultException;
import com.uscm.lmig.booktransfer.processresult.client.domain.ProcessResultItem;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.util.Pair;
import org.springframework.test.util.ReflectionTestUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OpportunityHelperTest {

	@InjectMocks
	@Spy
	OpportunityHelper opportunityHelper;

	@InjectMocks
	@Spy
	ReportGenerator reportGenerator;

	@Mock
	QuoteReportItemHelper quoteReportItemHelper;
	@Mock
	private ProcessResultService processResultService;
	@Mock
	QuotingAdapterService quotingAdapterService;

	@Mock
	OpportunityRepoHelper opportunityRepoHelper;

	@Mock
	BookTransferService bookTransferService;
	@Mock
	TransformationService transformationService;

	@Mock
	QuotingGuidelineHelper quotingGuidelineHelper;

	@Mock
	BTPaymentServiceHelper paymentServiceHelper;

	@Mock
	CustomerAccountHelper customerAccountHelper;

	@Mock
	SensitiveDataHelper sensitiveDataHelper;

	@Spy
	ObjectMapper objectMapper = new JacksonConfig().objectMapper();

	String packageId;
	Document transformationServiceXmlDoc;
	TransformationResult transformationResult;
	private final String VALIDATION_PACKAGE = "612d548bc5eef2601e472274";

	static final String INSURER_NAME_VALUE = "<ACORD>\n" +
			"  <PersPolicy>\n" +
			"    <OtherOrPriorPolicy>\n" +
			"      <PolicyCd>Prior</PolicyCd>\n" +
			"      <InsurerName>JOE-SHMOE</InsurerName>\n" +
			"    </OtherOrPriorPolicy>\n" +
			"  </PersPolicy>\n" +
			"</ACORD>";

	static final String INSURER_NO_NAME_VALUE = "<ACORD>\n" +
			"  <PersPolicy>\n" +
			"    <OtherOrPriorPolicy>\n" +
			"      <PolicyCd>Prior</PolicyCd>\n" +
			"      <InsurerName></InsurerName>\n" +
			"    </OtherOrPriorPolicy>\n" +
			"  </PersPolicy>\n" +
			"</ACORD>";

	static final String INSURER_NULL_NAME_VALUE = "<ACORD>\n" +
			"  <PersPolicy>\n" +
			"    <OtherOrPriorPolicy>\n" +
			"      <PolicyCd>Prior</PolicyCd>\n" +
			"    </OtherOrPriorPolicy>\n" +
			"  </PersPolicy>\n" +
			"</ACORD>";

	private static final BillingDetails DEFAULT_PAYMENT_DATA_RESP = BillingDetails.builder()
			.paymentData(PaymentDataDTO
				.builder()
				.tokenizedAccountNumber("tac")
				.instrumentId("id")
				.last4("xxxxxxx1234")
				.accountType("SV")
				.accountHolderName("FullName")
				.routingNumber("*********")
				.build())
			.build();

	List<Integer> ids ;
	List<Opportunity> opps ;
	Opportunity opportunity ;

	@BeforeEach
	public void setup() throws Exception {
		setUpMockTransformationService();
		ReflectionTestUtils.setField(opportunityHelper, "reportGenerator", reportGenerator);
		ReflectionTestUtils.setField(opportunityHelper, "VALIDATION_PACKAGE", VALIDATION_PACKAGE);
		ReflectionTestUtils.setField(opportunityHelper, "spipolicydata", "https://spi-policy-data-service");
		ids = new ArrayList<>();
		opps = new ArrayList<>();
		opportunity = new Opportunity();
		opportunity.setOpportunityId(123456);
		opportunity.setPriorCarrierGuid("Joes Crab Shack Insurance");
	}

	@Test
	void testRunRules_shouldReturnXml() throws ParserConfigurationException, IOException, SAXException, TransformerException {
		Document transformedXml = XmlHelper.getDocument("<ExpectedXml></ExpectedXml>");
		Document inputXml = XmlHelper.getDocument("<InputXml></InputXml>");
		setUpMockTransformationService();
		transformationResult.setResultDocument(transformedXml);
		transformationResult.setExecutionStatus(ExecutionStatus.SUCCESS);

		Document actualXml = transformationService.runRules(inputXml, "11", 0);

		assertEquals(XmlHelper.getDocumentString(transformedXml), XmlHelper.getDocumentString(actualXml));
	}

	@Test
	void testAddPersonName() throws Exception {
		Path path = Paths.get("src/test/resources/xml/test.xml");
		byte[] content = Files.readAllBytes(path);

		String acordXml = new String(content);

		Document doc = XmlHelper.getDocument(acordXml);
		doc = AcordHelper.defaultInsuredOrPrincipalPersonName(doc);
		AcordHelper.addCommercialName(doc);

		XPath xFactory = XPathFactory.newInstance().newXPath();
		Element givenName =
				(Element) xFactory.evaluate("//InsuredOrPrincipal/GeneralPartyInfo/NameInfo/PersonName/GivenName", doc,
						XPathConstants.NODE);
		Element surname =
				(Element) xFactory.evaluate("//InsuredOrPrincipal/GeneralPartyInfo/NameInfo/PersonName/Surname", doc,
						XPathConstants.NODE);
		assertEquals("Fierro", givenName.getTextContent(), "givenName not set correctly");
		assertEquals("Guadalupe", surname.getTextContent(), "surname not set correctly");

		Element commercialName =
				(Element) xFactory.evaluate("//InsuredOrPrincipal/GeneralPartyInfo/NameInfo/CommlName/CommercialName",
						doc, XPathConstants.NODE);
		commercialName.setTextContent("");
		givenName.setTextContent("");
		surname.setTextContent("");

		doc = AcordHelper.defaultInsuredOrPrincipalPersonName(doc);
		AcordHelper.addCommercialName(doc);
		givenName = (Element) xFactory.evaluate("//InsuredOrPrincipal/GeneralPartyInfo/NameInfo/PersonName/GivenName",
				doc, XPathConstants.NODE);
		surname = (Element) xFactory.evaluate("//InsuredOrPrincipal/GeneralPartyInfo/NameInfo/PersonName/Surname", doc,
				XPathConstants.NODE);
		assertEquals("Insured", givenName.getTextContent(), "givenName not set correctly");
		assertEquals("Unknown", surname.getTextContent(), "surname not set correctly");
	}

	@Test
	void testAddPersDriverName() throws Exception {
		Path path = Paths.get("src/test/resources/xml/auto2.xml");
		byte[] content = Files.readAllBytes(path);

		String acordXml = new String(content);
		Document doc = XmlHelper.getDocument(acordXml);
		doc = AcordHelper.defaultInsuredOrPrincipalPersonName(doc);
		AcordHelper.addCommercialName(doc);
		doc = AcordHelper.defaultPersDriverPersonNames(doc);
		AcordHelper.fixupDriverIDs(doc);
		XPath xFactory = XPathFactory.newInstance().newXPath();

		NodeList persDrivers = (NodeList) xFactory.evaluate("//PersDriver", doc, XPathConstants.NODESET);
		assertEquals(4, persDrivers.getLength());

		Node persDriver = persDrivers.item(0);
		Node givenName = (Node) xFactory.evaluate("GeneralPartyInfo/NameInfo/PersonName/GivenName", persDriver,
				XPathConstants.NODE);
		Node surname = (Node) xFactory.evaluate("GeneralPartyInfo/NameInfo/PersonName//Surname", persDriver,
				XPathConstants.NODE);
		assertEquals("Insured", givenName.getTextContent(), "givenName not set correctly");
		assertEquals("Unknown", surname.getTextContent(), "surname not set correctly");

		persDriver = persDrivers.item(1);
		givenName = (Node) xFactory.evaluate("GeneralPartyInfo/NameInfo/PersonName/GivenName", persDriver,
				XPathConstants.NODE);
		surname = (Node) xFactory.evaluate("GeneralPartyInfo/NameInfo/PersonName//Surname", persDriver,
				XPathConstants.NODE);
		assertEquals("Bugs", givenName.getTextContent(), "givenName not set correctly");
		assertEquals("Bunny", surname.getTextContent(), "surname not set correctly");

		persDriver = persDrivers.item(2);
		givenName = (Node) xFactory.evaluate("GeneralPartyInfo/NameInfo/PersonName/GivenName", persDriver,
				XPathConstants.NODE);
		surname = (Node) xFactory.evaluate("GeneralPartyInfo/NameInfo/PersonName//Surname", persDriver,
				XPathConstants.NODE);
		assertEquals("Lee", givenName.getTextContent(), "givenName not set correctly");
		assertEquals("Pusateri", surname.getTextContent(), "surname not set correctly");

		persDriver = persDrivers.item(3);
		givenName = (Node) xFactory.evaluate("GeneralPartyInfo/NameInfo/PersonName/GivenName", persDriver,
				XPathConstants.NODE);
		surname = (Node) xFactory.evaluate("GeneralPartyInfo/NameInfo/PersonName//Surname", persDriver,
				XPathConstants.NODE);
		assertEquals("Dorene", givenName.getTextContent(), "givenName not set correctly");
		assertEquals("name", surname.getTextContent(), "surname not set correctly");

	}

	@Test
	void testAddCommercialName() throws Exception {
		Path path = Paths.get("src/test/resources/xml/Home_InsuredOrPrincipalMissingCommercialName.xml");
		byte[] content = Files.readAllBytes(path);

		String acordXml = new String(content);

		Document doc = XmlHelper.getDocument(acordXml);
		doc = AcordHelper.defaultInsuredOrPrincipalPersonName(doc);
		AcordHelper.addCommercialName(doc);
		doc = AcordHelper.defaultPersDriverPersonNames(doc);
		AcordHelper.fixupDriverIDs(doc);
		XPath xFactory = XPathFactory.newInstance().newXPath();

		NodeList insuredsOrPrincipals =
				(NodeList) xFactory.evaluate("//InsuredOrPrincipal", doc, XPathConstants.NODESET);
		assertEquals(3, insuredsOrPrincipals.getLength());

		Node insured = insuredsOrPrincipals.item(0);
		Node commercialName = (Node) xFactory.evaluate("GeneralPartyInfo/NameInfo/CommlName/CommercialName", insured,
				XPathConstants.NODE);
		assertEquals("RAYMONT EDWARDS", commercialName.getTextContent(), "commercialName not set correctly");
	}

	@Test
	void testAddCommercialNameNameMissing() throws Exception {
		Path path = Paths.get("src/test/resources/xml/commercialNameNamesMissing.xml");
		byte[] content = Files.readAllBytes(path);

		String acordXml = new String(content);
		Document doc = XmlHelper.getDocument(acordXml);
		doc = AcordHelper.defaultInsuredOrPrincipalPersonName(doc);
		AcordHelper.addCommercialName(doc);
		doc = AcordHelper.defaultPersDriverPersonNames(doc);
		AcordHelper.fixupDriverIDs(doc);

		XPath xFactory = XPathFactory.newInstance().newXPath();

		NodeList insuredsOrPrincipals =
				(NodeList) xFactory.evaluate("//InsuredOrPrincipal", doc, XPathConstants.NODESET);
		assertEquals(2, insuredsOrPrincipals.getLength());

		Node insured = insuredsOrPrincipals.item(0);
		Node commercialName = (Node) xFactory.evaluate("GeneralPartyInfo/NameInfo/CommlName/CommercialName", insured,
				XPathConstants.NODE);
		assertEquals("Insured Unknown", commercialName.getTextContent(), "commercialName not set correctly");
	}

	@Test
	void testAddFirePlaces() throws Exception {
		Path path = Paths.get("src/test/resources/xml/FirePlaces.xml");
		byte[] content = Files.readAllBytes(path);

		String acordXml = new String(content);
		Document doc = XmlHelper.getDocument(acordXml);
		AcordHelper.defaultFirePlaces(doc);
		XPath xFactory = XPathFactory.newInstance().newXPath();
		NodeList firePlaces = (NodeList) xFactory.evaluate(
				"/ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq/HomeLineBusiness/Dwell/DwellInspectionValuation/FireplaceInfo",
				doc, XPathConstants.NODESET);
		assertTrue(firePlaces.getLength() >= 0);
		for (int i = 0; i < firePlaces.getLength(); i++) {
			Node firePlaceInfoNode = firePlaces.item(i);
			Node numFireplaces = (Node) xFactory.evaluate("NumFireplaces", firePlaceInfoNode, XPathConstants.NODE);
			Node numChimneys = (Node) xFactory.evaluate("NumChimneys", firePlaceInfoNode, XPathConstants.NODE);
			Node firePlaceTypeCd = (Node) xFactory.evaluate("FirePlaceTypeCd", firePlaceInfoNode, XPathConstants.NODE);
			assertFalse(numFireplaces.getTextContent().isEmpty());
			assertFalse(numChimneys.getTextContent().isEmpty());
			assertFalse(firePlaceTypeCd.getTextContent().isEmpty());
		}
	}

	/**
	 * This test case is testing what happens if coinsured is first in the xml and making sure that we still get the
	 * correct Name from the insured tag
	 */
	@Test
	void testUpdateOppWithCorrectInsuredOrPrincipalWithCoInsuredFirst() throws Exception {
		Path path = Paths.get("src/test/resources/xml/spqe-xml-with-coinsured-before-insured.xml");

		byte[] content = Files.readAllBytes(path);
		Opportunity opportunity = new Opportunity();
		String acordXml = new String(content);
		opportunity.setData(acordXml);
		opportunity = opportunityHelper.updateOpportunity(opportunity);
		assertEquals("Krieger", opportunity.getCustomerName());
	}

	/**
	 * This test case is testing what happens if insured is first in the xml we will still get that tag
	 */
	@Test
	void testUpdateOppWithCorrectInsuredOrPrincipalWithJustInsured() throws Exception {
		Path path = Paths.get("src/test/resources/xml/boat.xml");

		byte[] content = Files.readAllBytes(path);
		Opportunity opportunity = new Opportunity();
		String acordXml = new String(content);
		opportunity.setData(acordXml);
		opportunity = opportunityHelper.updateOpportunity(opportunity);
		assertEquals("Ralph Cesnick", opportunity.getCustomerName());
	}

	/**
	 * This test case is testing what happens if insured does not exist to make sure it reverts to what it was checking
	 * before
	 */
	@Test
	void testUpdateOppWithNoInsuredTag() throws Exception {
		Path path = Paths.get("src/test/resources/xml/boat.xml");

		byte[] content = Files.readAllBytes(path);
		String acordXml = new String(content);
		Document doc = XmlHelper.getDocument(acordXml);
		doc = XmlHelper.deleteNode(doc, "//InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd");
		assertEquals(0, XmlHelper.getNodeList(doc, "//InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd").getLength());
		Opportunity opportunity = new Opportunity();
		opportunity.setData(acordXml);
		opportunity = opportunityHelper.updateOpportunity(opportunity);
		assertEquals("Ralph Cesnick", opportunity.getCustomerName());
	}

	@Test
	void testInitializeArgument() {
		String testContent = "this is original";

		opportunityHelper.initilizeArgument(null);

		opportunityHelper.initilizeArgument(testContent);
		assertEquals("this is original", testContent);
	}

	@ParameterizedTest
	@ValueSource(strings = {"src/test/resources/xml/boatWithAmt.xml",
			"src/test/resources/xml/home.xml",
			"src/test/resources/xml/missingUmbrellaLob.xml",
			"src/test/resources/xml/effdate-dfire-03-05-2017.xml",
			"src/test/resources/xml/Jenny_Auto2.xml"})
	void testValidateMissingData(String path) throws Exception {
		String acordXml = OpportunityTestUtil.generateStringData(Paths.get(path));
		Opportunity opp = OpportunityTestUtil.createDummyOpportunity(11, acordXml, 1, "x");
		transformationResult.setExecutionStatus(ExecutionStatus.SUCCESS);
		transformationResult.setResultDocument(XmlHelper.getDocument(acordXml));

		opportunityHelper.validateMissingData(opp);

		assertNotNull(opp);
		assertNotEquals(opp.getData(), acordXml);
		verify(transformationService, times(1)).executePackage(transformationServiceXmlDoc, VALIDATION_PACKAGE, 0);
	}

	@Test
	void testValidateMissingDataForWorkersComp() throws Exception {
		String acordXml = OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/converterV2_BL_WORK.xml"));
		Opportunity opp = OpportunityTestUtil.createDummyOpportunity(11, acordXml, 1, "x");
		opp.setLineType(LineType.Business);
		transformationResult.setExecutionStatus(ExecutionStatus.SUCCESS);
		transformationResult.setResultDocument(XmlHelper.getDocument(acordXml));

		opportunityHelper.validateMissingData(opp);

		assertNotNull(opp);
		assertEquals(opp.getData(), acordXml);
		verify(transformationService, times(1)).executePackage(transformationServiceXmlDoc, VALIDATION_PACKAGE, 0);
	}

	@Test
	void testBuildParametersUpdateIssued() {

		int statusAsIssued = 10;
		String oppId = "2";

		Map<String, Object> response = opportunityHelper.buildParametersUpdateIssued(statusAsIssued, oppId);

		assertEquals(response.get("OppStatus"), statusAsIssued);
		assertEquals(response.get("OppID"), oppId);
	}

	@Test
	void testBuildParametersPolicyGuid() {

		String policyGuid = "D52C22A9-DD59-44E7-A4F6-1B2A286AD7D2";

		Map<String, Object> response = opportunityHelper.buildParametersPolicyGuid(policyGuid);
		assertEquals(response.get("PolicyGUID"), policyGuid);
	}

	/**
	 * This makes sure that correct plumbing is in place when calling saveUpdated Opp: That we actually update the given
	 * Opportunity That we update the quoteReportItem with the new Opportunity info
	 */
	@Test
	void testSavingUpdatedOpportunity() throws Exception {
		Opportunity opportunity = new Opportunity();
		doAnswer(invocation -> invocation.getArgument(0)).when(opportunityRepoHelper)
				.save(any(Opportunity.class));
		opportunityHelper.saveUpdatedOpportunity(opportunity);

		then(opportunityRepoHelper).should().save(eq(opportunity));

		then(quoteReportItemHelper).should().updateQuoteReportItem(eq(opportunity));

	}

	/**
	 * Test updating opportunity with the qni data
	 */
	@Test
	void testQnIUpdateOpp() throws Exception {
		String acordXml = OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml"));
		Opportunity opp = OpportunityTestUtil.createDummyOpportunity(11, acordXml, 1, "x");
		Mockito.when(opportunityRepoHelper.getDataXmlStringByLastPolicyGuid(anyString())).thenReturn(acordXml);
		Mockito.when(opportunityRepoHelper.updateOpportunityWithQnIData(anyString(), any(QuoteAndIssueUpdate.class))).thenReturn(opp);
		QuoteAndIssueUpdate qniData = new QuoteAndIssueUpdate(10, new Date(), "firstname", "lastname", "WA", "1", 357.0,
				10.00, null, "D52C22A9-DD59-44E7-A4F6-1B2A286AD7D2", "A111111", "1", "*********0", true, true);
		opportunityHelper.updateOppWithQnIData(qniData);
		then(opportunityHelper).should().updateDataXmlWithQnIData(eq(qniData));
		then(opportunityRepoHelper).should().updateOpportunityWithQnIData(anyString(), eq(qniData));
	}

	/**
	 * Test the opportunity data xml has been updated with qni data
	 */
	@Test
	void testUpdateOppXmlWithQnIData() throws Exception {
		String acordXml = OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml"));
		Mockito.when(opportunityRepoHelper.getDataXmlStringByLastPolicyGuid(anyString())).thenReturn(acordXml);
		Date expectedEffectiveDate = new Date();
		String firstName = "firstname";
		String lastName = "lastname";
		String expectedCustomerName = firstName + " " + lastName;
		String expectedPolicyGuid = "D52C22A9-DD59-44E7-A4F6-1B2A286AD7D2";
		QuoteAndIssueUpdate qniData = new QuoteAndIssueUpdate(10, expectedEffectiveDate, firstName, lastName, "WA", "1",
				357.0, 10.00, null, expectedPolicyGuid, "A111111", "1", "*********0", true, true);
		Document dataXml = opportunityHelper.updateDataXmlWithQnIData(qniData);

		assertEquals(AcordHelper.getEffectiveDt(dataXml), DateUtils.getSQLDateString(expectedEffectiveDate));
		assertEquals(AcordHelper.getInsuredCommercialName(dataXml), expectedCustomerName);
		assertEquals(AcordHelper.getInsuredGivenName(dataXml), firstName);
		assertEquals(AcordHelper.getInsuredSurname(dataXml), lastName);
		assertEquals(AcordHelper.getPolicyGuid(dataXml), expectedPolicyGuid);
	}

	/**
	 * Given that I have a request to get my custom filterdata with no oppids given When I make that request Then it
	 * should throw an error
	 */
	@Test
	void testGetCustomFilterDataWithInvalidRequest() {
		// given
		CustomFilterRequest customFilterRequest = new CustomFilterRequest();
		PaginationRequest paginationRequest = new PaginationRequest();
		customFilterRequest.setPaginationRequest(paginationRequest);
		paginationRequest.setPageNumber(5);
		paginationRequest.setSize(10);

		assertThrows(IllegalArgumentException.class, () -> opportunityHelper.getCustomFilterResponse(customFilterRequest));
	}

	/**
	 * Given I have an oppId, and it is present in DB When I build brush map Then I should get header as wll as values
	 */
	@Test
	void testBuildOutCSVForBrushMapData() throws Exception {
		int oppId = 12345;
		Opportunity opp = OpportunityTestUtil.createDummyOpportunity(4,
				OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml")), 2,
				OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml")));
		opp.setOpportunityId(oppId);
		opp.setBusinessType("AUTOP");
		opp.setCustomerName("safeco customer");
		List<Opportunity> listOpportunities = new ArrayList<>();
		listOpportunities.add(opp);
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(oppId);
		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());
		String actualBrushMapData = opportunityHelper.buildOutBrushMapData(oppIds);
		String expectedBrushMap = "BookTransfer Brush Map Data\r\n"
				+ "LOB,Client Name,Location Address,City,State,Zip,Roof Material,OppId,Policy Type\r\n"
				+ "AUTOP,safeco customer,22045 SW COLE CT,TUALATIN,OR,97062-7038,,12345,\r\n";

		assertEquals(expectedBrushMap, actualBrushMapData);
	}

	/**
	 * Given I have an oppId, and it is not present in DB When I build brush map Then I should get only header part
	 */
	@Test
	void testBuildOutCSVForBrushMapDataWithHeaderOnly() {
		int oppId = 12345;

		List<Opportunity> listOpportunities = new ArrayList<>();

		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(oppId);
		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());
		String actualBrushMapData = opportunityHelper.buildOutBrushMapData(oppIds);
		String expectedBrushMap = "BookTransfer Brush Map Data\r\n"
				+ "LOB,Client Name,Location Address,City,State,Zip,Roof Material,OppId,Policy Type\r\n";

		assertEquals(expectedBrushMap, actualBrushMapData);
	}

	/**
	 * Given I have an oppId for condo And it is present in DB When I build brush map Then I should get header as well
	 * as values
	 */
	@Test
	void testBuildOutCSVForBrushMapDataForCondoPolicyType() throws Exception {
		int oppId = 12345;
		Opportunity opp = OpportunityTestUtil.createDummyOpportunity(4,
				OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobCondo.xml")), 2,
				OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobCondo.xml")));
		opp.setOpportunityId(oppId);
		opp.setBusinessType("HOME");
		opp.setCustomerName("safeco customer");
		List<Opportunity> listOpportunities = new ArrayList<>();
		listOpportunities.add(opp);
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(oppId);
		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());
		String actualBrushMapData = opportunityHelper.buildOutBrushMapData(oppIds);
		String expectedBrushMap = "BookTransfer Brush Map Data\r\n"
				+ "LOB,Client Name,Location Address,City,State,Zip,Roof Material,OppId,Policy Type\r\n"
				+ "HOME,safeco customer,45 HANCOCK ST,BOSTON,MA,02114,,12345,06\r\n";

		assertEquals(expectedBrushMap, actualBrushMapData);
	}

	@Test
	void testBuildOutCSVForBrushMapData_BL() throws Exception {
		int oppId = 12345;
		String dataXml = XmlHelper.getFileContentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");
		Opportunity opp = OpportunityTestUtil.createDummyOpportunity(4, dataXml, 12345, dataXml, LineType.Business);
		opp.setOpportunityId(oppId);
		opp.setBusinessType("WORK");
		opp.setCustomerName("safeco customer");
		List<Opportunity> listOpportunities = new ArrayList<>();
		listOpportunities.add(opp);
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(oppId);
		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());

		String actualBrushMapData = opportunityHelper.buildOutBrushMapData(oppIds);
		String expectedBrushMap = "BookTransfer Brush Map Data\r\n"
				+ "LOB,Client Name,Location Address,City,State,Zip,Roof Material,OppId,Policy Type\r\n"
				+ "WORK,safeco customer,\"33 Newtown Lane, Suite #205 Building #1 East Hampton\",Suffolk,NY,11937,,12345,\r\n";

		assertEquals(expectedBrushMap, actualBrushMapData);
	}

	@Test
	void testBuildOutCSVForBrushMapData_MixedPLAndBL() throws Exception {
		String dataXml = XmlHelper.getFileContentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");
		Opportunity blOpp = OpportunityTestUtil.createDummyOpportunity(4, dataXml, 12345, dataXml, LineType.Business);
		blOpp.setOpportunityId(12345);
		blOpp.setBusinessType("WORK");
		blOpp.setCustomerName("safeco customer");
		Opportunity plOpp = OpportunityTestUtil.createDummyOpportunity(4,
				OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml")), 2,
				OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml")));
		plOpp.setOpportunityId(23456);
		plOpp.setBusinessType("AUTOP");
		plOpp.setCustomerName("safeco customer");

		List<Opportunity> listOpportunities = new ArrayList<>();
		listOpportunities.add(blOpp);
		listOpportunities.add(plOpp);
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(12345);
		oppIds.add(23456);
		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());

		String actualBrushMapData = opportunityHelper.buildOutBrushMapData(oppIds);
		String expectedBrushMap = "BookTransfer Brush Map Data\r\n"
				+ "LOB,Client Name,Location Address,City,State,Zip,Roof Material,OppId,Policy Type\r\n"
				+ "WORK,safeco customer,\"33 Newtown Lane, Suite #205 Building #1 East Hampton\",Suffolk,NY,11937,,12345,\r\n"
				+ "AUTOP,safeco customer,22045 SW COLE CT,TUALATIN,OR,97062-7038,,23456,\r\n";

		assertEquals(expectedBrushMap, actualBrushMapData);
	}

	@Test
	void testBuildCustomerListCSV_nullData_throwsNullException() {
		int oppId = 123456;
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(oppId);
		Mockito.doReturn(null).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());

		assertThrows(NullPointerException.class, () -> opportunityHelper.buildCustomerListCSVAsString(oppIds));
	}

	@Test
	void testBuildCustomerListCSV_emptyData_emptyCSVWithHeaders() {
		Mockito.doReturn(new ArrayList<>()).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());
		String actualCustomerList = opportunityHelper.buildCustomerListCSVAsString(new ArrayList<>());
		String expectedCustomerList = "Prior Policy #,Customer Name,Customer First Name,Customer Last Name,Effective Date,Expiration Date,Contract Term,LOB,Address,City,State,Zip Code,Rating State,Prior Policy Premium,NAIC,AgencyID,Opp ID,Policy Status ID,Policy Status\r\n";
		assertEquals(expectedCustomerList, actualCustomerList);
	}

	@Test
	void testBuildCustomerListCSV_missingData_fillsCSVWithEmpties() throws Exception {
		int oppId = 12345;
		Opportunity opp = OpportunityTestUtil.createDummyOpportunity(4,
				OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml")), 2,
				OpportunityTestUtil.generateStringData(Paths.get("src/test/resources/xml/lobAuto.xml")));
		opp.setOpportunityId(oppId);
		opp.setBusinessType("AUTOP");
		opp.setEffectiveDate("2017-05-29");
		List<Opportunity> listOpportunities = new ArrayList<>();
		listOpportunities.add(opp);
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(oppId);
		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());
		String actualCustomerList = opportunityHelper.buildCustomerListCSVAsString(oppIds);
		String expectedCustomerList = "Prior Policy #,Customer Name,Customer First Name,Customer Last Name,Effective Date,Expiration Date,Contract Term,LOB,Address,City,State,Zip Code,Rating State,Prior Policy Premium,NAIC,AgencyID,Opp ID,Policy Status ID,Policy Status\r\n" +
				",,Insured,Unknown,2017-05-29,,12,AUTOP,22045 SW COLE CT,TUALATIN,OR,97062-7038,,,,,12345,4,Quoted - Success\r\n";
		assertEquals(expectedCustomerList, actualCustomerList);
	}

	@Test
	void testBuildCustomerListCSV_multipleOpps_fillsCSV() throws Exception {
		int opp1Id = 12345;
		int opp2Id = 54321;
		String dateOpp2Earlier = DateUtils.getDateXDaysInTheFuture(1);
		String dateOpp1Later = DateUtils.getDateXDaysInTheFuture(5);

		Document doc1 = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/lobAuto.xml");
		AcordHelper.setExpirationDt(doc1, dateOpp1Later);
		String opp1Xml = XmlHelper.getDocumentString(doc1);

		Document doc2 = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/home.xml");
		AcordHelper.setExpirationDt(doc2, dateOpp2Earlier);
		String opp2Xml = XmlHelper.getDocumentString(doc2);

		Opportunity opp1 = OpportunityTestUtil.createDummyOpportunity(4, opp1Xml, 2, opp1Xml);
		Opportunity opp2 = OpportunityTestUtil.createDummyOpportunity(6, opp2Xml, 2, opp2Xml);
		opp1.setOpportunityId(opp1Id);
		opp1.setBusinessType("AUTOP");
		opp1.setEffectiveDate("2017-05-29");
		opp2.setOpportunityId(opp2Id);
		opp2.setBusinessType("HOME");
		List<Opportunity> listOpportunities = new ArrayList<>();
		listOpportunities.add(opp1);
		listOpportunities.add(opp2);
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(opp1Id);
		oppIds.add(opp2Id);
		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());
		String actualCustomerList = opportunityHelper.buildCustomerListCSVAsString(oppIds);
		String expectedCustomerList = "Prior Policy #,Customer Name,Customer First Name,Customer Last Name,Effective Date,Expiration Date,Contract Term,LOB,Address,City,State,Zip Code,Rating State,Prior Policy Premium,NAIC,AgencyID,Opp ID,Policy Status ID,Policy Status\r\n" +
				",,,,," + dateOpp2Earlier + ",12,HOME,1875 NW HARTFORD AVE,BEND,OR,977032484,,,,,54321,6,Quoted - Error\r\n" +
				",,Insured,Unknown,2017-05-29," + dateOpp1Later + ",12,AUTOP,22045 SW COLE CT,TUALATIN,OR,97062-7038,,,,,12345,4,Quoted - Success\r\n";

		assertEquals(expectedCustomerList, actualCustomerList);
	}

	@Test
	void testBuildCustomerListCSV_multipleOppsPLAndBL_fillsCSV() throws Exception {
		int opp1Id = 12345;
		int opp2Id = 54321;
		int opp3Id = 67890;
		String dateOpp2Earlier = DateUtils.getDateXDaysInTheFuture(1);
		String dateOpp1Later = DateUtils.getDateXDaysInTheFuture(5);

		Document doc1 = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/lobAuto.xml");
		AcordHelper.setExpirationDt(doc1, dateOpp1Later);
		String opp1Xml = XmlHelper.getDocumentString(doc1);

		Document doc2 = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");
		BusinessAcordHelper.setExpirationDt(doc2, dateOpp2Earlier);
		String opp2Xml = XmlHelper.getDocumentString(doc2);

		Document doc3 = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/blCommlPackage_vertafore.xml");
		String opp3Xml = XmlHelper.getDocumentString(doc3);

		Opportunity plOpp = OpportunityTestUtil.createDummyOpportunity(4, opp1Xml, 2, opp1Xml);
		Opportunity blOpp = OpportunityTestUtil.createDummyOpportunity(6, opp2Xml, 2, opp2Xml);
		Opportunity blOpp2 = OpportunityTestUtil.createDummyOpportunity(6, opp3Xml, 2, opp3Xml);
		plOpp.setOpportunityId(opp1Id);
		plOpp.setBusinessType("AUTOP");
		plOpp.setEffectiveDate("2017-05-29");

		blOpp.setOpportunityId(opp2Id);
		blOpp.setBusinessType("WORK");
		blOpp.setLineType(LineType.Business);

		blOpp2.setOpportunityId(opp3Id);
		blOpp2.setBusinessType("CPKGE");
		blOpp2.setLineType(LineType.Business);

		List<Opportunity> listOpportunities = new ArrayList<>();
		listOpportunities.add(plOpp);
		listOpportunities.add(blOpp);
		listOpportunities.add(blOpp2);
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(opp1Id);
		oppIds.add(opp2Id);
		oppIds.add(opp3Id);
		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());
		String actualCustomerList = opportunityHelper.buildCustomerListCSVAsString(oppIds);
		String expectedCustomerList = "Prior Policy #,Customer Name,Customer First Name,Customer Last Name,Effective Date,Expiration Date,Contract Term,LOB,Package Sub LOBS,Operations Description,Address,City,State,Zip Code,Rating State,Prior Policy Premium,NAIC,AgencyID,Opp ID,Policy Status ID,Policy Status\r\n" +
		",,Insured,Unknown,2017-05-29," + dateOpp1Later + ",12,AUTOP,,,22045 SW COLE CT,TUALATIN,OR,97062-7038,,,,,12345,4,Quoted - Success\r\n" +
				",,East,East,,,12,WORK,,MANUFACTURER/PLASTIC,10 County Road 27,Hampton,NY,11937,,,,,54321,6,Quoted - Error\r\n" +
				",,,,,,12,CPKGE,\"PROPC, CGL\",MANUFACTURER/PLASTIC,123 Main Street,Louisville,KY,402610273,,,,,67890,6,Quoted - Error\r\n";
		assertEquals(expectedCustomerList, actualCustomerList);
	}

	@Test
	void testBuildCustomerListCSV_nullSortValue_noExceptions() throws Exception {
		String effectiveDate = "2017-05-29";
		Document doc1 = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/lobAuto.xml");
		String opp1LaterDate = DateUtils.getDateXDaysInTheFuture(5);
		AcordHelper.setExpirationDt(doc1, opp1LaterDate);
		String doc1String = XmlHelper.getDocumentString(doc1);
		Opportunity opp1 = OpportunityTestUtil.createDummyOpportunity(4, doc1String, 2, doc1String);

		AcordHelper.setExpirationDt(doc1, "");
		String doc2String = XmlHelper.getDocumentString(doc1);
		Opportunity opp2NullDate = OpportunityTestUtil.createDummyOpportunity(4, doc2String, 2, doc2String);

		AcordHelper.setExpirationDt(doc1, null);
		String doc3String = XmlHelper.getDocumentString(doc1);
		Opportunity opp3NullDate = OpportunityTestUtil.createDummyOpportunity(4, doc3String, 2, doc3String);

		String opp4EarliestDate = DateUtils.getDateXDaysInTheFuture(1);
		AcordHelper.setExpirationDt(doc1, opp4EarliestDate);
		String doc4String = XmlHelper.getDocumentString(doc1);
		Opportunity opp4 = OpportunityTestUtil.createDummyOpportunity(4, doc4String, 2, doc4String);

		AcordHelper.setExpirationDt(doc1, "05/01/19");
		String doc5String = XmlHelper.getDocumentString(doc1);
		Opportunity opp5NullDate = OpportunityTestUtil.createDummyOpportunity(4, doc5String, 2, doc2String);

		opp1.setOpportunityId(12345);
		opp1.setBusinessType("AUTOP");
		opp1.setEffectiveDate(effectiveDate);
		opp2NullDate.setOpportunityId(23456);
		opp2NullDate.setBusinessType("AUTOP");
		opp2NullDate.setEffectiveDate(effectiveDate);
		opp3NullDate.setOpportunityId(34567);
		opp3NullDate.setBusinessType("AUTOP");
		opp3NullDate.setEffectiveDate(effectiveDate);
		opp4.setOpportunityId(45678);
		opp4.setBusinessType("AUTOP");
		opp4.setEffectiveDate(effectiveDate);
		opp5NullDate.setOpportunityId(56789);
		opp5NullDate.setBusinessType("AUTOP");
		opp5NullDate.setEffectiveDate(effectiveDate);
		List<Opportunity> listOpportunities = new ArrayList<>();
		listOpportunities.add(opp1);
		listOpportunities.add(opp2NullDate);
		listOpportunities.add(opp3NullDate);
		listOpportunities.add(opp4);
		listOpportunities.add(opp5NullDate);
		List<Integer> oppIds = Arrays.asList(12345, 23456, 34567, 45678, 56789);

		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());
		String actualCustomerList = opportunityHelper.buildCustomerListCSVAsString(oppIds);

		String expectedCustomerList = "Prior Policy #,Customer Name,Customer First Name,Customer Last Name,Effective Date,Expiration Date,Contract Term,LOB,Address,City,State,Zip Code,Rating State,Prior Policy Premium,NAIC,AgencyID,Opp ID,Policy Status ID,Policy Status\r\n" +
				",,Insured,Unknown," + effectiveDate + "," + opp4EarliestDate + ",12,AUTOP,22045 SW COLE CT,TUALATIN,OR,97062-7038,,,,,45678,4,Quoted - Success\r\n" +
				",,Insured,Unknown," + effectiveDate + "," + opp1LaterDate + ",12,AUTOP,22045 SW COLE CT,TUALATIN,OR,97062-7038,,,,,12345,4,Quoted - Success\r\n" +
				",,Insured,Unknown," + effectiveDate + ",,12,AUTOP,22045 SW COLE CT,TUALATIN,OR,97062-7038,,,,,23456,4,Quoted - Success\r\n" +
				",,Insured,Unknown," + effectiveDate + ",,12,AUTOP,22045 SW COLE CT,TUALATIN,OR,97062-7038,,,,,34567,4,Quoted - Success\r\n" +
				",,Insured,Unknown," + effectiveDate + ",,,AUTOP,22045 SW COLE CT,TUALATIN,OR,97062-7038,,,,,56789,4,Quoted - Success\r\n";

		assertEquals(expectedCustomerList, actualCustomerList);
	}

	@Test
	void testBuildCustomerListCSV_MixedExpirationDateValues() throws Exception {
		String effectiveDate = "2017-05-29";
		Document doc1 = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/lobAuto.xml");
		String doc1String = XmlHelper.getDocumentString(doc1);
		List<Opportunity> listOpportunities = new ArrayList<>();
		List<Integer> oppIds = new ArrayList<>();
		String opp1LaterDate = DateUtils.getDateXDaysInTheFuture(5);

		int oppCounter = 32; // IDK why 32 is the min number to recreate the exception
		//https://bugs.openjdk.java.net/browse/JDK-8134576
		for(int i = 1; i <= oppCounter; i++) {
			String docString = doc1String;
			// Set mixed expiration dates
			if(i % 10 == 0) {
				AcordHelper.setExpirationDt(doc1, opp1LaterDate);
				docString = XmlHelper.getDocumentString(doc1);
			} else if(i % 7 == 0) {
				AcordHelper.setExpirationDt(doc1, "");
				docString = XmlHelper.getDocumentString(doc1);
			}

			Opportunity opp = OpportunityTestUtil.createDummyOpportunity(4, docString, 2, docString);
			opp.setOpportunityId(i);
			opp.setBusinessType("AUTOP");
			opp.setEffectiveDate(effectiveDate);

			listOpportunities.add(opp);
			oppIds.add(i);
		}

		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findByOpportunityIdIn(anyList());

		assertDoesNotThrow(() -> opportunityHelper.buildCustomerListCSVAsString(oppIds));
	}

	@Test
	void testGetEffectiveDateRange() {
		EffectiveDateRange expectedDateRange = new EffectiveDateRange();
		expectedDateRange.setMinEffectiveDate("2019-01-01");
		expectedDateRange.setMaxEffectiveDate("2019-01-01");
		Mockito.doReturn(expectedDateRange).when(opportunityRepoHelper)
				.getCustomFilterOpportunitiesEffectiveDateRange(anyList());
		EffectiveDateRange actualDateRange = opportunityRepoHelper.getCustomFilterOpportunitiesEffectiveDateRange(new ArrayList<>());

		assertEquals(expectedDateRange, actualDateRange);
	}

	/**
	 * Given I have a list of oppoids Then get the booktransfer ids associated to the oppids
	 */
	@Test
	void testGetBookIdsForOppIds() {
		Map<Integer, Integer> bookIdMap = new HashMap<>();

		doReturn(bookIdMap).when(opportunityRepoHelper).getBookIdsForOppIds(anyList());

		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(1);

		assertEquals(bookIdMap, opportunityRepoHelper.getBookIdsForOppIds(oppIds));
	}

	/**
	 * Given I don't have a quote response
	 * And I don't have a process result item
	 * When I get the preview data
	 * Then I shouldn't get the quote response data
	 */
	@Test
	void testGetPreviewDataPLNoQuoteResponseOrProcessResultItem() throws Exception {
		String data = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><LOBCd>AUTOP</LOBCd>" +
				"</PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";
		Opportunity opp = new Opportunity(data, 12345, 6);
		opp.setLineType(LineType.Personal);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opp);
		when(quotingAdapterService.getLatestQuoteResponseByOppId(anyInt())).thenReturn(null);
		when(processResultService.getLatestQuoteResultForOpportunity(anyInt()))
				.thenThrow(new ProcessResultException("not found"));
		PreviewDataResponse response = opportunityHelper.getPreviewData(opp.getOpportunityId());

		assertEquals(LineOfBusiness.AUTOP, response.getPreview().getLob());
		assertNull(response.getQuoteResponse());
	}

	/**
	 * Given I have a LineType of Business
	 * When I get the preview data
	 * Then I shouldn't get the quote response data
	 */
	@Test
	void testGetPreviewDataBL() throws Exception {
		String data = "<ACORD><InsuranceSvcRq><WorkCompPolicyQuoteInqRq><CommlPolicy><LOBCd>WORK</LOBCd>" +
				"</CommlPolicy></WorkCompPolicyQuoteInqRq></InsuranceSvcRq></ACORD>";
		Opportunity opp = new Opportunity(data, 12345, 6);
		opp.setLineType(LineType.Business);
		QuoteResponse quoteResponse = mockQuoteResponse();
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opp);
		when(quotingAdapterService.getLatestQuoteResponseByOppId(anyInt())).thenReturn(quoteResponse);
		PreviewDataResponse response = opportunityHelper.getPreviewData(opp.getOpportunityId());
		String expectedKoCode = "23";
		assertEquals(LineOfBusiness.WORK, response.getPreview().getLob());
		assertNotNull(response.getQuoteResponse());
		assertEquals(response.getQuoteResponse().getKoCode(), expectedKoCode);
	}

	/**
	 * Given I have an opp that has been quoted success
	 * And I have a QuoteResponse
	 * When I get the preview data
	 * Then I should get a status back success
	 */
	@Test
	void testGetPreviewDataFromPLQuoteResponse() throws Exception {
		String data = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><LOBCd>AUTOP</LOBCd>" +
				"</PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";
		Opportunity opp = new Opportunity(data, 12345, 6);
		QuoteResponse quoteResponse = mockQuoteResponse();
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opp);
		when(quotingAdapterService.getLatestQuoteResponseByOppId(anyInt())).thenReturn(quoteResponse);
		PreviewDataResponse response = opportunityHelper.getPreviewData(opp.getOpportunityId());

		String expectedKoCode = "23";
		assertEquals(LineOfBusiness.AUTOP, response.getPreview().getLob());
		assertNotNull(response.getQuoteResponse());
		assertEquals(response.getQuoteResponse().getKoCode(), expectedKoCode);
	}

	@Test
	void testGetPreviewDataSerializesQuoteResponseToQuoteResult() throws Exception {
		String data = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><LOBCd>AUTOP</LOBCd>" +
				"</PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";
		Opportunity opp = new Opportunity(data, 12345, 6);

		long time = 1614354595625L;
		String expectedDate = "2021-02-26 15:49:55.625";

		QuoteResponse quoteResponse = mockQuoteResponseWithTime(time);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opp);
		when(quotingAdapterService.getLatestQuoteResponseByOppId(anyInt())).thenReturn(quoteResponse);
		PreviewDataResponse response = opportunityHelper.getPreviewData(opp.getOpportunityId());

		assertEquals(LineOfBusiness.AUTOP, response.getPreview().getLob());
		assertNotNull(response.getQuoteResponse());

		assertEquals(expectedDate, response.getQuoteResponse().getTimeStamp());
	}

	/**
	 * Given I have an opp that has been quoted success
	 * And I don't have a QuoteResponse
	 * And I do have a ProcessResultItem
	 * When I get the preview data
	 * Then I should get a status back success
	 */
	@Test
	void testGettingStatusFromProcessResult() throws Exception {
		String data = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><LOBCd>AUTOP</LOBCd>" +
				"</PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";
		Opportunity opp = new Opportunity(data, 12345, 6);
		opp.setLineType(LineType.Personal);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opp);

		String expectedStatus = "Success";
		LegacyQuoteDataResponse qdr = new LegacyQuoteDataResponse(opp.getOpportunityId(), "", "", "", expectedStatus, "", "TAC_RC");
		when(quotingAdapterService.getLatestQuoteResponseByOppId(anyInt())).thenReturn(null);
		when(processResultService.getLatestQuoteResultForOpportunity(anyInt())).thenReturn(qdr);

		PreviewDataResponse response = opportunityHelper.getPreviewData(opp.getOpportunityId());

		assertEquals(expectedStatus, response.getQuoteResponse().getStatus());

	}

	/**
	 * Given I have an opp that has been quoted success When I get the preview data Then I should get a status back
	 * success
	 */
	@Test
	void testGettingKOCodeFromProcessResult() throws Exception {
		String data = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><LOBCd>AUTOP</LOBCd>" +
				"</PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";
		Opportunity opp = new Opportunity(data, 12345, 6);
		opp.setLineType(LineType.Personal);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opp);

		String expectedPolicyGuid = "Guid";
		String expectedStatus = "Success";
		String expectedKoCode = "Code";
		String expectedKoMessage = "This message was automatically generated.(Code)";
		LegacyQuoteDataResponse qdr = new LegacyQuoteDataResponse(opp.getOpportunityId(), "", expectedPolicyGuid, expectedKoMessage,
				expectedStatus, expectedKoCode, "TAC_RC");
		when(quotingAdapterService.getLatestQuoteResponseByOppId(anyInt())).thenReturn(null);
		when(processResultService.getLatestQuoteResultForOpportunity(anyInt())).thenReturn(qdr);

		PreviewDataResponse response = opportunityHelper.getPreviewData(opp.getOpportunityId());

		assertEquals(expectedStatus, response.getQuoteResponse().getStatus());
		assertEquals(response.getQuoteResponse().getKoCode(), expectedKoCode);
		assertEquals(response.getQuoteResponse().getKoMessage(), expectedKoMessage);
		assertEquals(response.getQuoteResponse().getPolicyGuid(), expectedPolicyGuid);

	}

	/**
	 * Given we have a leade cloud processresultxml for Auto When we try to build the preview data Then we should get
	 * Common, Vehicle, driver required data, quote details and vehicle details
	 */
	@Test
	void testLeadCloudAutoP() throws Exception {
		String data = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><LOBCd>AUTOP</LOBCd>" +
				"</PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";
		Opportunity opp = new Opportunity(data, 12345, 6);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opp);
		opp.setLineType(LineType.Personal);
		String expectedPolicyGuid = "Guid";
		String expectedStatus = "Success";
		String expectedKoCode = "Code";
		String expectedKoMessage = "This message was automatically generated.(Code)";
		LegacyQuoteDataResponse qdr = new LegacyQuoteDataResponse(opp.getOpportunityId(), "", expectedPolicyGuid, expectedKoMessage,
				expectedStatus, expectedKoCode, "TAC_RC");
		when(quotingAdapterService.getLatestQuoteResponseByOppId(anyInt())).thenReturn(null);
		when(processResultService.getLatestQuoteResultForOpportunity(anyInt())).thenReturn(qdr);

		PreviewDataResponse response = opportunityHelper.getPreviewData(opp.getOpportunityId());

		assertEquals(expectedStatus, response.getQuoteResponse().getStatus());
		assertEquals(response.getQuoteResponse().getKoCode(), expectedKoCode);
		assertEquals(response.getQuoteResponse().getKoMessage(), expectedKoMessage);
		assertEquals(response.getQuoteResponse().getPolicyGuid(), expectedPolicyGuid);
	}

	@Test
	void testGetPreviewDataException() throws Exception {

		String data = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><LOBCd>AUTOP</LOBCd>" +
				"</PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";
		Opportunity opp = new Opportunity(data, 212121, 6);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opp);
		opp.setLineType(LineType.Personal);
		when(quotingAdapterService.getLatestQuoteResponseByOppId(anyInt())).thenReturn(null);
		when(processResultService.getLatestQuoteResultForOpportunity(anyInt())).thenThrow(new RuntimeException("Unexpected error"));

		PreviewDataResponse response = opportunityHelper.getPreviewData(opp.getOpportunityId());

		assertEquals(LineOfBusiness.AUTOP, response.getPreview().getLob());
		assertNull(response.getQuoteResponse());
		verify(processResultService, times(1)).getLatestQuoteResultForOpportunity(opp.getOpportunityId());
	}

	/**
	 * When I query for preview data with bad id Then I expect it to throw an exception
	 */
	@Test
	void testGettingPreviewDataWithBadOppId() throws Exception {
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenThrow(new EmptyResultDataAccessException(1));

		assertThrows(EmptyResultDataAccessException.class, () -> opportunityHelper.getPreviewData(1236649));
	}

	@Test
	void testIsOpportunityQuotable() throws Exception {
		Opportunity opp = mockOpportunity(13);
		assertFalse(opportunityHelper.isOpportunityQuotable(opp));
		opp.setStatus(OpportunityStatus.getOppStatusFromStatusValue("Heritage").getOppStatusCode());
		assertFalse(opportunityHelper.isOpportunityQuotable(opp));

		// WITHDRAWN is not quotable
		opp.setStatus(11);
		assertFalse(opportunityHelper.isOpportunityQuotable(opp));
		opp.setStatus(OpportunityStatus.getOppStatusFromStatusValue("Withdrawn").getOppStatusCode());
		assertFalse(opportunityHelper.isOpportunityQuotable(opp));

		// ISSUED is not quotable
		opp.setStatus(10);
		assertFalse(opportunityHelper.isOpportunityQuotable(opp));
		opp.setStatus(OpportunityStatus.getOppStatusFromStatusValue("Issued").getOppStatusCode());
		assertFalse(opportunityHelper.isOpportunityQuotable(opp));

		// UNQUOTED is quotable
		opp.setStatus(3);
		assertTrue(opportunityHelper.isOpportunityQuotable(opp));
		opp.setStatus(OpportunityStatus.getOppStatusFromStatusValue("Unquoted").getOppStatusCode());
		assertTrue(opportunityHelper.isOpportunityQuotable(opp));

		// QUOTE CLEAN UP is not quotable
		opp.setStatus(9);
		assertTrue(opportunityHelper.isOpportunityQuotable(opp));
		opp.setStatus(OpportunityStatus.getOppStatusFromStatusValue("Quote Clean Up").getOppStatusCode());
		assertTrue(opportunityHelper.isOpportunityQuotable(opp));
	}

	@Test
	void testFetQuotableOpportunities() throws OpportunityException {
		Opportunity opp1 = new Opportunity();
		opp1.setOpportunityId(1);
		opp1.setStatus(OpportunityStatus.getOppStatusFromStatusValue("Heritage").getOppStatusCode());
		Opportunity opp2 = new Opportunity();
		opp2.setOpportunityId(2);
		opp2.setStatus(OpportunityStatus.getOppStatusFromStatusValue("Unquoted").getOppStatusCode());
		List<Opportunity> listOpportunities = new ArrayList<>();
		listOpportunities.add(opp1);
		listOpportunities.add(opp2);
		Mockito.doReturn(listOpportunities).when(opportunityRepoHelper).findOpportunitiesByIds(anyList());
		List<Integer> ids = new ArrayList<>();
		ActionableOpportunitiesResponse resp = opportunityHelper.getQuotableOpportunities(ids);
		assertEquals(1, resp.getActionableOpps().size());
		assertEquals(2, resp.getActionableOpps().get(0));
		assertEquals(1, resp.getNonActionableOpps().size());
		assertEquals(1, resp.getNonActionableOpps().get(0));
	}

  //Private enum for setting mocks correctly
	private enum QuoteErrorPolicyType {
		HOME(),
		AUTO(),
		HOME_AND_AUTO();
	}

	@Test
	void testGetOpportunitiesErrorInfo_HOME_AND_AUTO() throws BookTransferException {
		buildTestDataForQuoteErrorData(QuoteErrorPolicyType.HOME_AND_AUTO);
		List<OpportunityErrorInfo> result = opportunityHelper.getOpportunitiesErrorInfo(Arrays.asList(1, 2));

		assertEquals(2, result.size());

		OpportunityHomeErrorInfo homeResult = (OpportunityHomeErrorInfo) result.get(0);
		assertEquals(OpportunityHomeErrorInfo.class, homeResult.getClass());
		assertEquals(1, homeResult.getOpportunityId());
		assertEquals("a", homeResult.getSalesForceCode());
		assertEquals("name1", homeResult.getAgency());
		assertEquals("policy code", homeResult.getPolicyForm());


		OpportunityErrorInfo autoResult = result.get(1);
		assertEquals(2, autoResult.getOpportunityId());
		assertEquals("b", autoResult.getSalesForceCode());
		assertEquals("name2", autoResult.getAgency());


		verify(opportunityRepoHelper, times(1)).getDataXmlForOppIds(anySet());
	}

	@Test
	void testGetOpportunitiesErrorInfo_AUTO() throws BookTransferException {
		buildTestDataForQuoteErrorData(QuoteErrorPolicyType.AUTO);
		List<OpportunityErrorInfo> result = opportunityHelper.getOpportunitiesErrorInfo(List.of(2));

		assertEquals(1, result.size());

		OpportunityErrorInfo autoResult = result.get(0);
		assertEquals(2, autoResult.getOpportunityId());
		assertEquals("b", autoResult.getSalesForceCode());
		assertEquals("name2", autoResult.getAgency());

		verify(opportunityRepoHelper, times(0)).getDataXmlForOppIds(anySet());

	}

	@Test
	void testGetOpportunitiesErrorInfo_HOME() throws BookTransferException {
		buildTestDataForQuoteErrorData(QuoteErrorPolicyType.HOME);
		List<OpportunityErrorInfo> result = opportunityHelper.getOpportunitiesErrorInfo(List.of(1));

		assertEquals(1, result.size());

		OpportunityHomeErrorInfo homeResult = (OpportunityHomeErrorInfo) result.get(0);
		assertEquals(1, homeResult.getOpportunityId());
		assertEquals("a", homeResult.getSalesForceCode());
		assertEquals("name1", homeResult.getAgency());
		assertEquals("policy code", homeResult.getPolicyForm());

		verify(opportunityRepoHelper, times(1)).getDataXmlForOppIds(anySet());
	}

	@Test
	void testGetPartnerResult_ProcessResultException() throws ProcessResultException {
		when(processResultService.getProcessResultItemForOpportunity(anyInt())).thenThrow(new ProcessResultException("exception"));
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(new Opportunity());

		assertThrows(ProcessResultException.class, () -> opportunityHelper.maskAndBuildPartnerResult("1"));
	}


	@Test
	void testGetPartnerResult_testOpportunityExceptionException() throws ProcessResultException {
		when(processResultService.getProcessResultItemForOpportunity(anyInt())).thenThrow(new NumberFormatException());
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(new Opportunity());

		assertThrows(OpportunityException.class, () -> opportunityHelper.maskAndBuildPartnerResult("1"));
	}

	@Test
	void testGetPartnerResult_success() throws Exception {
		Document unmaskedDoc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		String unmaskedString = XmlHelper.getDocumentString(unmaskedDoc);
		Opportunity opp = new Opportunity();
		opp.setData(unmaskedString);
		opp.setOriginalXML(unmaskedString);
		opp.setTimestampCallPartner(LocalDateTime.now());  // LocalDateTime
		ProcessResultItem pri = new ProcessResultItem();
		pri.setData(unmaskedString);
		pri.setPartnerRequestXML(unmaskedString);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opp);
		when(processResultService.getProcessResultItemForOpportunity(anyInt())).thenReturn(pri);

		assertNotNull(opportunityHelper.maskAndBuildPartnerResult("1"));  // making sure the LocalDateTime can be serialized
	}

	@Test
	void testUpdateOpportunityFromBLQuoteResultWithoutFirstCall()
			throws IOException, ParserConfigurationException, SAXException, XPathExpressionException {
		ArgumentCaptor<String> timeStampCaptured = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> premiumCaptured = ArgumentCaptor.forClass(String.class);

		final String dataXml = XmlHelper.getFileContentFromPath("src/test/resources/xml/InsuredFullToBePaidAmt.xml");
		final Long seconds = 1618935600000L;
		final String time = DateUtils.getDateTimeFromMilliseconds(seconds);

		when(opportunityRepoHelper.doesFirstTimestampExist(anyInt())).thenReturn(false);

		opportunityHelper.updateOpportunityFromBLQuoteResult(dataXml, 123, seconds);

		verify(opportunityRepoHelper).updateOpportunityForBLCall(anyInt(),
				timeStampCaptured.capture(),
				premiumCaptured.capture(),
				eq(false));

		assertEquals(time, timeStampCaptured.getValue());
		assertEquals("1363.00", premiumCaptured.getValue());
	}

	@Test
	void testUpdateOpportunityFromBLQuoteResultWithFirstCall() throws Exception {
		ArgumentCaptor<String> timeStampCaptured = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> premiumCaptured = ArgumentCaptor.forClass(String.class);

		final String dataXml = XmlHelper.getFileContentFromPath("src/test/resources/xml/InsuredFullToBePaidAmt.xml");
		final Long seconds = 1618935600000L;
		final String time = DateUtils.getDateTimeFromMilliseconds(seconds);

		when(opportunityRepoHelper.doesFirstTimestampExist(anyInt())).thenReturn(true);

		opportunityHelper.updateOpportunityFromBLQuoteResult(dataXml, 123, seconds);

		verify(opportunityRepoHelper).updateOpportunityForBLCall(anyInt(),
				timeStampCaptured.capture(),
				premiumCaptured.capture(),
				eq(true));

		assertEquals(time, timeStampCaptured.getValue());
		assertEquals("1363.00", premiumCaptured.getValue());
	}

	@Test
	void testGetOpportunityForEditContainsQuotingGuideline() throws
			ParserConfigurationException, IOException, SAXException, XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Document oppDataXmlDoc = getDataXmlDocForEdit("<QuoteGuideline><Id>12345</Id></QuoteGuideline>");

		assertNotNull(AcordHelper.getBTMetaQG(oppDataXmlDoc));
	}

	@Test
	void testGetOpportunityForEditNotContainsQuotingGuideline() throws
			ParserConfigurationException, IOException, SAXException, XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Document oppDataXmlDoc = getDataXmlDocForEdit("");

		assertNull(AcordHelper.getBTMetaQG(oppDataXmlDoc));
	}

	@ParameterizedTest
	@CsvSource({"BAN,tac,id,**********1234,SV,FullName,*********",
			"NOT THE SAME BAN,'','','','','',''",
			" '','','','','','',''"})
	void testGetOpportunityForEdit_paymentData(String billingAccountNumber, String expectedTAN, String expectedInstrumentId,
													  String expectedACTNumberId, String expectedACTType,
													  String expectedACTHolderName, String expectedRoutingNumber)
			throws ParserConfigurationException, IOException, SAXException,
			XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Document oppDataXmlDoc = getDataXmlDocForEdit(
				"", billingAccountNumber, DEFAULT_PAYMENT_DATA_RESP);

		assertPaymentData(expectedInstrumentId, expectedTAN, expectedRoutingNumber, expectedACTHolderName, expectedACTType, expectedACTNumberId, oppDataXmlDoc);
	}

	@Test
	void testGetOpportunityForEdit_paymentData_nullBAN() throws
			ParserConfigurationException, IOException, SAXException, XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Document oppDataXmlDoc = getDataXmlDocForEdit(
				"", null, DEFAULT_PAYMENT_DATA_RESP);

		assertPaymentData("", "", "", "", "", "", oppDataXmlDoc);

	}

	@Test
	void testGetOpportunityForEditPaymentDataException() throws
			ParserConfigurationException, IOException, SAXException, XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Document oppDataXmlDoc = getDataXmlDocForEdit("", new RuntimeException("expected"));

		// tags should not have been created
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/com.safeco_TokenizedAccountNumber", oppDataXmlDoc));
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/com.safeco_InstrumentId", oppDataXmlDoc));
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/FromAcct/AccountNumberId", oppDataXmlDoc));
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/AcctTypeCd/MethodOfPayment", oppDataXmlDoc));
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/ElectronicFundsTransfer/FromAcct/CommercialName", oppDataXmlDoc));
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/ElectronicFundsTransfer/FromAcct/BankInfo/BankId", oppDataXmlDoc));
	}

	private Document getDataXmlDocForEdit(
			String quotingGuidelineXml,
			Exception thrownException)
			throws BookTransferException, ParserConfigurationException, SAXException, IOException, QuotingGuidelineException {
		lenient().when(paymentServiceHelper.getPaymentInfoUsingBillingAccountNumber(nullable(String.class)))
				.thenThrow(thrownException);
		return getDataXmlDocForEdit(quotingGuidelineXml);
	}

	private Document getDataXmlDocForEdit(
			String quotingGuidelineXml,
			String billingAccountNumber,
			BillingDetails billingDetails)
			throws BookTransferException,
			ParserConfigurationException, SAXException, IOException, QuotingGuidelineException {
		lenient().when(paymentServiceHelper.getPaymentInfoUsingBillingAccountNumber(billingAccountNumber))
				.thenReturn(billingDetails);
		return getDataXmlDocForEdit(quotingGuidelineXml);
	}

	private Document getDataXmlDocForEdit(String quotingGuidelineXml) throws
			BookTransferException, ParserConfigurationException, SAXException, IOException, QuotingGuidelineException {
		int oppId = 12345;
		String dataXml = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><PaymentOption>\n" +
				"</PaymentOption></PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";
		Opportunity opp = new Opportunity(dataXml, dataXml);
		int bookTransferID = 56789;
		opp.setBookTransferID(bookTransferID);
		opp.setNAICCd("98765");
		opp.setBusinessType("HOME");
		opp.setBillingAccountNumber("BAN");
		opp.setLineType(LineType.Personal);
		when(opportunityRepoHelper.findOpportunityForEdit(oppId)).thenReturn(opp);
		when(quotingGuidelineHelper.getQuotingGuidelineFunctionalXml(any(QGFunctionalXmlRequest.class)))
				.thenReturn(quotingGuidelineXml);
		mockBook("BT-12345", bookTransferID, "State Auto Book Migration");
		Opportunity opportunity = opportunityHelper.getOpportunityForEdit(oppId);
		return XmlHelper.getDocument(opportunity.getData());
	}

	@Test
	void testGetOpportunityForEdit_blAcord() throws
			IOException, ParserConfigurationException, SAXException, TransformerException, BookTransferException, QuotingGuidelineException {
		Document doc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/blWorkersComp_vertafore.xml");
		String acordString = XmlHelper.getDocumentString(doc);
		int oppId = 123;
		int bookTransferID = 56789;
		Opportunity opp = new Opportunity();
		opp.setOpportunityId(oppId);
		opp.setData(acordString);
		opp.setBookTransferID(bookTransferID);
		BookTransferDTO bookTransferDTO = new BookTransferDTO();
		bookTransferDTO.setFirstEffectiveDate(new Date());
		bookTransferDTO.setSalesforceCode("BT-12345");
		bookTransferDTO.setBookTransferID(56789);
		when(opportunityRepoHelper.findOpportunityForEdit(oppId)).thenReturn(opp);
		when(quotingGuidelineHelper.getQuotingGuidelineFunctionalXml(any(QGFunctionalXmlRequest.class)))
				.thenReturn(StringUtils.EMPTY);
		when(bookTransferService.findByBookTransferId(bookTransferID)).thenReturn(
				bookTransferDTO);

		Opportunity ignored = opportunityHelper.getOpportunityForEdit(oppId);
		// Just making sure that this does not throw
		assertNotNull(ignored);
	}

	@Test
	void testGetOpportunityForEdit_blDetokenize() throws Exception {
		Document doc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/blWorkersComp_vertafore.xml");
		String acordString = XmlHelper.getDocumentString(doc);
		int oppId = 123;
		int bookTransferID = 56789;
		Opportunity opp = new Opportunity();
		opp.setOpportunityId(oppId);
		opp.setOriginalXML(acordString);
		opp.setData(acordString);
		opp.setBookTransferID(56789);
		opp.setLineType(LineType.Business);
		BookTransferDTO bookTransferDTO = new BookTransferDTO();

		DetokenizeRequest request = new DetokenizeRequest(true, "n#", false, false);
		when(opportunityRepoHelper.findOpportunityForEdit(oppId)).thenReturn(opp);
		when(quotingGuidelineHelper.getQuotingGuidelineFunctionalXml(any(QGFunctionalXmlRequest.class)))
				.thenReturn(StringUtils.EMPTY);
		when(bookTransferService.findByBookTransferId(bookTransferID)).thenReturn(bookTransferDTO);
		when(bookTransferService.findByBookTransferId(bookTransferID)).thenReturn(bookTransferDTO);
		when(sensitiveDataHelper.deTokenizeXml(any(), anyString())).thenReturn(doc);

		Opportunity ignored = opportunityHelper.getOpportunityForEdit(oppId, request);

		verify(sensitiveDataHelper, times(2)).deTokenizeXml(any(), anyString());
	}

	@Test
	void testGetQuotableOpportunityContainsQuotingGuideline() throws
			ParserConfigurationException, IOException, SAXException, XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Document oppDataXmlDoc = getDataXmlDocForGetQuotableOpportunity(
				"<QuoteGuideline><Id>12345</Id></QuoteGuideline>").getFirst();

		assertNotNull(AcordHelper.getBTMetaQG(oppDataXmlDoc));
	}

	@Test
	void testGetQuotableOpportunityNotContainsQuotingGuideline() throws
			ParserConfigurationException, IOException, SAXException, XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Document oppDataXmlDoc = getDataXmlDocForGetQuotableOpportunity("").getFirst();

		assertNull(AcordHelper.getBTMetaQG(oppDataXmlDoc));
	}

	@Test
	void testGetQuotableOpportunityQuotingGuidelineException() throws
			ParserConfigurationException, IOException, SAXException, XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Pair<Document, List<String>> oppDataXmlDoc = getDataXmlDocForGetQuotableOpportunity(new RuntimeException("expected"));

		assertNull(AcordHelper.getBTMetaQG(oppDataXmlDoc.getFirst()));
		assertEquals("Error while adding quoting guideline information, please try again.", oppDataXmlDoc.getSecond().get(0));
	}

	@ParameterizedTest
	@CsvSource({"BAN,tac,id,xxxxxxx1234,SV,FullName,*********",
			"NOT THE SAME BAN,'','','','','',''",
			" '','','','','','',''"})
	void testGetQuotableOpportunity_paymentData(String billingAccountNumber, String expectedTAN, String expectedInstrumentId,
											   String expectedACTNumberId, String expectedACTType,
											   String expectedACTHolderName, String expectedRoutingNumber)
			throws ParserConfigurationException, IOException, SAXException,
			XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Document oppDataXmlDoc = getDataXmlDocForGetQuotableOpportunity(
				"", billingAccountNumber, DEFAULT_PAYMENT_DATA_RESP).getFirst();

		assertPaymentData(expectedInstrumentId, expectedTAN, expectedRoutingNumber, expectedACTHolderName, expectedACTType, expectedACTNumberId, oppDataXmlDoc);
	}

	@Test
	void testGetQuotableOpportunity_paymentData_nullBAN() throws
			ParserConfigurationException, IOException, SAXException, XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Document oppDataXmlDoc = getDataXmlDocForGetQuotableOpportunity(
				"", null, DEFAULT_PAYMENT_DATA_RESP).getFirst();

		assertPaymentData("", "", "", "", "", "", oppDataXmlDoc);
	}

	@Test
	void testGetQuotableOpportunity_returnsNullPaymentData_nullBAN() throws
			ParserConfigurationException, IOException, SAXException, XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Document oppDataXmlDoc = getDataXmlDocForGetQuotableOpportunity(
				"", null, null).getFirst();

		assertPaymentData("", "", "", "", "", "", oppDataXmlDoc);
	}

	@Test
	void testGetQuotableOpportunityPaymentDataException() throws
			ParserConfigurationException, IOException, SAXException, XPathExpressionException, BookTransferException, QuotingGuidelineException {
		Pair<Document, List<String>> oppDataXmlDoc = getDataXmlDocForGetQuotableOpportunity("", new RuntimeException("expected"));
		Document dataXml = oppDataXmlDoc.getFirst();

		// tags should not have been created
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/com.safeco_TokenizedAccountNumber", dataXml));
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/com.safeco_InstrumentId", dataXml));
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/FromAcct/AccountNumberId", dataXml));
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/AcctTypeCd/MethodOfPayment", dataXml));
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/ElectronicFundsTransfer/FromAcct/CommercialName", dataXml));
		assertNull(XmlHelper.getNodeFromDoc("//com.safeco_RecurringPaymentInfo/ElectronicFundsTransfer/FromAcct/BankInfo/BankId", dataXml));

		// error should be present
		assertEquals("Error while adding payment data, please try again.", oppDataXmlDoc.getSecond().get(0));
	}

	private Pair<Document, List<String>> getDataXmlDocForGetQuotableOpportunity(
			String quotingGuidelineXml,
			Exception thrownException
	) throws BookTransferException, ParserConfigurationException, SAXException, IOException, QuotingGuidelineException {
		lenient().when(paymentServiceHelper.getPaymentInfoUsingBillingAccountNumber(nullable(String.class)))
				.thenThrow(thrownException);
		return getDataXmlDocForGetQuotableOpportunity(quotingGuidelineXml);
	}

	private Pair<Document, List<String>> getDataXmlDocForGetQuotableOpportunity(
			String quotingGuidelineXml,
			String billingAccountNumber,
			BillingDetails billingDetails
	) throws BookTransferException, ParserConfigurationException, SAXException, IOException, QuotingGuidelineException {
		lenient().when(paymentServiceHelper.getPaymentInfoUsingBillingAccountNumber(eq(billingAccountNumber)))
				.thenReturn(billingDetails);
		return getDataXmlDocForGetQuotableOpportunity(quotingGuidelineXml);
	}

	private Pair<Document, List<String>> getDataXmlDocForGetQuotableOpportunity(Exception thrown) throws
			BookTransferException, ParserConfigurationException, SAXException, IOException, QuotingGuidelineException {
		lenient().when(quotingGuidelineHelper.getQuotingGuidelineFunctionalXml(any(QGFunctionalXmlRequest.class)))
				.thenThrow(thrown);

		return getDataXmlDocForGetQuotableOpportunity("");
	}

	private Pair<Document, List<String>> getDataXmlDocForGetQuotableOpportunity(String quotingGuidelineXml) throws
			BookTransferException, ParserConfigurationException, SAXException, IOException, QuotingGuidelineException {
		int oppId = 12345;
		String dataXml = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><PaymentOption>\n" +
				"</PaymentOption></PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";
		Opportunity opp = new Opportunity(dataXml, dataXml);
		int bookTransferID = 56789;
		opp.setBookTransferID(bookTransferID);
		opp.setNAICCd("98765");
		opp.setBusinessType("HOME");
		opp.setBillingAccountNumber("BAN");
		opp.setLineType(LineType.Personal);
		when(opportunityRepoHelper.findOpportunityById(oppId)).thenReturn(opp);
		mockBook("BT-12345", bookTransferID, "State Auto Book Migration");

		if (StringUtils.isNotBlank(quotingGuidelineXml)) {
			when(quotingGuidelineHelper.getQuotingGuidelineFunctionalXml(any(QGFunctionalXmlRequest.class)))
					.thenReturn(quotingGuidelineXml);
		}

		Pair<Opportunity, List<String>> opportunity = opportunityHelper.getQuotableOpportunity(oppId);

		return Pair.of(XmlHelper.getDocument(opportunity.getFirst().getData()), opportunity.getSecond());
	}

	@Test
	void testGetQuotableOpportunityThrowsEmptyResultOnEmptyData() {
		Opportunity oppWithoutData = new Opportunity();
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(oppWithoutData);

		assertThrows(NoSuchElementException.class, () -> opportunityHelper.getQuotableOpportunity(1236649));
	}

	@Test
	void testGetQuotableOpportunity_blAcord() throws
			IOException, ParserConfigurationException, SAXException, TransformerException, BookTransferException, QuotingGuidelineException {
		Document doc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/blWorkersComp_vertafore.xml");
		String acordString = XmlHelper.getDocumentString(doc);
		int oppId = 123;
		int bookTransferID = 56789;
		//String btFirstEffectiveDate = "2023-12-12";
		Opportunity opp = new Opportunity();
		opp.setOpportunityId(oppId);
		opp.setData(acordString);
		opp.setBookTransferID(bookTransferID);
		BookTransferDTO bookTransferDTO = new BookTransferDTO();
		bookTransferDTO.setFirstEffectiveDate(new Date());
		bookTransferDTO.setSalesforceCode("BT-12345");
		bookTransferDTO.setBookTransferID(56789);
		when(opportunityRepoHelper.findOpportunityById(oppId)).thenReturn(opp);
		when(quotingGuidelineHelper.getQuotingGuidelineFunctionalXml(any(QGFunctionalXmlRequest.class)))
				.thenReturn(StringUtils.EMPTY);
		mockBook("BT-12345", bookTransferID, "State Auto Book Migration");
		when(bookTransferService.findByBookTransferId(bookTransferID)).thenReturn(
				bookTransferDTO);

		Pair<Opportunity, List<String>> ignored = opportunityHelper.getQuotableOpportunity(oppId);
		// Just making sure that this does not throw
		assertNotNull(ignored);
	}

	@Test
	void updateBLOppStatus() {
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(12345);
		oppIds.add(24567);
		opportunityRepoHelper.updateOpportunitiesStatus(oppIds, 2);
		verify(opportunityRepoHelper, times(1)).updateOpportunitiesStatus(any(), anyInt());
	}

	@Test
	void testMaskAndBuildPartnerResult_masksData() throws Exception {
		Document unmaskedDoc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		String unmaskedString = XmlHelper.getDocumentString(unmaskedDoc);
		Opportunity opp = new Opportunity();
		opp.setData(unmaskedString);
		opp.setOriginalXML(unmaskedString);
		ProcessResultItem pri = new ProcessResultItem();
		pri.setData(unmaskedString);
		pri.setPartnerRequestXML(unmaskedString);

		PartnerResult result = OpportunityHelper.maskAndBuildPartnerResult(opp, pri);

		assertEachNodeIsMasked(result.getOppData().getData());
		assertEachNodeIsMasked(result.getOppData().getOriginalXML());
		assertEachNodeIsMasked(result.getProcessResultData().getData());
		assertEachNodeIsMasked(result.getProcessResultData().getPartnerRequestXML());
	}

	@Test
	void testMaskAndBuildPartnerResult_badData_null() throws Exception {
		Document unmaskedDoc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		String unmaskedString = XmlHelper.getDocumentString(unmaskedDoc);
		Opportunity opp = new Opportunity();
		opp.setData(unmaskedString);
		opp.setOriginalXML("<some bad data />");
		ProcessResultItem pri = new ProcessResultItem();
		pri.setData("<some bad data />");
		pri.setPartnerRequestXML(unmaskedString);

		PartnerResult result = OpportunityHelper.maskAndBuildPartnerResult(opp, pri);

		assertEachNodeIsMasked(result.getOppData().getData());
		assertNull(result.getOppData().getOriginalXML());
		assertNull(result.getProcessResultData().getData());
		assertEachNodeIsMasked(result.getProcessResultData().getPartnerRequestXML());
	}

	@Test
	void testUpdateOpportunityXml_updatedMaskedData_updatesCorrectly() throws Exception {
		Document unmaskedDoc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/AutoRatingDefaultsPostRuleEngine.xml");
		String unmaskedString = XmlHelper.getDocumentString(unmaskedDoc);

		final String updatedBirthDt = "2020-01-01";
		final String updatedGivenName = "givenName";
		final String expectedDLNum = XmlHelper.nullSafeExtractFromXml(unmaskedDoc, PersonalAcordXPaths.DRIVERS_LICENSE);
		final String expectedAccountNumberId = "C0000012345678";

		Opportunity savedOpp = new Opportunity();
		savedOpp.setData(unmaskedString);
		savedOpp.setLineType(LineType.Personal);

		Document updatedMaskedDoc = MaskHelper.cloneAndMaskDocument(unmaskedDoc);
		XmlHelper.setNodesTextContent(updatedMaskedDoc, PersonalAcordXPaths.DATE_OF_BIRTH, updatedBirthDt);
		XmlHelper.setNodesTextContent(updatedMaskedDoc, PersonalAcordXPaths.INSURED_OR_PRINCIPAL_GIVEN_NAME, updatedGivenName);
		String updatedMaskedString = XmlHelper.getDocumentString(updatedMaskedDoc);

		Opportunity updatedOpp = new Opportunity();
		updatedOpp.setData(updatedMaskedString);
		updatedOpp.setLineType(LineType.Personal);

		final Opportunity[] saveOppPointer = new Opportunity[1];

		doAnswer(invocation -> saveOppPointer[0] = invocation.getArgument(0))
				.when(opportunityHelper).saveUpdatedOpportunity(any());

		// ensure test meets test case criteria
		assertTrue(MaskHelper.doesMaskedDocumentHaveUnmaskedNodes(updatedMaskedDoc));

		opportunityHelper.updateOpportunityXml(updatedOpp, savedOpp);

		// opp gets saved to DB
		assertNotNull(saveOppPointer[0]);

		final Document savedDoc = XmlHelper.getDocument(saveOppPointer[0].getData());

		Node savedDLNode = XmlHelper.getNodeFromDoc(PersonalAcordXPaths.DRIVERS_LICENSE, savedDoc);
		Node savedBDtNode = XmlHelper.getNodeFromDoc(PersonalAcordXPaths.DATE_OF_BIRTH, savedDoc);
		Node savedNameNode = XmlHelper.getNodeFromDoc(PersonalAcordXPaths.INSURED_OR_PRINCIPAL_GIVEN_NAME, savedDoc);
		Node savedAccountNumberIdNode = XmlHelper.getNodeFromDoc(
				PersonalAcordXPaths.PAYMENT_OPTION_RELATIVE_ACCOUNT_NUMBER_ID, savedDoc);

		// contains unaltered sensitive data
		assertEquals(expectedDLNum, savedDLNode.getTextContent());
		// contains updated sensitive data
		assertEquals(updatedBirthDt, savedBDtNode.getTextContent());
		// contains updated non-sensitive data
		assertEquals(updatedGivenName, savedNameNode.getTextContent());
		// contains masked account number id
		assertEquals(expectedAccountNumberId, savedAccountNumberIdNode.getTextContent());

		// no isMasked attribute going to DB
		assertNull(savedDLNode.getAttributes().getNamedItem("isMasked"));
		assertNull(savedBDtNode.getAttributes().getNamedItem("isMasked"));
		assertNull(savedNameNode.getAttributes().getNamedItem("isMasked"));
		assertNull(savedAccountNumberIdNode.getAttributes().getNamedItem("isMasked"));
	}

	@Test
	void testUpdateOpportunityXml_withoutQGData() throws Exception {
		String xmlWithQGData = "<ACORD><BookTransferMeta><QuoteGuideline><Id>12345</Id></QuoteGuideline>" +
				"</BookTransferMeta></ACORD>";
		String xmlWithOutQGData = "<ACORD><BookTransferMeta></BookTransferMeta></ACORD>";
		Opportunity updatedOpp = new Opportunity();
		updatedOpp.setData(xmlWithQGData);
		updatedOpp.setLineType(LineType.Personal);

		Opportunity savedOpp = new Opportunity();
		savedOpp.setData(xmlWithOutQGData);
		savedOpp.setLineType(LineType.Personal);

		final Opportunity[] saveOppPointer = new Opportunity[1];

		doAnswer(invocation -> saveOppPointer[0] = invocation.getArgument(0))
				.when(opportunityHelper).saveUpdatedOpportunity(any());

		opportunityHelper.updateOpportunityXml(updatedOpp, savedOpp);

		// opp gets saved to DB
		assertNotNull(saveOppPointer[0]);

		final Document savedDoc = XmlHelper.getDocument(saveOppPointer[0].getData());

		assertNull(AcordHelper.getBTMetaQG(savedDoc));
	}

	@Test
	void testUpdateOpportunityXml_noUpdatesToMaskedData_updatesCorrectly() throws Exception {
		Document unmaskedDoc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/AutoRatingDefaultsPostRuleEngine.xml");
		String unmaskedString = XmlHelper.getDocumentString(unmaskedDoc);

		final String updatedGivenName = "givenName";
		final String expectedDLNum = XmlHelper.nullSafeExtractFromXml(unmaskedDoc, PersonalAcordXPaths.DRIVERS_LICENSE);
		final String expectedBDt = XmlHelper.nullSafeExtractFromXml(unmaskedDoc, PersonalAcordXPaths.DATE_OF_BIRTH);
		final String expectedAccountNumberId = "C0000012345678";

		Opportunity savedOpp = new Opportunity();
		savedOpp.setData(unmaskedString);
		savedOpp.setLineType(LineType.Personal);

		Document updatedMaskedDoc = MaskHelper.cloneAndMaskDocument(unmaskedDoc);
		XmlHelper.setNodesTextContent(updatedMaskedDoc, PersonalAcordXPaths.INSURED_OR_PRINCIPAL_GIVEN_NAME, updatedGivenName);
		String updatedMaskedString = XmlHelper.getDocumentString(updatedMaskedDoc);

		Opportunity updatedOpp = new Opportunity();
		updatedOpp.setData(updatedMaskedString);
		updatedOpp.setLineType(LineType.Personal);

		final Opportunity[] saveOppPointer = new Opportunity[1];

		doAnswer(invocation -> saveOppPointer[0] = invocation.getArgument(0))
				.when(opportunityHelper).saveUpdatedOpportunity(any());

		// ensure test meets test case criteria
		assertFalse(MaskHelper.doesMaskedDocumentHaveUnmaskedNodes(updatedMaskedDoc));

		opportunityHelper.updateOpportunityXml(updatedOpp, savedOpp);

		// opp gets saved to DB
		assertNotNull(saveOppPointer[0]);

		final Document savedDoc = XmlHelper.getDocument(saveOppPointer[0].getData());

		Node savedDLNode = XmlHelper.getNodeFromDoc(PersonalAcordXPaths.DRIVERS_LICENSE, savedDoc);
		Node savedBDtNode = XmlHelper.getNodeFromDoc(PersonalAcordXPaths.DATE_OF_BIRTH, savedDoc);
		Node savedNameNode = XmlHelper.getNodeFromDoc(PersonalAcordXPaths.INSURED_OR_PRINCIPAL_GIVEN_NAME, savedDoc);
		Node savedAccountNumberIdNode = XmlHelper.getNodeFromDoc(
				PersonalAcordXPaths.PAYMENT_OPTION_RELATIVE_ACCOUNT_NUMBER_ID, savedDoc);

		// contains unaltered sensitive data
		assertEquals(expectedDLNum, savedDLNode.getTextContent());
		assertEquals(expectedBDt, savedBDtNode.getTextContent());
		assertEquals(expectedAccountNumberId, savedAccountNumberIdNode.getTextContent());
		// contains updated non-sensitive data
		assertEquals(updatedGivenName, savedNameNode.getTextContent());

		// no isMasked attribute going to DB
		assertNull(savedDLNode.getAttributes().getNamedItem("isMasked"));
		assertNull(savedBDtNode.getAttributes().getNamedItem("isMasked"));
		assertNull(savedNameNode.getAttributes().getNamedItem("isMasked"));
		assertNull(savedAccountNumberIdNode.getAttributes().getNamedItem("isMasked"));
	}

	private void assertEachNodeIsMasked(String maskedDocAsString) throws Exception {
		Document maskedDoc = XmlHelper.getDocument(maskedDocAsString);

		NodeList dlNums = XmlHelper.getNodeList(maskedDoc, PersonalAcordXPaths.DRIVERS_LICENSE);
		NodeList birthDts = XmlHelper.getNodeList(maskedDoc, PersonalAcordXPaths.DATE_OF_BIRTH);

		for (Node maskedNode : XmlHelper.nodeListToList(dlNums)) {
			assertNotNull(maskedNode.getAttributes().getNamedItem("isMasked"));
		}

		for (Node maskedNode : XmlHelper.nodeListToList(birthDts)) {
			assertNotNull(maskedNode.getAttributes().getNamedItem("isMasked"));
		}
	}

	private void buildTestDataForQuoteErrorData(QuoteErrorPolicyType quoteErrorPolicyType) throws BookTransferException {
		List<Integer> opportunityIds = new ArrayList<>();
		List<OpportunityErrorInfo> errorOpportunityInfos = new ArrayList<>();

		OpportunityErrorInfo opportunityErrorInfoHome = new OpportunityHomeErrorInfo();
		opportunityErrorInfoHome.setOpportunityId(1);
		opportunityErrorInfoHome.setBookTransferId(1);

		OpportunityErrorInfo opportunityErrorInfoAuto = new OpportunityErrorInfo();
		opportunityErrorInfoAuto.setOpportunityId(2);
		opportunityErrorInfoAuto.setBookTransferId(2);
		if (quoteErrorPolicyType.equals(QuoteErrorPolicyType.HOME_AND_AUTO)) {
			errorOpportunityInfos.addAll(Arrays.asList(opportunityErrorInfoHome, opportunityErrorInfoAuto));
			opportunityIds.addAll(Arrays.asList(1, 2));
		} else if (quoteErrorPolicyType.equals(QuoteErrorPolicyType.HOME)) {
			errorOpportunityInfos.add(opportunityErrorInfoHome);
			opportunityIds.add(1);
		} else {
			errorOpportunityInfos.add(opportunityErrorInfoAuto);
			opportunityIds.add(2);
		}

		Mockito.doReturn(errorOpportunityInfos).when(opportunityRepoHelper).getErrorItemsByOppIds(opportunityIds);
		List<OpportunityXmlData> xmlDataList = new ArrayList<>();
		OpportunityXmlData xmlData = OpportunityXmlData.builder().opportunityId(1).build();

		xmlData.setXmlData("<ACORD><InsuranceSvcRq><HomePolicyQuoteInqRq><HomeLineBusiness><Dwell><PolicyTypeCd>policy code</PolicyTypeCd></Dwell></HomeLineBusiness></HomePolicyQuoteInqRq></InsuranceSvcRq></ACORD>");
		xmlDataList.add(xmlData);

		lenient().doReturn(xmlDataList).when(opportunityRepoHelper).getDataXmlForOppIds(anySet());

		List<BookTransferDTO> list = new ArrayList<>();
		BookTransferDTO bookTransfer1 = new BookTransferDTO();
		bookTransfer1.setBookTransferID(1);
		bookTransfer1.setSalesforceCode("a");
		bookTransfer1.setName("name1");
		list.add(bookTransfer1);

		BookTransferDTO bookTransfer2 = new BookTransferDTO();
		bookTransfer2.setBookTransferID(2);
		bookTransfer2.setSalesforceCode("b");
		bookTransfer2.setName("name2");
		list.add(bookTransfer2);

		Mockito.doReturn(list).when(bookTransferService).getBookTransfersForQuoteErrors(anySet());
	}

	private QuoteResponse mockQuoteResponse() {
		QuoteOrigin origin = new QuoteOrigin();
		origin.setOriginRequestId("processRequestId");
		origin.setOriginData("partnerRequestXML");
		origin.setRequestEnvironment(RequestEnvironment.DEV_ML);
		origin.setNNumber("nNumber");

		Response response = new Response();
		response.setResponseData("data");
		response.setResponseId("policyGuid");

		QuoteResponse quoteResponse = new QuoteResponse();
		quoteResponse.setOpportunityId("oppId");
		quoteResponse.setRequestOrigin(origin);
		quoteResponse.setResponse(response);
		quoteResponse.setCompletionTimestamp(new Date().getTime());
		quoteResponse.setStatus(QuoteStatus.Success);

		Message message = new Message();
		message.setMessageStatusDescription("A test with code (23)");
		quoteResponse.setMessages(new Message[]{message});

		return quoteResponse;
	}

	private QuoteResponse mockQuoteResponseWithTime(long timestamp) {
		QuoteResponse quoteResponse = mockQuoteResponse();
		quoteResponse.setCompletionTimestamp(timestamp);
		return quoteResponse;
	}

	private void setUpMockTransformationService() {
		transformationResult = new TransformationResult();
		//set default status
		transformationResult.setExecutionStatus(ExecutionStatus.SUCCESS);
		lenient().doCallRealMethod().when(transformationService).runRules(any(), any(), any());
		lenient().doAnswer(invocation -> {
			packageId = invocation.getArgument(1);
			transformationServiceXmlDoc = invocation.getArgument(0);
			//set default xml result doc if test does not set it
			if (transformationResult.getResultDocument() == null) {
				transformationResult.setResultDocument(transformationServiceXmlDoc);
			}
			return transformationResult;
		}).when(transformationService).executePackage(any(Document.class), any(String.class), any());
	}

	@Test
	void testGetPriorInsurerNameFromOpportunityXml(){
		opportunity.setData(INSURER_NAME_VALUE);
		opps.add(opportunity);
		ids.add(opportunity.getOpportunityId());
		when(opportunityRepoHelper.findOpportunitiesByIds(ids)).thenReturn(opps);
		List<PriorCarrierData> carriers = opportunityHelper.findPriorCarrierData(ids);
		PriorCarrierData data = carriers.get(0);
		String name = data.getPriorInsurerName();
		assertEquals("JOE-SHMOE", name);

	}
	@Test
	void testGetPriorInsurerNameEmptyFromOpportunityXml(){
		opportunity.setData(INSURER_NO_NAME_VALUE);
		opps.add(opportunity);
		ids.add(opportunity.getOpportunityId());
		when(opportunityRepoHelper.findOpportunitiesByIds(ids)).thenReturn(opps);
		List<PriorCarrierData> carriers = opportunityHelper.findPriorCarrierData(ids);
		PriorCarrierData data = carriers.get(0);
		String name = data.getPriorInsurerName();
		assertEquals("", name);
	}
	@Test
	void testGetPriorInsurerNameNullFromOpportunityXml(){
		opportunity.setData(INSURER_NULL_NAME_VALUE);
		opps.add(opportunity);
		ids.add(opportunity.getOpportunityId());
		when(opportunityRepoHelper.findOpportunitiesByIds(ids)).thenReturn(opps);
		List<PriorCarrierData> carriers = opportunityHelper.findPriorCarrierData(ids);
		PriorCarrierData data = carriers.get(0);
		String name = data.getPriorInsurerName();
		assertEquals("", name);
	}

	@Test
	void testGetPriorCarrierGuid(){
		opportunity.setPriorCarrierGuid("*********");
		opportunity.setData(INSURER_NULL_NAME_VALUE);
		opps.add(opportunity);
		ids.add(opportunity.getOpportunityId());
		when(opportunityRepoHelper.findOpportunitiesByIds(ids)).thenReturn(opps);
		List<PriorCarrierData> carriers = opportunityHelper.findPriorCarrierData(ids);
		PriorCarrierData data = carriers.get(0);
		assertEquals("*********", data.getPriorCarrierGuid());
	}

	@Test
	void testNonIssuedPolicy() throws BookTransferException, OpportunityException {
		// Arrange test data
		Opportunity opp = mockOpportunity(9);
		// Act - save safeco billing data
		opportunityHelper.saveSafecoBillingDetails(opp, new QuoteAndIssueUpdate());
		// Assert - Shouldn't call payment service for non-state auto
		verify(bookTransferService, times(0)).findByBookTransferId(anyInt());
		verify(paymentServiceHelper, times(0)).saveSafecoBillingData(any(SafecoBillingDataDTO.class));
	}

	@Test
	void testNonStateAutoBook() throws BookTransferException, OpportunityException {
		// Arrange test data
		Opportunity opp = mockOpportunity(10);
		mockBook("Safeco");
		// Act - save safeco billing data
		opportunityHelper.saveSafecoBillingDetails(opp, new QuoteAndIssueUpdate());
		// Assert - Shouldn't call payment service for non-state auto
		verify(paymentServiceHelper, times(0)).saveSafecoBillingData(any(SafecoBillingDataDTO.class));
	}

	@Test
	void testFailedToRetrieveSafecoBillingDetailsFromQnI() throws BookTransferException, OpportunityException {
		Optional<AccountDetails> optionalAccountDetails = Optional.empty();
		doReturn(optionalAccountDetails).when(opportunityHelper).getAccountDetails(any(QuoteAndIssueUpdate.class));
		Opportunity opp = mockOpportunity(10);
		mockBook("State Auto Book Migration");
		// Act - save safeco billing data
		opportunityHelper.saveSafecoBillingDetails(opp, new QuoteAndIssueUpdate());
		// Assert - Shouldn't call payment service for non-state auto
		then(opportunityHelper).should().getAccountDetails(any(QuoteAndIssueUpdate.class));
		verify(paymentServiceHelper, times(0)).saveSafecoBillingData(any(SafecoBillingDataDTO.class));
	}

	@Test
	void testSaveSafecoBillingDetailsFromQnI() throws BookTransferException, OpportunityException {
		Optional<AccountDetails> optionalAccountDetails = mockAccountDetails();
		doReturn(optionalAccountDetails).when(opportunityHelper).getAccountDetails(any(QuoteAndIssueUpdate.class));
		Opportunity opp = mockOpportunity(10);
		mockBook("State Auto Book Migration");
		QuoteAndIssueUpdate qniData = mockQniData();
		// Act - save safeco billing data
		opportunityHelper.saveSafecoBillingDetails(opp, qniData);
		// Assert - Should call payment service for non-state auto
		then(opportunityHelper).should().getAccountDetails(any(QuoteAndIssueUpdate.class));
		verify(paymentServiceHelper).saveSafecoBillingData(any(SafecoBillingDataDTO.class));
	}

	@Test
	void testFailSafecoBillingDetailsFromQnI() throws BookTransferException, OpportunityException {
		Optional<AccountDetails> optionalAccountDetails = mockAccountDetails();
		doReturn(optionalAccountDetails).when(opportunityHelper).getAccountDetails(any(QuoteAndIssueUpdate.class));
		Opportunity opp = mockOpportunity(10);
		mockBook("State Auto Book Migration");
		when(paymentServiceHelper.saveSafecoBillingData(any(SafecoBillingDataDTO.class))).thenThrow(new RuntimeException("expected"));
		QuoteAndIssueUpdate qniData = mockQniData();
		// Act - save safeco billing data
		OpportunityException exception = assertThrows(OpportunityException.class, () -> opportunityHelper.saveSafecoBillingDetails(opp, qniData));
		// Assert - Should throw exception when payment service called
		assertEquals("Failed to save safeco billing account details", exception.getMessage());
		then(opportunityHelper).should().getAccountDetails(any(QuoteAndIssueUpdate.class));
		verify(paymentServiceHelper, times(3)).saveSafecoBillingData(any(SafecoBillingDataDTO.class));
	}

	@Test
	void testInjectPaymentDataForNonStateAutoBook() throws BookTransferException {
		Opportunity opp = mockOpportunity(3);
		mockBook("test migration");
		opportunityHelper.addPaymentDataToOpp(opp);
		verify(paymentServiceHelper, times(0)).getPaymentInfoUsingBillingAccountNumber(anyString());
	}

	@Test
	void testInjectPaymentDataForStateAutoBook_PaymentDataAndSafecoBillingDataExists() throws BookTransferException, ParserConfigurationException, IOException, SAXException, XPathExpressionException {
		String instrumentId = "1234567";
		String insuredAccountNumber = "876543";
		String tokenizedAccountNumber = "214356";
		String routingNumber = "132142";
		String accountHolderName = "test act";
		String accountType = "CK";
		String last4 = "xxxxx4567";
		String lob = "Auto";
		String safecoBillingAccountNumber = "3257568";
		String policyNumber = "X3345478";

		Opportunity opp = mockOpportunity(3);
		opp.setBillingAccountNumber(insuredAccountNumber);
		mockBook("State Auto Book Migration");

		when(paymentServiceHelper.getPaymentInfoUsingBillingAccountNumber(anyString()))
				.thenReturn(BillingDetails.builder()
						.paymentData(PaymentDataDTO.builder()
								.instrumentId(instrumentId)
								.insuredAccountNumber(insuredAccountNumber)
								.tokenizedAccountNumber(tokenizedAccountNumber)
								.routingNumber(routingNumber)
								.accountHolderName(accountHolderName)
								.accountType(accountType)
								.last4(last4)
								.build())
						.safecoBillingDetails(List.of(SafecoBillingDataDTO.builder()
										.lob(lob)
										.priorCarrierBillingAccountNumber(insuredAccountNumber)
										.safecoBillingAccountNumber(safecoBillingAccountNumber)
										.safecoPolicyNumber(policyNumber)
								.build()))
						.build());

		opportunityHelper.addPaymentDataToOpp(opp);

		Document oppDataXmlDoc = XmlHelper.getDocument(opp.getData());
		//Assert Payment data
		assertPaymentData(instrumentId, tokenizedAccountNumber, routingNumber, accountHolderName, accountType, last4, oppDataXmlDoc);
		//Assert Safeco Billing Data
		NodeList safecoBillingNodes = XmlHelper.getNodeList(oppDataXmlDoc, "//SafecoBillingAccountNumber");
		assertEquals(1, safecoBillingNodes.getLength());
		assertSafecoBillingData(0, lob, safecoBillingAccountNumber, policyNumber, safecoBillingNodes);
		verify(paymentServiceHelper).getPaymentInfoUsingBillingAccountNumber(anyString());
	}

	@Test
	void testInjectPaymentDataForStateAutoBook_PaymentDataAndSafecoBillingDataDoesntExists() throws BookTransferException, ParserConfigurationException, IOException, SAXException, XPathExpressionException {
		String insuredAccountNumber = "876543";

		Opportunity opp = mockOpportunity(3);
		opp.setBillingAccountNumber(insuredAccountNumber);
		mockBook("State Auto Book Migration");

		when(paymentServiceHelper.getPaymentInfoUsingBillingAccountNumber(anyString()))
				.thenReturn(BillingDetails.builder()
						.paymentData(null)
						.safecoBillingDetails(null)
						.build());

		opportunityHelper.addPaymentDataToOpp(opp);

		Document oppDataXmlDoc = XmlHelper.getDocument(opp.getData());
		//Assert Payment data
		assertPaymentData("", "", "", "", "", "", oppDataXmlDoc);
		//Assert Safeco Billing Data
		NodeList safecoBillingNodes = XmlHelper.getNodeList(oppDataXmlDoc, "//SafecoBillingAccountNumber");
		assertEquals(1, safecoBillingNodes.getLength());
		assertEquals(0, safecoBillingNodes.item(0).getChildNodes().getLength());
		verify(paymentServiceHelper).getPaymentInfoUsingBillingAccountNumber(anyString());
	}

	@Test
	void testInjectPaymentDataForStateAutoBook_PaymentDataExistsButSafecoBillingDataDoesntExists() throws BookTransferException, ParserConfigurationException, IOException, SAXException, XPathExpressionException {
		String instrumentId = "1234567";
		String insuredAccountNumber = "876543";
		String tokenizedAccountNumber = "214356";
		String routingNumber = "132142";
		String accountHolderName = "test act";
		String accountType = "CK";
		String last4 = "xxxxx4567";

		Opportunity opp = mockOpportunity(3);
		opp.setBillingAccountNumber(insuredAccountNumber);
		mockBook("State Auto Book Migration");

		when(paymentServiceHelper.getPaymentInfoUsingBillingAccountNumber(anyString()))
				.thenReturn(BillingDetails.builder()
						.paymentData(PaymentDataDTO.builder()
								.instrumentId(instrumentId)
								.insuredAccountNumber(insuredAccountNumber)
								.tokenizedAccountNumber(tokenizedAccountNumber)
								.routingNumber(routingNumber)
								.accountHolderName(accountHolderName)
								.accountType(accountType)
								.last4(last4)
								.build())
						.safecoBillingDetails(null)
						.build());

		opportunityHelper.addPaymentDataToOpp(opp);

		Document oppDataXmlDoc = XmlHelper.getDocument(opp.getData());
		//Assert Payment data
		assertPaymentData(instrumentId, tokenizedAccountNumber, routingNumber, accountHolderName, accountType, last4, oppDataXmlDoc);
		//Assert Safeco Billing Data
		NodeList safecoBillingNodes = XmlHelper.getNodeList(oppDataXmlDoc, "//SafecoBillingAccountNumber");
		assertEquals(1, safecoBillingNodes.getLength());
		assertEquals(0, safecoBillingNodes.item(0).getChildNodes().getLength());
		verify(paymentServiceHelper).getPaymentInfoUsingBillingAccountNumber(anyString());
	}

	@Test
	void testInjectPaymentDataForStateAutoBook_PaymentDataDoesntExistsButSafecoBillingDataExists() throws BookTransferException, ParserConfigurationException, IOException, SAXException, XPathExpressionException {
		String insuredAccountNumber = "876543";
		String lob = "Auto";
		String safecoBillingAccountNumber = "3257568";
		String policyNumber = "X3345478";

		Opportunity opp = mockOpportunity(3);
		opp.setBillingAccountNumber(insuredAccountNumber);
		mockBook("State Auto Book Migration");

		when(paymentServiceHelper.getPaymentInfoUsingBillingAccountNumber(anyString()))
				.thenReturn(BillingDetails.builder()
						.paymentData(null)
						.safecoBillingDetails(List.of(SafecoBillingDataDTO.builder()
								.lob(lob)
								.priorCarrierBillingAccountNumber(insuredAccountNumber)
								.safecoBillingAccountNumber(safecoBillingAccountNumber)
								.safecoPolicyNumber(policyNumber)
								.build()))
						.build());

		opportunityHelper.addPaymentDataToOpp(opp);

		Document oppDataXmlDoc = XmlHelper.getDocument(opp.getData());
		//Assert Payment data
		assertPaymentData("", "", "", "", "", "", oppDataXmlDoc);
		//Assert Safeco Billing Data
		NodeList safecoBillingNodes = XmlHelper.getNodeList(oppDataXmlDoc, "//SafecoBillingAccountNumber");
		assertEquals(1, safecoBillingNodes.getLength());
		assertSafecoBillingData(0, lob, safecoBillingAccountNumber, policyNumber, safecoBillingNodes);
		verify(paymentServiceHelper).getPaymentInfoUsingBillingAccountNumber(anyString());
	}

	@Test
	void testInjectPaymentDataForStateAutoBook_MultipleSafecoBillingDetails() throws BookTransferException, ParserConfigurationException, IOException, SAXException, XPathExpressionException {
		String insuredAccountNumber = "876543";
		String lobAuto = "Auto";
		String lobHome = "Home";
		String safecoBillingAccountNumber = "3257568";
		String autoPolicyNumber = "X3345478";
		String homePolicyNumber = "OK3545465";

		Opportunity opp = mockOpportunity(3);
		opp.setBillingAccountNumber(insuredAccountNumber);
		mockBook("State Auto Book Migration");

		when(paymentServiceHelper.getPaymentInfoUsingBillingAccountNumber(anyString()))
				.thenReturn(BillingDetails.builder()
						.safecoBillingDetails(List.of(SafecoBillingDataDTO.builder()
								.lob(lobAuto)
								.priorCarrierBillingAccountNumber(insuredAccountNumber)
								.safecoBillingAccountNumber(safecoBillingAccountNumber)
								.safecoPolicyNumber(autoPolicyNumber)
								.build(),
							SafecoBillingDataDTO.builder()
								.lob(lobHome)
								.priorCarrierBillingAccountNumber(insuredAccountNumber)
								.safecoBillingAccountNumber(safecoBillingAccountNumber)
								.safecoPolicyNumber(homePolicyNumber)
								.build()))
						.build());

		opportunityHelper.addPaymentDataToOpp(opp);

		Document oppDataXmlDoc = XmlHelper.getDocument(opp.getData());
		//Assert Safeco Billing Data
		NodeList safecoBillingNodes = XmlHelper.getNodeList(oppDataXmlDoc, "//SafecoBillingAccountNumber");
		assertEquals(2, safecoBillingNodes.getLength());
		assertSafecoBillingData(0, lobHome, safecoBillingAccountNumber, homePolicyNumber, safecoBillingNodes);
		assertSafecoBillingData(1, lobAuto, safecoBillingAccountNumber, autoPolicyNumber, safecoBillingNodes);
		verify(paymentServiceHelper).getPaymentInfoUsingBillingAccountNumber(anyString());
	}

	@Test
	void testEcliqAccountNumberNotInjectedForPL() throws OpportunitySensitiveDataException, OpportunityException, BookTransferException {
		// Arrange
		when(bookTransferService.findByBookTransferId(anyInt())).thenReturn(new BookTransferDTO());
		when(opportunityRepoHelper.getDataXmlForOppIds(anySet())).thenReturn(List.of(OpportunityXmlData
			.builder()
			.opportunityId(1)
			.lineType(LineType.Personal)
			.xmlData("<ACORD><InsuranceSvcRq><PersAutoPolicyQuoteInqRq><PersPolicy><Producer><ProducerInfo><ContractNumber/></ProducerInfo></Producer></PersPolicy></PersAutoPolicyQuoteInqRq></InsuranceSvcRq></ACORD>").build()));
		// Act
		opportunityHelper.getOpportunityXml(1, false);
		// Assert
		verify(customerAccountHelper, times(0)).getEcliqAccountNumber(anyString());
	}

	@Test
	void testEcliqAccountNumberInjectedForBL() throws OpportunitySensitiveDataException, OpportunityException, BookTransferException {
		// Arrange
		when(bookTransferService.findByBookTransferId(anyInt())).thenReturn(new BookTransferDTO());
		when(customerAccountHelper.getEcliqAccountNumber(anyString())).thenReturn("E123456");
		when(opportunityRepoHelper.getDataXmlForOppIds(anySet())).thenReturn(List.of(OpportunityXmlData
			.builder()
			.opportunityId(1)
			.lineType(LineType.Business)
			.xmlData("<ACORD><InsuranceSvcRq><WorkCompPolicyQuoteInqRq><Policy><PolicyNumber>L45678</PolicyNumber><Producer><ProducerInfo><ContractNumber/></ProducerInfo></Producer></Policy></WorkCompPolicyQuoteInqRq></InsuranceSvcRq></ACORD>").build()));
		// Act
		String response = opportunityHelper.getOpportunityXml(1, false);
		// Assert
		verify(customerAccountHelper, times(1)).getEcliqAccountNumber(anyString());
		assertTrue(response.contains("E123456"));
	}

	@Test
	void testEcliqAccountNumberNotInjectedForBLWhenPolicyNumberNotPresent() throws OpportunitySensitiveDataException, OpportunityException, BookTransferException {
		// Arrange
		when(bookTransferService.findByBookTransferId(anyInt())).thenReturn(new BookTransferDTO());
		when(opportunityRepoHelper.getDataXmlForOppIds(anySet())).thenReturn(List.of(OpportunityXmlData
			.builder()
			.opportunityId(1)
			.lineType(LineType.Business)
			.xmlData("<ACORD><InsuranceSvcRq><WorkCompPolicyQuoteInqRq><Policy><Producer><ProducerInfo><ContractNumber/></ProducerInfo></Producer></Policy></WorkCompPolicyQuoteInqRq></InsuranceSvcRq></ACORD>").build()));
		// Act
		opportunityHelper.getOpportunityXml(1, false);
		// Assert
		verify(customerAccountHelper, times(0)).getEcliqAccountNumber(anyString());
	}

	@Test
	void testMoveTaxIdNodes() throws Exception {
		Document unmaskedDoc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");

		Document maskedDoc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");
		for (Node node : XmlHelper.nodeListToList(XmlHelper.getNodeList(maskedDoc, "//TaxId"))) {
			node.setTextContent("masked TaxId");
		}

		OpportunityHelper.moveTaxIdNodes(maskedDoc, unmaskedDoc);

		for (Node node : XmlHelper.nodeListToList(XmlHelper.getNodeList(unmaskedDoc, "//TaxId"))) {
			assertEquals("masked TaxId", node.getTextContent());
		}
	}

	@Test
	void testAddLobToMasterOppSPQEMeta(){
		int oppId = 12345;
		MasterOppSPQEMeta masterOppSPQEMeta = new MasterOppSPQEMeta(Arrays.asList(oppId));

		OpportunityErrorInfo opportunityErrorInfo = new OpportunityErrorInfo();
		opportunityErrorInfo.setOpportunityId(12345);
		opportunityErrorInfo.setProductLine("AUTOP");

		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(12345);

		when(opportunityRepoHelper.getErrorItemsByOppIds(oppIds)).thenReturn(Arrays.asList(opportunityErrorInfo));

		opportunityHelper.addLobToMasterOppSPQEMeta(masterOppSPQEMeta);

		assertEquals("AUTOP", masterOppSPQEMeta.getPreviousOpportunities().get(0).getLineOfBusiness());
		assertEquals(12345, masterOppSPQEMeta.getPreviousOpportunities().get(0).getOpportunityId());
	}

	@Test
	void testUpdateQRIForStatusChange() {
		opportunityHelper.updateQRIForStatusChange(List.of(1,2), 4);
		verify(quoteReportItemHelper).updateQuoteReportItemForStatus(any(), anyString());
	}

	@Test
	void testUpdateQRIForStatusChangeInvalidStatus() {
		opportunityHelper.updateQRIForStatusChange(List.of(1,2), 14);
		verify(quoteReportItemHelper, times(0)).updateQuoteReportItemForStatus(any(), anyString());
	}

	private static void assertSafecoBillingData(int index, String lob, String safecoBillingAccountNumber, String policyNumber, NodeList safecoBillingNodes) throws XPathExpressionException {
		assertEquals(lob, XmlHelper.nullSafeExtractFromNode(safecoBillingNodes.item(index), "LOBCd"));
		assertEquals(policyNumber, XmlHelper.nullSafeExtractFromNode(safecoBillingNodes.item(index), "PolicyNumber"));
		assertEquals(safecoBillingAccountNumber, XmlHelper.nullSafeExtractFromNode(safecoBillingNodes.item(index), "com.Safeco_BillingAccountNumber"));
	}

	private static void assertPaymentData(String instrumentId, String tokenizedAccountNumber, String routingNumber, String accountHolderName, String accountType, String last4, Document oppDataXmlDoc) throws XPathExpressionException {
		assertEquals(tokenizedAccountNumber, AcordHelper.getTokenizedAccountNumber(oppDataXmlDoc));
		assertEquals(instrumentId, AcordHelper.getInstrumentId(oppDataXmlDoc));
		assertEquals(last4, AcordHelper.getAccountNumberId(oppDataXmlDoc));
		assertEquals(accountType, AcordHelper.getAccountType(oppDataXmlDoc));
		assertEquals(accountHolderName, AcordHelper.getAccountHolderName(oppDataXmlDoc));
		assertEquals(routingNumber, AcordHelper.getRoutingNumber(oppDataXmlDoc));
	}


	private Optional<AccountDetails> mockAccountDetails() {
		AccountDetails accountDetails = new AccountDetails();
		accountDetails.setBillingAccountNumber("testb1");
		accountDetails.setProduct("PRF");
		return Optional.of(accountDetails);
	}

	@NotNull
	private QuoteAndIssueUpdate mockQniData() {
		QuoteAndIssueUpdate qniData = new QuoteAndIssueUpdate();
		qniData.setLineOfBusiness("1");
		return qniData;
	}

	private void mockBook(String nbdRelationship) throws BookTransferException {
		BookTransferDTO book = new BookTransferDTO();
		book.setNBDRelationship(nbdRelationship);
		when(bookTransferService.findByBookTransferId(anyInt())).thenReturn(book);
	}
	private void mockBook(String salesforcecode, int bookTransferId, String nbdRelationship) throws BookTransferException {
		BookTransferDTO book = new BookTransferDTO(salesforcecode, bookTransferId);
		book.setNBDRelationship(nbdRelationship);
		book.setFirstEffectiveDate(new Date());
		when(bookTransferService.findByBookTransferId(anyInt())).thenReturn(book);
	}

	@NotNull
	private Opportunity mockOpportunity(int status) {
		Opportunity opp = new Opportunity();
		opp.setLineType(LineType.Personal);
		opp.setStatus(status);
		opp.setData("<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy><PaymentOption>" +
		"</PaymentOption></PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>");
		return opp;
	}
}

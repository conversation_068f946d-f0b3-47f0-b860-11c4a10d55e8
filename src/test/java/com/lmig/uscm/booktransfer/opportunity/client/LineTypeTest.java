package com.lmig.uscm.booktransfer.opportunity.client;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.Assertions.assertThat;

class LineTypeTest {

    @ParameterizedTest
    @CsvSource({"PL, Personal",
                "Personal, Personal",
                "BL, Business",
                "Business, Business",
                "BuSiness, Business",
                ", All",
                "null, All"
                })
    void determineLineTypeFromRawValue(String input, LineType expected) {
        LineType lineType = LineType.determineLineType(input);
        assertThat(lineType).isEqualTo(expected);
    }

    @Test
    void determineLineTypeFromXmlForPersonalLines() throws OpportunityException {
        String data = "<ACORD><InsuranceSvcRq><PersAutoLineBusinessRq><PersPolicy></PersPolicy></PersAutoLineBusinessRq></InsuranceSvcRq></ACORD>";
        assertThat(LineType.determineLineTypeFromXml(data)).isEqualTo(LineType.Personal);
    }

    @Test
    void determineLineTypeFromXmlForBusinessLines() throws OpportunityException {
        String data = "<ACORD><InsuranceSvcRq><WorkCompPolicyQuoteInqRq><CommlPolicy></CommlPolicy></WorkCompPolicyQuoteInqRq></InsuranceSvcRq></ACORD>";
        assertThat(LineType.determineLineTypeFromXml(data)).isEqualTo(LineType.Business);
    }
}
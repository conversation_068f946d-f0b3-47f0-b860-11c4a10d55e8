package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger;

import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.config.BookTransferUrlProvider;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.config.MockReposTestConfig;
import com.lmig.uscm.booktransfer.opportunity.domain.BookTransferConversion;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.exceptions.FieldChangerException;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeField;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.opportunity.services.UploadEventService;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

@SpringBootTest(classes = {MockReposTestConfig.class})
class UpdateBookHelperTest {

	@Autowired
	private UploadEventService uploadEventService;

	/**
	 * Given we have a book that exists with a new upload event And we have the
	 * field value we want to change it too and the opp and qri we are changing When
	 * we update the values Then it will update the values and tell us it did not
	 * fail
	 */
	@Test
	void testUpdateOpportunityAndQuoteReportItemForMoveBook() {
		Map<String, BookTransferConversion> books = new HashMap<>();
		BookTransferDTO bookTransfer = new BookTransferDTO();
		bookTransfer.setSfdcid(12345);
		bookTransfer.setBookTransferID(1);
		bookTransfer.setSubCode("12345678");

		BookTransferConversion bookTransferConversion = BookTransferConversion.builder()
				.bookTransferID(bookTransfer.getBookTransferID())
				.subcode(bookTransfer.getSubCode())
				.sfdcid(bookTransfer.getSfdcid())
				.nBDRelationship("nbd")
				.build();
		bookTransferConversion.setUploadEventId(99);
		books.put("12345", bookTransferConversion);
		OppChangeField oppChangeField = new OppChangeField(1, null, "12345");

		BookTransferDTO originalBookTransfer = new BookTransferDTO();
		originalBookTransfer.setBookTransferID(12);
		originalBookTransfer.setSfdcid(12346);

		Opportunity opp = new Opportunity();
		opp.setOpportunityId(1);
		opp.setBookTransferID(originalBookTransfer.getBookTransferID());
		QuoteReportItemLegacy quoteItem = new QuoteReportItemLegacy();
		quoteItem.setQuoteSalesforceID("1");
		UpdateBookHelper updateBookHelper = new UpdateBookHelper(Lists.list(opp), Lists.list(quoteItem));
		updateBookHelper.setBookTransferAndUploadMap(books);
		updateBookHelper.setSalesForceBtIdToBooksWithUploadEvent(
				Collections.singletonMap(originalBookTransfer.getBookTransferID(), originalBookTransfer));
		OppChangeFieldResult res = updateBookHelper.buildInitialResult(oppChangeField);

		assertFalse(res.didFail());
		assertEquals(opp, res.getOpportunity());
		assertEquals("12346", res.getOriginalValue());
	}

	/**
	 * Given we don't have an existing book. And we have the field value we want to
	 * change it too and the opp and qri we are changing When we update the values
	 * Then it will update the values and tell us it did not fail
	 */
	@Test
	void testBuildingBookTransferAndUploadEventsForMoveBook() {
		List<OppChangeField> oppChangeFields = new ArrayList<>();
		oppChangeFields.add(new OppChangeField(1, "SFDCID", "12345"));
		oppChangeFields.add(new OppChangeField(2, "SFDCID", "12345"));
		oppChangeFields.add(new OppChangeField(3, "SFDCID", "21222"));
		oppChangeFields.add(new OppChangeField(3, "OtherField", "21222"));
		UpdateBookHelper updateBookHelper = new UpdateBookHelper(new ArrayList<>(), new ArrayList<>());

		BookTransferService bookTransferService = new BookTransferService(null,
				new BookTransferUrlProvider("https://booktransfer.com")) {

			@Override
			public BookTransferDTO getTopBySFDCIDOrderByBookTransferID(int sfdcid) {
				BookTransferDTO bookTransfer = new BookTransferDTO();
				bookTransfer.setSfdcid(sfdcid);
				bookTransfer.setBookTransferID(sfdcid);
				return bookTransfer;
			}
		};

		updateBookHelper.setDatabaseHelpersForMoveBook(bookTransferService, uploadEventService);
		updateBookHelper.setBookTransferAndUploadMap(oppChangeFields);

		Map<String, BookTransferConversion> mapOfBookTransferConversion = updateBookHelper
				.getSalesForceBtIdToBooksWithUploadEvent();
		assertEquals(2, mapOfBookTransferConversion.size());
		assertEquals(21222, mapOfBookTransferConversion.get("21222").getSfdcid());
		assertEquals(12345, mapOfBookTransferConversion.get("12345").getSfdcid());
		assertEquals(1299, mapOfBookTransferConversion.get("12345").getUploadEventId());
	}

	/**
	 * Given we have a list of opportunities that we will need to know the book of
	 * for reporting Then it will give us those book transfers we can use to report
	 */
	@Test
	void testGettingOldBookTransfersMapAsWell() {
		List<OppChangeField> oppChangeFields = new ArrayList<>();
		oppChangeFields.add(new OppChangeField(1, "SFDCID", "12345"));
		Opportunity opportunity = new Opportunity();
		opportunity.setBookTransferID(12345);
		opportunity.setOpportunityId(1);
		oppChangeFields.add(new OppChangeField(2, "SFDCID", "12345"));
		Opportunity opportunity2 = new Opportunity();
		opportunity2.setBookTransferID(1);
		opportunity2.setOpportunityId(2);

		oppChangeFields.add(new OppChangeField(5, "OtherValue", "21222"));
		Opportunity opportunity3 = new Opportunity();
		opportunity3.setBookTransferID(4);
		opportunity3.setOpportunityId(5);

		UpdateBookHelper updateBookHelper = new UpdateBookHelper(Lists.list(opportunity, opportunity2, opportunity3),
				new ArrayList<>());

		BookTransferService bookTransferService = new BookTransferService(null,
				new BookTransferUrlProvider("https://booktransfer.com")) {

			@Override
			public BookTransferDTO getTopBySFDCIDOrderByBookTransferID(int sfdcid) {
				BookTransferDTO bookTransfer = new BookTransferDTO();
				bookTransfer.setSfdcid(sfdcid);
				bookTransfer.setBookTransferID(sfdcid);
				return bookTransfer;
			}

			@Override
			public BookTransferDTO findByBookTransferId(int bookTransferId) {
				BookTransferDTO bookTransfer = new BookTransferDTO();
				bookTransfer.setSfdcid(bookTransferId);
				bookTransfer.setBookTransferID(bookTransferId);
				return bookTransfer;
			}
		};
		updateBookHelper.setDatabaseHelpersForMoveBook(bookTransferService, uploadEventService);
		updateBookHelper.setBookTransferAndUploadMap(oppChangeFields);

		Map<String, BookTransferConversion> mapOfBookTransferConversion = updateBookHelper
				.getSalesForceBtIdToBooksWithUploadEvent();

		// make sure we didn't search for a book we don't need for move books
		assertEquals(1, mapOfBookTransferConversion.size());
		// make sure an upload event id was created for a book that is being uploaded
		assertEquals(12345, mapOfBookTransferConversion.get("12345").getSfdcid());
		assertEquals(1299, mapOfBookTransferConversion.get("12345").getUploadEventId());
		// make sure an upload was not created for a book we do not care about
		assertEquals(2, updateBookHelper.getBooksToMoveFrom().size());

		assertEquals(1, updateBookHelper.getBooksToMoveFrom().get(1).getSfdcid());
		assertEquals(12345, updateBookHelper.getBooksToMoveFrom().get(12345).getSfdcid());
	}

	/**
	 * Given we not have a book exists And we have the field value we want to
	 * change it too and the opp and qri we are changing When we update the values
	 * Then it will update the values and tell us it did not fail
	 */
	@Test
	void testUpdateOpportunityAndQuoteReportItemForMoveBookForNoExistenceBook() {
		OppChangeField oppChangeField = new OppChangeField(1, null, "12345");

		Opportunity opp = new Opportunity();
		opp.setOpportunityId(1);
		QuoteReportItemLegacy quoteItem = new QuoteReportItemLegacy();
		quoteItem.setQuoteSalesforceID("1");
		UpdateBookHelper updateBookHelper = new UpdateBookHelper(Lists.list(opp), Lists.list(quoteItem));
		updateBookHelper.setBookTransferAndUploadMap(new HashMap<>());
		OppChangeFieldResult res = updateBookHelper.buildInitialResult(oppChangeField);

		assertTrue(res.didFail());
		assertEquals(opp, res.getOpportunity());
	}

	/**
	 * Given we have items with a given subcode, booktransfer id, and salesforceId
	 * When I save them Then they should save them at 2k at a time
	 */
	@Test
	void testSavingOpportunitiesAndQuoteReportItemsToDataBase() throws Exception {
		List<QuoteReportItemLegacy> quoteReportItems = new ArrayList<>();
		List<Opportunity> opportunities = new ArrayList<>();
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(10);
		opportunity.setLineType(LineType.Personal);
		Path path = Paths.get("src/test/resources/xml/auto.xml");
		byte[] content = Files.readAllBytes(path);
		opportunity.setData(new String(content));
		opportunities.add(opportunity);
		BookTransferService bookTransferService = new BookTransferService(null,
				new BookTransferUrlProvider("https://booktransfer.com")) {

			@Override
			public BookTransferDTO getTopBySFDCIDOrderByBookTransferID(int sfdcid) {
				BookTransferDTO bookTransfer = new BookTransferDTO();
				bookTransfer.setSfdcid(sfdcid);
				bookTransfer.setBookTransferID(sfdcid);
				bookTransfer.setNBDRelationship("testnbd");
				return bookTransfer;
			}
		};

		UpdateBookHelper updateBookHelper = new UpdateBookHelper(opportunities, quoteReportItems);
		updateBookHelper.setDatabaseHelpersForMoveBook(bookTransferService, uploadEventService);
		OpportunityHelper mockOpportunityHelper = FieldChangerTest.getMockedOpportunityHelper();
		QuoteReportItemHelper mockQuoteReportItemHelper = mock(QuoteReportItemHelper.class);
		OpportunityRepoHelper mockOpportunityRepoHelper = FieldChangerTest.getMockedOpportunityRepoHelper();
		updateBookHelper.setDatabaseHelpers(mockOpportunityHelper, mockQuoteReportItemHelper, mockOpportunityRepoHelper);

		BookTransferDTO bookTransfer = new BookTransferDTO();
		bookTransfer.setBookTransferID(1);
		bookTransfer.setSubCode("12345678");
		bookTransfer.setSfdcid(12345);
		bookTransfer.setNBDRelationship("testnbd");
		BookTransferConversion bookTransferConversion = BookTransferConversion.builder()
				.bookTransferID(bookTransfer.getBookTransferID())
				.subcode(bookTransfer.getSubCode())
				.sfdcid(bookTransfer.getSfdcid())
				.nBDRelationship(bookTransfer.getNBDRelationship())
				.build();
		bookTransferConversion.setUploadEventId(99);

		updateBookHelper.setBookTransferAndUploadMap(
				Collections.singletonMap(String.valueOf(bookTransferConversion.getSfdcid()), bookTransferConversion));

		updateBookHelper.saveFieldUpdate(opportunities, String.valueOf(bookTransferConversion.getSfdcid()));
		for(Opportunity opp : opportunities) {
			Document d = XmlHelper.getDocument(opp.getData());
			Node n = XmlHelper.getNodeFromDoc("//NBDRelationship", d);
			assertEquals(bookTransferConversion.getNBDRelationship(),  n.getTextContent());
		}
		verify(mockOpportunityRepoHelper).saveAll(opportunities);
		verify(mockQuoteReportItemHelper).updateQuoteReportItemForMoveBooks(List.of("10"), 12345, "12345678");
	}

	@Test
	void gettingOpportunityByOppChangeFieldExists() throws FieldChangerException {
		List<Opportunity> opps = new ArrayList<>();
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(1);
		opps.add(opportunity);
		Opportunity opportunity2 = new Opportunity();
		opportunity.setOpportunityId(2);
		opps.add(opportunity2);

		UpdateHelper updateHelper = new UpdateBookHelper(opps, new ArrayList<>());

		Opportunity opp = updateHelper.getOpportunity(new OppChangeField(opportunity.getOpportunityId(), null, null));

		assertEquals(opportunity, opp);
	}

	@Test
	void gettingOpportunityByOppChangeFieldDoesNotExists() {
		UpdateHelper updateHelper = new UpdateBookHelper(new ArrayList<>(), new ArrayList<>());
		assertThrows(
				FieldChangerException.class,
				() -> updateHelper.getOpportunity(new OppChangeField(1, null, null)));

	}
}

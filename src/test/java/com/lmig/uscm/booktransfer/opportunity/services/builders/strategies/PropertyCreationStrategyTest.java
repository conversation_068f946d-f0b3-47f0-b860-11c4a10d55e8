package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.PersonalAcordXPaths;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

public class PropertyCreationStrategyTest extends CreationStrategyTest {

    PropertyCreationStrategy creationStrategy = new PropertyCreationStrategy();

    @Test
    public void testPropertyCreationPolicy_SplitsDfireIntoThreeDocuments() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest propertyRequest = buildCreationRequest("src/test/resources/xml/DwellFire.xml");
        CreationStrategyBundle propertyBundle = buildCreationBundle(propertyRequest);
        List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(propertyBundle));

        creationStrategy.resolveCreationBundles(bundles, response);

        assertEquals(2, bundles.get(0).getRequests().size());
        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
        assertFalse(bundles.get(0).getRequests().get(1).isMasterOpp());
    }

    @Test
    public void testPropertyCreationPolicy_DoesNotSplitOnlyOneDwelling() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest propertyRequest = buildCreationRequest("src/test/resources/xml/home.xml");
        CreationStrategyBundle propertyBundle = buildCreationBundle(propertyRequest);
        List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(propertyBundle));

        creationStrategy.resolveCreationBundles(bundles, response);

        assertEquals(1, bundles.get(0).getRequests().size());
        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
    }

    /*
     * The third location node in this Acord file has no corresponding Dwell node so it will not split
     * into its own opportunity.
     * The fourth location node in this Acord file has a corresponding Dwell node but the Dwell node
     * must also contain a coverage with a dwell type in order for a successful split to happen. Since this
     * dwell node does not have one, it will not split into its own opportunity.
     */
    @Test
    public void testPropertyCreationPolicy_DoesNotSplitLocationWithNoCorrespondingDwellings() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest propertyRequest = buildCreationRequest("src/test/resources/xml/DwellFire2.xml");
        CreationStrategyBundle propertyBundle = buildCreationBundle(propertyRequest);
        List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(propertyBundle));

        creationStrategy.resolveCreationBundles(bundles, response);

        assertEquals(2, bundles.get(0).getRequests().size());
        assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
    }

    @Test
    public void testPropertyCreationPolicy_CalculatesDwellingCoverageAfterSplit() throws Exception {
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest propertyRequest = buildCreationRequest("src/test/resources/xml/DwellFire2.xml");
        CreationStrategyBundle propertyBundle = buildCreationBundle(propertyRequest);

        List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(propertyBundle));
        double totalDwellCoverageBeforeSplit = AcordHelper.getTotalDwellCoverageAmount(bundles.get(0).getRequests().get(0).getUploadedACORD());

        creationStrategy.resolveCreationBundles(bundles, response);

        // Assert that the new total dwell premium in the first document is the old amount minus the sum of dwelling premiums in the newly split document
        double totalDwellCoverageAfterSplit = AcordHelper.getTotalDwellCoverageAmount(bundles.get(0).getRequests().get(0).getUploadedACORD());
        double totalDwellCoverageInNewlySplitDocument = AcordHelper.getTotalDwellCoverageAmount(bundles.get(0).getRequests().get(1).getUploadedACORD());
        assertEquals((int) totalDwellCoverageAfterSplit, (int) (totalDwellCoverageBeforeSplit - totalDwellCoverageInNewlySplitDocument));
        assertEquals(394, (int) totalDwellCoverageInNewlySplitDocument); // new coverage sum in split opp
    }

    @Test
    public void testPropertyCreationPolicyMultipleLocationsMultiplePropertySchedule() throws Exception{
        OpportunityCreationResponse response = new OpportunityCreationResponse();
        OpportunityCreationRequest propertyRequest = buildCreationRequest("src/test/resources/xml/HomeMultiLocationAndPropertySchedule.xml");
        CreationStrategyBundle propertyBundle = buildCreationBundle(propertyRequest);
        List<CreationStrategyBundle> bundles = new ArrayList<>(List.of(propertyBundle));

        creationStrategy.resolveCreationBundles(bundles, response);

        assertEquals(25, XmlHelper.getNodeList(bundles.get(0).getRequests().get(0).getUploadedACORD(), PersonalAcordXPaths.PROPERTY_SCHEDULE).getLength());
        assertEquals(2, XmlHelper.getNodeList(bundles.get(0).getRequests().get(1).getUploadedACORD(), PersonalAcordXPaths.PROPERTY_SCHEDULE).getLength());
    }
}

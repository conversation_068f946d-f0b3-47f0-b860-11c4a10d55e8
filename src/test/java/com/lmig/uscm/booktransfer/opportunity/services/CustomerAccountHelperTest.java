package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountAqe;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountServiceResponseData;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountServiceResponseWrapper;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerMetadata;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerPolicy;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.LibertyPolicy;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.PriorPolicy;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.SearchCriteria;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.StartTransactionRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.response.TransactionResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;
import reactor.core.publisher.Mono;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class CustomerAccountHelperTest {

    private CustomerAccountHelper customerAccountHelper;
    private WebClient webClient;
    private WebClient.RequestBodyUriSpec requestBodyUriSpec;
    private WebClient.RequestBodySpec requestBodySpec;
    private WebClient.RequestHeadersSpec requestHeadersSpec;
    private WebClient.ResponseSpec responseSpec;
    private AuditLogHelper auditLogHelper;

    @BeforeEach
    public void setUp() {
        webClient = Mockito.mock(WebClient.class);
        requestBodyUriSpec = Mockito.mock(WebClient.RequestBodyUriSpec.class);
        requestBodySpec = Mockito.mock(WebClient.RequestBodySpec.class);
        requestHeadersSpec = Mockito.mock(WebClient.RequestHeadersSpec.class);
        responseSpec = Mockito.mock(WebClient.ResponseSpec.class);
        auditLogHelper = Mockito.mock(AuditLogHelper.class);
        customerAccountHelper = Mockito.spy(new CustomerAccountHelper(webClient, "", auditLogHelper));

    }

    @Test
    public void testCreateCustomerAccount_V1Acord() throws XPathExpressionException, IOException, ParserConfigurationException, SAXException {
        Opportunity opp = new Opportunity();
        opp.setOpportunityId(1);
        opp.setCustomerName("Test");
        opp.setBusinessType("AUTOB");
        opp.setOppPriorCarrier("ACUITY");
        opp.setEffectiveDate("2023-12-01");
        opp.setPriorCarrierGuid("123");
        opp.setPriorPremium(1.00);
        Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");
        CustomerAccountServiceResponseWrapper expectedResponse = CustomerAccountServiceResponseWrapper.builder()
                .data(List.of(CustomerAccountServiceResponseData.builder().id("123").build())).build();
        doReturn(expectedResponse).when(customerAccountHelper).sendCreateCustomerAccountRequest(any());
        CustomerMetadata customerMetadata = CustomerMetadata.builder()
                .btCode("1")
                .customerName("Test")
                .address1("10 County Road 27")
                .city("Hampton")
                .state("NY")
                .zip("11937")
                .build();
        CustomerAccountRequest expectedRequest = CustomerAccountRequest.builder()
                .searchCriteria(SearchCriteria.builder()
                        .priorCarrierPolicyNumber("********")
                        .btCode("1")
                        .customerMetadata(customerMetadata)
                        .build())
                .customerMetadata(customerMetadata)
                .customerPolicies(List.of(CustomerPolicy.builder()
                        .btCode(1)
                        .emailAddress("")
                        .phoneNumber("(*************")
                        .ratingState("NY")
                        .priorPolicy(PriorPolicy.builder()
                                .lineOfBusiness("AUTOB")
                                .carrier("ACUITY")
                                .policyNumber("123")
                                .build())
                        .libertyPolicy(LibertyPolicy.builder()
                            .effectiveDate(opp.getEffectiveDate())
                            .build())
                        .aqe(CustomerAccountAqe.builder()
                                .priorExpirationDate("2023-12-01")
                                .priorPremium(1.00)
                                .opportunityId(1)
                                .status("0")
                                .businessType("AUTOB")
                                .build())
                        .build()))
                .eventSource("AQE")
                .build();

        customerAccountHelper.createCustomerAccount(opp, document, 1);

        verify(customerAccountHelper, times(1)).sendCreateCustomerAccountRequest(expectedRequest);
    }

    @Test
    public void testCreateCustomerAccount_V2Acord() throws XPathExpressionException, IOException, ParserConfigurationException, SAXException {
        Opportunity opp = new Opportunity();
        opp.setOpportunityId(1);
        opp.setCustomerName("Test");
        opp.setBusinessType("AUTOB");
        opp.setOppPriorCarrier("ACUITY");
        opp.setEffectiveDate("2023-12-01");
        opp.setPriorCarrierGuid("123");
        opp.setPriorPremium(1.00);
        Document document = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/CPKGE2.x.xml");
        CustomerAccountServiceResponseWrapper expectedResponse = CustomerAccountServiceResponseWrapper.builder()
                .data(List.of(CustomerAccountServiceResponseData.builder().id("123").build())).build();
        doReturn(expectedResponse).when(customerAccountHelper).sendCreateCustomerAccountRequest(any());
        TransactionResponse mockTransactionResponse = Mockito.mock(TransactionResponse.class);
        when(auditLogHelper.startTransaction(any())).thenReturn(mockTransactionResponse);

        CustomerMetadata customerMetadata = CustomerMetadata.builder()
                .btCode("1")
                .customerName("Test")
                .address1("40535 Anchor Way")
                .city("Steamboat Springs")
                .state("CO")
                .zip("80487")
                .build();
        CustomerAccountRequest expectedRequest = CustomerAccountRequest.builder()
                .searchCriteria(SearchCriteria.builder()
                        .priorCarrierPolicyNumber("X56288")
                        .btCode("1")
                        .customerMetadata(customerMetadata)
                        .build())
                .customerMetadata(customerMetadata)
                .customerPolicies(List.of(CustomerPolicy.builder()
                        .btCode(1)
                        .emailAddress("<EMAIL>")
                        .phoneNumber("******-8792191")
                        .ratingState("CO")
                        .priorPolicy(PriorPolicy.builder()
                                .lineOfBusiness("AUTOB")
                                .carrier("ACUITY")
                                .policyNumber("123")
                                .build())
                        .libertyPolicy(LibertyPolicy.builder()
                            .effectiveDate(opp.getEffectiveDate())
                            .build())
                        .aqe(CustomerAccountAqe.builder()
                                .opportunityId(1)
                                .priorExpirationDate("2023-12-01")
                                .priorPremium(1.00)
                                .status("0")
                                .businessType("AUTOB")
                                .build())
                        .build()))
                .eventSource("AQE")
                .build();

        customerAccountHelper.createCustomerAccount(opp, document, 1);

        verify(customerAccountHelper, times(1)).sendCreateCustomerAccountRequest(expectedRequest);
        verify(auditLogHelper, times(1)).startTransaction(any(StartTransactionRequest.class));
    }

    @Test
    public void testSendCreateCustomerAccountRequest_RetryOn502() {
        CustomerAccountRequest request = CustomerAccountRequest.builder().build();
        CustomerAccountServiceResponseWrapper expectedResponse = CustomerAccountServiceResponseWrapper.builder().build();

        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(CustomerAccountServiceResponseWrapper.class))
                .thenReturn(Mono.error(new WebClientResponseException(504, "Gateway Timeout", null, null, null)))
                .thenReturn(Mono.error(new WebClientResponseException(504, "Gateway Timeout", null, null, null)))
                .thenReturn(Mono.just(expectedResponse));

        CustomerAccountServiceResponseWrapper response = customerAccountHelper.sendCreateCustomerAccountRequest(request);

        assertNotNull(response);
        verify(webClient, times(3)).post();
        verify(requestBodyUriSpec, times(3)).uri(anyString());
        verify(requestBodySpec, times(3)).bodyValue(any());
        verify(requestHeadersSpec, times(3)).retrieve();
        verify(responseSpec, times(9)).onStatus(any(), any());
        verify(responseSpec, times(3)).bodyToMono(CustomerAccountServiceResponseWrapper.class);
    }
}

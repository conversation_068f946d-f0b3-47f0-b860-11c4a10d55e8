package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger;

import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.config.BookTransferUrlProvider;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.opportunity.Helper.OpportunityTestUtil;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.QuoteReportStatus;
import com.lmig.uscm.booktransfer.opportunity.config.MockReposTestConfig;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.MassUpdateResultsOfFieldChange;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeField;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.opportunity.services.UploadEventService;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.web.client.RestTemplate;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.mock;

@SpringBootTest(classes = MockReposTestConfig.class)
@ExtendWith(MockitoExtension.class)
class FieldChangerTest {


    @Autowired
    private BookTransferService bookTransferService;

	private QuoteReportItemLegacy quoteReportItemLegacyWith(String quoteSalesforceId) {
		QuoteReportItemLegacy tmp = new QuoteReportItemLegacy();
		tmp.setStatus("1");
		tmp.setQuoteSalesforceID(quoteSalesforceId);
		return tmp;
	}

	@Autowired
	private FieldChangeHelper fieldUpdateHelper;

	@Autowired
	private UploadEventService uploadEventService;

	@BeforeEach
	public void setUp() {
		fieldUpdateHelper.setOpportunityRepoHelper(new OpportunityRepoHelper(null, null) {
			@Override
			public List<Opportunity> saveAll(List<Opportunity> opportunities) {
				return new ArrayList<>();
			}

		});

		fieldUpdateHelper.setQuoteReportItemHelper(new QuoteReportItemHelper(null) {
			@Override
			public void updateQuoteReportItems(List<String> itemsToUpdate, QuoteReportStatus status) {
				// do nothing
			}

			@Override
			public void updateQuoteReportItemForMoveBooks(List<String> quoteSalesforceId, Integer SFDCID,
														  String subCode) {
				// do nothing
			}
		});
		RestTemplate rt = mock(RestTemplate.class);
		BookTransferUrlProvider urlProv = new BookTransferUrlProvider("https://booktransfer.com");

		fieldUpdateHelper.setBookTransferService(new BookTransferService(rt, urlProv) {
			@Override
			public BookTransferDTO getTopBySFDCIDOrderByBookTransferID(int sfdcid) {
				BookTransferDTO bookTransfer = new BookTransferDTO();
				bookTransfer.setSfdcid(sfdcid);
				bookTransfer.setBookTransferID(1);
				bookTransfer.setSubCode("12345678");
				bookTransfer.setRampCode("87654321");
				bookTransfer.setNBDRelationship("test");
				return bookTransfer;
			}

			@Override
			public BookTransferDTO findByBookTransferId(int bookTransferId) {
				BookTransferDTO bookTransfer = new BookTransferDTO();
				bookTransfer.setSfdcid(99);
				bookTransfer.setBookTransferID(bookTransferId);
				bookTransfer.setSubCode("12345678");
				bookTransfer.setRampCode("87654321");
				return bookTransfer;
			}
		});

		fieldUpdateHelper.setUploadEventService(uploadEventService);
	}

	@Test
	void updateNonStatusOnUpdateStatus() {
		List<OppChangeFieldResult> ret = setUpAndCallUpdateOppStatus(null, "notStatus");
		assertEquals(1, ret.size());
		assertTrue(ret.get(0).didFail());
		assertEquals(
				"Rule has not been written to update field. Please request to tech team: notStatus",
				ret.get(0).getStatusMessage());

	}

	private List<OppChangeFieldResult> setUpAndCallUpdateOppStatus(OpportunityStatus oppStatus, String fieldName) {
		List<OppChangeField> oppChangeFields = new ArrayList<>();
		int oppId = 12345;
		oppChangeFields.add(new OppChangeField(oppId, fieldName, getStatus(oppStatus)));
		List<Opportunity> opportunities = new ArrayList<>();
		Opportunity opportunity = OpportunityTestUtil.buildOpportunityWithId(oppId);
		opportunities.add(opportunity);
		QuoteReportItemLegacy quoteReportItem = OpportunityTestUtil.createDummyQRI(null, "54321");
		quoteReportItem.setQuoteSalesforceID(String.valueOf(opportunity.getOpportunityId()));
		List<QuoteReportItemLegacy> qris = new ArrayList<>();
		qris.add(quoteReportItem);
		return fieldUpdateHelper.getResultsOfUpdatingOppAndQRI(oppChangeFields, opportunities, qris)
				.getAllOpportunitiesResults();
	}

	private String getStatus(OpportunityStatus oppStatus) {
		if (oppStatus != null) {
			return Integer.toString(oppStatus.getOppStatusCode());
		}
		return "-1";
	}

	@Test
	void updateStatusWithOpportunityAndOpportunityStatusInDifferentOrder() {
		List<OppChangeField> oppChangeFields = new ArrayList<>();
		int oppId = 12345;
		int oppId2 = 12346;
		int oppId3 = 12347;
		oppChangeFields
				.add(new OppChangeField(oppId, "status", getStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN)));
		oppChangeFields
				.add(new OppChangeField(oppId2, "status", getStatus(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED)));
		oppChangeFields.add(new OppChangeField(oppId3, "status",
				Integer.toString(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_IMPORTED.getOppStatusCode())));

		List<Opportunity> opportunities = new ArrayList<>();
		opportunities.add(OpportunityTestUtil.buildOpportunityWithId(oppId3, LineOfBusiness.UMBRP));
		opportunities.add(OpportunityTestUtil.buildOpportunityWithId(oppId2, LineOfBusiness.HOME));
		opportunities.add(OpportunityTestUtil.buildOpportunityWithId(oppId, LineOfBusiness.AUTOP));
		List<QuoteReportItemLegacy> qris = new ArrayList<>();
		qris.add(OpportunityTestUtil.createDummyQRI(QuoteReportStatus.QUOTE_REPORT_UNQUOTED, Integer.toString(oppId2)));
		qris.add(OpportunityTestUtil.createDummyQRI(QuoteReportStatus.QUOTE_REPORT_QUOTED_IMPORTED,
				Integer.toString(oppId3)));
		qris.add(OpportunityTestUtil.createDummyQRI(QuoteReportStatus.QUOTE_REPORT_QUOTE_WITHDRAWN,
				Integer.toString(oppId)));

		MassUpdateResultsOfFieldChange results =
				fieldUpdateHelper.getResultsOfUpdatingOppAndQRI(oppChangeFields, opportunities, qris);
		assertEquals(1, results.getAllOpportunitiesResults("status", "11").size());
		assertFalse(results.getAllOpportunitiesResults("status", "11").get(0).didFail());
		assertEquals(1, results.getAllOpportunitiesResults("status", "2").size());
		assertFalse(results.getAllOpportunitiesResults("status", "2").get(0).didFail());
		assertEquals(1, results.getAllOpportunitiesResults("status", "7").size());
		assertFalse(results.getAllOpportunitiesResults("status", "7").get(0).didFail());
	}

	@Test
	void updateStatusWithMixedLineType() {
		List<OppChangeField> oppChangeFields = new ArrayList<>();
		int oppId = 12345;
		int oppId2 = 12346; // BL
		int oppId3 = 12347; // BL
		oppChangeFields
				.add(new OppChangeField(oppId, "status", getStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN)));
		oppChangeFields
				.add(new OppChangeField(oppId2, "status", getStatus(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED)));
		oppChangeFields.add(new OppChangeField(oppId3, "status",
				Integer.toString(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_IMPORTED.getOppStatusCode())));

		List<Opportunity> opportunities = new ArrayList<>();
		opportunities.add(OpportunityTestUtil.buildOpportunityWithId(oppId3, LineOfBusiness.UMBRP));
		opportunities.add(OpportunityTestUtil.buildBusinessOpportunityWithId(oppId2));
		opportunities.add(OpportunityTestUtil.buildBusinessOpportunityWithId(oppId));
		List<QuoteReportItemLegacy> qris = new ArrayList<>();
		qris.add(OpportunityTestUtil.createDummyQRI(QuoteReportStatus.QUOTE_REPORT_QUOTED_IMPORTED,
				Integer.toString(oppId3)));

		MassUpdateResultsOfFieldChange results =
				fieldUpdateHelper.getResultsOfUpdatingOppAndQRI(oppChangeFields, opportunities, qris);
		assertEquals(1, results.getAllOpportunitiesResults("status", "11").size());
		assertFalse(results.getAllOpportunitiesResults("status", "11").get(0).didFail());
		assertEquals(1, results.getAllOpportunitiesResults("status", "2").size());
		assertFalse(results.getAllOpportunitiesResults("status", "2").get(0).didFail());
		assertEquals(1, results.getAllOpportunitiesResults("status", "7").size());
		assertFalse(results.getAllOpportunitiesResults("status", "7").get(0).didFail());
	}

	@Test
	void updateStatusWithOpportunityAndOpportunityStatusInDifferentOrderCSVBuildOut() {
		List<OppChangeField> oppChangeFields = new ArrayList<>();
		int oppId = 12345;
		int oppId2 = 12346;
		int oppId3 = 12347;
		oppChangeFields
				.add(new OppChangeField(oppId, "status", getStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN)));
		oppChangeFields
				.add(new OppChangeField(oppId2, "status", getStatus(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED)));
		oppChangeFields.add(new OppChangeField(oppId3, "status",
				Integer.toString(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_IMPORTED.getOppStatusCode())));

		List<Opportunity> opportunities = new ArrayList<>();
		opportunities.add(OpportunityTestUtil.buildOpportunityWithId(oppId3, LineOfBusiness.UMBRP));
		opportunities.add(OpportunityTestUtil.buildOpportunityWithId(oppId2, LineOfBusiness.HOME));
		opportunities.add(OpportunityTestUtil.buildOpportunityWithId(oppId, LineOfBusiness.AUTOP));
		List<QuoteReportItemLegacy> qris = new ArrayList<>();
		qris.add(OpportunityTestUtil.createDummyQRI(QuoteReportStatus.QUOTE_REPORT_UNQUOTED, Integer.toString(oppId2)));
		qris.add(OpportunityTestUtil.createDummyQRI(QuoteReportStatus.QUOTE_REPORT_QUOTED_IMPORTED,
				Integer.toString(oppId3)));
		qris.add(OpportunityTestUtil.createDummyQRI(QuoteReportStatus.QUOTE_REPORT_QUOTE_WITHDRAWN,
				Integer.toString(oppId)));

		String file = fieldUpdateHelper.updateOppAndQRI(oppChangeFields, opportunities, qris);
		String expected = "OpportunityID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "12345,status,2,11,YES,Success,\r\n" + "12346,status,2,2,NO,Success,\r\n"
				+ "12347,status,2,7,YES,Success,\r\n" + "\r\n"
				+ "Quote_SalesforceID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "12345,status,Unquoted,11,YES,Success,\r\n" + "12346,status,Unquoted,2,NO,Success,\r\n"
				+ "12347,status,Unquoted,7,YES,Success,\r\n\r\n"
				+ "Total number of opportunities successfully updated,3,\r\n"
				+ "Total number of quote report items successfully updated,3,\r\n";
		assertEquals(expected, file);
	}

	@Test
	void updateStatusWithMixedLineTypeOpportunityCSVBuildOut() {
		List<OppChangeField> oppChangeFields = new ArrayList<>();
		int oppId = 12345;
		int oppId2 = 12346; // BL
		int oppId3 = 12347; // BL
		oppChangeFields
				.add(new OppChangeField(oppId, "status", getStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN)));
		oppChangeFields
				.add(new OppChangeField(oppId2, "status", getStatus(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED)));
		oppChangeFields.add(new OppChangeField(oppId3, "status",
				Integer.toString(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_IMPORTED.getOppStatusCode())));

		List<Opportunity> opportunities = new ArrayList<>();
		opportunities.add(OpportunityTestUtil.buildOpportunityWithId(oppId3, LineOfBusiness.UMBRP));
		opportunities.add(OpportunityTestUtil.buildBusinessOpportunityWithId(oppId2));
		opportunities.add(OpportunityTestUtil.buildBusinessOpportunityWithId(oppId));
		List<QuoteReportItemLegacy> qris = new ArrayList<>();
		qris.add(OpportunityTestUtil.createDummyQRI(QuoteReportStatus.QUOTE_REPORT_QUOTED_IMPORTED,
				Integer.toString(oppId3)));

		String file = fieldUpdateHelper.updateOppAndQRI(oppChangeFields, opportunities, qris);
		String expected = "OpportunityID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "12345,status,2,11,YES,Success,\r\n"
				+ "12346,status,2,2,NO,Success,\r\n"
				+ "12347,status,2,7,YES,Success,\r\n" + "\r\n"
				+ "Quote_SalesforceID,FieldName,Original Value,Updated Value,Value Changed?,Status,\r\n"
				+ "12345,status,Unquoted,11,YES,Success,\r\n"
				+ "12346,status,Unquoted,2,NO,Success,\r\n"
				+ "12347,status,Unquoted,7,YES,Success,\r\n\r\n"
				+ "Total number of opportunities successfully updated,3,\r\n"
				+ "Total number of quote report items successfully updated,3,\r\n";
		assertEquals(expected, file);
	}

	@Test
	void updateStatusWithValidStatus() {
		List<OppChangeFieldResult> results =
				setUpAndCallUpdateOppStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN, "status");

		assertEquals(1, results.size());
		assertEquals("11", results.get(0).getFieldUpdatedValue());
		assertFalse(results.get(0).didFail());
	}

	@Test
	void updateStatusWithInvalidStatus() {
		List<OppChangeFieldResult> results =
				setUpAndCallUpdateOppStatus(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA, "status");

		assertEquals(1, results.size());
		assertTrue(results.get(0).didFail());
		assertEquals("Invalid Opp status to change too: 3", results.get(0).getStatusMessage());

	}

	@Test
	void testUpdateStatusWithNullOpportunityId() {
		List<OppChangeField> changeFields = new ArrayList<>();
		OppChangeField oppChangeField = new OppChangeField(null, "status", "1");
		changeFields.add(oppChangeField);

		List<OppChangeFieldResult> results =
				fieldUpdateHelper.getResultsOfUpdatingOppAndQRI(changeFields, new ArrayList<>(), new ArrayList<>())
						.getAllOpportunitiesResults();

		assertEquals(1, results.size());
		assertTrue(results.get(0).didFail());
		assertEquals("Opp does not exist", results.get(0).getStatusMessage());
	}

	@Test
	void testUpdateStatusWithBadOpportunityId() throws Exception {
		List<OppChangeField> changeFields = new ArrayList<>();
		OppChangeField oppChangeField = new OppChangeField(1, "status", "1");
		changeFields.add(oppChangeField);
		Opportunity opportunity = new Opportunity(2, 1);
		quoteReportItemLegacyWith("2");

		List<OppChangeFieldResult> results = fieldUpdateHelper
				.getResultsOfUpdatingOppAndQRI(changeFields, Lists.list(opportunity), new ArrayList<>())
				.getAllOpportunitiesResults();

		assertEquals(1, results.size());
		assertTrue(results.get(0).didFail());
		assertEquals("Opp does not exist", results.get(0).getStatusMessage());
	}

	@Test
	void testUpdateStatusWithBadQuoteReportItem() throws Exception {
		List<OppChangeField> changeFields = new ArrayList<>();
		OppChangeField oppChangeField = new OppChangeField(1, "status", "1");
		changeFields.add(oppChangeField);

		Opportunity opportunity = new Opportunity(1, 1);
		quoteReportItemLegacyWith("2");

		List<OppChangeFieldResult> results = fieldUpdateHelper
				.getResultsOfUpdatingOppAndQRI(changeFields, Lists.list(opportunity), new ArrayList<>())
				.getAllOpportunitiesResults();

		assertEquals(1, results.size());
		assertTrue(results.get(0).didFail());
		assertEquals("Quote report item does not exist", results.get(0).getStatusMessage());

	}

	@Test
	void testUpdateStatusWithBadStatusCode() throws Exception {
		List<OppChangeField> changeFields = new ArrayList<>();
		OppChangeField oppChangeField = new OppChangeField(1, "status", "DNE");
		changeFields.add(oppChangeField);

		Opportunity opportunity = new Opportunity(1, 1);
		QuoteReportItemLegacy quoteReportItem = quoteReportItemLegacyWith("1");

		List<OppChangeFieldResult> results = fieldUpdateHelper
				.getResultsOfUpdatingOppAndQRI(changeFields, Lists.list(opportunity), Lists.list(quoteReportItem))
				.getAllOpportunitiesResults();

		assertEquals(1, results.size());
		assertTrue(results.get(0).didFail());
		assertEquals("Invalid Opp status to change too: DNE", results.get(0).getStatusMessage());
	}

	/**
	 * Given that I have an invalid opp status in the db When I go to change the status Then it will let me
	 */
	@Test
	void testUpdateStatusWithBadStatusCodeOnOpp() throws Exception {
		List<OppChangeField> changeFields = new ArrayList<>();
		OppChangeField oppChangeField = new OppChangeField(1, "status",
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()));
		changeFields.add(oppChangeField);

		Opportunity opportunity = new Opportunity(1, -1);
		QuoteReportItemLegacy quoteReportItem = quoteReportItemLegacyWith("1");

		List<OppChangeFieldResult> results = fieldUpdateHelper
				.getResultsOfUpdatingOppAndQRI(changeFields, Lists.list(opportunity), Lists.list(quoteReportItem))
				.getAllOpportunitiesResults();

		assertEquals(1, results.size());
		assertFalse(results.get(0).didFail());
		assertEquals("-1", results.get(0).getOriginalValue());
		assertEquals("2", results.get(0).getFieldUpdatedValue());

	}

	@Test
	void testUpdateStatusWithoppThatIsAlreadyIssued() throws Exception {
		List<OppChangeField> changeFields = new ArrayList<>();
		OppChangeField oppChangeField = new OppChangeField(1, "status",
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()));
		changeFields.add(oppChangeField);

		Opportunity opportunity = new Opportunity(1, OpportunityStatus.OPPORTUNITY_STATUS_ISSUED.getOppStatusCode());
		QuoteReportItemLegacy quoteReportItem = quoteReportItemLegacyWith("1");
		List<OppChangeFieldResult> results = fieldUpdateHelper
				.getResultsOfUpdatingOppAndQRI(changeFields, Lists.list(opportunity), Lists.list(quoteReportItem))
				.getAllOpportunitiesResults();

		assertEquals(1, results.size());
		assertTrue(results.get(0).didFail());
		assertEquals("10", results.get(0).getOriginalValue());
		assertEquals("Status cannot be updated if policy has been issued", results.get(0).getStatusMessage());
	}

	@Test
	void updateDBWithUpdatedStatus() throws Exception {
		FieldChangeHelper fieldUpdateHelper = new FieldChangeHelper();
		OpportunityHelper mockedOpportunityHelper = getMockedOpportunityHelper();
		fieldUpdateHelper.setOpportunityHelper(mockedOpportunityHelper);

		OpportunityRepoHelper mockedOpportunityRepoHelper = getMockedOpportunityRepoHelper();
		fieldUpdateHelper.setOpportunityRepoHelper(mockedOpportunityRepoHelper);

		QuoteReportItemHelper mockQuoteReportItemHelper = getMockedQuoteReportItemHelper();
		fieldUpdateHelper.setQuoteReportItemHelper(mockQuoteReportItemHelper);

		Opportunity opp = new Opportunity(1, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		opp.setLineType(LineType.Personal);
		OppChangeField oppChangeField = new OppChangeField(1, "status",
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode()));
		QuoteReportItemLegacy quoteReportItem = quoteReportItemLegacyWith("1");
		List<Opportunity> oppList = Lists.list(opp);
		fieldUpdateHelper.updateOppAndQRI(Lists.list(oppChangeField), oppList, Lists.list(quoteReportItem));
		verify(mockedOpportunityRepoHelper).saveAll(oppList);
		verify(mockQuoteReportItemHelper).updateQuoteReportItems(Lists.list("1"),
				QuoteReportStatus.QUOTE_REPORT_QUOTE_WITHDRAWN);
	}

	private QuoteReportItemHelper getMockedQuoteReportItemHelper() {
		return mock(QuoteReportItemHelper.class);
	}

	protected static OpportunityHelper getMockedOpportunityHelper() {
		return mock(OpportunityHelper.class);
	}

	protected static OpportunityRepoHelper getMockedOpportunityRepoHelper() {
		return mock(OpportunityRepoHelper.class);
	}

	@Test
	void updateDBWithMultipleUpdatedStatusAndMultipleItems() throws Exception {
		FieldChangeHelper fieldUpdateHelper = new FieldChangeHelper();
		OpportunityHelper mockedOpportunityHelper = getMockedOpportunityHelper();
		fieldUpdateHelper.setOpportunityHelper(mockedOpportunityHelper);
		OpportunityRepoHelper mockedOpportunityRepoHelper = getMockedOpportunityRepoHelper();
		fieldUpdateHelper.setOpportunityRepoHelper(mockedOpportunityRepoHelper);
		QuoteReportItemHelper mockQuoteReportItemHelper = getMockedQuoteReportItemHelper();
		fieldUpdateHelper.setQuoteReportItemHelper(mockQuoteReportItemHelper);

		Opportunity oppWithdrawn = new Opportunity(1, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		oppWithdrawn.setLineType(LineType.Personal);
		Opportunity oppWithdrawn2 =
				new Opportunity(2, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		oppWithdrawn2.setLineType(LineType.Personal);
		Opportunity oppUnquoted = new Opportunity(3, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		oppUnquoted.setLineType(LineType.Personal);
		Opportunity oppImported = new Opportunity(4, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		oppImported.setLineType(LineType.Personal);
		OppChangeField oppChangeWithdrawn = new OppChangeField(1, "status",
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode()));
		OppChangeField oppChangeWithdrawn2 = new OppChangeField(2, "status",
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode()));
		OppChangeField oppChangeUnquoted = new OppChangeField(3, "status",
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode()));
		OppChangeField oppChangeImported = new OppChangeField(4, "status",
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_IMPORTED.getOppStatusCode()));
		QuoteReportItemLegacy quoteReportItemWithdrawn = quoteReportItemLegacyWith("1");
		QuoteReportItemLegacy quoteReportItemWithdrawn2 = quoteReportItemLegacyWith("2");
		QuoteReportItemLegacy quoteReportItemUnquoted = quoteReportItemLegacyWith("3");
		QuoteReportItemLegacy quoteReportItemImported = quoteReportItemLegacyWith("4");
		fieldUpdateHelper.updateOppAndQRI(
				Lists.list(oppChangeWithdrawn, oppChangeWithdrawn2, oppChangeImported, oppChangeUnquoted),
				Lists.list(oppWithdrawn, oppWithdrawn2, oppUnquoted, oppImported), Lists.list(quoteReportItemWithdrawn,
						quoteReportItemWithdrawn2, quoteReportItemUnquoted, quoteReportItemImported));

		verify(mockedOpportunityRepoHelper).saveAll(Lists.list(oppWithdrawn, oppWithdrawn2));
		verify(mockedOpportunityRepoHelper).saveAll(Lists.list(oppUnquoted));
		verify(mockedOpportunityRepoHelper).saveAll(Lists.list(oppImported));
		verify(mockQuoteReportItemHelper).updateQuoteReportItems(Lists.list("1", "2"),
				QuoteReportStatus.QUOTE_REPORT_QUOTE_WITHDRAWN);
		verify(mockQuoteReportItemHelper).updateQuoteReportItems(Lists.list("3"),
				QuoteReportStatus.QUOTE_REPORT_UNQUOTED);
		verify(mockQuoteReportItemHelper).updateQuoteReportItems(Lists.list("4"),
				QuoteReportStatus.QUOTE_REPORT_QUOTE_IN_PROGRESS);
	}

	/**
	 * Given that we have a status of issued When we go to update the status of the db Then it will not update it as it
	 * has an invalid status of withdrawn
	 */
	@Test
	void doNotUpdateDBWithUpdatedStatus() throws Exception {
		FieldChangeHelper fieldUpdateHelper = new FieldChangeHelper();

		OpportunityHelper mockedOpportunityHelper = getMockedOpportunityHelper();
		fieldUpdateHelper.setOpportunityHelper(mockedOpportunityHelper);

		OpportunityRepoHelper mockedOpportunityRepoHelper = getMockedOpportunityRepoHelper();
		fieldUpdateHelper.setOpportunityRepoHelper(mockedOpportunityRepoHelper);

		QuoteReportItemHelper mockQuoteReportItemHelper = getMockedQuoteReportItemHelper();
		fieldUpdateHelper.setQuoteReportItemHelper(mockQuoteReportItemHelper);

		Opportunity opp = new Opportunity(1, OpportunityStatus.OPPORTUNITY_STATUS_ISSUED.getOppStatusCode());
		OppChangeField oppChangeField = new OppChangeField(1, "status",
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode()));
		QuoteReportItemLegacy quoteReportItem = quoteReportItemLegacyWith("1");
		List<Opportunity> oppList = Lists.list(opp);
		fieldUpdateHelper.updateOppAndQRI(Lists.list(oppChangeField), oppList, Lists.list(quoteReportItem));

		verify(mockedOpportunityRepoHelper, never()).updateOpportunitiesStatus(oppList,
				OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);
		verify(mockQuoteReportItemHelper, never()).updateQuoteReportItems(Lists.list("1"),
				QuoteReportStatus.QUOTE_REPORT_QUOTE_WITHDRAWN);
	}

	@Test
	void testIfErrorHappensDuringSave() throws Exception {
		FieldChangeHelper fieldUpdateHelper = new FieldChangeHelper();
		fieldUpdateHelper.setOpportunityRepoHelper(new OpportunityRepoHelper(null, null) {
			@Override
			public List<Opportunity> saveAll(List<Opportunity> opportunities) {
				throw new RuntimeException("exception occurred while saving");
			}
		});
		fieldUpdateHelper.setQuoteReportItemHelper(new QuoteReportItemHelper(null));
		Opportunity opp = new Opportunity(1, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		OppChangeField oppChangeField = new OppChangeField(1, "status",
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode()));
		QuoteReportItemLegacy quoteReportItem = quoteReportItemLegacyWith("1");

		List<OppChangeFieldResult> ret = fieldUpdateHelper
				.getResultsOfUpdatingOppAndQRI(Lists.list(oppChangeField), Lists.list(opp), Lists.list(quoteReportItem))
				.getAllOpportunitiesResults();

		assertEquals(1, ret.size());
		assertTrue(ret.get(0).didFail());
		assertEquals("java.lang.RuntimeException: exception occurred while saving", ret.get(0).getStatusMessage());
	}

	/**
	 * This is more of an integration test making sure that updating sFDCID work all the way through
	 */
	@Test
	void updatingForPLOppMoveBook() throws Exception {
		String plFilePath1 = "src/test/resources/xml/auto.xml";
		String plFilePath2 = "src/test/resources/xml/auto2.xml";
		OpportunityHelper mockedOpportunityHelper = getMockedOpportunityHelper();
		fieldUpdateHelper.setOpportunityHelper(mockedOpportunityHelper);
		QuoteReportItemHelper mockQuoteReportItemHelper = getMockedQuoteReportItemHelper();
		fieldUpdateHelper.setQuoteReportItemHelper(mockQuoteReportItemHelper);

		OpportunityRepoHelper mockedOpportunityRepoHelper = getMockedOpportunityRepoHelper();
		fieldUpdateHelper.setOpportunityRepoHelper(mockedOpportunityRepoHelper);

		Opportunity opp = new Opportunity(1, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		opp.setLineType(LineType.Personal);
		opp.setData(setupOpportunityData(plFilePath1));
		Opportunity opp2 = new Opportunity(2, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		opp2.setLineType(LineType.Personal);
		opp2.setData(setupOpportunityData(plFilePath2));
		OppChangeField oppChange = new OppChangeField(1, "sFDCID", "12345");
		OppChangeField oppChange2 = new OppChangeField(2, "sFDCID", "12345");
		QuoteReportItemLegacy quoteReportItem = quoteReportItemLegacyWith("1");
		QuoteReportItemLegacy quoteReportItem2 = quoteReportItemLegacyWith("2");
		fieldUpdateHelper.updateOppAndQRI(Lists.list(oppChange, oppChange2), Lists.list(opp, opp2),
				Lists.list(quoteReportItem, quoteReportItem2));

		verify(mockedOpportunityRepoHelper).saveAll(Lists.list(opp, opp2));
		verify(mockQuoteReportItemHelper).updateQuoteReportItemForMoveBooks(
				Lists.list(String.valueOf(opp.getOpportunityId()), String.valueOf(opp2.getOpportunityId())), 12345,
				"12345678");
	}


	/**
	 * This is more of an integration test making sure that updating sFDCID work all the way through
	 */
	@Test
	void updatingForBLOppMoveBook() throws Exception {
		String blFilePath1 = "src/test/resources/xml/blProp_vertafore.xml";
		String blFilePath2 = "src/test/resources/xml/blCommlAuto_vertafore.xml";
		OpportunityHelper mockedOpportunityHelper = getMockedOpportunityHelper();
		fieldUpdateHelper.setOpportunityHelper(mockedOpportunityHelper);
		QuoteReportItemHelper mockQuoteReportItemHelper = getMockedQuoteReportItemHelper();
		fieldUpdateHelper.setQuoteReportItemHelper(mockQuoteReportItemHelper);

		OpportunityRepoHelper mockedOpportunityRepoHelper = getMockedOpportunityRepoHelper();
		fieldUpdateHelper.setOpportunityRepoHelper(mockedOpportunityRepoHelper);

		Opportunity opp = new Opportunity(1, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		opp.setLineType(LineType.Business);
		opp.setData(setupOpportunityData(blFilePath1));
		Opportunity opp2 = new Opportunity(2, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		opp2.setLineType(LineType.Business);
		opp2.setData(setupOpportunityData(blFilePath2));
		OppChangeField oppChange = new OppChangeField(1, "sFDCID", "12345");
		OppChangeField oppChange2 = new OppChangeField(2, "sFDCID", "12345");
		fieldUpdateHelper.updateOppAndQRI(Lists.list(oppChange, oppChange2), Lists.list(opp, opp2), List.of());

		verify(mockedOpportunityRepoHelper).saveAll(Lists.list(opp, opp2));
		verify(mockQuoteReportItemHelper).updateQuoteReportItemForMoveBooks(
				Lists.list(String.valueOf(opp.getOpportunityId()), String.valueOf(opp2.getOpportunityId())), 12345, "87654321");
	}

	/**
	 * This is more of an integration test making sure that updating sFDCID work all the way through
	 */
	@Test
	void updatingForMixedLineTypeOppMoveBook() throws Exception {
		String plFilePath = "src/test/resources/xml/auto.xml";
		String blFilePath = "src/test/resources/xml/blCommlAuto_vertafore.xml";
		OpportunityHelper mockedOpportunityHelper = getMockedOpportunityHelper();
		fieldUpdateHelper.setOpportunityHelper(mockedOpportunityHelper);
		QuoteReportItemHelper mockQuoteReportItemHelper = getMockedQuoteReportItemHelper();
		fieldUpdateHelper.setQuoteReportItemHelper(mockQuoteReportItemHelper);

		OpportunityRepoHelper mockedOpportunityRepoHelper = getMockedOpportunityRepoHelper();
		fieldUpdateHelper.setOpportunityRepoHelper(mockedOpportunityRepoHelper);

		Opportunity opp = new Opportunity(1, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		opp.setLineType(LineType.Personal);
		opp.setData(setupOpportunityData(plFilePath));
		Opportunity opp2 = new Opportunity(2, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		opp2.setLineType(LineType.Business);
		opp2.setData(setupOpportunityData(blFilePath));
		OppChangeField oppChange = new OppChangeField(1, "sFDCID", "12345");
		OppChangeField oppChange2 = new OppChangeField(2, "sFDCID", "12345");
		QuoteReportItemLegacy quoteReportItem = quoteReportItemLegacyWith("1");
		fieldUpdateHelper.updateOppAndQRI(Lists.list(oppChange, oppChange2), Lists.list(opp, opp2),
				Lists.list(quoteReportItem));

		verify(mockedOpportunityRepoHelper).saveAll(Lists.list(opp, opp2));
		verify(mockQuoteReportItemHelper).updateQuoteReportItemForMoveBooks(
				Lists.list(String.valueOf(opp.getOpportunityId())), 12345, "12345678");

		verify(mockQuoteReportItemHelper).updateQuoteReportItemForMoveBooks(
				Lists.list(String.valueOf(opp2.getOpportunityId())), 12345, "87654321");
	}

	/**
	 * This tests to make sure that our input reader will work for both change book and change opp status, both at the
	 * same time.
	 */
	@Test
	void updatingForMoveBookAndStatus() throws Exception {
		String plFilePath1 = "src/test/resources/xml/auto.xml";
		OpportunityHelper mockedOpportunityHelper = getMockedOpportunityHelper();
		fieldUpdateHelper.setOpportunityHelper(mockedOpportunityHelper);
		QuoteReportItemHelper mockQuoteReportItemHelper = getMockedQuoteReportItemHelper();
		fieldUpdateHelper.setQuoteReportItemHelper(mockQuoteReportItemHelper);

		OpportunityRepoHelper mockedOpportunityRepoHelper = getMockedOpportunityRepoHelper();
		fieldUpdateHelper.setOpportunityRepoHelper(mockedOpportunityRepoHelper);

		Opportunity oppToHaveStatusChange =
				new Opportunity(1, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		oppToHaveStatusChange.setLineType(LineType.Personal);
		Opportunity oppToHaveBookChange =
				new Opportunity(2, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		oppToHaveBookChange.setLineType(LineType.Personal);
		oppToHaveStatusChange.setData(setupOpportunityData(plFilePath1));
		OppChangeField oppStatusChange = new OppChangeField(1, "sFDCID", "12345");
		OppChangeField oppBookChange = new OppChangeField(2, "status", "11");
		QuoteReportItemLegacy quoteReportItem = quoteReportItemLegacyWith("1");
		QuoteReportItemLegacy quoteReportItem2 = quoteReportItemLegacyWith("2");
		fieldUpdateHelper.updateOppAndQRI(Lists.list(oppStatusChange, oppBookChange),
				Lists.list(oppToHaveStatusChange, oppToHaveBookChange), Lists.list(quoteReportItem, quoteReportItem2));

		verify(mockedOpportunityRepoHelper).saveAll(Lists.list(oppToHaveStatusChange));
		verify(mockQuoteReportItemHelper).updateQuoteReportItemForMoveBooks(
				Lists.list(String.valueOf(oppToHaveStatusChange.getOpportunityId())), 12345, "12345678");
		verify(mockedOpportunityRepoHelper).saveAll(Lists.list(oppToHaveBookChange));
		verify(mockQuoteReportItemHelper).updateQuoteReportItems(
				Lists.list(String.valueOf(oppToHaveBookChange.getOpportunityId())),
				QuoteReportStatus.QUOTE_REPORT_QUOTE_WITHDRAWN);
	}

	@Test
	void updateDBWithHeritageStatus() throws Exception {
		FieldChangeHelper fieldUpdateHelper = new FieldChangeHelper();
		OpportunityHelper mockedOpportunityHelper = getMockedOpportunityHelper();
		fieldUpdateHelper.setOpportunityHelper(mockedOpportunityHelper);
		QuoteReportItemHelper mockQuoteReportItemHelper = getMockedQuoteReportItemHelper();
		fieldUpdateHelper.setQuoteReportItemHelper(mockQuoteReportItemHelper);

		OpportunityRepoHelper mockedOpportunityRepoHelper = getMockedOpportunityRepoHelper();
		fieldUpdateHelper.setOpportunityRepoHelper(mockedOpportunityRepoHelper);

		Opportunity opp = new Opportunity(1, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
		opp.setLineType(LineType.Personal);
		OppChangeField oppChangeField = new OppChangeField(1, "status",
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_HERITAGE.getOppStatusCode()));
		QuoteReportItemLegacy quoteReportItem = quoteReportItemLegacyWith("1");
		List<Opportunity> oppList = Lists.list(opp);
		fieldUpdateHelper.updateOppAndQRI(Lists.list(oppChangeField), oppList, Lists.list(quoteReportItem));
		verify(mockedOpportunityRepoHelper).saveAll(oppList);
		verify(mockQuoteReportItemHelper).updateQuoteReportItems(Lists.list("1"),
				QuoteReportStatus.QUOTE_REPORT_HERITAGE);
	}

	private String setupOpportunityData(String filePath) throws IOException, ParserConfigurationException, SAXException {
		Path path = Paths.get(filePath);
		byte[] content = Files.readAllBytes(path);
		return new String(content);
	}

}

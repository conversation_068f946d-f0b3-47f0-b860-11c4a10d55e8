package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.opportunity.Helper.OpportunityTestUtil;
import com.lmig.uscm.booktransfer.opportunity.config.UnitTestConfig;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJpaRepository;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.DownloadOpportunityRowMapper;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.OpportunityForEditRowMapper;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.OpportunityForStatusChangeRowMapper;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.OpportunityRowMapper;
import com.lmig.uscm.booktransfer.utilityservice.DateUtils;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.test.context.ContextConfiguration;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.xpath.XPathExpressionException;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@DataJpaTest
@ContextConfiguration(classes = {
        UnitTestConfig.class,
        OpportunityJDBCRepo.class,
        OpportunityJpaRepository.class,
        OpportunityRowMapper.class,
        OpportunityForEditRowMapper.class,
        OpportunityForStatusChangeRowMapper.class,
        DownloadOpportunityRowMapper.class
})
@EntityScan(basePackageClasses = {Opportunity.class})
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@EnableJpaRepositories(basePackages = {"com.lmig.uscm.booktransfer.opportunity.repo"})
class DownloadZipFileHelperETETest {

    @Autowired
    BookTransferService bookTransferService;
    DownloadZipFileHelper downloadZipFileHelper;
    @Autowired
    OpportunityJpaRepository opportunityRepository;
    @Autowired
    OpportunityRepoHelper opportunityRepoHelper;

    @BeforeEach
    public void setUp() {
        opportunityRepository.deleteAll();
        downloadZipFileHelper = new DownloadZipFileHelper(bookTransferService, opportunityRepoHelper);
    }

    @Test
    void testDownloadZipFileOver100() throws Exception {

        List<Integer> oppIdList = new ArrayList<>();

        for (int i = 1; i < 101; i++) {
            String xml = "<?xml version=\"1.0\" encoding=\"utf-8\"?><comp><PersPolicy><PolicyNumber>********</PolicyNumber></PersPolicy><b></b><c></c></comp>";
            Opportunity opp = OpportunityTestUtil.saveCreatedDummyOpp(6, xml, 1, "x", opportunityRepository);
            opp.setLastPolicyGuid("4545");
            opp = opportunityRepository.save(opp);
            oppIdList.add(opp.getOpportunityId());

        }
        ByteArrayOutputStream response = downloadZipFileHelper.downloadZipFile(oppIdList);

        assertNotNull(response);
    }

    @Test
    void testDownloadZipFileLess100() throws XPathExpressionException, IOException, TransformerException,
            SAXException, ParserConfigurationException, RuntimeException, BookTransferException {

        Opportunity opportunity = OpportunityTestUtil.saveCreatedDummyOpp(6,
                "<?xml version=\"1.0\" encoding=\"utf-8\"?><comp><PersPolicy><PolicyNumber>********</PolicyNumber></PersPolicy><b></b><c></c></comp>",
                1, "x", opportunityRepository);
        opportunity.setLastPolicyGuid("2");
        opportunity = opportunityRepository.save(opportunity);

        Opportunity opportunity2 = OpportunityTestUtil.saveCreatedDummyOpp(6,
                "<?xml version=\"1.0\" encoding=\"utf-8\"?><comp><PersPolicy><PolicyNumber>********</PolicyNumber></PersPolicy><b></b><c></c></comp>",
                2, "x", opportunityRepository);
        opportunity2.setLastPolicyGuid("2");
        opportunity2 = opportunityRepository.save(opportunity2);

        List<Integer> oppIdList2 = new ArrayList<>();
        oppIdList2.add(opportunity.getOpportunityId());
        oppIdList2.add(opportunity2.getOpportunityId());

        ByteArrayOutputStream response = downloadZipFileHelper.downloadZipFile(oppIdList2);

        assertNotNull(response);
    }

    @Test
    void testCreateEditedXmlFile() throws Exception {
        Integer sfdcid = 9090;

        Opportunity opp = OpportunityTestUtil.saveCreatedDummyOpp(6,
                "<?xml version=\"1.0\" encoding=\"utf-8\"?><comp><PersPolicy><PolicyNumber>********</PolicyNumber></PersPolicy><b></b><c></c></comp>",
                1, "x", opportunityRepository);
        opp.setLastPolicyGuid("000");
        opp = opportunityRepository.save(opp);
        Document doc = downloadZipFileHelper.createEditedXmlFile(opp, sfdcid);
        XmlHelper.nullSafeExtractFromXml(doc, "//SalesforceBookTransferName");
        String safecoAQEOpportunityID = XmlHelper.nullSafeExtractFromXml(doc, "//com.Safeco_AQEOpportunityID");
        String companysQuoteNumber = XmlHelper.nullSafeExtractFromXml(doc, "//CompanysQuoteNumber");

        assertEquals(Integer.toString(opp.getOpportunityId()), safecoAQEOpportunityID);
        assertEquals("", companysQuoteNumber);

        String safeco_CompanyURLTest = XmlHelper.nullSafeExtractFromXml(doc, "//com_safeco_CompanyURL");
        assertNotNull(safeco_CompanyURLTest);

        Opportunity opp2 = OpportunityTestUtil.saveCreatedDummyOpp(6,
                "<?xml version=\"1.0\" encoding=\"utf-8\"?><comp><PersPolicy><QuoteInfo><com_safeco_CompanyURL>test</com_safeco_CompanyURL></QuoteInfo><PolicyNumber>********</PolicyNumber></PersPolicy><b></b><c></c></comp>",
                1, "x", opportunityRepository);
        opp2.setLastPolicyGuid("2");
        opp2 = opportunityRepository.save(opp2);

        Document doc2 = downloadZipFileHelper.createEditedXmlFile(opp2, sfdcid);
        assertNotNull(doc2);

        String safeco_CompanyURL = XmlHelper.nullSafeExtractFromXml(doc2, "//com_safeco_CompanyURL");
        assertNotNull(safeco_CompanyURL);
    }

    @Test
    void testGenerateZipFile() {

        String testingNameZipFile = downloadZipFileHelper.generateZipFileName();

        assertNotNull(testingNameZipFile);
        assertTrue(testingNameZipFile.matches("(BT_IVANS_)[0-9]+\\.(zip)"));
    }

    /**
     * Given I have a valid customer name, effectivedate for the opp id When I
     * generate file name Then the Filename should be generated with the given data
     */
    @Test
    void testGenerateFileNameWithValidData() {
        String actual = downloadZipFileHelper.generateFileName("test", 23, "2020-03-02");
        String expected = "BT_IVANS_2020-03-02_23_test.xml";
        assertEquals(expected, actual);
    }

    /**
     * Given I have a valid effectivedate But invalid customer name for the oppid
     * When I generate file name Then the Filename should be generated with the
     * formated customername data
     */
    @Test
    void testGenerateFileNameWithInValidCustomerName() {
        String actual = downloadZipFileHelper.generateFileName("test. t@#", 23, "2020-03-02");
        String expected = "BT_IVANS_2020-03-02_23_test. t.xml";
        assertEquals(expected, actual);
    }

    /**
     * Given I have in valid customer name, effectivedate for the opp id When I
     * generate file name Then the Filename should be generated with the given data
     */
    @Test
    void testGenerateFileNameWithInValidData() {
        String actual = downloadZipFileHelper.generateFileName(null, 23, null);
        String expected = "BT_IVANS_" + DateUtils.getSQLDateString(new Date()) + "_23_.xml";
        assertEquals(expected, actual);
    }

    @Test
    void testWriteXmlToZip() throws IOException {
        Integer sfdcid = 9090;
        Opportunity opp = OpportunityTestUtil.saveCreatedDummyOpp(6,
                "<?xml version=\"1.0\" encoding=\"utf-8\"?><comp><PersPolicy><PolicyNumber>********</PolicyNumber></PersPolicy><b></b><c></c>" +
                        "<BirthDt>2000-01-01</BirthDt>" +
                        "<DriversLicenseNumber>1234512345678</DriversLicenseNumber>" +
                        "<LicensePermitNumber>1234512345678</LicensePermitNumber>" +
                        "</comp>",
                2, "x", opportunityRepository);
        opp.setLastPolicyGuid("4545");
        opp = opportunityRepository.save(opp);
        List<Opportunity> listOpp = new ArrayList<>();
        listOpp.add(opp);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(byteArrayOutputStream);
        ZipOutputStream zipOutputStream = new ZipOutputStream(bufferedOutputStream);
        Map<Integer, Integer> btIdToSfdcid = new HashMap<>();
        btIdToSfdcid.put(1, sfdcid);

        downloadZipFileHelper.writeXmlToZip(zipOutputStream, listOpp, btIdToSfdcid);

        zipOutputStream.close();
        String contents = readZipData(byteArrayOutputStream.toByteArray());
        assertTrue(contents.contains("<BirthDt isMasked=\"true\">2000-**-**</BirthDt>"));
        assertTrue(contents.contains("<DriversLicenseNumber isMasked=\"true\">12345********</DriversLicenseNumber>"));
        assertTrue(contents.contains("<LicensePermitNumber isMasked=\"true\">12345********</LicensePermitNumber>"));
    }

    @Test
    void testWriteMixLineTypeOppsXmlToZip() throws IOException, XPathExpressionException, SAXException,
            ParserConfigurationException {
        Integer sfdcid = 9090;

        Opportunity oppPL = OpportunityTestUtil.saveCreatedDummyOpp(6,
                "<?xml version=\"1.0\" encoding=\"utf-8\"?><comp><PersPolicy><PolicyNumber>********</PolicyNumber></PersPolicy><b></b><c></c></comp>",
                2, "x", opportunityRepository);
        oppPL.setLastPolicyGuid("4545");
        oppPL = opportunityRepository.save(oppPL);

        String workerCompXml = XmlHelper.getFileContentFromPath("src/test/resources/xml/converterV2_BL_WORK.xml");
        Opportunity oppBL = OpportunityTestUtil.saveCreatedDummyBLOpp(6, workerCompXml, 2, workerCompXml, opportunityRepository);

        List<Opportunity> listOpp = new ArrayList<>();
        listOpp.add(oppPL);
        listOpp.add(oppBL);

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(byteArrayOutputStream);
        ZipOutputStream zipOutputStream = new ZipOutputStream(bufferedOutputStream);

        Map<Integer, Integer> btIdToSfdcid = new HashMap<>();
        btIdToSfdcid.put(2, sfdcid);
        zipOutputStream = downloadZipFileHelper.writeXmlToZip(zipOutputStream, listOpp, btIdToSfdcid);
        assertNotNull(zipOutputStream);
    }

    public String readZipData(byte[] zipData) throws IOException {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(zipData);
        ZipInputStream zipInputStream = new ZipInputStream(byteArrayInputStream);

        StringBuilder extractedData = new StringBuilder();
        while (zipInputStream.getNextEntry() != null) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = zipInputStream.read(buffer)) > 0) {
                extractedData.append(new String(buffer, 0, len));
            }
            zipInputStream.closeEntry();
        }
        zipInputStream.close();
        return extractedData.toString();
    }
}

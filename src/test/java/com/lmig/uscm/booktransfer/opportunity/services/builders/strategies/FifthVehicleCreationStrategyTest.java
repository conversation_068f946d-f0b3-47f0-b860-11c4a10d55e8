package com.lmig.uscm.booktransfer.opportunity.services.builders.strategies;

import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.creation.CreationStrategyBundle;
import com.lmig.uscm.booktransfer.utilityservice.UtilityService;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import org.junit.jupiter.api.Test;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class FifthVehicleCreationStrategyTest extends CreationStrategyTest {

	FifthVehicleCreationStrategy creationStrategy = new FifthVehicleCreationStrategy();

	MotorcycleCreationStrategy motorcycleCreationStrategy = new MotorcycleCreationStrategy();

	static final String ACCORD_HOME_XML_PATH = "src/test/resources/xml/accordhome.xml";
	static final String AUTO_XML_PATH = "src/test/resources/xml/auto.xml";
	static final String AUTO_16_VEHICLES_XML_PATH = "src/test/resources/xml/auto16Vehs.xml";
	static final String AUTO_AND_MTR_22_VEHICLES_XML_PATH = "src/test/resources/xml/autoMtr22Vehs.xml";
	static final String MTR_6_VEHICLES_XML_PATH = "src/test/resources/xml/mtr6Vehs.xml";
	static final String AUTO_12_VEHICLES_XML_PATH = "src/test/resources/xml/auto12Vehs.xml";
	static final String AUTO_11_VEHICLES_6_MTR_XML_PATH = "src/test/resources/xml/auto11Vehs6mtr.xml";
	static final String AUTO_2_AND_1_MTR_XML_PATH = "src/test/resources/xml/autoAndMtc.xml";

	@Test
	void testSplitFifthCarStrategyShouldResolve() throws Exception {
		OpportunityCreationRequest homeRequest = buildCreationRequest(ACCORD_HOME_XML_PATH);
		OpportunityCreationRequest autoTwoVehRequest = buildCreationRequest(AUTO_XML_PATH);
		OpportunityCreationRequest autoFiveVehRequest = buildCreationRequest(AUTO_16_VEHICLES_XML_PATH);

		boolean shouldResolveHome = creationStrategy.shouldResolveCreationRequest(homeRequest);
		boolean shouldResolveTwoVehicleAuto = creationStrategy.shouldResolveCreationRequest(autoTwoVehRequest);
		boolean shouldResolveFiveVehicleAuto = creationStrategy.shouldResolveCreationRequest(autoFiveVehRequest);

		assertFalse(shouldResolveHome);
		assertFalse(shouldResolveTwoVehicleAuto);
		assertTrue(shouldResolveFiveVehicleAuto);
	}

	@Test
	void testSplitFifthCarStrategy_SingleCreationBundleWithHome_NoResolves() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest homeRequest = buildCreationRequest(ACCORD_HOME_XML_PATH);
		CreationStrategyBundle simpleHomeBundle = buildCreationBundle(homeRequest);

		List<CreationStrategyBundle> bundles = List.of(simpleHomeBundle);

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(1, bundles.get(0).getRequests().size());
	}

	@Test
	void testSplitFifthCarStrategy_CreationBundleWithHomeAndAuto_OneResolves() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest homeRequest = buildCreationRequest(ACCORD_HOME_XML_PATH);
		OpportunityCreationRequest auto16VehRequest = buildCreationRequest(AUTO_16_VEHICLES_XML_PATH);

		CreationStrategyBundle simpleHomeBundle = buildCreationBundle(homeRequest);
		CreationStrategyBundle splitFifthVehBundle = buildCreationBundle(auto16VehRequest);

		List<CreationStrategyBundle> bundles = new LinkedList<>(Arrays.asList(simpleHomeBundle, splitFifthVehBundle));
		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(1, bundles.get(0).getRequests().size());
		assertEquals(4, bundles.get(1).getRequests().size());

		// Check that the split auto request links to MasterOpp properly
		assertTrue(bundles.get(1).getRequests().get(0).isMasterOpp());
		assertFalse(bundles.get(1).getRequests().get(1).isMasterOpp());

		// Check the requests have 4 vehicles each
		assertAllRequestsHaveFourOrLessVehicles(bundles.get(1).getRequests());
	}

	@Test
	void testSplitFifthCarStrategy_CreationBundleWithTwoAuto_TwoResolves() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest auto16VehRequest = buildCreationRequest(AUTO_16_VEHICLES_XML_PATH);
		OpportunityCreationRequest auto12Veh2Request = buildCreationRequest(AUTO_12_VEHICLES_XML_PATH);

		CreationStrategyBundle splitFifthVehBundle = buildCreationBundle(auto16VehRequest);
		CreationStrategyBundle splitFifthVeh2Bundle = buildCreationBundle(auto12Veh2Request);

		List<CreationStrategyBundle> bundles = new LinkedList<>(Arrays.asList(splitFifthVehBundle, splitFifthVeh2Bundle));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(4, bundles.get(0).getRequests().size());
		assertEquals(3, bundles.get(1).getRequests().size());

		// Check that the split auto request links to MasterOpp properly
		assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
		assertFalse(bundles.get(0).getRequests().get(1).isMasterOpp());
		assertTrue(bundles.get(1).getRequests().get(0).isMasterOpp());
		assertFalse(bundles.get(1).getRequests().get(1).isMasterOpp());

		// Check the requests have 4 vehicles each
		assertAllRequestsHaveFourOrLessVehicles(bundles.get(0).getRequests());
		assertAllRequestsHaveFourOrLessVehicles(bundles.get(1).getRequests());
	}

	@Test
	void testMotorCycleCreationStrategy_To_FithVehicleCreationStrategyWithMixedAuto_MTRXML_EigthCarEnabledFlag() throws ParserConfigurationException, IOException, SAXException, XPathExpressionException {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest auto17VehRequest = buildCreationRequest(AUTO_11_VEHICLES_6_MTR_XML_PATH);

		CreationStrategyBundle splitFifthVehBundle = buildCreationBundle(auto17VehRequest);
		List<CreationStrategyBundle> bundles = new ArrayList<>();
		bundles.add(splitFifthVehBundle);

		creationStrategy.setEighthCarEnabled(true);
		motorcycleCreationStrategy.resolveCreationBundles(bundles, response);
		creationStrategy.resolveCreationBundles(bundles, response);

		assertEquals(0, (int) response.getFailedCreationCount());

		//MotorCycleCreationStrategy splits Motorcycle and Auto into separate bundles
		assertEquals(2, bundles.size());

		//check the OpportunityRequest numbers
		assertEquals(2, bundles.get(0).getRequests().size());
		assertEquals(2, bundles.get(1).getRequests().size());

		// Check that the split auto request links to MasterOpp properly
		assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
		assertFalse(bundles.get(0).getRequests().get(1).isMasterOpp());
		assertTrue(bundles.get(1).getRequests().get(0).isMasterOpp());
		assertFalse(bundles.get(1).getRequests().get(1).isMasterOpp());


		// Check the requests have 8 auto and 4 mtr vehicles each
		assertAllRequestsHaveEightOrLessVehicles(bundles.get(0).getRequests());
		assertAllRequestsHaveFourOrLessVehicles(bundles.get(1).getRequests());

	}

	@Test
	public void testSplitFifthVehicleStrategy_CreationBundleWithHomeAndAutoAndMotorcycle_CalculatesCoverageTermAmt() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest homeRequest = buildCreationRequest(ACCORD_HOME_XML_PATH);
		OpportunityCreationRequest autoMtr22VehRequest = buildCreationRequest(AUTO_AND_MTR_22_VEHICLES_XML_PATH);

		CreationStrategyBundle simpleHomeBundle = buildCreationBundle(homeRequest);
		CreationStrategyBundle splitFifthVehBundle = buildCreationBundle(autoMtr22VehRequest);

		List<CreationStrategyBundle> bundles = new LinkedList<>(Arrays.asList(simpleHomeBundle, splitFifthVehBundle));

		motorcycleCreationStrategy.resolveCreationBundles(bundles, response);

		// Grab auto and mtr pers policy term amts before strategies are applied
		double originalAutoPersPolicyPrice = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(1).getCreationRequest(0).getUploadedACORD()));
		double originalMtrPersPolicyPrice = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(2).getCreationRequest(0).getUploadedACORD()));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(1, bundles.get(0).getRequests().size());
		assertEquals(5, bundles.get(1).getRequests().size());
		assertEquals(2, bundles.get(2).getRequests().size());

		// Check that the split auto request links to MasterOpp properly
		assertTrue(bundles.get(1).getRequests().get(0).isMasterOpp());
		assertFalse(bundles.get(1).getRequests().get(1).isMasterOpp());

		// Check the requests have 4 vehicles each
		assertAllRequestsHaveFourOrLessVehicles(bundles.get(1).getRequests());
		assertAllRequestsHaveFourOrLessVehicles(bundles.get(2).getRequests());

		// Assert that the new auto pers policy is the old amount minus the sum of pers policy amts after the first opportunity
		double newAutoPersPolicyPrice = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(1).getCreationRequest(0).getUploadedACORD()));
		double totalAutoPersCoverageTermAmts = 0;
		for (int i = 1; i < bundles.get(1).getRequests().size(); i++) {
			totalAutoPersCoverageTermAmts +=
					AcordHelper.getPersVehTotalCoverageCurrentTermAmt(bundles.get(1).getCreationRequest(i).getUploadedACORD());
		}
		assertEquals((int) newAutoPersPolicyPrice, (int) (originalAutoPersPolicyPrice - totalAutoPersCoverageTermAmts));

		// Assert that the new mtr pers policy is the old amount minus the sum of pers policy amts after the first opportunity
		double newMtrPersPolicyPrice = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(2).getCreationRequest(0).getUploadedACORD()));
		double totalMtrPersCoverageTermAmts = 0;
		for (int i = 1; i < bundles.get(2).getRequests().size(); i++) {
			totalMtrPersCoverageTermAmts +=
					AcordHelper.getPersVehTotalCoverageCurrentTermAmt(bundles.get(2).getCreationRequest(i).getUploadedACORD());
		}
		assertEquals((int) newMtrPersPolicyPrice, (int) (originalMtrPersPolicyPrice - totalMtrPersCoverageTermAmts));
	}

	@Test
	public void testSplitFifthVehicleStrategy_CreationBundleWithMotorcycle_CalculatesCoverageTermAmt() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest mtr6VehRequest = buildCreationRequest(MTR_6_VEHICLES_XML_PATH);

		CreationStrategyBundle splitFifthVehBundle = buildCreationBundle(mtr6VehRequest);

		List<CreationStrategyBundle> bundles = new LinkedList<>(Collections.singletonList(splitFifthVehBundle));

		// Grab mtr pers policy term amt before strategies are applied
		double originalMtrPersPolicyPrice = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getCreationRequest(0).getUploadedACORD()));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(2, bundles.get(0).getRequests().size());

		// Assert that motorcycles were divided properly into 2 requests
		assertEquals(4, AcordHelper.getPersVehs(bundles.get(0).getCreationRequest(0).getUploadedACORD()).getLength());
		assertEquals(2, AcordHelper.getPersVehs(bundles.get(0).getCreationRequest(1).getUploadedACORD()).getLength());

		// Check that the split auto request links to MasterOpp properly
		assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
		assertFalse(bundles.get(0).getRequests().get(1).isMasterOpp());

		// Check the requests have 4 vehicles each
		assertAllRequestsHaveFourOrLessVehicles(bundles.get(0).getRequests());

		// Assert that the new mtr pers policy is the old amount minus the sum of pers policy amts after the first opportunity
		double newMtrPersPolicyPrice = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getCreationRequest(0).getUploadedACORD()));
		double totalPersCoverageTermAmts = 0;
		for (int i = 1; i < bundles.get(0).getRequests().size(); i++) {
			totalPersCoverageTermAmts +=
					AcordHelper.getPersVehTotalCoverageCurrentTermAmt(bundles.get(0).getCreationRequest(i).getUploadedACORD());
		}

		assertEquals((int) newMtrPersPolicyPrice, (int) (originalMtrPersPolicyPrice - totalPersCoverageTermAmts));
	}

	@Test
	public void testSplitFifthVehicleStrategy_CreationBundleWithAuto_CalculatesCoverageTermAmt() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest auto12VehRequest = buildCreationRequest(AUTO_12_VEHICLES_XML_PATH);

		CreationStrategyBundle splitFifthVehBundle = buildCreationBundle(auto12VehRequest);

		List<CreationStrategyBundle> bundles = new LinkedList<>(Collections.singletonList(splitFifthVehBundle));

		// Grab mtr pers policy term amt before strategies are applied
		double originalAutoPersPolicyPrice = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getCreationRequest(0).getUploadedACORD()));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(3, bundles.get(0).getRequests().size());


		// Check that the split auto request links to MasterOpp properly
		assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());
		assertFalse(bundles.get(0).getRequests().get(1).isMasterOpp());

		// Check the requests have 4 vehicles each
		assertAllRequestsHaveFourOrLessVehicles(bundles.get(0).getRequests());

		// Assert that the new mtr pers policy is the old amount minus the sum of pers policy amts after the first opportunity
		double newAutoPersPolicyPrice = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getCreationRequest(0).getUploadedACORD()));
		double totalPersCoverageTermAmts = 0;
		for (int i = 1; i < bundles.get(0).getRequests().size(); i++) {
			totalPersCoverageTermAmts +=
					AcordHelper.getPersVehTotalCoverageCurrentTermAmt(bundles.get(0).getCreationRequest(i).getUploadedACORD());
		}

		assertEquals((int) newAutoPersPolicyPrice, (int) (originalAutoPersPolicyPrice - totalPersCoverageTermAmts));
	}

	@Test
	public void testSplitFifthVehicleStrategy_CreationBundleWithThreeAuto_DoesNotCalculateCoverageTermAmt() throws Exception {
		OpportunityCreationResponse response = new OpportunityCreationResponse();
		OpportunityCreationRequest auto3VehRequest = buildCreationRequest(AUTO_2_AND_1_MTR_XML_PATH);

		CreationStrategyBundle splitFifthVehBundle = buildCreationBundle(auto3VehRequest);

		List<CreationStrategyBundle> bundles = new LinkedList<>(Collections.singletonList(splitFifthVehBundle));

		// Grab mtr pers policy term amt before strategies are applied
		double originalAutoPersPolicyPrice = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getCreationRequest(0).getUploadedACORD()));

		creationStrategy.resolveCreationBundles(bundles, response);

		// Check that none errored
		assertEquals(0, (int) response.getFailedCreationCount());
		assertEquals(1, bundles.get(0).getRequests().size());
		assertEquals(1, bundles.get(0).getRequests().size());


		// Check that the split auto request links to MasterOpp properly
		assertTrue(bundles.get(0).getRequests().get(0).isMasterOpp());

		// Check the requests have 4 vehicles each
		assertAllRequestsHaveFourOrLessVehicles(bundles.get(0).getRequests());

		// Assert that the new mtr pers policy is the old amount minus the sum of pers policy amts after the first opportunity
		double newAutoPersPolicyPrice = UtilityService.convertStringToDouble(
				AcordHelper.getPolicyCurrentTermAmt(bundles.get(0).getCreationRequest(0).getUploadedACORD()));

		assertEquals((int) newAutoPersPolicyPrice, (int) (originalAutoPersPolicyPrice));
	}

	private void assertAllRequestsHaveFourOrLessVehicles(List<OpportunityCreationRequest> creationRequests) throws XPathExpressionException {
		for (OpportunityCreationRequest request: creationRequests) {
			assertTrue(AcordHelper.getPersVehs(request.getUploadedACORD()).getLength() <= 4);
		}
	}

	private void assertAllRequestsHaveEightOrLessVehicles(List<OpportunityCreationRequest> creationRequests) throws XPathExpressionException {
			for (OpportunityCreationRequest request: creationRequests) {
				assertTrue(AcordHelper.getPersVehs(request.getUploadedACORD()).getLength() <= 8);
			}
	}

}

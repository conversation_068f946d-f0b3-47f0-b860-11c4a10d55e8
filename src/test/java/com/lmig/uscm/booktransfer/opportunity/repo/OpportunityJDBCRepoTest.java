/*
 * Copyright (c) 2019, Liberty Mutual
 * Proprietary and Confidential
 * All Rights Reserved
 */

package com.lmig.uscm.booktransfer.opportunity.repo;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.ScheduleRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequestForOppIds;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.EffectiveDateRange;
import com.lmig.uscm.booktransfer.sql.domain.PaginationRequest;
import com.lmig.uscm.booktransfer.utilityservice.DateUtils;
import org.assertj.core.util.Lists;
import org.javatuples.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.hamcrest.CoreMatchers.not;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.core.Is.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * This test the actual building of the query while the other test case test the
 * database call
 */
@ExtendWith(MockitoExtension.class)
class OpportunityJDBCRepoTest {

	@Mock
	JdbcTemplate jdbcTemplate;

	@Mock
	NamedParameterJdbcTemplate parameterJdbcTemplate;

	@InjectMocks
	OpportunityJDBCRepo opportunityJDBCRepo;

	/**
	 * Given that I have one Business Type (Autop) saved on the scheduler When I go
	 * to query the database Then I will get a query that searches for that one
	 * businessType.
	 */
	@Test
	void testQueryWithOneBusinessType() {
		ScheduleRequest schedule = getSchedule();
		schedule.addBusinessType(LineOfBusiness.AUTOP);
		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);

		assertThat(query, containsString("BusinessType in ('AUTO','AUTOP','AUTO - 5TH CAR POLICY','RV')"));
	}

	private ScheduleRequest getSchedule() {
		ScheduleRequest schedule = new ScheduleRequest();
		schedule.setExportSLA(0);
		return schedule;
	}

	/**
	 * Given that I have selected all Business Types saved on the scheduler When I
	 * go to query the database Then I will get a query that searches for all the
	 * businessType.
	 */
	@Test
	void testQueryWithAllBusinessType() {
		ScheduleRequest schedule = getSchedule();
		schedule.addBusinessType(LineOfBusiness.AUTOP);
		schedule.addBusinessType(LineOfBusiness.HOME);
		schedule.addBusinessType(LineOfBusiness.BOAT);
		schedule.addBusinessType(LineOfBusiness.UMBRP);
		schedule.addBusinessType(LineOfBusiness.DFIRE);

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		String lobs = getInValues(query, "BusinessType");

		for (LineOfBusiness lineOfBusiness : schedule.getBusinessTypes()) {
			for (String businessType : lineOfBusiness.getPossibleLobAcordValues()) {
				assertThat(lobs, containsString("'" + businessType + "'"));
			}
		}
	}

	/**
	 * Given that I have one Rating State (IN) saved on the scheduler When I go to
	 * query the database Then I will get a query that searches for that one Rating
	 * State.
	 */
	@Test
	void testQueryWithOneState() {
		ScheduleRequest schedule = getSchedule();
		schedule.addRatingState("IN");

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, containsString("And Opp.STATE in ('IN')"));
	}

	/**
	 * Given that I have selected mutiple Rating states saved on the scheduler When
	 * I go to query the database Then I will get a query that searches for all the
	 * different.
	 */
	@Test
	void testQueryWithMultipleStates() {
		ScheduleRequest schedule = getSchedule();
		schedule.addRatingState("IN");
		schedule.addRatingState("WA");
		schedule.addRatingState("IL");
		schedule.addRatingState("OH");
		schedule.addRatingState("HI");

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		String states = getInValues(query, "STATE");

		for (String state : schedule.getRatingStates()) {
			assertThat(states, containsString("'" + state + "'"));
		}
	}

	/**
	 * Given that I have no states When I go to query the database Then I will not
	 * search by state
	 */
	@Test
	void testQueryWithNoStates() {
		ScheduleRequest schedule = getSchedule();

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, not(containsString("STATE")));
	}

	/**
	 * this helper function will take the query with an 'in' in it and return all
	 * the values inside the () to beable to make sure they exist. ie. the query
	 * Select OpportunityID from Opportunities Where BusinessType in ()AND STATE in
	 * ('HI','IL','IN','OH','WA') with the value passed to it "STATE" will return
	 * 'HI','IL','IN','OH','WA'.
	 */
	private String getInValues(String query, String value) {
		value += " in \\((.*)\\)";
		Pattern pattern = Pattern.compile(value);
		Matcher m = pattern.matcher(query);
		// asserts that the businessType is included
		assertTrue(m.find());

		return m.group(1);
	}

	/**
	 * I only want to select the oppId from database and that we are join on the BT table
	 */
	@Test
	void testQuerySelectOppId() {
		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(getSchedule());
		assertThat(query, containsString(
				"Select OpportunityID, Opp.LineType from Opportunities Opp Join BookTransfer bt on Opp.BookTransferID = bt.BookTransferID"));
	}

	/**
	 * Given that I have no prior Carrier When I go to query the database Then I
	 * will not search by prior carrier
	 */
	@Test
	void testQueryWithNoPriorCarrier() {
		ScheduleRequest schedule = getSchedule();

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, not(containsString("OppPriorCarrier")));
	}

	/**
	 * Given that I have mutiple prior Carrier When I go to query the database Then
	 * I will search by those prior carrier
	 */
	@Test
	void testQueryWithMultiplePriorCarrier() {
		ScheduleRequest schedule = getSchedule();
		schedule.addPriorCarriers("AAA");
		schedule.addPriorCarriers("DONGLE");
		schedule.addPriorCarriers("MAPFRE");
		schedule.addPriorCarriers("QBE");

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		String priorCarriers = getInValues(query, "OppPriorCarrier");

		for (String priorCarrier : schedule.getPriorCarriers()) {
			assertThat(priorCarriers, containsString("'" + priorCarrier + "'"));
		}
	}


	/**
	 * Given that I have a schedule to build a query from When I go
	 * to query the database Then I will get a query that excludes manual Quotes
	 */
	@Test
	void testQueryFORMANUAL_EXCUSION() {
		ScheduleRequest schedule = getSchedule();
		schedule.addBusinessType(LineOfBusiness.AUTOP);
		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, containsString("AND QuoteType IS NULL"));
	}

	/**
	 * Given that I have no prior Carrier When I go to query the database Then I
	 * will not search by opp status, but we still want to remove Heritage status from
	 * the results
	 */
	@Test
	void testQueryWithNoOppStatuses() {
		ScheduleRequest schedule = getSchedule();

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, not(containsString("Opp.Status in(")));
		assertThat(query, containsString(opportunityJDBCRepo.getRestrictedStatuses()));
	}

	/**
	 * Given that I have mutiple Opp Statis When I go to query the database Then I
	 * will search by multiple Opp Status
	 */
	@Test
	void testQueryWithMultipleOppStatus() {
		ScheduleRequest schedule = getSchedule();
		schedule.addQuoteStatuses(
				new Pair<>(String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_QUOTE_CLEAN_UP.getOppStatusCode()),
						OpportunityStatus.OPPORTUNITY_STATUS_QUOTE_CLEAN_UP.getOppStatusValue()));
		schedule.addPriorCarriers(
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_AGENT_UPDATED.getOppStatusCode()));
		schedule.addPriorCarriers(
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_MISSING_REQUIRED_DATA.getOppStatusCode()));
		schedule.addPriorCarriers(
				String.valueOf(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_FAILED.getOppStatusCode()));

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		String statuses = getInValues(query, "Status");

		for (String status : schedule.readQuoteStatusKeys()) {
			assertThat(statuses, containsString("'" + status + "'"));
		}
		
		// do not include heritage opps in query
		assertThat(query, containsString(opportunityJDBCRepo.getRestrictedStatuses()));
	}

	/**
	 * Given that I have no BookTransferId (Salesforce Code) When I go to query the
	 * database Then I will not search by BookTransferId (Salesforce Code)
	 */
	@Test
	void testQueryWithNoBookTransferId() {
		ScheduleRequest schedule = getSchedule();

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, not(containsString("SalesforceCode")));
	}

	/**
	 * Given that I have no StatCode (SubCode) When I go to query the database Then
	 * I will not search by StatCode (SubCode)
	 */
	@Test
	void testQueryWithNoStatCode() {
		ScheduleRequest schedule = getSchedule();

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, not(containsString("SubCode")));
	}

	/**
	 * Given that I have no MasterStatCode (AgentNum Code) When I go to query the
	 * database Then I will not search by MasterStatCode (AgentNum Code)
	 */
	@Test
	void testQueryWithNoMasterStatCode() {
		ScheduleRequest schedule = getSchedule();

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, not(containsString("AgentNum")));
	}

	/**
	 * Given that I have an sla of 65 days When I go to query the database Then I
	 * will for items with effective date of 65 days from now
	 */
	@Test
	void testQueryWithEffectiveDate() {
		ScheduleRequest schedule = getSchedule();
		schedule.setExportSLA(65);

		String futureDate = DateUtils.getDateXDaysInTheFuture(schedule.getExportSLA());
		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, containsString("AND EffectiveDate <= '" + futureDate + "'"));
	}

	/**
	 * Given that I have multiple BookTransferId (Salesforce Code) When I go to
	 * query the database Then I will search by multiple BookTransferId (Salesforce
	 * Code)
	 */
	@Test
	void testQueryWithMultipleBookTransferId() {
		ScheduleRequest schedule = getSchedule();
		schedule.addSalesForceCodes("BT - 40133");
		schedule.addSalesForceCodes("BT - 09561");
		schedule.addSalesForceCodes("BT - 14226");

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		String salesforceCode = getInValues(query, "SalesforceCode");

		for (String codes : schedule.getSalesforceCodes()) {
			assertThat(salesforceCode, containsString("'" + codes + "'"));
		}
	}

	/**
	 * Given that I have a Stat Code When I go to query the database Then I will
	 * search by that stat code
	 */
	@Test
	void testQueryWitStatCode() {
		ScheduleRequest schedule = getSchedule();
		schedule.setStatCode("11539527");

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, containsString("AND (bt.status = 'active' AND bt.SubCode = '11539527')"));

	}

	/**
	 * Given that I have a MasterStatCode (AgentNum Code) When I go to query the
	 * database Then I will search by that MasterStatCode (AgentNum Code)
	 */
	@Test
	void testQueryWitMasterStatCode() {
		ScheduleRequest schedule = getSchedule();
		schedule.setMasterStatCode("11539527");

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, containsString("AND (bt.status = 'active' AND bt.AgentNum = '11539527')"));
	}

	/**
	 * Given that I have a MasterStatCode (AgentNum Code) When I go to query the
	 * database Then I will search by that MasterStatCode (AgentNum Code)
	 */
	@Test
	void testQueryWithPastDay() {
		ScheduleRequest schedule = getSchedule();
		schedule.setExportSLA(65);

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		String today = DateUtils.getDateXDaysInTheFuture(0);
		assertThat(query, containsString("AND EffectiveDate >= '" + today + "'"));
	}

	/**
	 * Testing added all business Type to it to make sure that is being built
	 * correctly
	 */
	@Test
	void testAddingAllBusinessTypes() {
		ScheduleRequest schedule = getSchedule();
		schedule.setMasterStatCode("11539528");
		schedule.setStatCode("11539527");
		schedule.addSalesForceCodes("BT - 40133");
		schedule.addSalesForceCodes("BT - 09561");
		schedule.addSalesForceCodes("BT - 14226");
		schedule.setExportSLA(65);

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);

		// one extra space inbetween ) and AND
		assertThat(query, containsString(
				"AND (bt.SalesforceCode in ('BT - 40133','BT - 14226','BT - 09561')  AND bt.status = 'active' AND bt.SubCode = '11539527' AND bt.AgentNum = '11539528')"));
	}

	/**
	 * Given that i have custom filter request with all the search options. When
	 * I build the query Then the query will search for all of those options.
	 */
	@Test
	void testCustomFilterQuery() {
		CustomFilterRequest request = new CustomFilterRequest();
		request.setOppIds(Lists.list(12345, 12346));
		request.setPaginationRequest(new PaginationRequest());

		String expectedQuery = "SELECT Opp.OpportunityID, Opp.bookTransferID, OppStatus.Status, "
				+ "BT.SubCode, Opp.EffectiveDate, Opp.LastPolicyGuid as safecoPolicyGuid, "
				+ "Opp.NAICCd, Opp.PriorPremium, Opp.LastQuotedPremium, "
				+ "Opp.BusinessType as LOB, Opp.CustomerName, Opp.PriorCarrierGuid, " + "Opp.State, Opp.MasterOppID, "
				+ "BT.Nnumber, BT.SalesforceCode, Opp.LineType, BT.NBDRelationship " + "FROM Opportunities Opp with (nolock) "
				+ "JOIN BookTransfer BT with (nolock) ON Opp.BookTransferID = BT.BookTransferID "
				+ "JOIN OpportunityStatus OppStatus with (nolock) ON Opp.Status = OppStatus.ID "
				+ "WHERE Opp.OpportunityID IN (12345,12346)";
		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryFromObject(request);
		assertThat(actualQuery, containsString(expectedQuery));
	}

	/**
	 * Given that i have custom filter request with all of the search options. When
	 * I build the query Then the query will search for all of those option oppids.
	 */
	@Test
	void testCustomFilterQueryForOppIds() throws Exception {
		CustomFilterRequestForOppIds filterList = new CustomFilterRequestForOppIds();
		filterList.setOpportunityIDs(List.of(2));
		filterList.setStatuses(List.of(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS.getOppStatusValue()));
		filterList.setStatCode("Status");
		filterList.setStartDate("2010-05-23");
		filterList.setEndDate("2010-08-23");
		filterList.setPriorCarriers(List.of("Liberty Mutual"));
		filterList.setStates(List.of("OH"));
		filterList.setLobs(List.of("HOME"));
		filterList.setPriorPolicyGuid("452");
		filterList.setSafecoPolicyGuid("532");
		filterList.setCustomerName("Collin Williams");
		filterList.setAssignedUser("n0310627");
		filterList.setAgentNumber("42");
		filterList.setOppPriorCarriers(List.of("Safeco"));
		filterList.setSfdcids(List.of(13255));
		filterList.setLineType(LineType.Personal);
		filterList.setPriorCarrierBillingAccountNumbers(List.of("asd"));
		filterList.setNbdRelationship(List.of("Relationship"));

		String expectedQuery = "SELECT Opp.OpportunityID "
				+ "FROM Opportunities Opp with (nolock) , BookTransfer BT with (nolock) , OpportunityStatus OppStatus with (nolock) "
				+ "WHERE Opp.BookTransferID = BT.BookTransferID AND Opp.Status = OppStatus.ID "
				+ "AND Opp.Status in (:statuses) AND BT.SubCode = :statCode "
				+ "AND Opp.EffectiveDate >= :startDate AND Opp.EffectiveDate <= :endDate "
				+ "AND BT.Carrier in (:priorCarriers) AND Opp.State in (:states) "
				+ "AND ((Opp.BusinessType in (:lobs) AND (policytype is null or PolicyType = ''))) AND Opp.OpportunityID in (:opportunityIDs) "
				+ "AND BT.Nnumber = :assignedUser AND Opp.CustomerName LIKE :customerName "
				+ "AND BT.NBDRelationship in (:nbdRelationship) "
				+ "AND Opp.PriorCarrierGuid = :priorPolicyGuid AND Opp.LastPolicyGuid = :safecoPolicyGuid "
				+ "AND BT.AgentNum = :agentNumber AND Opp.OppPriorCarrier in (:oppPriorCarriers) "
				+ "AND BT.SFDCID in (:sfdcids) "
				+ "AND Opp.BillingAccountNumber in (:priorCarrierBillingAccountNumbers) "
				+ "AND Opp.LineType = '" + LineType.LineTypeValues.PERSONAL + "'";
		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryForOppIds(filterList);
		assertThat(actualQuery, containsString(expectedQuery));
	}

	@Test
	void testCustomFilterWithNaicCds() {
		//Given I have naic cds
		CustomFilterRequestForOppIds filterList = new CustomFilterRequestForOppIds();
		filterList.setNaicCds(List.of(1));
		String searchCondition = " AND Opp.NAICCd in (:naicCds)";

		//When I build query for oppIds with custom filter
		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryForOppIds(filterList);

		//Then the search condition should be appended to query
		assertTrue(actualQuery.contains(searchCondition));
	}

	@Test
	void testCustomFilterWithNoNaicCd() {
		//Given I have no naic cds
		CustomFilterRequestForOppIds filterList = new CustomFilterRequestForOppIds();
		String searchCondition = " AND Opp.NAICCd in (:naicCds)";

		//When I build query for oppIds with custom filter
		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryForOppIds(filterList);

		//Then the search condition shouldn't be appended to query
		assertFalse(actualQuery.contains(searchCondition));
	}

	@Test
	void testCustomFilterWithBillingAccountNumbers() {
		//Given I have naic cds
		CustomFilterRequestForOppIds filterList = new CustomFilterRequestForOppIds();
		filterList.setPriorCarrierBillingAccountNumbers(List.of("asd"));
		String searchCondition = " AND Opp.BillingAccountNumber in (:priorCarrierBillingAccountNumbers)";

		//When I build query for oppIds with custom filter
		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryForOppIds(filterList);

		//Then the search condition should be appended to query
		assertTrue(actualQuery.contains(searchCondition));
	}

	@Test
	void testCustomFilterWithNoBillingAccountNumbers() {
		//Given I have no naic cds
		CustomFilterRequestForOppIds filterList = new CustomFilterRequestForOppIds();
		String searchCondition = " AND Opp.BillingAccountNumber in (:priorCarrierBillingAccountNumbers)";

		//When I build query for oppIds with custom filter
		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryForOppIds(filterList);

		//Then the search condition shouldn't be appended to query
		assertFalse(actualQuery.contains(searchCondition));
	}

	@ParameterizedTest
	@CsvSource({"CONDO,true", "RENTERS,true", "HOME,false"})
	void testCustomFilterForPolicyTypeCdSearch(String lob, boolean expected) {
		List<String> lobs = new ArrayList<>();
		lobs.add(lob);
		//Given I have a condo lob
		CustomFilterRequestForOppIds filterList = new CustomFilterRequestForOppIds();
		filterList.setLobs(lobs);
		filterList.addPolicyTypeCds();
		String searchCondition = " OR (Opp.BusinessType = 'Home' AND Opp.PolicyType in (:policyTypeCds))";

		//When I build query for oppIds with custom filter
		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryForOppIds(filterList);

		//Then the search condition should be appended to query
		assertEquals(expected, actualQuery.contains(searchCondition));
	}

	/**
	 * Given that I have custom filter request with a search When I search for that
	 * query Then it will return those items that match the query
	 */
	@Test
	void testCustomFilterSearch() {
		CustomFilterRequest filterList = new CustomFilterRequest();
		PaginationRequest paginationRequest = new PaginationRequest();
		paginationRequest.setSearch("hello'");
		filterList.setPaginationRequest(paginationRequest);

		String expectedQuery = " AND (Opp.OpportunityID LIKE :search OR OppStatus.Status LIKE :search "
				+ "OR BT.SubCode LIKE :search OR Opp.EffectiveDate LIKE :search OR Opp.NAICCd LIKE :search "
				+ "OR Opp.PriorPremium LIKE :search OR Opp.LastQuotedPremium LIKE :search OR Opp.BusinessType LIKE :search "
				+ "OR Opp.CustomerName LIKE :search OR Opp.State LIKE :search OR Opp.MasterOppID LIKE :search "
				+ "OR BT.Nnumber LIKE :search OR BT.SalesforceCode LIKE :search "
				+ "OR Opp.LineType LIKE :search)";
		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryFromObject(filterList);
		assertThat(actualQuery, containsString(expectedQuery));
	}

	/**
	 * Given that I have custom filter request with pagination When I build the
	 * query Then the query will be paginated
	 */
	@Test
	void testCustomFilterQueryWithPagination() {
		CustomFilterRequest filterList = new CustomFilterRequest();
		PaginationRequest req = new PaginationRequest();
		req.setPageNumber(2);
		req.setSize(1000);
		filterList.setPaginationRequest(req);

		String expectedQuery = "OFFSET 2000 ROWS FETCH NEXT 1000 ROWS ONLY";

		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryFromObject(filterList);
		assertThat(actualQuery, containsString(expectedQuery));
	}

	/**
	 * Given that I have custom filter request with sorting pagination When I build
	 * the query Then the query will be sorted correctly
	 */
	@Test
	void testCustomFilterQueryWithPaginationForSorting() {
		CustomFilterRequest filterList = new CustomFilterRequest();
		PaginationRequest req = new PaginationRequest();
		req.setSorting("id");
		req.setSortDirection("desc");
		filterList.setPaginationRequest(req);

		String expectedQuery = "ORDER BY id desc";

		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryFromObject(filterList);
		assertThat(actualQuery, containsString(expectedQuery));
	}

	/**
	 * Given that I have custom filter request with an upload event id When I build
	 * the query Then the query will search for that upload event id.
	 */
	@Test
	void testCustomFilterQueryForUploadEvent() {
		CustomFilterRequestForOppIds filterList = new CustomFilterRequestForOppIds();
		filterList.setUploadEventId(12345);

		String expectedQuery = "AND Opp.UploadEventID = :uploadEventId";

		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryForOppIds(filterList);
		assertThat(actualQuery, containsString(expectedQuery));
	}

	/**
	 * Given that I have custom filter request with an lineType
	 * When I build the query
	 * Then the query will search for that lineType
	 */
	@Test
	void testCustomFilterQueryForOppIdsByLineType() {
		CustomFilterRequestForOppIds filterList = new CustomFilterRequestForOppIds();
		filterList.setLineType(LineType.Personal);

		String expectedQuery = "AND Opp.LineType = 'Personal'";
		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryForOppIds(filterList);
		assertThat(actualQuery, containsString(expectedQuery));
	}

	/**
	 * Given that I have custom filter request without a lineType
	 * When I build the query
	 * Then the query will search for all lineTypes
	 */
	@Test
	void testCustomFilterQueryForOppIdsAllLineType() {
		CustomFilterRequestForOppIds filterList = new CustomFilterRequestForOppIds();
		filterList.setLineType(LineType.All);

		String expectedQueryToNotExist = "AND Opp.LineType =";
		String actualQuery = opportunityJDBCRepo.buildCustomFilterQueryForOppIds(filterList);
		assertThat(actualQuery, not(containsString(expectedQueryToNotExist)));
	}

	/**
	 * Given that I have a blank date When I go to query the database Then I will
	 * not search by date
	 */
	@Test
	void testQueryWithEmptyDates() {
		CustomFilterRequestForOppIds customFilterRequest = new CustomFilterRequestForOppIds();
		customFilterRequest.setStartDate("");
		customFilterRequest.setEndDate("");
		String query = opportunityJDBCRepo.buildCustomFilterQueryForOppIds(customFilterRequest);
		assertThat(query, not(containsString("AND Opp.EffectiveDate >= :startDate")));
		assertThat(query, not(containsString("AND Opp.EffectiveDate <= :endDate")));

	}

	/**
	 * When I build the search query on scheduler Then it will search for only books
	 * with active statuses.
	 */
	@Test
	void testOnlyGettingActiveBookTransferOpps() {
		ScheduleRequest schedule = getSchedule();
		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, containsString("bt.status = 'active'"));
	}

	/**
	 * Given that I have no NBDRelationShip When I go to query the database Then I
	 * will not search by NBDRelationShip
	 */
	@Test
	void testQueryWithNoNBDRelationShip() {
		ScheduleRequest schedule = getSchedule();

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, not(containsString("NBDRelationship")));
	}

	/**
	 * Given that I have a NBDRelationShip When I go to query the database Then I
	 * will search by that NBDRelationShip
	 */
	@Test
	void testQueryWitNBDRelationShip() {
		ScheduleRequest schedule = getSchedule();
		schedule.setNbdRelationship("QBE");

		String query = opportunityJDBCRepo.buildQueryFromTheGivenSchedule(schedule);
		assertThat(query, containsString("AND (bt.status = 'active' AND bt.NBDRelationship = 'QBE')"));

	}

	/**
	 * Given I have a list of opportunities When I build the query Then the query
	 * should be built correctly
	 */
	@Test
	void testBuildQueryToFindOpportunitiesWithIds() {
		Set<String> oppIds = new HashSet<>();
		oppIds.add("123456");
		String expectedQuery = "SELECT * FROM Opportunities Opp WITH (nolock) WHERE OpportunityID IN (:opportunityIDs)";
		String actualQuery = opportunityJDBCRepo.buildQueryToFindOpportunitiesWithIds();
		assertThat(actualQuery, is(expectedQuery));
	}

	@Test
	void testBuildQueryForDownLoadOpportunities() {
		String expectedQuery = "SELECT Opp.lastPolicyGuid, Opp.bookTransferID, Opp.data, Opp.customername, Opp.effectivedate, Opp.opportunityId, Opp.lineType FROM Opportunities Opp WITH (nolock) WHERE OpportunityID IN (:opportunityIDs)";
		String actualQuery = opportunityJDBCRepo.buildQueryForDownLoadOpportunities();
		assertThat(actualQuery, is(expectedQuery));
	}

	@Test
	void testBuildingSearchParametersWithNoDate() {
		PaginationRequest paginationRequest = new PaginationRequest();
		String search = "Hello World";
		paginationRequest.setSearch(search);
		opportunityJDBCRepo.cleanUpSearchIfSearchingForDate(paginationRequest);

		assertThat(paginationRequest.getSearch(), is(search));
	}

	@Test
	void testBuildingSearchParametersWithNoCustomerNameSlash() {
		PaginationRequest paginationRequest = new PaginationRequest();
		String search = "John/Julie";
		paginationRequest.setSearch(search);
		opportunityJDBCRepo.cleanUpSearchIfSearchingForDate(paginationRequest);

		assertThat(paginationRequest.getSearch(), is(search));
	}

	@Test
	void testBuildingSearchParametersWithFullDate() {
		PaginationRequest paginationRequest = new PaginationRequest();
		String search = "05/07/2019";
		paginationRequest.setSearch(search);
		opportunityJDBCRepo.cleanUpSearchIfSearchingForDate(paginationRequest);

		assertThat(paginationRequest.getSearch(), is("2019-05-07"));
	}

	@Test
	void testBuildingSearchParametersWithStartOfYearSearch() {
		PaginationRequest paginationRequest = new PaginationRequest();
		String search = "05/07/";
		paginationRequest.setSearch(search);
		opportunityJDBCRepo.cleanUpSearchIfSearchingForDate(paginationRequest);

		assertThat(paginationRequest.getSearch(), is("05-07"));
	}

	@Test
	void testBuildingSearchParametersWithStartOfYearWithNumberSearch() {
		PaginationRequest paginationRequest = new PaginationRequest();
		String search = "05/07/2";
		paginationRequest.setSearch(search);
		opportunityJDBCRepo.cleanUpSearchIfSearchingForDate(paginationRequest);

		assertThat(paginationRequest.getSearch(), is("2%-05-07"));
	}

	@Test
	void testBuildingSearchParametersWithMonthStart() {
		PaginationRequest paginationRequest = new PaginationRequest();
		String search = "05/";
		paginationRequest.setSearch(search);
		opportunityJDBCRepo.cleanUpSearchIfSearchingForDate(paginationRequest);

		assertThat(paginationRequest.getSearch(), is("05-"));
	}

	@Test
	void testBuildingSearchParametersWithMonth() {
		PaginationRequest paginationRequest = new PaginationRequest();
		String search = "05/07";
		paginationRequest.setSearch(search);
		opportunityJDBCRepo.cleanUpSearchIfSearchingForDate(paginationRequest);

		assertThat(paginationRequest.getSearch(), is("05-07"));
	}
	
	@Test
	void testGetRestrictedStatuses() {
		String expectedQuery = " And Opp.Status NOT in (13,10,11)";
		String actualQuery = opportunityJDBCRepo.getRestrictedStatuses();
		assertThat(actualQuery, is(expectedQuery));
	}

	@Test
	void testGetEffectiveDateRange_2Partitions() {
		EffectiveDateRange firstDateRange = new EffectiveDateRange()
				.setMinEffectiveDate("2016-01-01")
				.setMaxEffectiveDate("2017-01-01");
		EffectiveDateRange secondDateRange = new EffectiveDateRange()
				.setMinEffectiveDate("2015-01-01")
				.setMaxEffectiveDate("2018-01-01");
		Mockito.doReturn(firstDateRange).doReturn(secondDateRange).when(parameterJdbcTemplate)
				.queryForObject(anyString(), any(SqlParameterSource.class), any(RowMapper.class));

		EffectiveDateRange actualDateRange = opportunityJDBCRepo
				.getCustomFilterOpportunitiesEffectiveDateRange(
						IntStream.rangeClosed(1, 3000).boxed().collect(Collectors.toList()));

		EffectiveDateRange expectedDateRange = new EffectiveDateRange()
				.setMinEffectiveDate("2015-01-01")
				.setMaxEffectiveDate("2018-01-01");
		assertEquals(expectedDateRange, actualDateRange);
	}

	@Test
	void testMaxDate() {
		String d1 = "2017-09-18";
		String d2 = "2018-09-18";

		assertEquals(d2, OpportunityJDBCRepo.maxDate(d1, d2));
		assertEquals(d2, OpportunityJDBCRepo.maxDate(d2, d1));
	}

	@Test
	void testMinDate() {
		String d1 = "2017-09-18";
		String d2 = "2018-09-18";

		assertEquals(d1, OpportunityJDBCRepo.minDate(d1, d2));
		assertEquals(d1, OpportunityJDBCRepo.minDate(d2, d1));
	}
}

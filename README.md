# OpportunityService

<!-- shields -->

[![Build Status](https://shields.lmig.com/bamboo/tests/BOOK/OPPORTUN.svg?style=flat-square)](https://forge.lmig.com/builds/browse/BOOK-OPPORTUN)<!-- /shields -->

---

## Project Structure

**[/src/main/deployment/](./src/main/deployment/)** - contains Cloud Foundry manifest files for each of your deployment environments

## Lucidcharts

[Upload Flows](https://lucid.app/lucidchart/209a9692-b842-429b-a60a-5b5e96b3a8fc/edit?invitationId=inv_82c62e6a-1e15-43e0-9b7e-2243b8141e07&page=0_0#)
[AQE PL Manual Quote](https://lucid.app/lucidchart/ba9efd81-ffec-4dc3-91e7-b5f2db4884f1/edit?page=EMTt_lZNr-9h&invitationId=inv_ef7cc0bf-8595-4387-a602-701499b8a062#)

## Packaging
This project packages the `/client` subdirectory into a jar file for external use. 
This jar defines how to call the API endpoints based upon a user-given RestTemplate. 

After giving a RestTemplate, the service will autoconfigure a `OpportunityService` for use. 
The `client` subdirectory contains the domains returned by this service or controllers.

## Mongo Connections
This application attempts to connect to mongo by default. If you supply the profile `mongodisabled`,
then the application will not attempt to connect to a remote mongo instance (and will fail to run).
If you also supply the profile `mongoembedded`, a local mongo instance is booted up at run time to simulate a successful connection.


## Running locally
1. In IntelliJ, click on the dropdown to the left of the play/debug button
2. Select "Edit Configurations..."
3. In the field "Active Profiles" add "local"
4. Click "Apply" and then "OK"
5. In src/main/resources, if you don't have a file called "application-local.properties", create it and get the contents of the file from another teammate
6. If you see an error with rabbitMQ, you can ignore it - the application will run fine locally without it
7. List of secrets / their corresponding spots in the ".properties" file
    - book-transfer-aqe-sql-server-db
        - accessUrlValue -> spring.datasource.url
        - accessKeyId -> spring.datasource.username
        - secretAccessKey-> spring.datasource.password
    - opportunityServiceUser
        - userAccessKeyId -> cloud.aws.credentials.access-key
        - userSecretAccessKey -> cloud.aws.credentials.secret-key
